[{"id": "find_money_1", "content": "你在路边发现了一袋银两，看来是有人不小心遗落的。", "type": "money", "base_reward": {"银两": [5, 10]}, "weight": 25}, {"id": "find_money_2", "content": "你在古井旁发现了一个小钱袋，里面装着不少银两。", "type": "money", "base_reward": {"银两": [5, 10]}, "weight": 20}, {"id": "merchant_gift", "content": "你遇到了一位慷慨的商人，他感谢你指路，送给你一些银两。", "type": "money", "base_reward": {"银两": [5, 50]}, "weight": 25}, {"id": "help_elder", "content": "你帮助了一位迷路的老者，他感激地传授了你一些武学心得。", "type": "experience", "base_reward": {"武学点": [5, 12]}, "weight": 20}, {"id": "martial_master", "content": "你遇到了一位江湖前辈，他指点你武功，让你受益匪浅。", "type": "experience", "base_reward": {"武学点": [20, 50]}, "weight": 15}, {"id": "tavern_story", "content": "你在一家客栈里听到了一些江湖秘闻，增长了见识。", "type": "experience", "base_reward": {"历练值": [30, 80]}, "weight": 20}, {"id": "ancient_relic", "content": "你在古庙中发现了一块古老的石碑，从中领悟了一些武学奥义。", "type": "skill_book", "base_reward": {"武学点": [10, 20]}, "special_reward": {"type": "skill_book", "probability": 0.3}, "weight": 8}, {"id": "scholar_meeting", "content": "你与一位博学的书生交流，从他那里学到了很多江湖知识。", "type": "experience", "base_reward": {"历练值": [10, 30]}, "weight": 15}, {"id": "hermit_gift", "content": "你在深山中遇到了一位隐士，他赠送给你一些修炼心得。", "type": "skill_book", "base_reward": {"武学点": [50, 100]}, "special_reward": {"type": "skill_book", "probability": 0.8}, "weight": 50}, {"id": "treasure_discovery", "content": "你发现了一个隐藏的宝箱，里面有银两和武功秘籍。", "type": "mixed", "base_reward": {"银两": [50, 100], "历练值": [50, 100]}, "special_reward": {"type": "skill_book", "probability": 0.4}, "weight": 50}, {"id": "find_weapon", "content": "你在路边发现了一把遗落的武器，虽然品质普通，但还算实用。", "type": "equipment", "base_reward": {"银两": [10, 30]}, "special_reward": {"type": "equipment", "equipment_type": "weapon", "quality": "common", "probability": 1.0}, "weight": 15}, {"id": "blacksmith_gift", "content": "你帮助了一位铁匠搬运材料，他感激地送给你一件护甲。", "type": "equipment", "base_reward": {"银两": [10, 30]}, "special_reward": {"type": "equipment", "equipment_type": "armor", "quality": "common", "probability": 1.0}, "weight": 15}, {"id": "merchant_helmet", "content": "你从一位行商那里以优惠价格买到了一顶头盔。", "type": "equipment", "base_reward": {"银两": [5, 15]}, "special_reward": {"type": "equipment", "equipment_type": "helmet", "quality": "common", "probability": 1.0}, "weight": 12}, {"id": "tailor_shoes", "content": "你帮助了一位裁缝整理布料，他送给你一双新鞋。", "type": "equipment", "base_reward": {"银两": [5, 15]}, "special_reward": {"type": "equipment", "equipment_type": "shoes", "quality": "common", "probability": 1.0}, "weight": 12}, {"id": "jeweler_necklace", "content": "你在珠宝店帮忙，店主赠送了你一条朴素的项链。", "type": "equipment", "base_reward": {"银两": [5, 20]}, "special_reward": {"type": "equipment", "equipment_type": "necklace", "quality": "common", "probability": 1.0}, "weight": 10}, {"id": "craftsman_ring", "content": "你遇到了一位手工艺人，他制作了一枚简单的戒指送给你。", "type": "equipment", "base_reward": {"银两": [5, 20]}, "special_reward": {"type": "equipment", "equipment_type": "ring", "quality": "common", "probability": 1.0}, "weight": 10}, {"id": "guard_shield", "content": "你协助城门守卫维持秩序，他们奖励了你一面盾牌。", "type": "equipment", "base_reward": {"银两": [10, 25]}, "special_reward": {"type": "equipment", "equipment_type": "shield", "quality": "common", "probability": 1.0}, "weight": 8}]