import sqlite3
import json
import os

db_path = os.path.join(os.path.dirname(__file__), 'game.db')
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

# 找到测试账号
cursor.execute("SELECT id FROM users WHERE username=?", ("t0001",))
row = cursor.fetchone()
if not row:
    print("未找到测试账号")
    exit(1)
user_id = row[0]

# 取出玩家数据
cursor.execute("SELECT id, data FROM players WHERE user_id=?", (user_id,))
row = cursor.fetchone()
if not row:
    print("未找到玩家数据")
    exit(1)
player_id, data_json = row
player_data = json.loads(data_json)

# 添加生活技能
for skill in ['采药', '伐木', '挖矿', '剥皮']:
    player_data.setdefault('martial_skills', {})[skill] = {
        'level': 1, 'exp': 0, 'unlocked': True, 'equipped': False
    }

# 更新回数据库
cursor.execute("UPDATE players SET data=? WHERE id=?", (json.dumps(player_data, ensure_ascii=False), player_id))
conn.commit()
conn.close()
print('已为测试账号添加生活技能')