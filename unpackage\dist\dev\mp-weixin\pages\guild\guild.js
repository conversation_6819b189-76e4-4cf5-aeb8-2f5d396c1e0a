"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_gameState = require("../../utils/gameState.js");
const utils_gameData = require("../../utils/gameData.js");
const _sfc_main = {
  data() {
    return {
      player: {},
      playerGuild: null,
      sectInfo: null,
      showTasksSection: false,
      showSkillsSection: false,
      showMembersSection: false,
      showShopSection: false,
      showGuildListModal: false,
      showTaskDetail: false,
      selectedTask: null,
      availableGuilds: [],
      loading: false,
      availableTasks: [],
      guildSkills: [],
      guildMembers: [],
      guildShopItems: []
    };
  },
  onLoad() {
    this.updateData();
    this.loadSectInfo();
  },
  onShow() {
    this.updateData();
    this.loadSectInfo();
  },
  methods: {
    updateData() {
      this.player = { ...utils_gameState.gameState.player };
    },
    // 加载门派信息
    async loadSectInfo() {
      if (!utils_gameState.gameState.isAuthed) {
        return;
      }
      try {
        this.loading = true;
        const sectInfoResponse = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_info" }
        });
        if (sectInfoResponse.type === "sect_info_success") {
          if (sectInfoResponse.data.has_sect) {
            this.sectInfo = sectInfoResponse.data;
            this.playerGuild = {
              id: sectInfoResponse.data.sect_id,
              name: sectInfoResponse.data.sect_name,
              level: sectInfoResponse.data.rank,
              contribution: sectInfoResponse.data.contribution,
              position: sectInfoResponse.data.rank_name
            };
          } else {
            this.sectInfo = null;
            this.playerGuild = null;
          }
        }
        if (!this.playerGuild) {
          const availableResponse = await utils_gameData.gameUtils.sendMessage({
            type: "sect_action",
            data: { action: "get_available_sects" }
          });
          if (availableResponse.type === "available_sects_success") {
            this.availableGuilds = availableResponse.data.sects.map((sect) => ({
              id: sect.sect_id,
              name: sect.sect_name,
              description: sect.description,
              requirement: sect.requirements.level || 1,
              can_join: sect.can_join,
              reasons: sect.reasons
            }));
          }
        }
      } catch (error) {
        console.error("加载门派信息失败:", error);
        common_vendor.index.showToast({
          title: "加载门派信息失败",
          icon: "none"
        });
      } finally {
        this.loading = false;
      }
    },
    // 领取每日奖励
    async claimDailyReward() {
      var _a;
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "claim_daily_reward" }
        });
        if (response.type === "daily_reward_success") {
          common_vendor.index.showToast({
            title: response.data.message,
            icon: "success"
          });
          await this.loadSectInfo();
          this.updateData();
        } else {
          common_vendor.index.showToast({
            title: ((_a = response.data) == null ? void 0 : _a.message) || "领取失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("领取每日奖励失败:", error);
        common_vendor.index.showToast({
          title: "领取失败",
          icon: "none"
        });
      }
    },
    // 加载门派武功
    async loadSectSkills() {
      var _a;
      if (!utils_gameState.gameState.isAuthed || !this.playerGuild) {
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "sect_action",
          data: { action: "get_sect_skills" }
        });
        if (response.type === "sect_skills_success") {
          this.guildSkills = response.data.skills.map((skill) => ({
            id: skill.skill_name,
            name: skill.skill_name,
            type: skill.skill_type,
            quality: skill.quality,
            weapon: skill.weapon,
            description: `攻击+${skill.attack} 防御+${skill.defense} 内力+${skill.internal_power}`,
            rank_requirement: skill.rank_requirement,
            contribution_requirement: skill.contribution_requirement,
            can_learn: skill.can_learn,
            reason: skill.reason.join("，"),
            learned: false
            // TODO: 检查是否已学会
          }));
        } else {
          console.error("加载门派武功失败:", (_a = response.data) == null ? void 0 : _a.message);
        }
      } catch (error) {
        console.error("加载门派武功失败:", error);
      }
    },
    getPositionName(position) {
      const positions = {
        "master": "掌门",
        "elder": "长老",
        "disciple": "弟子",
        "outer": "外门弟子"
      };
      return positions[position] || "弟子";
    },
    showGuildList() {
      this.showGuildListModal = true;
    },
    closeGuildList() {
      this.showGuildListModal = false;
    },
    async selectGuild(guild) {
      if (!guild.can_join) {
        const reasons = guild.reasons.join("，");
        common_vendor.index.showToast({
          title: `无法加入：${reasons}`,
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认加入",
        content: `确定要加入 ${guild.name} 吗？`,
        success: async (res) => {
          var _a;
          if (res.confirm) {
            try {
              const response = await utils_gameData.gameUtils.sendMessage({
                type: "sect_action",
                data: {
                  action: "join_sect",
                  sect_id: guild.id
                }
              });
              if (response.type === "join_sect_success") {
                this.closeGuildList();
                common_vendor.index.showToast({
                  title: response.data.message,
                  icon: "success"
                });
                await this.loadSectInfo();
              } else {
                common_vendor.index.showToast({
                  title: ((_a = response.data) == null ? void 0 : _a.message) || "加入门派失败",
                  icon: "none"
                });
              }
            } catch (error) {
              console.error("加入门派失败:", error);
              common_vendor.index.showToast({
                title: "加入门派失败",
                icon: "none"
              });
            }
          }
        }
      });
    },
    showTasks() {
      this.showTasksSection = true;
      this.hideOtherSections();
    },
    hideTasks() {
      this.showTasksSection = false;
    },
    async showSkills() {
      this.showSkillsSection = true;
      this.hideOtherSections();
      await this.loadSectSkills();
    },
    hideSkills() {
      this.showSkillsSection = false;
    },
    showMembers() {
      this.showMembersSection = true;
      this.hideOtherSections();
    },
    hideMembers() {
      this.showMembersSection = false;
    },
    showShop() {
      this.showShopSection = true;
      this.hideOtherSections();
    },
    hideShop() {
      this.showShopSection = false;
    },
    hideOtherSections() {
      this.showTasksSection = false;
      this.showSkillsSection = false;
      this.showMembersSection = false;
      this.showShopSection = false;
    },
    getDifficultyClass(difficulty) {
      const classes = {
        "easy": "difficulty-easy",
        "medium": "difficulty-medium",
        "hard": "difficulty-hard"
      };
      return classes[difficulty] || "difficulty-easy";
    },
    getDifficultyName(difficulty) {
      const names = {
        "easy": "简单",
        "medium": "中等",
        "hard": "困难"
      };
      return names[difficulty] || "简单";
    },
    getSkillTypeName(type) {
      const types = {
        "external": "外功",
        "internal": "内功",
        "light": "轻功",
        "heart": "心法",
        "special": "特技"
      };
      return types[type] || "武功";
    },
    canAcceptTask(task) {
      return true;
    },
    canLearnSkill(skill) {
      return skill.can_learn;
    },
    canBuyItem(item) {
      return this.playerGuild.contribution >= item.price;
    },
    showTaskDetail(task) {
      this.selectedTask = task;
      this.showTaskDetail = true;
    },
    closeTaskDetail() {
      this.showTaskDetail = false;
      this.selectedTask = null;
    },
    acceptTask(task) {
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!this.canAcceptTask(task)) {
        common_vendor.index.showToast({
          title: "不满足任务要求",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "接受任务",
        content: `确定要接受任务 "${task.name}" 吗？`,
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "任务已接受",
              icon: "success"
            });
            this.closeTaskDetail();
          }
        }
      });
    },
    showSkillDetail(skill) {
    },
    async learnSkill(skill) {
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!skill.can_learn) {
        common_vendor.index.showToast({
          title: skill.reason || "无法学习",
          icon: "none"
        });
        return;
      }
      if (skill.learned) {
        common_vendor.index.showToast({
          title: "开始修炼",
          icon: "success"
        });
      } else {
        common_vendor.index.showModal({
          title: "学习武功",
          content: `确定要学习 ${skill.name} 吗？
消耗贡献: ${skill.contribution_requirement}`,
          success: async (res) => {
            var _a;
            if (res.confirm) {
              try {
                const response = await utils_gameData.gameUtils.sendMessage({
                  type: "sect_action",
                  data: {
                    action: "learn_sect_skill",
                    skill_name: skill.name
                  }
                });
                if (response.type === "learn_skill_success") {
                  common_vendor.index.showToast({
                    title: response.data.message,
                    icon: "success"
                  });
                  await this.loadSectInfo();
                  await this.loadSectSkills();
                } else {
                  common_vendor.index.showToast({
                    title: ((_a = response.data) == null ? void 0 : _a.message) || "学习失败",
                    icon: "none"
                  });
                }
              } catch (error) {
                console.error("学习武功失败:", error);
                common_vendor.index.showToast({
                  title: "学习失败",
                  icon: "none"
                });
              }
            }
          }
        });
      }
    },
    showShopItemDetail(item) {
    },
    buyShopItem(item) {
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!this.canBuyItem(item)) {
        common_vendor.index.showToast({
          title: "贡献不足",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "购买物品",
        content: `确定要购买 ${item.name} 吗？
消耗贡献: ${item.price}`,
        success: (res) => {
          if (res.confirm) {
            this.playerGuild.contribution -= item.price;
            const type = item.type || "";
            const sellable = (typeof item.sellable !== "undefined" ? item.sellable : true) ? true : false;
            const unique_id = item.unique_id || `${item.id}_${Date.now()}_${Math.floor(Math.random() * 1e4)}`;
            utils_gameState.gameState.addItem({ ...item, type, sellable, unique_id });
            utils_gameState.gameState.save();
            common_vendor.index.showToast({
              title: "购买成功！",
              icon: "success"
            });
          }
        }
      });
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.playerGuild
  }, $data.playerGuild ? common_vendor.e({
    b: common_vendor.t($data.playerGuild.name),
    c: common_vendor.t($data.playerGuild.level),
    d: common_vendor.t($data.player.reputation || 0),
    e: common_vendor.t($data.playerGuild.contribution),
    f: common_vendor.t($options.getPositionName($data.playerGuild.position)),
    g: $data.sectInfo
  }, $data.sectInfo ? {
    h: common_vendor.t($data.sectInfo.can_claim_daily ? "领取每日奖励" : "今日已领取"),
    i: common_vendor.o((...args) => $options.claimDailyReward && $options.claimDailyReward(...args)),
    j: !$data.sectInfo.can_claim_daily
  } : {}) : {
    k: common_vendor.o((...args) => $options.showGuildList && $options.showGuildList(...args))
  }, {
    l: $data.playerGuild
  }, $data.playerGuild ? {
    m: common_vendor.o((...args) => $options.showTasks && $options.showTasks(...args)),
    n: common_vendor.o((...args) => $options.showSkills && $options.showSkills(...args)),
    o: common_vendor.o((...args) => $options.showMembers && $options.showMembers(...args)),
    p: common_vendor.o((...args) => $options.showShop && $options.showShop(...args))
  } : {}, {
    q: $data.showTasksSection
  }, $data.showTasksSection ? common_vendor.e({
    r: common_vendor.o((...args) => $options.hideTasks && $options.hideTasks(...args)),
    s: common_vendor.f($data.availableTasks, (task, index, i0) => {
      return {
        a: common_vendor.t(task.name),
        b: common_vendor.t(task.description),
        c: common_vendor.t(task.reward),
        d: common_vendor.t($options.getDifficultyName(task.difficulty)),
        e: common_vendor.n($options.getDifficultyClass(task.difficulty)),
        f: common_vendor.o(($event) => $options.acceptTask(task), index),
        g: !$options.canAcceptTask(task),
        h: index,
        i: common_vendor.o(($event) => $options.showTaskDetail(task), index)
      };
    }),
    t: $data.availableTasks.length === 0
  }, $data.availableTasks.length === 0 ? {} : {}) : {}, {
    v: $data.showSkillsSection
  }, $data.showSkillsSection ? {
    w: common_vendor.o((...args) => $options.hideSkills && $options.hideSkills(...args)),
    x: common_vendor.f($data.guildSkills, (skill, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(skill.name),
        b: common_vendor.t($options.getSkillTypeName(skill.type)),
        c: common_vendor.t(skill.description),
        d: skill.level
      }, skill.level ? {
        e: common_vendor.t(skill.level)
      } : {}, {
        f: common_vendor.t(skill.learned ? "修炼" : "学习"),
        g: common_vendor.o(($event) => $options.learnSkill(skill), index),
        h: !$options.canLearnSkill(skill),
        i: index,
        j: common_vendor.o(($event) => $options.showSkillDetail(skill), index)
      });
    })
  } : {}, {
    y: $data.showMembersSection
  }, $data.showMembersSection ? {
    z: common_vendor.o((...args) => $options.hideMembers && $options.hideMembers(...args)),
    A: common_vendor.f($data.guildMembers, (member, index, i0) => {
      return {
        a: common_vendor.t(member.name),
        b: common_vendor.t($options.getPositionName(member.position)),
        c: common_vendor.t(member.level),
        d: common_vendor.t(member.contribution),
        e: index
      };
    })
  } : {}, {
    B: $data.showShopSection
  }, $data.showShopSection ? {
    C: common_vendor.o((...args) => $options.hideShop && $options.hideShop(...args)),
    D: common_vendor.f($data.guildShopItems, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t(item.description),
        c: common_vendor.t(item.price),
        d: common_vendor.o(($event) => $options.buyShopItem(item), index),
        e: !$options.canBuyItem(item),
        f: index,
        g: common_vendor.o(($event) => $options.showShopItemDetail(item), index)
      };
    })
  } : {}, {
    E: $data.showGuildListModal
  }, $data.showGuildListModal ? {
    F: common_vendor.o((...args) => $options.closeGuildList && $options.closeGuildList(...args)),
    G: common_vendor.f($data.availableGuilds, (guild, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(guild.name),
        b: common_vendor.t(guild.description),
        c: guild.reasons && guild.reasons.length > 0
      }, guild.reasons && guild.reasons.length > 0 ? {
        d: common_vendor.t(guild.reasons.join("，"))
      } : {}, {
        e: index,
        f: common_vendor.o(($event) => $options.selectGuild(guild), index),
        g: !guild.can_join ? 1 : ""
      });
    }),
    H: common_vendor.o(() => {
    }),
    I: common_vendor.o((...args) => $options.closeGuildList && $options.closeGuildList(...args))
  } : {}, {
    J: $options.showTaskDetail
  }, $options.showTaskDetail ? common_vendor.e({
    K: common_vendor.o((...args) => $options.closeTaskDetail && $options.closeTaskDetail(...args)),
    L: $data.selectedTask
  }, $data.selectedTask ? {
    M: common_vendor.t($data.selectedTask.name),
    N: common_vendor.t($data.selectedTask.description),
    O: common_vendor.t($data.selectedTask.requirement),
    P: common_vendor.t($data.selectedTask.reward)
  } : {}, {
    Q: common_vendor.o((...args) => $options.closeTaskDetail && $options.closeTaskDetail(...args)),
    R: common_vendor.o(($event) => $options.acceptTask($data.selectedTask)),
    S: !$options.canAcceptTask($data.selectedTask),
    T: common_vendor.o(() => {
    }),
    U: common_vendor.o((...args) => $options.closeTaskDetail && $options.closeTaskDetail(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-44e2720a"]]);
wx.createPage(MiniProgramPage);
