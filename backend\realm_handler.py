#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
境界系统处理模块
负责境界突破相关的业务逻辑处理
"""

import logging

try:
    from realm_system import RealmSystem
except ImportError:
    # 如果模块不存在，提供默认实现
    class RealmSystem:
        @staticmethod
        def get_realm_info(experience):
            return {
                'current_realm': '凡人',
                'progress': 0.0,
                'current_min': 0,
                'current_max': 1000
            }

logger = logging.getLogger(__name__)

class RealmHandler:
    """境界系统处理器"""
    
    def __init__(self, bonus_system):
        self.bonus_system = bonus_system
    
    def get_realm_info(self, experience: int) -> dict:
        """根据历练值计算境界信息"""
        return RealmSystem.get_realm_info(experience)
    
    def check_realm_breakthrough(self, player: dict, old_experience: int) -> dict:
        """检查是否突破境界
        
        Returns:
            dict: 境界突破信息，如果没有突破则返回None
        """
        old_realm = self.get_realm_info(old_experience)['current_realm']
        new_realm = player['realm_info']['current_realm']
        
        if old_realm != new_realm:
            return {
                'old_realm': old_realm,
                'new_realm': new_realm,
                'message': f'恭喜！你突破到了{new_realm}境界！'
            }
        
        return None
    
    def calculate_realm_breakthrough_rewards(self, player: dict) -> dict:
        """计算境界突破奖励
        
        Returns:
            dict: 奖励信息
        """
        # 境界突破奖励武学点（应用境界增益）
        base_skill_points_reward = 5
        skill_points_gain_bonus = player.get('skill_points_gain_bonus', 0)
        skill_points_reward = base_skill_points_reward + skill_points_gain_bonus
        
        return {
            '武学点': skill_points_reward
        }
    
    async def handle_realm_breakthrough(self, player: dict, user_id: str, save_func, update_bonuses_func) -> dict:
        """处理境界突破请求"""
        try:
            # 检查是否可以突破
            current_experience = player.get('experience', 0)
            realm_info = self.get_realm_info(current_experience)
            
            # 检查是否已达到当前境界的最大经验值
            if realm_info['progress'] < 1.0:
                return {
                    'type': 'realm_breakthrough_failed',
                    'data': {'message': f'当前境界进度不足，无法突破。进度：{realm_info["progress"]:.1%}'}
                }
            
            # 检查是否已经是最高境界
            if realm_info['current_realm'] == '大罗金仙':
                return {
                    'type': 'realm_breakthrough_failed',
                    'data': {'message': '你已达到最高境界，无法继续突破。'}
                }
            
            # 计算突破所需的额外经验值
            breakthrough_cost = realm_info['current_max'] - realm_info['current_min']
            required_experience = realm_info['current_max']
            
            if current_experience < required_experience:
                return {
                    'type': 'realm_breakthrough_failed',
                    'data': {'message': f'历练值不足，需要{required_experience}点历练值才能突破。'}
                }
            
            # 执行境界突破
            old_realm = realm_info['current_realm']
            
            # 突破后的经验值（保留超出部分）
            progress = current_experience - realm_info['current_min']
            
            # 境界突破，提升境界
            player['experience'] = progress
            player['realm_info'] = self.get_realm_info(player['experience'])
            new_realm = player['realm_info']['current_realm']
            
            # 使用统一的增益系统应用境界增益
            player = await update_bonuses_func(str(user_id), player)
            
            # 境界突破奖励武学点
            base_skill_points_reward = 5
            skill_points_gain_bonus = player.get('skill_points_gain_bonus', 0)
            skill_points_reward = base_skill_points_reward + skill_points_gain_bonus
            player['skill_points'] = player.get('skill_points', 0) + skill_points_reward
            
            # 保存玩家数据
            await save_func(str(user_id), player)
            
            logger.info(f"玩家 {user_id} 境界突破：{old_realm} -> {new_realm}")
            
            return {
                'type': 'realm_breakthrough_success',
                'data': {
                    'message': f'恭喜！成功突破到{new_realm}境界！',
                    'old_realm': old_realm,
                    'new_realm': new_realm,
                    'skill_points_reward': skill_points_reward,
                    'player': player
                }
            }
            
        except Exception as e:
            logger.error(f"境界突破处理失败: {e}")
            return {
                'type': 'realm_breakthrough_failed',
                'data': {'message': f'境界突破失败：{str(e)}'}
            }
    
    def calculate_experience_reward(self, base_reward: int, player: dict) -> int:
        """计算历练值奖励（应用境界增益）"""
        experience_gain_bonus = player.get('experience_gain_bonus', 0)
        return base_reward + experience_gain_bonus
    
    def apply_realm_bonuses_to_rewards(self, rewards: dict, player: dict) -> dict:
        """应用境界增益到奖励"""
        # 历练值增益
        if '历练值' in rewards:
            experience_gain_bonus = player.get('experience_gain_bonus', 0)
            rewards['历练值'] += experience_gain_bonus
        
        # 武学点增益
        if '武学点' in rewards:
            skill_points_gain_bonus = player.get('skill_points_gain_bonus', 0)
            rewards['武学点'] += skill_points_gain_bonus
        
        # 银两增益
        if '银两' in rewards:
            money_gain_bonus = player.get('money_gain_bonus', 0)
            rewards['银两'] += money_gain_bonus
        
        return rewards
