import sqlite3
import json
from enhanced_martial_system import enhanced_martial_system, MARTIAL_CATEGORIES
import os

def add_all_martials_to_user(username='t0001', db_path=os.path.join(os.path.dirname(__file__), 'game.db')):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 获取用户id
    cursor.execute("SELECT id FROM users WHERE username=?", (username,))
    row = cursor.fetchone()
    if not row:
        print(f"未找到账号: {username}")
        return
    user_id = row[0]

    # 获取玩家数据
    cursor.execute("SELECT id, data FROM players WHERE user_id=?", (user_id,))
    row = cursor.fetchone()
    if not row:
        print(f"未找到玩家数据: {username}")
        return
    player_id, data_json = row
    player_data = json.loads(data_json)

    # 获取所有武学
    all_martials = set()
    for level in ['基础', '初级', '中级', '高级', '绝学']:
        all_martials.update(enhanced_martial_system.get_martial_by_level(level))
    for category in MARTIAL_CATEGORIES.keys():
        all_martials.update(enhanced_martial_system.get_martial_by_category(category))

    # 添加所有武学到玩家 martial_skills
    player_data.setdefault('martial_skills', {})
    for martial in all_martials:
        player_data['martial_skills'][martial] = {
            'level': 0, 'exp': 0, 'unlocked': True, 'equipped': False
        }

    # 更新数据库
    cursor.execute("UPDATE players SET data=? WHERE id=?", (json.dumps(player_data, ensure_ascii=False), player_id))
    conn.commit()
    conn.close()
    print(f"账号 {username} 已添加全部武学。")

if __name__ == '__main__':
    import sys
    # 支持命令行参数传用户名
    if len(sys.argv) > 1:
        add_all_martials_to_user(sys.argv[1])
    else:
        add_all_martials_to_user()