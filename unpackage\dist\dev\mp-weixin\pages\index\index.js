"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_gameState = require("../../utils/gameState.js");
const utils_gameData = require("../../utils/gameData.js");
const utils_websocket = require("../../utils/websocket.js");
const GatheringPopup = () => "../../components/GatheringPopup.js";
const BattlePopup = () => "../../components/BattlePopup.js";
const _sfc_main = {
  components: { GatheringPopup, BattlePopup },
  data() {
    return {
      player: {},
      money: 0,
      gold: 0,
      status: "normal",
      eventLog: [],
      requiredExp: 0,
      connectionStatus: "未连接",
      isAuthed: false,
      showGatheringPopup: false,
      gatheringEvent: null,
      gatheringTimes: 0,
      gatheringResult: "",
      lastGatheringResultTime: 0,
      // 防止重复处理采集结果
      currentMap: null,
      showMapPopup: false,
      announcementText: "欢迎来到仗剑江湖行！系统运行正常，祝您游戏愉快！新版本已上线，新增多种武功秘籍和装备道具，快来体验吧！",
      mapNpcs: [],
      showNpcMenuModal: false,
      selectedNpc: {},
      mapsConfig: {},
      // 购买弹窗相关
      showBuyModal: false,
      buyItem: {},
      buyQuantity: 1,
      buyNpcName: "",
      showBattlePopup: false,
      battleLog: [],
      // 排行榜弹窗
      showRankingModal: false,
      rankingCurrentTab: "wealth",
      rankingLoading: false,
      rankingList: [],
      myRanking: null,
      // 兑换码弹窗
      showRedeemModal: false,
      redeemCode: "",
      redeemLoading: false,
      // 排行榜弹窗
      showRankingModal: false,
      rankingCurrentTab: "wealth",
      rankingLoading: false,
      rankingList: [],
      myRanking: null,
      battlePlayer: {},
      battleMonster: {},
      battleStage: "",
      battleAttackMode: "",
      healingMeditationMessages: [],
      healingMeditationLoading: false,
      // 新增：疗伤/打坐loading
      npcSidebarVisible: false,
      // 新增：NPC侧边栏显示状态
      npcBtnTop: "70%",
      // NPC按钮初始top，调整为屏幕下半部分
      npcBtnDragging: false,
      npcBtnStartY: 0,
      npcBtnStartTop: 0,
      // 侠客相关
      playerSidebarVisible: false,
      // 侠客侧边栏显示状态
      playerBtnTop: "60%",
      // 侠客按钮初始位置
      playerBtnDragging: false,
      playerBtnStartY: 0,
      playerBtnStartTop: 0,
      mapPlayers: [],
      // 当前地图的玩家列表
      showPlayerMenuModal: false,
      selectedPlayer: {},
      // 聊天相关
      showChatPopup: false,
      chatMessages: [],
      chatInputText: "",
      chatTargetName: "",
      currentChatType: "world",
      chatTypeIndex: 0,
      chatTypes: [
        { value: "world", name: "世界" },
        { value: "private", name: "私聊" },
        { value: "rumor", name: "谣言" }
      ],
      leftMenuVisible: false,
      leftBtnTop: "70%",
      // 左侧按钮初始top
      leftBtnDragging: false,
      leftBtnStartY: 0,
      leftBtnStartTop: 0,
      // 防重复请求标志
      fetchingBonusSummary: false,
      fetchingMapNpcs: false,
      fetchingMapPlayers: false,
      isInitializing: false,
      // 聊天按钮相关
      chatBtnTop: "55%",
      // 聊天按钮初始位置
      chatBtnDragging: false,
      chatBtnStartY: 0,
      chatBtnStartTop: 0,
      lastBattleHpUpdate: 0
      // 记录最后一次战斗血量更新时间
    };
  },
  computed: {
    // 获取当前背包数据
    currentInventory() {
      const inventory = utils_gameState.gameState.inventory || [];
      console.log("当前背包数据:", inventory);
      return inventory;
    },
    // 聊天类型名称数组
    chatTypeNames() {
      return this.chatTypes.map((type) => type.name);
    },
    hpPercent() {
      const hp = this.player.hp || 0;
      const maxHp = this.player.max_hp || this.player.base_max_hp || 100;
      const percent = hp / maxHp * 100;
      return percent;
    },
    mpPercent() {
      return this.player.mp && (this.player.max_mp || this.player.base_max_mp) ? this.player.mp / (this.player.max_mp || this.player.base_max_mp) * 100 : 100;
    },
    staminaPercent() {
      return this.player.energy && (this.player.max_energy || this.player.base_max_energy) ? this.player.energy / (this.player.max_energy || this.player.base_max_energy) * 100 : 100;
    },
    energyPercent() {
      return this.player.spirit && this.player.max_spirit ? this.player.spirit / this.player.max_spirit * 100 : 100;
    },
    expPercent() {
      return this.player.exp && this.requiredExp ? this.player.exp / this.requiredExp * 100 : 0;
    },
    connectionStatusClass() {
      const status = this.connectionStatus;
      if (status === "已连接")
        return "connected";
      if (status === "未连接")
        return "disconnected";
      if (status === "连接失败")
        return "disconnected";
      return "disconnected";
    },
    currentMapName() {
      const mapId = this.player && this.player.current_map;
      const mapObj = this.mapsConfig && mapId ? this.mapsConfig[mapId] : null;
      return mapObj ? mapObj.名称 || mapObj.name : "未知";
    },
    mapList() {
      if (!this.mapsConfig)
        return [];
      return Object.values(this.mapsConfig).map((m) => {
        const map = { ...m };
        if (!Array.isArray(map.NPC)) {
          if (typeof map.NPC === "string") {
            map.NPC = map.NPC.split(",").map((n) => ({ 名称: n.trim() }));
          } else {
            map.NPC = [];
          }
        }
        if (!Array.isArray(map.怪物)) {
          if (typeof map.怪物 === "string") {
            map.怪物 = map.怪物.split(",").map((n) => ({ 名称: n.trim() }));
          } else {
            map.怪物 = [];
          }
        }
        if (!Array.isArray(map.monsters)) {
          if (typeof map.monsters === "string") {
            map.monsters = map.monsters.split(",").map((n) => ({ name: n.trim() }));
          } else {
            map.monsters = [];
          }
        }
        if (!Array.isArray(map.采集物品)) {
          if (typeof map.采集物品 === "string") {
            map.采集物品 = map.采集物品.split(",").map((item) => {
              const [name, prob] = item.split(":");
              return { 物品: name.trim(), 概率: prob ? parseFloat(prob) : void 0 };
            });
          } else {
            map.采集物品 = [];
          }
        }
        if (!Array.isArray(map.gather_items)) {
          if (typeof map.gather_items === "string") {
            map.gather_items = map.gather_items.split(",").map((item) => {
              const [name, prob] = item.split(":");
              return { item: name.trim(), prob: prob ? parseFloat(prob) : void 0 };
            });
          } else {
            map.gather_items = [];
          }
        }
        return map;
      });
    },
    currentMapNpcsLocal() {
      const mapId = this.player && this.player.current_map;
      const map = this.mapsConfig && this.mapsConfig[mapId];
      if (!map || !Array.isArray(map.NPC))
        return [];
      return map.NPC.map((name, idx) => ({
        id: `npc_${idx}`,
        name,
        avatar: "static/npc/default.png",
        desc: `${name}：一位神秘的江湖人物。`,
        functions: [
          { key: "talk", label: "对话" },
          { key: "shop", label: "交易" }
        ]
      }));
    },
    displayMapNpcs() {
      return this.mapNpcs && this.mapNpcs.length > 0 ? this.mapNpcs : this.currentMapNpcsLocal;
    },
    getStrengthBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.strength) == null ? void 0 : _c.bonus_percentage) || 0;
    },
    getIntelligenceBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.intelligence) == null ? void 0 : _c.bonus_percentage) || 0;
    },
    getAgilityDefenseBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.agility) == null ? void 0 : _c.defense_bonus_percentage) || 0;
    },
    getConstitutionBonus() {
      return 0;
    },
    getConstitutionHpBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.constitution) == null ? void 0 : _c.hp_bonus_percentage) || 0;
    },
    get hasTalentBonuses() {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;
      return (((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.strength) == null ? void 0 : _c.bonus_percentage) || 0) > 0 || (((_f = (_e = (_d = this.player) == null ? void 0 : _d.talent_bonuses) == null ? void 0 : _e.intelligence) == null ? void 0 : _f.bonus_percentage) || 0) > 0 || (((_i = (_h = (_g = this.player) == null ? void 0 : _g.talent_bonuses) == null ? void 0 : _h.agility) == null ? void 0 : _i.defense_bonus_percentage) || 0) > 0 || (((_l = (_k = (_j = this.player) == null ? void 0 : _j.talent_bonuses) == null ? void 0 : _k.constitution) == null ? void 0 : _l.hp_bonus_percentage) || 0) > 0;
    },
    // 是否可以购买
    canBuy() {
      const totalPrice = (this.buyItem.price || 0) * this.buyQuantity;
      const hasEnoughMoney = this.money >= totalPrice;
      const hasStock = (this.buyItem.stock || 0) >= this.buyQuantity;
      const validQuantity = this.buyQuantity > 0;
      return hasEnoughMoney && hasStock && validQuantity;
    }
  },
  async onLoad() {
    await this.loadMapsConfig();
    this.checkLoginAndInit();
    if (typeof utils_gameState.gameState.onUpdate === "function") {
      utils_gameState.gameState.onUpdate(this.handleStateUpdate);
    }
    this.updateData();
    utils_websocket.wsManager.on("announcement", (data) => {
      if (data && data.content) {
        this.announcementText = data.content;
      }
    });
    utils_websocket.wsManager.on("game_event", this.handleGameEvent);
    this.fetchBonusSummary();
    setTimeout(() => {
      this.fetchMapNpcs();
    }, 2e3);
    utils_websocket.wsManager.on("encounter_monster", this.handleEncounterMonster);
    utils_websocket.wsManager.on("battle_round", this.handleBattleRound);
    utils_websocket.wsManager.on("battle_result", this.handleBattleResult);
    utils_websocket.wsManager.on("player_data", this.handleBattlePlayerData);
    utils_websocket.wsManager.on("escape_battle_result", this.handleEscapeBattleResult);
    console.log("📡 采集结果将通过 Promise 处理，避免重复调用");
  },
  onReady() {
  },
  onShow() {
    if (this.isInitializing) {
      return;
    }
    this.isInitializing = true;
    if (!utils_websocket.wsManager.isConnected) {
      utils_websocket.wsManager.connect().then(() => {
        if (!utils_websocket.wsManager.isAuthed && utils_websocket.wsManager.autoAuthenticate) {
          utils_websocket.wsManager.autoAuthenticate();
        }
        setTimeout(() => {
          if (utils_gameState.gameState.requestAllData)
            utils_gameState.gameState.requestAllData();
          this.isInitializing = false;
        }, 500);
      });
    } else if (!utils_websocket.wsManager.isAuthed && utils_websocket.wsManager.autoAuthenticate) {
      utils_websocket.wsManager.autoAuthenticate();
      setTimeout(() => {
        if (utils_gameState.gameState.requestAllData)
          utils_gameState.gameState.requestAllData();
        this.isInitializing = false;
      }, 500);
    } else {
      this.updateConnectionStatus && this.updateConnectionStatus();
      this.isInitializing = false;
    }
    if (this.player && this.player.current_map && !this.fetchingMapPlayers) {
      this.fetchMapPlayers();
    }
  },
  onTabItemTap(item) {
    if (item.index === 2)
      ;
  },
  onUnload() {
    utils_gameState.gameState.offUpdate(this.handleStateUpdate);
    utils_websocket.wsManager.off("game_event", this.handleGameEvent);
    utils_websocket.wsManager.off("select_map_success", this.onSelectMapSuccess);
    utils_websocket.wsManager.off("error", this.onMapError);
  },
  created() {
  },
  watch: {
    "player.current_map": {
      handler(newVal, oldVal) {
        if (newVal && newVal !== oldVal && this.mapList && this.mapList.length) {
          this.fetchMapNpcs();
        }
      },
      immediate: false
    },
    mapList: {
      handler(newVal, oldVal) {
        if (utils_gameState.gameState.player && utils_gameState.gameState.player.current_map && newVal && newVal.length && newVal !== oldVal) {
          this.fetchMapNpcs();
        }
      },
      immediate: false
    }
  },
  methods: {
    checkLoginAndInit() {
      const token = common_vendor.index.getStorageSync("token");
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      if (!token || !userInfo) {
        common_vendor.index.reLaunch({
          url: "/pages/login/login"
        });
        return;
      }
      this.initGame();
    },
    async initGame() {
      try {
        utils_gameState.gameState.onUpdate(this.handleStateUpdate);
        await utils_gameState.gameState.init();
        this.updateConnectionStatus();
        this.updateData();
      } catch (error) {
        this.connectionStatus = "连接失败";
        common_vendor.index.showToast({
          title: "游戏初始化失败: " + error.message,
          icon: "none",
          duration: 3e3
        });
      }
    },
    updateConnectionStatus() {
      this.connectionStatus = utils_websocket.wsManager.isConnected ? "已连接" : "未连接";
      if (!utils_websocket.wsManager.isConnected) {
        this.isAuthed = false;
      }
    },
    handleStateUpdate(type, gameStateInstance) {
      switch (type) {
        case "player":
          if (gameStateInstance.player) {
            const currentHp = this.player.hp;
            const currentMaxHp = this.player.max_hp;
            const isInBattle = this.showBattlePopup && this.battleStage === "battle";
            this.player = { ...gameStateInstance.player };
            if (isInBattle) {
              if (typeof currentHp !== "undefined" && currentHp !== null) {
                this.player.hp = currentHp;
              }
              if (typeof currentMaxHp !== "undefined" && currentMaxHp !== null) {
                this.player.max_hp = currentMaxHp;
              }
            }
          }
          break;
        case "currency":
          this.money = gameStateInstance.money;
          this.gold = gameStateInstance.gold;
          break;
        case "status":
          this.status = gameStateInstance.status;
          break;
        case "eventLog":
          this.eventLog = gameStateInstance.eventLog.map((log, idx) => {
            if (idx === 0) {
              return { ...log, displayText: "" };
            } else {
              return { ...log, displayText: log.description };
            }
          });
          if (this.eventLog.length > 0) {
            this.typeWriterEffect(this.eventLog[0], 0);
          }
          break;
        case "auth":
          this.isAuthed = gameStateInstance.isAuthed || false;
          if (this.isAuthed)
            ;
          break;
        default:
          this.updateData();
      }
      if (type !== "auth") {
        this.isAuthed = utils_websocket.wsManager.isAuthed || gameStateInstance.isAuthed || false;
      }
    },
    updateData() {
      this.player = utils_gameState.gameState.getPlayerData ? utils_gameState.gameState.getPlayerData() : utils_gameState.gameState.player;
      if (utils_gameState.gameState.player) {
        this.player = { ...utils_gameState.gameState.player };
        if (this.mapList && this.mapList.length && utils_gameState.gameState.player.current_map) {
          this.currentMap = this.mapList.find((m) => m.id === utils_gameState.gameState.player.current_map) || null;
        }
      }
      this.money = this.player.money || utils_gameState.gameState.money || 0;
      this.gold = this.player.gold || utils_gameState.gameState.gold || 0;
      this.status = utils_gameState.gameState.status || "normal";
      this.eventLog = [...utils_gameState.gameState.eventLog || []];
      this.fetchBonusSummary();
      this.fetchMapNpcs();
    },
    formatNumber(num) {
      return utils_gameData.gameUtils.formatNumber(num);
    },
    getStatusText() {
      const statusTexts = {
        "injured": "重伤",
        "internal_injury": "内伤",
        "poisoned": "中毒",
        "tired": "疲劳"
      };
      return statusTexts[this.status] || "正常";
    },
    triggerAdventure() {
      if (!this.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!utils_websocket.wsManager.isConnected) {
        common_vendor.index.showToast({ title: "网络连接失败", icon: "none" });
        return;
      }
      utils_gameState.gameState.triggerAdventure();
    },
    clearLog() {
      common_vendor.index.showModal({
        title: "确认清空",
        content: "确定要清空江湖日志吗？",
        success: (res) => {
          if (res.confirm) {
            utils_gameState.gameState.eventLog = [];
            this.eventLog = [];
            utils_gameState.gameState.save();
          }
        }
      });
    },
    navigateTo(path) {
      common_vendor.index.navigateTo({
        url: path
      });
    },
    testConnection() {
      const token = common_vendor.index.getStorageSync("token");
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      const statusInfo = `WebSocket连接: ${utils_websocket.wsManager.isConnected ? "已连接" : "未连接"}
服务器地址: ${utils_websocket.wsManager.serverUrl}
WebSocket认证: ${utils_websocket.wsManager.isAuthed ? "已认证" : "未认证"}
页面认证: ${this.isAuthed ? "已认证" : "未认证"}
GameState认证: ${utils_gameState.gameState.isAuthed ? "已认证" : "未认证"}
本地token: ${token ? "存在" : "不存在"}
本地userInfo: ${userInfo ? "存在" : "不存在"}
玩家数据: ${utils_gameState.gameState.player ? "存在" : "不存在"}
连接状态: ${this.connectionStatus}`;
      common_vendor.index.showModal({
        title: "连接状态详情",
        content: statusInfo,
        showCancel: true,
        cancelText: "手动认证",
        confirmText: "确定",
        success: (res) => {
          if (res.cancel) {
            utils_websocket.wsManager.autoAuthenticate();
          }
        }
      });
      if (!utils_websocket.wsManager.isConnected) {
        utils_websocket.wsManager.connect().then(() => {
          this.updateConnectionStatus();
        }).catch((error) => {
        });
      }
    },
    // 新增：根据装备动态计算可采集次数
    getGatherToolInfo(requiredTool, requiredLevel = 1) {
      console.log("查找工具:", requiredTool, "需要等级:", requiredLevel);
      const toolTypeMap = {
        "镰刀": "sickle",
        "斧头": "axe",
        "矿镐": "pickaxe",
        "小刀": "knife",
        "sickle": "sickle",
        "axe": "axe",
        "pickaxe": "pickaxe",
        "knife": "knife"
      };
      const findAllTools = (items) => {
        if (!Array.isArray(items))
          return [];
        return items.filter((item) => {
          if (!item)
            return false;
          if (item.name === requiredTool || item.id === requiredTool) {
            console.log("找到直接匹配工具:", item);
            return true;
          }
          const requiredType = toolTypeMap[requiredTool];
          if (requiredType && item.type === requiredType) {
            console.log("找到类型匹配工具:", item, "需要类型:", requiredType);
            return true;
          }
          if (item.name && item.name.includes(requiredTool)) {
            console.log("找到名称包含匹配工具:", item);
            return true;
          }
          return false;
        });
      };
      let allTools = [];
      if (utils_gameState.gameState.equipment) {
        allTools = allTools.concat(findAllTools(Object.values(utils_gameState.gameState.equipment)));
      }
      if (utils_gameState.gameState.inventory) {
        allTools = allTools.concat(findAllTools(utils_gameState.gameState.inventory));
      }
      if (allTools.length === 0) {
        console.log("未找到匹配工具");
        return null;
      }
      const validTools = allTools.filter((tool) => {
        const toolLevel = parseInt(tool.level || 1);
        const isValid = toolLevel >= requiredLevel;
        console.log(`工具 ${tool.name} 等级${toolLevel} ${isValid ? "✅" : "❌"} 需要等级${requiredLevel}`);
        return isValid;
      });
      if (validTools.length === 0) {
        console.log("没有等级足够的工具");
        return null;
      }
      const bestTool = validTools.reduce((best, current) => {
        const bestLevel = parseInt(best.level || 1);
        const currentLevel = parseInt(current.level || 1);
        return currentLevel > bestLevel ? current : best;
      });
      console.log("选择最高级工具:", bestTool, "等级:", bestTool.level);
      return bestTool;
    },
    // 工具等级与可采集次数映射
    getGatherTimesByTool(tool) {
      if (!tool)
        return 0;
      return tool.gather_times || 1;
    },
    handleGameEvent(data) {
      console.log("处理游戏事件:", data);
      const toolField = data.requiredTool || data.toolType || data.tool || data.gatherTool || "";
      const gatherType = data.gatherType || data.type || "";
      const hasToolField = !!(data.requiredTool || data.toolType || data.tool || data.gatherTool);
      const isGatherPoint = data.eventType === "gathering" || data.type === "gathering" || data.type === 3 || gatherType === "gathering" || gatherType === "gather" || /(采集点|可以采集|发现资源|发现矿脉|发现草药|资源地.*可以采集|可采集)/.test(data.content) || hasToolField;
      const isGatherResult = /采集到|获得了|挖到了|收获了/.test(data.content);
      console.log("🔍 事件判断:", { isGatherPoint, isGatherResult, toolField, gatherType });
      if (isGatherPoint && !isGatherResult) {
        console.log("✅ 确认为采集点事件，开始处理");
        const gatherEventLog = {
          timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", { hour12: false }),
          name: "采集事件",
          description: data.content || "你发现了可采集的资源。",
          displayText: data.content || "你发现了可采集的资源。"
        };
        utils_gameState.gameState.eventLog.unshift(gatherEventLog);
        if (utils_gameState.gameState.eventLog.length > 50)
          utils_gameState.gameState.eventLog = utils_gameState.gameState.eventLog.slice(0, 50);
        utils_gameState.gameState.notifyUpdate("eventLog");
        console.log("🔧 准备采集数据，toolField:", toolField);
        console.log("🔧 事件数据:", data);
        console.log("🔧 资源名称:", data.resource, data.fixedResource);
        const resourceLevel = data.resourceLevel || 1;
        console.log("🔧 采集品等级要求:", resourceLevel);
        const tool = this.getGatherToolInfo(toolField || "hoe", resourceLevel);
        console.log("🔧 找到的工具:", tool);
        const maxTimes = this.getGatherTimesByTool(tool);
        console.log("🔧 计算的采集次数:", maxTimes);
        const toolName = tool ? tool.name : toolField || "采集工具";
        const requiredToolDesc = data.requiredToolDesc || `${toolField}(${resourceLevel}级以上)`;
        this.gatheringEvent = {
          content: data.content || "你发现了可采集的资源。",
          gatherType,
          requiredTool: toolField || "hoe",
          requiredToolDesc,
          resourceLevel,
          gatherTimes: maxTimes,
          toolName,
          resource: data.resource || data.fixedResource
          // 添加资源名称
        };
        this.gatheringTimes = maxTimes;
        console.log("🔧 设置采集次数:", this.gatheringTimes);
        this.gatheringResult = "";
        utils_websocket.wsManager.sendMessage("get_inventory_data");
        this.typeWriterEffect(gatherEventLog, 0, () => {
          setTimeout(() => {
            this.showGatheringPopup = true;
          }, 500);
        });
      } else if (isGatherResult) {
        console.log("采集结果事件，应该由 handleGatheringResult 处理");
      } else {
        if (utils_gameState.gameState && typeof utils_gameState.gameState.handleGameEvent === "function") {
          utils_gameState.gameState.handleGameEvent({
            type: data.type || data.eventType,
            content: data.content,
            rewards: data.rewards || {}
          });
        }
      }
    },
    doGather() {
      console.log("🎯 doGather 方法被调用");
      console.log("开始采集，检查工具...");
      console.log("当前采集事件:", this.gatheringEvent);
      if (!this.gatheringEvent) {
        console.error("采集事件不存在");
        this.gatheringResult = "采集事件异常，请重新触发！";
        return;
      }
      const gatheringEvent = this.gatheringEvent;
      const vm = this;
      setTimeout(() => {
        const requiredTool = gatheringEvent.requiredTool;
        const resourceLevel = gatheringEvent.resourceLevel || 1;
        const tool = vm.getGatherToolInfo(requiredTool, resourceLevel);
        console.log("采集工具检查:", { requiredTool, resourceLevel, tool, inventory: utils_gameState.gameState.inventory });
        console.log("当前采集次数:", vm.gatheringTimes);
        if (!tool) {
          console.log("❌ 没有找到合适等级的工具，停止采集");
          const requiredToolDesc = gatheringEvent.requiredToolDesc || `${requiredTool}(${resourceLevel}级以上)`;
          vm.gatheringResult = `需要${requiredToolDesc}才能采集！`;
          return;
        }
        if (vm.gatheringTimes <= 0) {
          console.log("❌ 采集次数用完，停止采集");
          vm.gatheringResult = "本次采集已完成！";
          return;
        }
        console.log("✅ 工具检查通过，准备发送采集请求");
        const gatherMessage = {
          type: "gather_action",
          data: {
            gatherType: gatheringEvent.gatherType,
            fixedResource: gatheringEvent.resource
            // 传递事件指定的资源
          }
        };
        console.log("🚀 发送采集请求:", gatherMessage);
        console.log("🚀 gatheringEvent.resource:", gatheringEvent.resource);
        utils_gameData.gameUtils.sendMessage(gatherMessage).then((response) => {
          console.log("🎯 收到采集响应:", response);
          if (response && response.type === "gather_action_success" && response.data) {
            console.log("🎯 直接处理采集结果");
            console.log("🎯 vm对象:", vm);
            console.log("🎯 vm.handleGatheringResult方法:", vm.handleGatheringResult);
            try {
              vm.handleGatheringResult(response);
              console.log("✅ handleGatheringResult 调用完成");
            } catch (error) {
              console.error("❌ 调用 handleGatheringResult 时出错:", error);
            }
          } else {
            console.log("❌ 响应格式不正确:", response);
          }
        }).catch((error) => {
          console.error("❌ 采集请求失败:", error);
          vm.gatheringResult = "采集失败，请重试！";
        });
        vm.gatheringTimes--;
        console.log("✅ 采集请求已发送，剩余次数:", vm.gatheringTimes);
        if (vm.gatheringTimes <= 0) {
          vm.gatheringResult = "采集中，请等待结果...";
        }
      }, 100);
    },
    handleGatheringResult(data) {
      try {
        if (this.lastGatheringResultTime && Date.now() - this.lastGatheringResultTime < 1e3) {
          console.log("🚫 防止重复处理采集结果，忽略此次调用");
          return;
        }
        this.lastGatheringResultTime = Date.now();
        console.log("🎯 handleGatheringResult 被调用，采用战斗结果相同的处理方式");
        console.log("🎯 收到的数据:", data);
        let content = data.content;
        if (!content && data.data && data.data.content) {
          content = data.data.content;
          console.log("🎯 使用 data.data.content:", content);
        }
        if (!content) {
          console.error("❌ 没有找到采集结果内容");
          return;
        }
        this.gatheringResult = content.replace(/\n/g, "\n");
        console.log("✅ 采集弹窗内容已更新:", this.gatheringResult);
        const gatherResultLog = {
          timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", { hour12: false }),
          name: "采集结果",
          description: content,
          displayText: content
        };
        console.log("🎯 准备添加到江湖日志:", gatherResultLog);
        console.log("🎯 当前 gameState.eventLog:", utils_gameState.gameState.eventLog);
        utils_gameState.gameState.eventLog.unshift(gatherResultLog);
        console.log("🎯 添加后的 gameState.eventLog:", utils_gameState.gameState.eventLog);
        if (utils_gameState.gameState.eventLog.length > 50)
          utils_gameState.gameState.eventLog = utils_gameState.gameState.eventLog.slice(0, 50);
        console.log('🎯 调用 gameState.notifyUpdate("eventLog")');
        utils_gameState.gameState.notifyUpdate("eventLog");
        console.log("✅ 江湖日志更新完成");
        let playerData = data.player_data;
        if (!playerData && data.data && data.data.player_data) {
          playerData = data.data.player_data;
        }
        if (playerData) {
          if (playerData.inventory) {
            utils_gameState.gameState.inventory = playerData.inventory;
            utils_gameState.gameState.notifyUpdate("inventory");
          }
          if (playerData.gather_skills) {
            this.player.gather_skills = playerData.gather_skills;
          }
        }
        if (this.gatheringTimes <= 0) {
          setTimeout(() => {
            this.closeGatheringPopup();
          }, 2e3);
        }
      } catch (error) {
        console.error("❌ handleGatheringResult 执行出错:", error);
        console.error("❌ 错误堆栈:", error.stack);
      }
    },
    closeGatheringPopup() {
      console.log("🚪 closeGatheringPopup 被调用，当前次数:", this.gatheringTimes);
      if (this.gatheringTimes > 0 && !this.gatheringResult && this.gatheringEvent) {
        const abandonLog = {
          timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", { hour12: false }),
          name: "放弃采集",
          description: "你放弃了这次采集机会，离开了采集点。",
          displayText: "你放弃了这次采集机会，离开了采集点。"
        };
        utils_gameState.gameState.eventLog.unshift(abandonLog);
        if (utils_gameState.gameState.eventLog.length > 50)
          utils_gameState.gameState.eventLog = utils_gameState.gameState.eventLog.slice(0, 50);
        utils_gameState.gameState.notifyUpdate("eventLog");
      }
      this.showGatheringPopup = false;
      this.gatheringEvent = null;
      this.gatheringResult = "";
      this.gatheringTimes = 0;
    },
    // 天赋增益计算方法
    getStrengthBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.strength) == null ? void 0 : _c.bonus_percentage) || 0;
    },
    getIntelligenceBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.intelligence) == null ? void 0 : _c.bonus_percentage) || 0;
    },
    getAgilityDefenseBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.agility) == null ? void 0 : _c.defense_bonus_percentage) || 0;
    },
    getConstitutionBonus() {
      return 0;
    },
    getConstitutionHpBonus() {
      var _a, _b, _c;
      return ((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.constitution) == null ? void 0 : _c.hp_bonus_percentage) || 0;
    },
    get hasTalentBonuses() {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;
      return (((_c = (_b = (_a = this.player) == null ? void 0 : _a.talent_bonuses) == null ? void 0 : _b.strength) == null ? void 0 : _c.bonus_percentage) || 0) > 0 || (((_f = (_e = (_d = this.player) == null ? void 0 : _d.talent_bonuses) == null ? void 0 : _e.intelligence) == null ? void 0 : _f.bonus_percentage) || 0) > 0 || (((_i = (_h = (_g = this.player) == null ? void 0 : _g.talent_bonuses) == null ? void 0 : _h.agility) == null ? void 0 : _i.defense_bonus_percentage) || 0) > 0 || (((_l = (_k = (_j = this.player) == null ? void 0 : _j.talent_bonuses) == null ? void 0 : _k.constitution) == null ? void 0 : _l.hp_bonus_percentage) || 0) > 0;
    },
    // 获取采集技能信息
    getGatherSkillInfo(gatherType) {
      const player = utils_gameState.gameState.player || {};
      const gatherSkills = player.gather_skills || {};
      const skill = gatherSkills[gatherType] || { level: 1, exp: 0 };
      const coefficient = 60;
      const needExp = coefficient * Math.pow(skill.level + 1, 2);
      return {
        level: skill.level,
        exp: skill.exp,
        needExp,
        progress: needExp > 0 ? (skill.exp / needExp * 100).toFixed(1) : 0
      };
    },
    // 获取所有采集技能信息
    getAllGatherSkills() {
      const gatherTypes = ["mining", "logging", "herbalism", "skinning"];
      const typeNames = {
        "mining": "挖矿",
        "logging": "伐木",
        "herbalism": "采药",
        "skinning": "剥皮"
      };
      return gatherTypes.map((type) => ({
        type,
        name: typeNames[type],
        ...this.getGatherSkillInfo(type)
      }));
    },
    // 获取天赋加成信息
    async fetchBonusSummary() {
      if (this.fetchingBonusSummary) {
        return;
      }
      this.fetchingBonusSummary = true;
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "get_bonus_summary",
          data: {}
        });
        if (response.type === "get_bonus_summary_timeout") {
          return;
        }
        if (response.type === "get_bonus_summary_success" || response.type === "bonus_summary") {
          const bonusData = response.data || {};
          if (!this.player.talent_bonuses) {
            this.player.talent_bonuses = {};
          }
          if (bonusData.strength) {
            this.player.talent_bonuses.strength = {
              bonus_percentage: bonusData.strength.attack_bonus || 0
            };
          }
          if (bonusData.intelligence) {
            this.player.talent_bonuses.intelligence = {
              bonus_percentage: bonusData.intelligence.exp_bonus || 0
            };
          }
          if (bonusData.agility) {
            this.player.talent_bonuses.agility = {
              defense_bonus_percentage: bonusData.agility.defense_bonus || 0
            };
          }
          if (bonusData.constitution) {
            this.player.talent_bonuses.constitution = {
              hp_bonus_percentage: bonusData.constitution.hp_bonus || 0
            };
          }
        }
      } catch (error) {
      } finally {
        this.fetchingBonusSummary = false;
      }
    },
    canEnterMap(map) {
      const player = utils_gameState.gameState.player || {};
      const req = map.进入要求 || map.enter_requirements || {};
      if (req.item) {
        const inv = player.inventory || [];
        if (!inv.some((i) => i.name === req.item))
          return false;
      }
      if (req.attack) {
        if ((player.attack || 0) < req.attack)
          return false;
      }
      return true;
    },
    async selectMap(map) {
      if (!map || !map.id) {
        common_vendor.index.showToast({ title: "地图ID无效", icon: "none" });
        return;
      }
      this.currentMap = map;
      if (!this.canEnterMap(map)) {
        common_vendor.index.showToast({ title: "不满足进入条件", icon: "none" });
        return;
      }
      try {
        common_vendor.index.showLoading({
          title: "正在切换地图...",
          mask: true
        });
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "select_map",
          data: { map_id: map.id }
        });
        common_vendor.index.hideLoading();
        if (response.type === "select_map_success") {
          this.currentMap = this.mapList.find((m) => m.id === response.data.map_id);
          if (utils_gameState.gameState.player) {
            utils_gameState.gameState.player.current_map = response.data.map_id;
            utils_gameState.gameState.notifyUpdate("player");
          }
          this.refreshPlayerData();
          this.showMapPopup = false;
          common_vendor.index.showToast({ title: "切换成功", icon: "success" });
        } else if (response.type === "error") {
          if (response.data && response.data.message) {
            common_vendor.index.showToast({ title: response.data.message, icon: "none" });
          }
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({
          title: "切换地图失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    async fetchMapNpcs() {
      var _a;
      if (this.fetchingMapNpcs) {
        return;
      }
      this.fetchingMapNpcs = true;
      try {
        const mapId = ((_a = utils_gameState.gameState.player) == null ? void 0 : _a.current_map) || utils_gameState.gameState.player && utils_gameState.gameState.player.current_map;
        if (!mapId) {
          return;
        }
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "get_map_npcs",
          data: { map_id: mapId }
        });
        if (response.type === "get_map_npcs_timeout") {
          if (this.mapsConfig && this.mapsConfig[mapId]) {
            const mapConfig = this.mapsConfig[mapId];
            if (mapConfig.NPC && Array.isArray(mapConfig.NPC)) {
              this.mapNpcs = mapConfig.NPC.map((npc, idx) => {
                if (typeof npc === "object") {
                  return {
                    id: npc.id || `npc_${idx}`,
                    name: npc.名称 || npc.name || "未知NPC",
                    avatar: "static/npc/default.png",
                    desc: npc.描述 || npc.desc || `${npc.名称 || npc.name || "未知NPC"}：一位神秘的江湖人物。`,
                    functions: [
                      { key: "talk", label: "对话" },
                      { key: "shop", label: "交易" }
                    ]
                  };
                }
                return {
                  id: `npc_${idx}`,
                  name: npc,
                  avatar: "static/npc/default.png",
                  desc: `${npc}：一位神秘的江湖人物。`,
                  functions: [
                    { key: "talk", label: "对话" },
                    { key: "shop", label: "交易" }
                  ]
                };
              });
            }
          }
          return;
        }
        let npcs = [];
        if (Array.isArray(response.data)) {
          npcs = response.data;
        } else if (response.data && Array.isArray(response.data.npcs)) {
          npcs = response.data.npcs;
        } else if (response.data && response.data.data && Array.isArray(response.data.data)) {
          npcs = response.data.data;
        }
        if (npcs.length > 0) {
          this.mapNpcs = npcs;
        } else {
          this.mapNpcs = [];
        }
      } catch (e) {
        this.mapNpcs = [];
        common_vendor.index.showToast({
          title: "获取NPC数据失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        this.fetchingMapNpcs = false;
      }
    },
    showNpcMenu(npc) {
      this.selectedNpc = npc;
      this.showNpcMenuModal = true;
      this.npcSidebarVisible = false;
    },
    closeNpcMenu() {
      this.showNpcMenuModal = false;
      this.selectedNpc = {};
    },
    async onNpcFunction(func, npc) {
      try {
        if (func.key === "talk") {
          await this.handleNpcTalk(npc);
        } else if (func.key === "shop") {
          await this.handleNpcShop(npc);
        } else if (func.key === "sell") {
          await this.handleNpcSell(npc);
        } else if (func.key === "transport") {
          await this.handleNpcTransport(npc);
        } else if (func.key === "heal") {
          await this.handleNpcHeal(npc);
        } else if (func.key === "info") {
          await this.handleNpcInfo(npc);
        } else if (func.key === "learn") {
          await this.handleNpcLearn(npc);
        } else {
          common_vendor.index.showToast({ title: `${func.label}功能暂未开放`, icon: "none" });
        }
      } catch (e) {
        console.error("NPC功能处理错误:", e);
        common_vendor.index.showToast({ title: "操作失败", icon: "none" });
      }
      this.closeNpcMenu();
    },
    debugSendMapList() {
    },
    async loadMapsConfig() {
      this.mapsConfig = await utils_gameState.gameState.getMapsConfig();
    },
    async refreshPlayerData() {
      if (utils_gameState.gameState.requestAllData) {
        await utils_gameState.gameState.requestAllData();
      } else {
        await utils_gameData.gameUtils.sendMessage({ type: "get_player_data" });
      }
      this.updateData();
    },
    // NPC功能处理方法
    async handleNpcTalk(npc) {
      const response = await utils_gameData.gameUtils.sendMessage({
        type: "npc_function",
        npc_name: npc.name,
        function: "talk"
      });
      if (response && response.type === "npc_talk") {
        common_vendor.index.showModal({
          title: response.data.npc_name,
          content: response.data.dialogue,
          showCancel: false
        });
      }
    },
    async handleNpcShop(npc) {
      try {
        console.log("发送NPC商店请求:", npc.name);
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "npc_function",
          npc_name: npc.name,
          function: "shop",
          data: { action: "list" }
        });
        console.log("收到NPC商店响应:", response);
        if (response && response.type === "shop_items") {
          console.log("商店物品:", response.data.items);
          this.showNpcShopModal(response.data.items, npc.name);
        } else if (response && response.data && response.data.items && Array.isArray(response.data.items)) {
          console.log("兼容处理商店物品:", response.data.items);
          this.showNpcShopModal(response.data.items, npc.name);
        } else if (response && response.type === "error") {
          console.error("NPC商店错误:", response.data.message);
          common_vendor.index.showToast({
            title: response.data.message,
            icon: "none"
          });
        } else {
          console.error("未知响应类型:", response);
          common_vendor.index.showToast({
            title: `未知响应: ${response ? response.type : "无响应"}`,
            icon: "none"
          });
        }
      } catch (error) {
        console.error("获取NPC商店失败:", error);
        common_vendor.index.showToast({
          title: "获取商店信息失败",
          icon: "none"
        });
      }
    },
    async handleNpcSell(npc) {
      common_vendor.index.showModal({
        title: npc.name,
        content: "请在背包中选择要出售的物品",
        showCancel: true,
        cancelText: "取消",
        confirmText: "去背包",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.navigateTo({
              url: `/pages/character/backpack?mode=sell&npc=${encodeURIComponent(npc.name)}`
            });
          }
        }
      });
    },
    async handleNpcTransport(npc) {
      const response = await utils_gameData.gameUtils.sendMessage({
        type: "npc_function",
        npc_name: npc.name,
        function: "transport"
      });
      if (response && response.type === "transport_destinations") {
        this.showTransportModal(response.data.destinations);
      }
    },
    async handleNpcHeal(npc) {
      common_vendor.index.showModal({
        title: "治疗服务",
        content: "是否花费100银两完全恢复生命值？",
        success: async (res) => {
          if (res.confirm) {
            const response = await utils_gameData.gameUtils.sendMessage({
              type: "npc_function",
              npc_name: npc.name,
              function: "heal"
            });
            if (response && response.type === "heal_success") {
              common_vendor.index.showToast({
                title: "治疗成功",
                icon: "success"
              });
              this.updateData();
            } else if (response && response.type === "error") {
              common_vendor.index.showToast({
                title: response.data.message,
                icon: "none"
              });
            }
          }
        }
      });
    },
    async handleNpcInfo(npc) {
      const response = await utils_gameData.gameUtils.sendMessage({
        type: "npc_function",
        npc_name: npc.name,
        function: "info"
      });
      if (response && response.type === "info_services") {
        this.showInfoServicesModal(response.data.services);
      }
    },
    async handleNpcLearn(npc) {
      common_vendor.index.showModal({
        title: "学习读书写字",
        content: "学费：10银两\n\n学习读书写字可以增加1点经验，悟性越高获得的经验越多。如果你还没有掌握读书写字技能，第一次学习将自动掌握该技能。\n\n确定要学习吗？",
        success: async (res) => {
          if (res.confirm) {
            try {
              const response = await utils_gameData.gameUtils.sendMessage({
                type: "npc_function",
                npc_name: npc.name,
                function: "learn"
              });
              if (response && response.type === "learn_success") {
                common_vendor.index.showModal({
                  title: "学习成功",
                  content: response.data.message,
                  showCancel: false,
                  success: () => {
                    this.updateData();
                  }
                });
              } else if (response && response.type === "error") {
                common_vendor.index.showToast({
                  title: response.data.message,
                  icon: "none"
                });
              }
            } catch (error) {
              console.error("学习失败:", error);
              common_vendor.index.showToast({
                title: "学习失败",
                icon: "none"
              });
            }
          }
        }
      });
    },
    showTransportModal(destinations) {
      const items = destinations.map((dest) => `${dest.map} (${dest.price}银两)`);
      common_vendor.index.showActionSheet({
        itemList: items,
        success: async (res) => {
          const selectedDest = destinations[res.tapIndex];
          const response = await utils_gameData.gameUtils.sendMessage({
            type: "npc_function",
            npc_name: this.selectedNpc.name,
            function: "transport",
            data: { destination: selectedDest.map }
          });
          if (response && response.type === "transport_success") {
            common_vendor.index.showToast({
              title: "传送成功",
              icon: "success"
            });
            this.updateData();
          } else if (response && response.type === "error") {
            common_vendor.index.showToast({
              title: response.data.message,
              icon: "none"
            });
          }
        }
      });
    },
    showInfoServicesModal(services) {
      const items = services.map((service) => `${service.description} (${service.price}银两)`);
      common_vendor.index.showActionSheet({
        itemList: items,
        success: async (res) => {
          const selectedService = services[res.tapIndex];
          const response = await utils_gameData.gameUtils.sendMessage({
            type: "npc_function",
            npc_name: this.selectedNpc.name,
            function: "info",
            data: { info_type: selectedService.type }
          });
          if (response && response.type === "info_success") {
            common_vendor.index.showModal({
              title: "消息",
              content: response.data.message,
              showCancel: false
            });
            this.updateData();
          } else if (response && response.type === "error") {
            common_vendor.index.showToast({
              title: response.data.message,
              icon: "none"
            });
          }
        }
      });
    },
    showNpcShopModal(items, npcName) {
      console.log("显示NPC商店模态框:", npcName, items);
      if (!items || !Array.isArray(items) || items.length === 0) {
        console.error("商店物品数据无效:", items);
        common_vendor.index.showToast({
          title: "该NPC没有商品出售",
          icon: "none"
        });
        return;
      }
      const itemList = items.map((item) => {
        const itemName = item.name || item.item_id;
        const price = item.price || 0;
        return `${itemName} - ${price}银两 (库存:${item.stock})`;
      });
      console.log("商店物品列表:", itemList);
      common_vendor.index.showActionSheet({
        itemList: itemList.slice(0, 6),
        // 微信小程序最多显示6个选项
        success: async (res) => {
          console.log("选择了物品:", res.tapIndex, items[res.tapIndex]);
          const selectedItem = items[res.tapIndex];
          await this.buyNpcItem(selectedItem, npcName);
        },
        fail: (err) => {
          console.log("用户取消选择:", err);
        }
      });
    },
    getItemName(itemId) {
      const itemNames = {
        // 药品类
        "heal_potion": "治疗药水",
        "mana_potion": "法力药水",
        "antidote": "解毒剂",
        "strength_pill": "力量丹",
        "agility_pill": "敏捷丹",
        "intelligence_pill": "智力丹",
        "great_heal_potion": "大治疗药水",
        "exp_pill": "经验丹",
        "breakthrough_pill": "突破丹",
        "immortal_pill": "仙丹",
        // 装备类
        "iron_sword": "铁剑",
        "steel_sword": "钢剑",
        "iron_armor": "铁甲",
        "steel_armor": "钢甲",
        "iron_helmet": "铁盔",
        "steel_helmet": "钢盔",
        // 日用品类
        "bread": "面包",
        "water": "清水",
        "rope": "绳索",
        "torch": "火把",
        "map_scroll": "地图卷轴",
        "teleport_scroll": "传送卷轴",
        // 药材类
        "common_herb": "普通草药",
        "rare_herb": "稀有草药",
        "ginseng": "人参",
        "lingzhi": "灵芝"
      };
      return itemNames[itemId] || itemId;
    },
    async buyNpcItem(item, npcName) {
      this.buyItem = item;
      this.buyNpcName = npcName;
      this.buyQuantity = 1;
      this.showBuyModal = true;
    },
    // 关闭购买弹窗
    closeBuyModal() {
      this.showBuyModal = false;
      this.buyItem = {};
      this.buyNpcName = "";
      this.buyQuantity = 1;
    },
    // 减少购买数量
    decreaseQuantity() {
      if (this.buyQuantity > 1) {
        this.buyQuantity--;
      }
    },
    // 增加购买数量
    increaseQuantity() {
      const maxQuantity = this.buyItem.stock || 0;
      if (this.buyQuantity < maxQuantity) {
        this.buyQuantity++;
      }
    },
    // 数量输入处理
    onQuantityInput(e) {
      const value = parseInt(e.detail.value) || 1;
      const maxQuantity = this.buyItem.stock || 0;
      this.buyQuantity = Math.max(1, Math.min(value, maxQuantity));
    },
    // 确认购买
    async confirmBuy() {
      if (!this.canBuy) {
        common_vendor.index.showToast({
          title: "无法购买",
          icon: "none"
        });
        return;
      }
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "npc_function",
          npc_name: this.buyNpcName,
          function: "shop",
          data: {
            action: "buy",
            item_id: this.buyItem.item_id,
            quantity: this.buyQuantity
          }
        });
        if (response && response.type === "buy_success") {
          common_vendor.index.showToast({
            title: "购买成功",
            icon: "success"
          });
          this.updateData();
          this.closeBuyModal();
        } else if (response && response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message,
            icon: "none"
          });
        }
      } catch (error) {
        console.error("购买失败:", error);
        common_vendor.index.showToast({
          title: "购买失败",
          icon: "none"
        });
      }
    },
    // 可在需要时通过 this.mapsConfig[mapId] 获取地图详情
    // 获取地图NPC列表
    getMapNpcs(map) {
      if (!map)
        return [];
      let npcs = map.NPC || map.npcs || [];
      if (Array.isArray(npcs)) {
        return npcs.map((npc) => {
          if (typeof npc === "string")
            return npc;
          return npc.名称 || npc.name || npc.id || "";
        }).filter((name) => name);
      }
      return [];
    },
    // 获取地图怪物列表
    getMapMonsters(map) {
      if (!map)
        return [];
      let monsters = map.怪物 || map.monsters || map.monster || [];
      if (Array.isArray(monsters)) {
        return monsters.map((monster) => {
          if (typeof monster === "string")
            return monster;
          return monster.名称 || monster.name || monster.id || "";
        }).filter((name) => name);
      }
      return [];
    },
    // 获取地图采集物品列表
    getMapGatherItems(map) {
      if (!map)
        return [];
      let items = map.采集物品 || map.gather_items || map.gatherItems || [];
      if (Array.isArray(items)) {
        return items.map((item) => {
          if (typeof item === "string")
            return item;
          const itemName = item.物品 || item.item || item.name || "";
          const prob = item.概率 || item.prob || item.probability || "";
          if (itemName && prob) {
            return `${itemName}:${prob}`;
          }
          return itemName;
        }).filter((name) => name);
      }
      return [];
    },
    // 获取地图进入要求
    getMapRequirements(map) {
      if (!map)
        return "";
      const requirements = map.进入要求 || map.enter_requirements || map.requirements || {};
      if (typeof requirements === "object" && requirements !== null) {
        return Object.entries(requirements).map(([key, value]) => `${key}:${value}`).join("、");
      }
      return "";
    },
    // 侠客按钮拖拽相关方法
    onPlayerBtnTouchStart(e) {
      this.playerBtnDragging = true;
      this.playerBtnStartY = e.touches[0].clientY;
      this.playerBtnStartTop = parseInt(this.playerBtnTop);
    },
    onPlayerBtnTouchMove(e) {
      if (!this.playerBtnDragging)
        return;
      const deltaY = e.touches[0].clientY - this.playerBtnStartY;
      let newTop = this.playerBtnStartTop + deltaY;
      const minTop = 10;
      const maxTop = 90;
      newTop = Math.max(minTop, Math.min(maxTop, newTop));
      this.playerBtnTop = newTop + "%";
    },
    onPlayerBtnTouchEnd() {
      this.playerBtnDragging = false;
    },
    onPlayerBtnLongPress() {
      this.playerBtnTop = "60%";
    },
    // 获取当前地图玩家列表
    async fetchMapPlayers() {
      var _a;
      if (this.fetchingMapPlayers) {
        return;
      }
      this.fetchingMapPlayers = true;
      try {
        console.log("fetchMapPlayers - this.player:", this.player);
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "get_map_players",
          data: {
            map_id: ((_a = this.player) == null ? void 0 : _a.current_map) || "changan"
          }
        });
        console.log("fetchMapPlayers - response:", response);
        if (response.type === "map_players_success") {
          const otherPlayers = response.data.players || [];
          if (this.player) {
            const selfPlayer = {
              id: this.player.id || "self",
              name: this.player.name || this.player.character_name || "我",
              character_name: this.player.character_name || this.player.name || "我",
              level: this.player.level || 1,
              status: this.player.status || "online",
              avatar: "/static/npc/default.png",
              isSelf: true
              // 标记为自己
            };
            console.log("fetchMapPlayers - selfPlayer:", selfPlayer);
            this.mapPlayers = [selfPlayer, ...otherPlayers];
          } else {
            console.log("fetchMapPlayers - no player data, only showing other players");
            this.mapPlayers = otherPlayers;
          }
          console.log("fetchMapPlayers - final mapPlayers:", this.mapPlayers);
        }
      } catch (error) {
        console.error("获取地图玩家失败:", error);
        if (this.player) {
          const selfPlayer = {
            id: this.player.id || "self",
            name: this.player.name || this.player.character_name || "我",
            character_name: this.player.character_name || this.player.name || "我",
            level: this.player.level || 1,
            status: this.player.status || "online",
            avatar: "/static/npc/default.png",
            isSelf: true
            // 标记为自己
          };
          this.mapPlayers = [selfPlayer];
        } else {
          this.mapPlayers = [];
        }
      } finally {
        this.fetchingMapPlayers = false;
      }
    },
    // 显示侠客侧边栏
    async showPlayerSidebar() {
      this.playerSidebarVisible = true;
      await this.fetchMapPlayers();
    },
    // 显示玩家菜单
    showPlayerMenu(player) {
      this.selectedPlayer = player;
      this.showPlayerMenuModal = true;
      this.playerSidebarVisible = false;
    },
    // 玩家状态文本
    getPlayerStatusText(status) {
      const statusMap = {
        "online": "在线",
        "busy": "忙碌",
        "battle": "战斗中",
        "meditation": "打坐中",
        "healing": "疗伤中",
        "offline": "离线"
      };
      return statusMap[status] || "未知";
    },
    // 玩家交互操作
    async playerAction(action) {
      const actionMap = {
        "sneak_attack": "偷袭",
        "give": "给与",
        "steal": "偷窃",
        "view": "查看"
      };
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "player_action",
          data: {
            action,
            target_player_id: this.selectedPlayer.id,
            target_player_name: this.selectedPlayer.name || this.selectedPlayer.character_name
          }
        });
        if (response.type === "player_action_success") {
          if (action === "sneak_attack" && response.data.battle_started) {
            this.showPlayerMenuModal = false;
            this.showBattlePopup = true;
            this.battlePlayer = response.data.player;
            this.battleMonster = response.data.enemy;
            this.battleLog = response.data.battle_log || [];
            this.battleStage = response.data.stage || "battle";
            return;
          }
          common_vendor.index.showToast({
            title: response.data.message || `${actionMap[action]}成功`,
            icon: "success"
          });
        } else if (response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message || `${actionMap[action]}失败`,
            icon: "none"
          });
        }
        this.showPlayerMenuModal = false;
      } catch (error) {
        common_vendor.index.showToast({
          title: `${actionMap[action]}失败: ${error.message}`,
          icon: "none"
        });
      }
    },
    // 聊天相关方法
    async loadChatMessages() {
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "get_chat_messages",
          data: {}
        });
        if (response.type === "chat_messages_success") {
          this.chatMessages = response.data.messages || [];
          this.scrollChatToBottom();
        }
      } catch (error) {
        console.error("加载聊天消息失败:", error);
      }
    },
    // 发送聊天消息
    async sendChatMessage() {
      const content = this.chatInputText.trim();
      if (!content)
        return;
      if (this.currentChatType === "private" && !this.chatTargetName.trim()) {
        common_vendor.index.showToast({
          title: "请输入目标玩家名",
          icon: "none"
        });
        return;
      }
      const filteredContent = this.filterSensitiveWords(content);
      try {
        const messageData = {
          content: filteredContent,
          chat_type: this.currentChatType
        };
        if (this.currentChatType === "private") {
          messageData.target_name = this.chatTargetName.trim();
        }
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "send_chat_message",
          data: messageData
        });
        if (response.type === "chat_message_success") {
          this.chatInputText = "";
          common_vendor.index.showToast({
            title: "发送成功",
            icon: "success"
          });
        } else if (response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message || "发送失败",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.showToast({
          title: "发送失败: " + error.message,
          icon: "none"
        });
      }
    },
    // 敏感词过滤
    filterSensitiveWords(text) {
      const sensitiveWords = [
        "政治",
        "反动",
        "暴力",
        "色情",
        "赌博",
        "毒品",
        "诈骗",
        "外挂",
        "作弊",
        "习近平",
        "共产党",
        "法轮功",
        "台独",
        "藏独",
        "疆独",
        "六四",
        "天安门",
        "操你妈",
        "草你妈",
        "傻逼",
        "煞笔",
        "智障",
        "脑残",
        "去死",
        "死全家"
      ];
      let filteredText = text;
      sensitiveWords.forEach((word) => {
        const regex = new RegExp(word, "gi");
        filteredText = filteredText.replace(regex, "*".repeat(word.length));
      });
      return filteredText;
    },
    // 格式化聊天时间
    formatChatTime(timestamp) {
      const date = new Date(timestamp);
      const now = /* @__PURE__ */ new Date();
      if (date.toDateString() === now.toDateString()) {
        return date.toLocaleTimeString("zh-CN", {
          hour: "2-digit",
          minute: "2-digit"
        });
      } else {
        return date.toLocaleString("zh-CN", {
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit"
        });
      }
    },
    // 格式化聊天时间（包含秒）
    formatChatTimeWithSeconds(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit"
      });
    },
    // 获取聊天类型名称
    getChatTypeName(chatType) {
      const typeMap = {
        "world": "世界",
        "private": "私聊",
        "rumor": "谣言",
        "system": "系统"
      };
      return typeMap[chatType] || "未知";
    },
    // 获取聊天输入框占位符
    getChatPlaceholder() {
      const placeholderMap = {
        "world": "输入世界聊天内容...",
        "private": "输入私聊内容...",
        "rumor": "输入谣言内容..."
      };
      return placeholderMap[this.currentChatType] || "输入聊天内容...";
    },
    // 获取当前聊天类型名称
    getCurrentChatTypeName() {
      const currentType = this.chatTypes.find((type) => type.value === this.currentChatType);
      return currentType ? currentType.name : "世界";
    },
    // 聊天类型选择改变
    onChatTypeChange(e) {
      const index = e.detail.value;
      this.chatTypeIndex = index;
      this.currentChatType = this.chatTypes[index].value;
      if (this.currentChatType !== "private") {
        this.chatTargetName = "";
      }
    },
    // 滚动聊天到底部
    scrollChatToBottom() {
      this.$nextTick(() => {
      });
    },
    // 处理聊天消息推送
    handleChatMessage(data) {
      this.chatMessages.push({
        sender: data.sender,
        content: data.content,
        time: data.time || Date.now(),
        chat_type: data.chat_type || "world",
        target_name: data.target_name,
        isOwn: data.sender === (this.player.name || this.player.character_name) || data.sender === "你"
      });
      this.scrollChatToBottom();
    },
    // 处理地图玩家更新
    handleMapPlayersUpdate(data) {
      if (data.map_id === this.player.current_map) {
        this.mapPlayers = data.players || [];
      }
    },
    typeWriterEffect(log, idx, onFinish) {
      log.displayText = "";
      const fullText = log.description;
      const segments = [];
      let currentSegment = "";
      let inTag = false;
      for (let i = 0; i < fullText.length; i++) {
        const char = fullText[i];
        if (char === "<") {
          if (currentSegment) {
            segments.push({ type: "text", content: currentSegment });
            currentSegment = "";
          }
          inTag = true;
          currentSegment = char;
        } else if (char === ">") {
          currentSegment += char;
          segments.push({ type: "tag", content: currentSegment });
          currentSegment = "";
          inTag = false;
        } else {
          currentSegment += char;
        }
      }
      if (currentSegment) {
        segments.push({ type: inTag ? "tag" : "text", content: currentSegment });
      }
      let segmentIndex = 0;
      let charIndex = 0;
      const printNext = () => {
        if (segmentIndex < segments.length) {
          const segment = segments[segmentIndex];
          if (segment.type === "tag") {
            log.displayText += segment.content;
            segmentIndex++;
            charIndex = 0;
          } else {
            if (charIndex < segment.content.length) {
              log.displayText += segment.content[charIndex];
              charIndex++;
            } else {
              segmentIndex++;
              charIndex = 0;
            }
          }
          this.$set(this.eventLog, idx, { ...log });
          setTimeout(printNext, 30);
        } else {
          log.displayText = fullText;
          this.$set(this.eventLog, idx, { ...log });
          if (typeof onFinish === "function")
            onFinish();
        }
      };
      printNext();
    },
    handleEncounterMonster(data) {
      const monster = data.monster || {};
      let encounterDescription = "";
      if (monster.attack_mode === "active") {
        encounterDescription = `${monster.name || "未知怪物"}向你大喊一声扑了上来！`;
      } else {
        encounterDescription = `你遇到了${monster.name || "未知怪物"}，你要怎么办呢？`;
      }
      const encounterLog = {
        timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", { hour12: false }),
        name: "遭遇战斗",
        description: encounterDescription
      };
      utils_gameState.gameState.eventLog.unshift(encounterLog);
      if (utils_gameState.gameState.eventLog.length > 50)
        utils_gameState.gameState.eventLog = utils_gameState.gameState.eventLog.slice(0, 50);
      utils_gameState.gameState.notifyUpdate("eventLog");
      this.typeWriterEffect(encounterLog, 0, () => {
        setTimeout(() => {
          this.battleMonster = { ...monster };
          this.battlePlayer = { ...this.player };
          this.battleLog = [];
          this.battleStage = "encounter";
          this.battleAttackMode = monster.attack_mode || "passive";
          this.battleResultProcessed = false;
          this.escapeProcessed = false;
          this.showBattlePopup = true;
          if (monster.attack_mode === "active") {
            setTimeout(() => {
              utils_gameData.gameUtils.sendMessage({ type: "start_battle_from_encounter", data: { monster_id: this.battleMonster.id } });
              this.battleStage = "battle";
            }, 1e3);
          }
        }, 1e3);
      });
    },
    handleBattleAttack() {
      if (this.battleStage === "encounter" && this.battleAttackMode === "passive") {
        this.battleResultProcessed = false;
        this.escapeProcessed = false;
        utils_gameData.gameUtils.sendMessage({ type: "start_battle_from_encounter", data: { monster_id: this.battleMonster.id } });
        this.battleStage = "battle";
      }
    },
    handleBattleEscape() {
      if (this.escapeRequested) {
        return;
      }
      if (this.escapeProcessed) {
        return;
      }
      this.escapeRequested = true;
      utils_gameData.gameUtils.sendMessage({ type: "escape_battle", data: { monster_id: this.battleMonster.id } }).then((response) => {
      }).catch((error) => {
        this.escapeRequested = false;
        common_vendor.index.showToast({
          title: "逃跑请求失败",
          icon: "none"
        });
      });
    },
    handleBattleClose() {
      this.showBattlePopup = false;
      this.battleLog = [];
      this.battleStage = "";
      this.battleAttackMode = "";
      this.battlePlayer = {};
      this.battleMonster = {};
      this.escapeProcessed = false;
      this.battleResultProcessed = false;
      this.escapeRequested = false;
      this.updateData();
    },
    handleBattleRound(data) {
      const roundData = {
        ...data,
        timestamp: data.timestamp || (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", { hour12: false })
      };
      if (!Array.isArray(this.battleLog)) {
        this.battleLog = [];
      }
      this.battleLog.push(roundData);
      if (this.battleLog.length > 50) {
        this.battleLog = this.battleLog.slice(-50);
      }
      const updateTime = Date.now();
      if (typeof data.player_hp !== "undefined") {
        this.player.hp = data.player_hp;
        this.battlePlayer.hp = data.player_hp;
        this.lastBattleHpUpdate = updateTime;
      }
      if (typeof data.player_max_hp !== "undefined") {
        this.player.max_hp = data.player_max_hp;
        this.battlePlayer.max_hp = data.player_max_hp;
      }
      if (typeof data.player_mp !== "undefined")
        this.player.mp = data.player_mp;
      if (typeof data.player_max_mp !== "undefined")
        this.player.max_mp = data.player_max_mp;
      if (typeof data.enemy_hp !== "undefined")
        this.battleMonster.hp = data.enemy_hp;
      if (typeof data.enemy_max_hp !== "undefined")
        this.battleMonster.max_hp = data.enemy_max_hp;
    },
    handleBattleResult(data) {
      if (this.battleResultProcessed) {
        return;
      }
      if (this.escapeProcessed) {
        return;
      }
      this.battleResultProcessed = true;
      this.battleStage = "end";
      const battleResultLog = {
        timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", { hour12: false }),
        name: "战斗结束",
        description: data.win ? `你击败了${this.battleMonster.name || "怪物"}，战斗胜利！` : `你被${this.battleMonster.name || "怪物"}击败了，战斗失败！`
      };
      utils_gameState.gameState.eventLog.unshift(battleResultLog);
      if (utils_gameState.gameState.eventLog.length > 50)
        utils_gameState.gameState.eventLog = utils_gameState.gameState.eventLog.slice(0, 50);
      utils_gameState.gameState.notifyUpdate("eventLog");
      if (data.win && data.rewards) {
        this.showBattleRewards(data.rewards);
      } else if (!data.win) {
        this.showBattleFailure();
      }
    },
    // 显示战斗胜利奖励
    showBattleRewards(rewards) {
      let rewardText = "战斗胜利！获得奖励：";
      if (rewards["历练值"]) {
        rewardText += `
历练值 +${rewards["历练值"]}`;
      }
      if (rewards["银两"]) {
        rewardText += `
银两 +${rewards["银两"]}`;
      }
      if (rewards["武学点"]) {
        rewardText += `
武学点 +${rewards["武学点"]}`;
      }
      if (rewards["物品"] && rewards["物品"].length > 0) {
        rewardText += "\n物品：";
        rewards["物品"].forEach((item) => {
          rewardText += `
  ${item.id} x${item.quantity}`;
        });
      }
      if (rewardText === "战斗胜利！获得奖励：") {
        rewardText = "战斗胜利！但是没有获得任何奖励。";
      }
      setTimeout(() => {
        common_vendor.index.showModal({
          title: "战斗胜利",
          content: rewardText.trim(),
          showCancel: false,
          confirmText: "确定"
        });
      }, 1e3);
    },
    // 显示战斗失败
    showBattleFailure() {
      setTimeout(() => {
        common_vendor.index.showModal({
          title: "战斗失败",
          content: "你被击败了，受了重伤。",
          showCancel: false,
          confirmText: "确定"
        });
      }, 1e3);
    },
    handleBattlePlayerData(data) {
      if (this.showBattlePopup && this.battleStage === "battle") {
        const currentTime = Date.now();
        const timeSinceLastBattleUpdate = currentTime - (this.lastBattleHpUpdate || 0);
        const currentBattleHp = this.battlePlayer.hp;
        const currentPlayerHp = this.player.hp;
        this.battlePlayer = { ...this.battlePlayer, ...data };
        this.player = { ...this.player, ...data };
        if (timeSinceLastBattleUpdate < 1e3) {
          if (typeof currentBattleHp !== "undefined" && currentBattleHp !== null) {
            this.battlePlayer.hp = currentBattleHp;
          }
          if (typeof currentPlayerHp !== "undefined" && currentPlayerHp !== null) {
            this.player.hp = currentPlayerHp;
          }
        }
      } else {
        this.player = { ...this.player, ...data };
      }
    },
    handleEscapeBattleResult(data) {
      this.escapeRequested = false;
      if (this.escapeProcessed) {
        return;
      }
      if (data.success) {
        this.escapeProcessed = true;
        const escapeLog = {
          round: this.battleLog.length + 1,
          attacker: "系统",
          defender: "玩家",
          damage: 0,
          desc: data.message || "你成功逃离了战斗！",
          move: "逃跑",
          martial: "",
          player_hp: this.battlePlayer.hp,
          enemy_hp: this.battleMonster.hp,
          special_effect: null,
          effect_desc: "",
          timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit"
          })
        };
        this.battleLog.push(escapeLog);
        const escapeResultLog = {
          timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", { hour12: false }),
          name: "战斗结束",
          description: `你成功从${this.battleMonster.name || "怪物"}的追击中逃脱，逃之夭夭！`
        };
        utils_gameState.gameState.eventLog.unshift(escapeResultLog);
        if (utils_gameState.gameState.eventLog.length > 50)
          utils_gameState.gameState.eventLog = utils_gameState.gameState.eventLog.slice(0, 50);
        utils_gameState.gameState.notifyUpdate("eventLog");
        setTimeout(() => {
          this.showBattlePopup = false;
          this.escapeProcessed = false;
        }, 2e3);
      } else {
        const escapeLog = {
          round: this.battleLog.length + 1,
          attacker: "系统",
          defender: "玩家",
          damage: 0,
          desc: data.message || "你试图逃跑，但被怪物拦住了！",
          move: "逃跑",
          martial: "",
          player_hp: this.battlePlayer.hp,
          enemy_hp: this.battleMonster.hp,
          special_effect: null,
          effect_desc: "",
          timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", {
            hour12: false,
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit"
          })
        };
        this.battleLog.push(escapeLog);
      }
    },
    async handleHealing() {
      try {
        this.healingMeditationMessages = [];
        this.healingMeditationLoading = true;
        common_vendor.index.showLoading({ title: "正在疗伤..." });
        utils_websocket.wsManager.sendMessage("healing", {});
      } catch (error) {
        this.healingMeditationLoading = false;
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({ title: "疗伤失败: " + error.message, icon: "none" });
      }
    },
    async handleMeditation() {
      try {
        this.healingMeditationMessages = [];
        this.healingMeditationLoading = true;
        common_vendor.index.showLoading({ title: "正在打坐..." });
        utils_websocket.wsManager.sendMessage("meditation", {});
      } catch (error) {
        this.healingMeditationLoading = false;
        common_vendor.index.hideLoading();
        common_vendor.index.showToast({ title: "打坐失败: " + error.message, icon: "none" });
      }
    },
    handleHealingLog(data) {
      if (data && data.message) {
        this.healingMeditationMessages.push(data.message);
        if (this.healingMeditationMessages.length > 10) {
          this.healingMeditationMessages = this.healingMeditationMessages.slice(-10);
        }
        this.healingMeditationLoading = !/疗伤结束|收功|耗尽/.test(data.message);
        if (/疗伤结束|收功|耗尽/.test(data.message)) {
          setTimeout(() => {
            this.healingMeditationMessages = [];
            this.healingMeditationLoading = false;
          }, 1200);
        }
      }
    },
    handleMeditationLog(data) {
      if (data && data.message) {
        this.healingMeditationMessages.push(data.message);
        if (this.healingMeditationMessages.length > 10) {
          this.healingMeditationMessages = this.healingMeditationMessages.slice(-10);
        }
        this.healingMeditationLoading = !/打坐结束|收功|耗尽/.test(data.message);
        if (/打坐结束|收功|耗尽/.test(data.message)) {
          setTimeout(() => {
            this.healingMeditationMessages = [];
            this.healingMeditationLoading = false;
            this.refreshPlayerData && this.refreshPlayerData();
          }, 1200);
        }
      }
    },
    // 删除重复的 handleGatheringResult 方法，使用前面完整版本的方法
    onNpcBtnLongPress(e) {
      this.npcBtnDragging = true;
      this.npcBtnStartY = e.touches[0].clientY;
      let topPx = 0;
      if (typeof this.npcBtnTop === "string" && this.npcBtnTop.includes("%")) {
        topPx = common_vendor.index.getSystemInfoSync().windowHeight * parseFloat(this.npcBtnTop) / 100;
      } else {
        topPx = parseFloat(this.npcBtnTop);
      }
      this.npcBtnStartTop = topPx;
    },
    onNpcBtnTouchMove(e) {
      if (!this.npcBtnDragging)
        return;
      const moveY = e.touches[0].clientY;
      let delta = moveY - this.npcBtnStartY;
      let newTop = this.npcBtnStartTop + delta;
      const minTop = 40;
      const maxTop = common_vendor.index.getSystemInfoSync().windowHeight - 80;
      if (newTop < minTop)
        newTop = minTop;
      if (newTop > maxTop)
        newTop = maxTop;
      this.npcBtnTop = newTop + "px";
    },
    onNpcBtnTouchEnd() {
      this.npcBtnDragging = false;
    },
    onLeftBtnTouchStart(e) {
      this.leftBtnDragging = true;
      this.leftBtnStartY = e.touches[0].clientY;
      let topPx = 0;
      if (typeof this.leftBtnTop === "string" && this.leftBtnTop.includes("%")) {
        topPx = common_vendor.index.getSystemInfoSync().windowHeight * parseFloat(this.leftBtnTop) / 100;
      } else {
        topPx = parseFloat(this.leftBtnTop);
      }
      this.leftBtnStartTop = topPx;
    },
    onLeftBtnLongPress() {
      this.leftBtnTop = "70%";
    },
    onLeftBtnTouchMove(e) {
      if (!this.leftBtnDragging)
        return;
      const moveY = e.touches[0].clientY;
      let delta = moveY - this.leftBtnStartY;
      let newTop = this.leftBtnStartTop + delta;
      const minTop = 40;
      const maxTop = common_vendor.index.getSystemInfoSync().windowHeight - 80;
      if (newTop < minTop)
        newTop = minTop;
      if (newTop > maxTop)
        newTop = maxTop;
      this.leftBtnTop = newTop + "px";
    },
    onLeftBtnTouchEnd() {
      this.leftBtnDragging = false;
    },
    toggleLeftMenu() {
      this.leftMenuVisible = !this.leftMenuVisible;
    },
    onMenuClick(type) {
      this.leftMenuVisible = false;
      if (type === "healing") {
        this.handleHealing();
      } else if (type === "meditation") {
        this.handleMeditation();
      } else if (type === "ranking") {
        this.openRanking();
      } else if (type === "redeem") {
        this.openRedeemCode();
      }
    },
    // 打开聊天
    openChat() {
      this.showChatPopup = true;
      this.loadChatMessages();
    },
    // 聊天按钮拖拽相关方法
    onChatBtnTouchStart(e) {
      this.chatBtnDragging = true;
      this.chatBtnStartY = e.touches[0].clientY;
      let topPx = 0;
      if (typeof this.chatBtnTop === "string" && this.chatBtnTop.includes("%")) {
        topPx = common_vendor.index.getSystemInfoSync().windowHeight * parseFloat(this.chatBtnTop) / 100;
      } else {
        topPx = parseFloat(this.chatBtnTop);
      }
      this.chatBtnStartTop = topPx;
    },
    onChatBtnTouchMove(e) {
      if (!this.chatBtnDragging)
        return;
      const deltaY = e.touches[0].clientY - this.chatBtnStartY;
      let newTop = this.chatBtnStartTop + deltaY;
      const minTop = 10;
      const maxTop = common_vendor.index.getSystemInfoSync().windowHeight - 80;
      if (newTop < minTop)
        newTop = minTop;
      if (newTop > maxTop)
        newTop = maxTop;
      this.chatBtnTop = newTop + "px";
    },
    onChatBtnTouchEnd() {
      this.chatBtnDragging = false;
    },
    onChatBtnLongPress() {
      this.chatBtnTop = "55%";
    },
    // 打开排行榜弹窗
    openRanking() {
      this.showRankingModal = true;
      this.loadRanking();
    },
    // 打开兑换码弹窗
    openRedeemCode() {
      this.showRedeemModal = true;
    },
    // 排行榜相关方法
    switchRankingTab(tab) {
      if (this.rankingCurrentTab !== tab) {
        this.rankingCurrentTab = tab;
        this.loadRanking();
      }
    },
    async loadRanking() {
      var _a;
      this.rankingLoading = true;
      try {
        const resp = await utils_gameData.gameUtils.sendMessage({
          type: "get_ranking",
          data: {
            type: this.rankingCurrentTab,
            limit: 30
          }
        });
        if (resp && (resp.type === "success" || resp.type === "get_ranking_success") && resp.data) {
          this.rankingList = resp.data.list || [];
          this.myRanking = resp.data.myRanking || null;
          const currentPlayerName = ((_a = utils_gameState.gameState.player) == null ? void 0 : _a.name) || utils_gameState.gameState.name;
          this.rankingList.forEach((item) => {
            item.isCurrentPlayer = item.name === currentPlayerName;
          });
        } else {
          common_vendor.index.showToast({ title: "获取排行榜失败", icon: "none" });
        }
      } catch (error) {
        console.error("获取排行榜失败:", error);
        common_vendor.index.showToast({ title: "网络错误", icon: "none" });
      } finally {
        this.rankingLoading = false;
      }
    },
    // 兑换码相关方法
    async submitRedeemCode() {
      var _a;
      if (!this.redeemCode.trim()) {
        common_vendor.index.showToast({ title: "请输入兑换码", icon: "none" });
        return;
      }
      this.redeemLoading = true;
      try {
        const resp = await utils_gameData.gameUtils.sendMessage({
          type: "redeem_code",
          data: {
            code: this.redeemCode.trim()
          }
        });
        if (resp && resp.type === "success") {
          const rewards = resp.data.rewards || [];
          const rewardText = rewards.map((r) => `${r.name} x${r.quantity}`).join("、");
          common_vendor.index.showToast({
            title: `兑换成功！获得：${rewardText}`,
            icon: "success",
            duration: 3e3
          });
          this.redeemCode = "";
          this.refreshPlayerData();
        } else {
          const message = ((_a = resp == null ? void 0 : resp.data) == null ? void 0 : _a.message) || "兑换失败";
          common_vendor.index.showToast({ title: message, icon: "none" });
        }
      } catch (error) {
        console.error("兑换码兑换失败:", error);
        common_vendor.index.showToast({ title: "网络错误，请重试", icon: "none" });
      } finally {
        this.redeemLoading = false;
      }
    },
    // 格式化方法
    formatMoney(money) {
      if (money >= 1e4) {
        return (money / 1e4).toFixed(1) + "万";
      }
      return money + "两";
    },
    formatNumber(num) {
      if (num >= 1e4) {
        return (num / 1e4).toFixed(1) + "万";
      }
      return num.toString();
    },
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = /* @__PURE__ */ new Date();
      const diff = now - date;
      if (diff < 6e4) {
        return "刚刚";
      } else if (diff < 36e5) {
        return Math.floor(diff / 6e4) + "分钟前";
      } else if (diff < 864e5) {
        return Math.floor(diff / 36e5) + "小时前";
      } else {
        return date.toLocaleDateString();
      }
    },
    addMeditationMessage(msg) {
      this.healingMeditationMessages.push(msg);
      this.$nextTick(() => {
        const msgList = this.$el.querySelector(".healing-meditation-msg-list");
        if (msgList)
          msgList.scrollTop = msgList.scrollHeight;
      });
    },
    endMeditation() {
      this.healingMeditationLoading = false;
    },
    handleReconnectCloseAllPopups() {
      this.showBattlePopup = false;
      this.showGatheringPopup = false;
      this.showMapPopup = false;
      this.showNpcMenuModal = false;
      this.npcSidebarVisible = false;
      this.leftMenuVisible = false;
      this.healingMeditationMessages = [];
      this.playerSidebarVisible = false;
      this.showPlayerMenuModal = false;
      this.showChatPopup = false;
      this.showBuyModal = false;
      this.chatBtnDragging = false;
      this.leftBtnDragging = false;
      this.npcBtnDragging = false;
      this.playerBtnDragging = false;
    }
  },
  mounted() {
    utils_gameState.gameState.requestMapsConfig().then((config) => {
      this.mapsConfig = config;
    });
    utils_websocket.wsManager.on("escape_battle_result", this.handleEscapeBattleResult);
    utils_websocket.wsManager.on("healing_log", this.handleHealingLog);
    utils_websocket.wsManager.on("meditation_log", this.handleMeditationLog);
    utils_websocket.wsManager.on("chat_message", this.handleChatMessage);
    utils_websocket.wsManager.on("map_players_update", this.handleMapPlayersUpdate);
    common_vendor.index.$on && common_vendor.index.$on("ws_reconnected", this.handleReconnectCloseAllPopups);
  },
  beforeDestroy() {
    utils_websocket.wsManager.off("chat_message", this.handleChatMessage);
    utils_websocket.wsManager.off("map_players_update", this.handleMapPlayersUpdate);
    common_vendor.index.$off && common_vendor.index.$off("ws_reconnected", this.handleReconnectCloseAllPopups);
  }
};
if (!Array) {
  const _component_gathering_popup = common_vendor.resolveComponent("gathering-popup");
  const _component_BattlePopup = common_vendor.resolveComponent("BattlePopup");
  (_component_gathering_popup + _component_BattlePopup)();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.player && $data.player.name ? $data.player.name : ""),
    b: common_vendor.t($options.currentMapName || ""),
    c: common_vendor.t($data.connectionStatus),
    d: common_vendor.n($options.connectionStatusClass),
    e: common_vendor.o(($event) => $data.showMapPopup = true),
    f: common_vendor.t($data.announcementText),
    g: common_vendor.t($data.announcementText),
    h: $options.hpPercent + "%",
    i: common_vendor.t(Math.floor($data.player.hp !== void 0 ? $data.player.hp : 100)),
    j: common_vendor.t(Math.floor($data.player.max_hp || $data.player.base_max_hp || 100)),
    k: $options.mpPercent + "%",
    l: common_vendor.t(Math.floor($data.player.mp || 50)),
    m: common_vendor.t(Math.floor($data.player.max_mp || $data.player.base_max_mp || 50)),
    n: $options.staminaPercent + "%",
    o: common_vendor.t(Math.floor($data.player.energy || 100)),
    p: common_vendor.t(Math.floor($data.player.max_energy || $data.player.base_max_energy || 100)),
    q: $options.energyPercent + "%",
    r: common_vendor.t(Math.floor($data.player.spirit || 100)),
    s: common_vendor.t(Math.floor($data.player.max_spirit || 100)),
    t: $options.hasTalentBonuses
  }, $options.hasTalentBonuses ? common_vendor.e({
    v: $options.getStrengthBonus() > 0
  }, $options.getStrengthBonus() > 0 ? {
    w: common_vendor.t(Math.floor($options.getStrengthBonus() || 0))
  } : {}, {
    x: $options.getIntelligenceBonus() > 0
  }, $options.getIntelligenceBonus() > 0 ? {
    y: common_vendor.t(Math.floor($options.getIntelligenceBonus() || 0))
  } : {}, {
    z: $options.getAgilityDefenseBonus() > 0
  }, $options.getAgilityDefenseBonus() > 0 ? {
    A: common_vendor.t(Math.floor($options.getAgilityDefenseBonus() || 0))
  } : {}, {
    B: $options.getConstitutionHpBonus() > 0
  }, $options.getConstitutionHpBonus() > 0 ? {
    C: common_vendor.t(Math.floor($options.getConstitutionHpBonus() || 0))
  } : {}) : {}, {
    D: common_vendor.o((...args) => $options.triggerAdventure && $options.triggerAdventure(...args)),
    E: !$data.isAuthed || $data.status !== "normal",
    F: $data.showNpcMenuModal
  }, $data.showNpcMenuModal ? {
    G: $data.selectedNpc.avatar,
    H: common_vendor.t($data.selectedNpc && $data.selectedNpc.name ? $data.selectedNpc.name : ""),
    I: common_vendor.t($data.selectedNpc.desc),
    J: common_vendor.f($data.selectedNpc.functions, (func, k0, i0) => {
      return {
        a: common_vendor.t(func.label),
        b: func.key,
        c: common_vendor.o(($event) => $options.onNpcFunction(func, $data.selectedNpc), func.key)
      };
    }),
    K: common_vendor.o((...args) => $options.closeNpcMenu && $options.closeNpcMenu(...args)),
    L: common_vendor.o(() => {
    }),
    M: common_vendor.o((...args) => $options.closeNpcMenu && $options.closeNpcMenu(...args))
  } : {}, {
    N: $data.showBuyModal
  }, $data.showBuyModal ? {
    O: common_vendor.o((...args) => $options.closeBuyModal && $options.closeBuyModal(...args)),
    P: common_vendor.t($data.buyItem.name || $data.buyItem.item_id),
    Q: common_vendor.t($data.buyItem.price || 0),
    R: common_vendor.t($data.buyItem.stock || 0),
    S: common_vendor.o((...args) => $options.decreaseQuantity && $options.decreaseQuantity(...args)),
    T: common_vendor.o([($event) => $data.buyQuantity = $event.detail.value, (...args) => $options.onQuantityInput && $options.onQuantityInput(...args)]),
    U: $data.buyQuantity,
    V: common_vendor.o((...args) => $options.increaseQuantity && $options.increaseQuantity(...args)),
    W: common_vendor.t(($data.buyItem.price || 0) * $data.buyQuantity),
    X: common_vendor.t($data.money),
    Y: common_vendor.o((...args) => $options.closeBuyModal && $options.closeBuyModal(...args)),
    Z: common_vendor.o((...args) => $options.confirmBuy && $options.confirmBuy(...args)),
    aa: !$options.canBuy,
    ab: common_vendor.o(() => {
    }),
    ac: common_vendor.o((...args) => $options.closeBuyModal && $options.closeBuyModal(...args))
  } : {}, {
    ad: $data.status !== "normal"
  }, $data.status !== "normal" ? {
    ae: common_vendor.t($options.getStatusText()),
    af: common_vendor.n("status-" + $data.status)
  } : {}, {
    ag: common_vendor.o((...args) => $options.clearLog && $options.clearLog(...args)),
    ah: common_vendor.f($data.eventLog, (event, index, i0) => {
      return {
        a: common_vendor.t(event.timestamp),
        b: common_vendor.t(event && event.name ? event.name : ""),
        c: event.displayText,
        d: index
      };
    }),
    ai: $data.eventLog.length === 0
  }, $data.eventLog.length === 0 ? {} : {}, {
    aj: $data.showGatheringPopup
  }, $data.showGatheringPopup ? {
    ak: common_vendor.o($options.closeGatheringPopup),
    al: common_vendor.o($options.doGather),
    am: common_vendor.p({
      visible: $data.showGatheringPopup,
      event: $data.gatheringEvent,
      times: $data.gatheringTimes,
      result: $data.gatheringResult,
      inventory: $options.currentInventory
    })
  } : {}, {
    an: $data.showMapPopup
  }, $data.showMapPopup ? {
    ao: common_vendor.f($options.mapList, (map, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(map.名称 || map.name || ""),
        b: common_vendor.t(map.描述 || map.desc),
        c: $options.getMapNpcs(map).length
      }, $options.getMapNpcs(map).length ? {
        d: common_vendor.t($options.getMapNpcs(map).join("、"))
      } : {}, {
        e: $options.getMapMonsters(map).length
      }, $options.getMapMonsters(map).length ? {
        f: common_vendor.t($options.getMapMonsters(map).join("、"))
      } : {}, {
        g: $options.getMapGatherItems(map).length
      }, $options.getMapGatherItems(map).length ? {
        h: common_vendor.t($options.getMapGatherItems(map).join("、"))
      } : {}, {
        i: $options.getMapRequirements(map)
      }, $options.getMapRequirements(map) ? {
        j: common_vendor.t($options.getMapRequirements(map))
      } : {}, {
        k: map.id,
        l: $data.currentMap && $data.currentMap.id === map.id ? 1 : "",
        m: !$options.canEnterMap(map) ? 1 : "",
        n: common_vendor.o(($event) => $options.selectMap(map), map.id)
      });
    }),
    ap: common_vendor.o(($event) => $data.showMapPopup = false),
    aq: common_vendor.o(() => {
    }),
    ar: common_vendor.o(($event) => $data.showMapPopup = false)
  } : {}, {
    as: $data.showBattlePopup
  }, $data.showBattlePopup ? {
    at: common_vendor.o($options.handleBattleAttack),
    av: common_vendor.o($options.handleBattleEscape),
    aw: common_vendor.o($options.handleBattleClose),
    ax: common_vendor.p({
      visible: $data.showBattlePopup,
      battleLog: $data.battleLog,
      player: $data.battlePlayer,
      monster: $data.battleMonster,
      battleStage: $data.battleStage,
      attackMode: $data.battleAttackMode
    })
  } : {}, {
    ay: $data.healingMeditationMessages.length > 0
  }, $data.healingMeditationMessages.length > 0 ? common_vendor.e({
    az: $data.healingMeditationLoading
  }, $data.healingMeditationLoading ? {} : {}, {
    aA: common_vendor.f($data.healingMeditationMessages, (msg, idx, i0) => {
      return {
        a: common_vendor.t(msg),
        b: idx
      };
    })
  }) : {}, {
    aB: $data.showChatPopup
  }, $data.showChatPopup ? common_vendor.e({
    aC: common_vendor.o(($event) => $data.showChatPopup = false),
    aD: common_vendor.f($data.chatMessages, (msg, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t($options.formatChatTimeWithSeconds(msg.time)),
        b: common_vendor.t(msg.sender),
        c: common_vendor.n(msg.isOwn ? "chat-sender-own" : ""),
        d: common_vendor.n("chat-sender-" + (msg.chat_type || "world")),
        e: msg.chat_type && msg.chat_type !== "world"
      }, msg.chat_type && msg.chat_type !== "world" ? {
        f: common_vendor.t($options.getChatTypeName(msg.chat_type)),
        g: common_vendor.n("chat-type-" + msg.chat_type)
      } : {}, {
        h: common_vendor.t(msg.content),
        i: index,
        j: common_vendor.n(msg.isOwn ? "chat-message-own" : ""),
        k: common_vendor.n("chat-message-" + (msg.chat_type || "world"))
      });
    }),
    aE: $data.chatMessages.length === 0
  }, $data.chatMessages.length === 0 ? {} : {}, {
    aF: common_vendor.t($options.getCurrentChatTypeName()),
    aG: $data.chatTypeIndex,
    aH: $options.chatTypeNames,
    aI: common_vendor.o((...args) => $options.onChatTypeChange && $options.onChatTypeChange(...args)),
    aJ: $data.currentChatType === "private"
  }, $data.currentChatType === "private" ? {
    aK: $data.chatTargetName,
    aL: common_vendor.o(($event) => $data.chatTargetName = $event.detail.value)
  } : {}, {
    aM: $options.getChatPlaceholder(),
    aN: common_vendor.o((...args) => $options.sendChatMessage && $options.sendChatMessage(...args)),
    aO: $data.chatInputText,
    aP: common_vendor.o(($event) => $data.chatInputText = $event.detail.value),
    aQ: common_vendor.o((...args) => $options.sendChatMessage && $options.sendChatMessage(...args)),
    aR: !$data.chatInputText.trim() || $data.currentChatType === "private" && !$data.chatTargetName.trim(),
    aS: common_vendor.o(() => {
    }),
    aT: common_vendor.o(($event) => $data.showChatPopup = false)
  }) : {}, {
    aU: $data.showPlayerMenuModal
  }, $data.showPlayerMenuModal ? {
    aV: common_vendor.t($data.selectedPlayer.name || $data.selectedPlayer.character_name),
    aW: common_vendor.o(($event) => $data.showPlayerMenuModal = false),
    aX: common_vendor.t($data.selectedPlayer.level || "未知"),
    aY: common_vendor.t($options.getPlayerStatusText($data.selectedPlayer.status)),
    aZ: common_vendor.o(($event) => $options.playerAction("sneak_attack")),
    ba: common_vendor.o(($event) => $options.playerAction("give")),
    bb: common_vendor.o(($event) => $options.playerAction("steal")),
    bc: common_vendor.o(($event) => $options.playerAction("view")),
    bd: common_vendor.o(() => {
    }),
    be: common_vendor.o(($event) => $data.showPlayerMenuModal = false)
  } : {}, {
    bf: $options.displayMapNpcs.length > 0
  }, $options.displayMapNpcs.length > 0 ? {
    bg: $data.npcBtnTop,
    bh: common_vendor.o((...args) => _ctx.onNpcBtnTouchStart && _ctx.onNpcBtnTouchStart(...args)),
    bi: common_vendor.o((...args) => $options.onNpcBtnTouchMove && $options.onNpcBtnTouchMove(...args)),
    bj: common_vendor.o((...args) => $options.onNpcBtnTouchEnd && $options.onNpcBtnTouchEnd(...args)),
    bk: common_vendor.o((...args) => $options.onNpcBtnLongPress && $options.onNpcBtnLongPress(...args)),
    bl: common_vendor.o(($event) => $data.npcSidebarVisible = true)
  } : {}, {
    bm: $data.playerBtnTop,
    bn: common_vendor.o((...args) => $options.onPlayerBtnTouchStart && $options.onPlayerBtnTouchStart(...args)),
    bo: common_vendor.o((...args) => $options.onPlayerBtnTouchMove && $options.onPlayerBtnTouchMove(...args)),
    bp: common_vendor.o((...args) => $options.onPlayerBtnTouchEnd && $options.onPlayerBtnTouchEnd(...args)),
    bq: common_vendor.o((...args) => $options.onPlayerBtnLongPress && $options.onPlayerBtnLongPress(...args)),
    br: common_vendor.o((...args) => $options.showPlayerSidebar && $options.showPlayerSidebar(...args)),
    bs: $data.npcSidebarVisible
  }, $data.npcSidebarVisible ? {
    bt: common_vendor.o(($event) => $data.npcSidebarVisible = false)
  } : {}, {
    bv: common_vendor.o(($event) => $data.npcSidebarVisible = false),
    bw: common_vendor.f($options.displayMapNpcs, (npc, k0, i0) => {
      return common_vendor.e({
        a: npc.avatar,
        b: common_vendor.t(npc && npc.name ? npc.name : ""),
        c: npc.desc
      }, npc.desc ? {
        d: common_vendor.t(npc.desc)
      } : {}, {
        e: npc.id,
        f: common_vendor.o(($event) => $options.showNpcMenu(npc), npc.id)
      });
    }),
    bx: $data.npcSidebarVisible ? 1 : "",
    by: $data.playerSidebarVisible
  }, $data.playerSidebarVisible ? {
    bz: common_vendor.o(($event) => $data.playerSidebarVisible = false)
  } : {}, {
    bA: common_vendor.t($data.mapPlayers.length),
    bB: common_vendor.o(($event) => $data.playerSidebarVisible = false),
    bC: common_vendor.f($data.mapPlayers, (player, k0, i0) => {
      return common_vendor.e({
        a: player.avatar || "/static/npc/default.png",
        b: common_vendor.t(player.name || player.character_name),
        c: player.isSelf
      }, player.isSelf ? {} : {}, {
        d: player.isSelf ? 1 : "",
        e: common_vendor.t(player.level || "未知"),
        f: common_vendor.t($options.getPlayerStatusText(player.status)),
        g: common_vendor.n(player.status),
        h: player.isSelf ? 1 : "",
        i: player.id,
        j: common_vendor.o(($event) => player.isSelf ? null : $options.showPlayerMenu(player), player.id)
      });
    }),
    bD: $data.mapPlayers.length === 0
  }, $data.mapPlayers.length === 0 ? {} : {}, {
    bE: $data.playerSidebarVisible ? 1 : "",
    bF: $data.leftBtnTop,
    bG: common_vendor.o((...args) => $options.onLeftBtnTouchStart && $options.onLeftBtnTouchStart(...args)),
    bH: common_vendor.o((...args) => $options.onLeftBtnTouchMove && $options.onLeftBtnTouchMove(...args)),
    bI: common_vendor.o((...args) => $options.onLeftBtnTouchEnd && $options.onLeftBtnTouchEnd(...args)),
    bJ: common_vendor.o((...args) => $options.onLeftBtnLongPress && $options.onLeftBtnLongPress(...args)),
    bK: common_vendor.o((...args) => $options.toggleLeftMenu && $options.toggleLeftMenu(...args)),
    bL: $data.chatBtnTop,
    bM: common_vendor.o((...args) => $options.onChatBtnTouchStart && $options.onChatBtnTouchStart(...args)),
    bN: common_vendor.o((...args) => $options.onChatBtnTouchMove && $options.onChatBtnTouchMove(...args)),
    bO: common_vendor.o((...args) => $options.onChatBtnTouchEnd && $options.onChatBtnTouchEnd(...args)),
    bP: common_vendor.o((...args) => $options.onChatBtnLongPress && $options.onChatBtnLongPress(...args)),
    bQ: common_vendor.o((...args) => $options.openChat && $options.openChat(...args)),
    bR: $data.leftMenuVisible
  }, $data.leftMenuVisible ? {
    bS: common_vendor.o(($event) => $options.onMenuClick("healing")),
    bT: common_vendor.o(($event) => $options.onMenuClick("meditation")),
    bU: common_vendor.o(($event) => $options.onMenuClick("ranking")),
    bV: common_vendor.o(($event) => $options.onMenuClick("redeem")),
    bW: $data.leftBtnTop,
    bX: common_vendor.o(() => {
    })
  } : {}, {
    bY: $data.leftMenuVisible
  }, $data.leftMenuVisible ? {
    bZ: common_vendor.o(($event) => $data.leftMenuVisible = false)
  } : {}, {
    ca: $data.showRankingModal
  }, $data.showRankingModal ? common_vendor.e({
    cb: common_vendor.o(($event) => $data.showRankingModal = false),
    cc: $data.rankingCurrentTab === "wealth" ? 1 : "",
    cd: common_vendor.o(($event) => $options.switchRankingTab("wealth")),
    ce: $data.rankingCurrentTab === "experience" ? 1 : "",
    cf: common_vendor.o(($event) => $options.switchRankingTab("experience")),
    cg: $data.rankingCurrentTab === "adventure" ? 1 : "",
    ch: common_vendor.o(($event) => $options.switchRankingTab("adventure")),
    ci: $data.rankingLoading
  }, $data.rankingLoading ? {} : $data.rankingList.length === 0 ? {} : {
    ck: common_vendor.f($data.rankingList, (item, index, i0) => {
      return common_vendor.e({
        a: index === 0
      }, index === 0 ? {} : index === 1 ? {} : index === 2 ? {} : {
        d: common_vendor.t(index + 1)
      }, {
        b: index === 1,
        c: index === 2,
        e: index < 3 ? 1 : "",
        f: common_vendor.t(item.name)
      }, $data.rankingCurrentTab === "wealth" ? {
        g: common_vendor.t($options.formatMoney(item.money))
      } : $data.rankingCurrentTab === "experience" ? {
        h: common_vendor.t($options.formatNumber(item.experience))
      } : $data.rankingCurrentTab === "adventure" ? {
        i: common_vendor.t($options.formatNumber(item.adventureCount))
      } : {}, {
        j: item.id || index,
        k: item.isCurrentPlayer ? 1 : "",
        l: index < 3 ? 1 : "",
        m: index === 0 ? 1 : "",
        n: index === 1 ? 1 : "",
        o: index === 2 ? 1 : ""
      });
    }),
    cl: $data.rankingCurrentTab === "wealth",
    cm: $data.rankingCurrentTab === "experience",
    cn: $data.rankingCurrentTab === "adventure"
  }, {
    cj: $data.rankingList.length === 0,
    co: $data.myRanking
  }, $data.myRanking ? common_vendor.e({
    cp: common_vendor.t($data.myRanking.rank),
    cq: $data.rankingCurrentTab === "wealth"
  }, $data.rankingCurrentTab === "wealth" ? {
    cr: common_vendor.t($options.formatMoney($data.myRanking.money))
  } : $data.rankingCurrentTab === "experience" ? {
    ct: common_vendor.t($options.formatNumber($data.myRanking.experience))
  } : $data.rankingCurrentTab === "adventure" ? {
    cw: common_vendor.t($options.formatNumber($data.myRanking.adventureCount))
  } : {}, {
    cs: $data.rankingCurrentTab === "experience",
    cv: $data.rankingCurrentTab === "adventure"
  }) : {}, {
    cx: common_vendor.o(() => {
    }),
    cy: common_vendor.o(($event) => $data.showRankingModal = false)
  }) : {}, {
    cz: $data.showRedeemModal
  }, $data.showRedeemModal ? common_vendor.e({
    cA: common_vendor.o(($event) => $data.showRedeemModal = false),
    cB: $data.redeemLoading,
    cC: $data.redeemCode,
    cD: common_vendor.o(($event) => $data.redeemCode = $event.detail.value),
    cE: $data.redeemLoading
  }, $data.redeemLoading ? {} : {}, {
    cF: !$data.redeemCode.trim() || $data.redeemLoading ? 1 : "",
    cG: common_vendor.o((...args) => $options.submitRedeemCode && $options.submitRedeemCode(...args)),
    cH: !$data.redeemCode.trim() || $data.redeemLoading,
    cI: common_vendor.o(() => {
    }),
    cJ: common_vendor.o(($event) => $data.showRedeemModal = false)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-1cf27b2a"]]);
wx.createPage(MiniProgramPage);
