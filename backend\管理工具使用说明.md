# 游戏管理工具使用说明

## 概述

游戏管理工具是一个命令行界面的管理程序，用于管理游戏数据库中的玩家数据，包括物品、属性、武功、采集技能等。

## 启动方式

### 方式1：直接运行Python脚本
```bash
cd backend
python admin_tool.py
```

### 方式2：使用批处理文件（Windows）
```bash
双击 backend/start_admin_tool.bat
```

## 功能列表

### 📋 基本查询功能

#### 1. 查看所有玩家
```
管理员> list_players
```
显示所有玩家的基本信息，包括ID、用户名、角色名、等级、金钱等。

#### 2. 查看玩家详细信息
```
管理员> player_info 1
```
显示指定玩家的详细信息，包括属性、背包物品数量、武功数量等。

#### 3. 搜索物品
```
管理员> search_item 镰刀
管理员> search_item sword
```
根据关键词搜索物品，支持中文和英文。

### 🎁 物品管理功能

#### 1. 给玩家添加物品
```
管理员> add_item 1 sickle_1 5
```
给玩家ID为1的玩家添加5个简易镰刀。

#### 2. 查看玩家背包
```
管理员> show_inventory 1
```
显示指定玩家背包中的所有物品。

#### 3. 清空玩家背包
```
管理员> clear_inventory 1
```
清空指定玩家的背包（谨慎使用）。

### 📊 属性管理功能

#### 1. 设置玩家等级
```
管理员> set_level 1 50
```

#### 2. 设置玩家金钱
```
管理员> set_money 1 10000
```

#### 3. 设置玩家血量
```
管理员> set_hp 1 500 500
```
设置当前血量和最大血量。

#### 4. 设置玩家内力
```
管理员> set_mp 1 300 300
```
设置当前内力和最大内力。

#### 5. 设置玩家精力
```
管理员> set_energy 1 200 200
```
设置当前精力和最大精力。

### ⚔️ 武功管理功能

#### 1. 给玩家添加武功
```
管理员> add_martial 1 基本剑法 20
```
给玩家添加指定等级的武功。

### 🔨 采集技能管理

#### 1. 设置采集技能
```
管理员> set_gather 1 herbalism 10 500
```
设置玩家的采药技能为10级，经验500点。

支持的采集技能类型：
- `herbalism` - 采药
- `mining` - 挖矿
- `logging` - 伐木
- `skinning` - 剥皮

## 常用物品ID

### 采集工具
- `sickle_1` - 简易镰刀
- `sickle_2` - 铜镰刀
- `sickle_3` - 铁镰刀
- `axe_1` - 简易斧头
- `axe_2` - 铜斧头
- `pickaxe_1` - 简易矿镐
- `pickaxe_2` - 铜矿镐
- `knife_1` - 简易小刀

### 采集物品
- `herb_wildgrass` - 野草
- `herb_baihua` - 百花瓣
- `herb_gancao` - 甘草
- `wood_firewood` - 柴火
- `wood_pine` - 松木
- `ore_coal` - 煤
- `ore_copper` - 铜矿石

### 门派信物
- `wudang_token` - 武当信物
- `shaolin_token` - 少林信物
- `emei_token` - 峨眉信物
- `huashan_token` - 华山信物

## 使用示例

### 场景1：新玩家快速升级
```
管理员> set_level 1 30
管理员> set_money 1 5000
管理员> set_hp 1 300 300
管理员> add_martial 1 基本剑法 15
管理员> add_item 1 sickle_3 1
```

### 场景2：测试采集系统
```
管理员> add_item 1 sickle_1 1
管理员> add_item 1 axe_1 1
管理员> add_item 1 pickaxe_1 1
管理员> set_gather 1 herbalism 5 100
管理员> set_gather 1 logging 3 50
```

### 场景3：测试门派系统
```
管理员> add_item 1 wudang_token 1
管理员> add_item 1 shaolin_token 1
```

## 注意事项

1. **备份数据库**：在进行大量修改前，建议备份 `game.db` 文件。

2. **玩家在线状态**：如果玩家正在游戏中，修改可能需要玩家重新登录才能生效。

3. **数据一致性**：修改属性时要注意数据的合理性，避免设置过高或过低的值。

4. **物品ID**：使用 `search_item` 命令查找正确的物品ID，避免输入错误。

5. **权限管理**：此工具具有完全的数据库访问权限，请谨慎使用。

## 故障排除

### 问题1：找不到玩家
- 确认玩家ID是否正确
- 使用 `list_players` 查看所有玩家

### 问题2：物品添加失败
- 使用 `search_item` 确认物品ID是否正确
- 检查物品ID的拼写

### 问题3：数据库连接失败
- 确认 `game.db` 文件存在
- 检查文件权限
- 确保没有其他程序占用数据库

## 扩展功能

如需添加新功能，可以修改 `admin_tool.py` 文件：

1. 在 `GameAdminTool` 类中添加新方法
2. 在 `main()` 函数中添加新的命令处理
3. 更新 `print_help()` 方法中的帮助信息

## 安全建议

1. 仅在开发和测试环境中使用
2. 生产环境使用前请充分测试
3. 定期备份数据库
4. 限制工具的访问权限
5. 记录重要的修改操作
