import sqlite3
import json
import os

# 加载武功配置
wugong_path = os.path.join(os.path.dirname(__file__), 'wugong_enhanced.json')
with open(wugong_path, encoding='utf-8') as f:
    wugong_config = {item['武功名']: item for item in json.load(f)}

life_skills = ['采药', '伐木', '挖矿', '剥皮']

db_path = os.path.join(os.path.dirname(__file__), 'game.db')
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

cursor.execute("SELECT id, data FROM players")
rows = cursor.fetchall()
for player_id, data_json in rows:
    data = json.loads(data_json)
    martial_skills = data.get('martial_skills')
    changed = False
    if isinstance(martial_skills, list):
        for skill in martial_skills:
            name = skill.get('name')
            if name in life_skills:
                if skill.get('是否可招架') != '否':
                    skill['是否可招架'] = '否'
                    changed = True
            elif name and '是否可招架' not in skill:
                wcfg = wugong_config.get(name)
                if wcfg and '是否可招架' in wcfg:
                    skill['是否可招架'] = wcfg['是否可招架']
                    changed = True
    elif isinstance(martial_skills, dict):
        for name, skill in martial_skills.items():
            if name in life_skills:
                if skill.get('是否可招架') != '否':
                    skill['是否可招架'] = '否'
                    changed = True
            elif '是否可招架' not in skill:
                wcfg = wugong_config.get(name)
                if wcfg and '是否可招架' in wcfg:
                    skill['是否可招架'] = wcfg['是否可招架']
                    changed = True
    if changed:
        print(f'修正玩家ID {player_id} 的武功可招架字段')
        cursor.execute("UPDATE players SET data=? WHERE id=?", (json.dumps(data, ensure_ascii=False), player_id))
conn.commit()
conn.close()
print('所有玩家武功已补全是否可招架字段') 