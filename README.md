# 🎮 仗剑江湖行 - 武侠点击式游戏

一个基于uniapp + Python WebSocket的武侠主题点击式游戏小程序。

## 📱 游戏特色

- **点击闯江湖**：每次点击触发随机事件，体验不同的江湖奇遇
- **装备系统**：收集和装备各种武器、护甲、饰品
- **武功修炼**：学习不同品级的武功，提升角色实力
- **商店交易**：购买装备、草药，出售多余物品
- **状态管理**：气血、内力、体力、精力等完整属性系统
- **实时通信**：WebSocket实时数据同步，支持多用户

## 🏗️ 系统架构

### 前端 (微信小程序)
- **框架**：uniapp + Vue.js
- **通信**：WebSocket实时通信
- **存储**：服务器端数据存储
- **界面**：武侠风格UI设计

### 后端 (Python服务器)
- **语言**：Python 3.7+
- **通信**：WebSocket服务器
- **数据**：内存存储（可扩展为数据库）
- **功能**：游戏逻辑处理、事件生成、状态管理

## 🚀 快速开始

### 环境要求

- HBuilderX 或 VS Code
- 微信开发者工具（用于小程序预览）
- Python 3.7+
- pip包管理器

### 安装运行

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 仗剑江湖行
   ```

2. **启动后端服务器**
   ```bash
   cd backend
   pip install -r requirements.txt
   python server.py
   ```
   服务器将在 `ws://localhost:8080/ws` 启动

3. **启动前端项目**
   - 使用HBuilderX打开项目
   - 点击工具栏的运行按钮
   - 选择运行到微信小程序
   - 在微信开发者工具中预览

## 🎯 游戏玩法

### 主要功能

1. **闯江湖**
   - 点击"闯江湖"按钮触发随机事件
   - 事件类型：好运、遭遇NPC、采集、奇遇等
   - 获得经验、银两、装备等奖励

2. **背包系统**
   - 查看和管理所有物品
   - 装备武器、护甲、饰品
   - 使用草药恢复状态

3. **武功系统**
   - 修炼武功提升等级
   - 使用武功获得临时加成
   - 不同品级武功效果各异

4. **商店系统**
   - 购买装备、草药、材料
   - 出售多余物品获得银两
   - 多种商店类型

### 属性说明

- **气血**：角色生命值，战斗失败会减少
- **内力**：使用武功消耗，打坐可恢复
- **体力**：进行各种活动消耗
- **精力**：修炼武功消耗
- **攻击/防御**：战斗属性
- **声望/因果**：江湖地位和善恶值

## 📁 项目结构

```
仗剑江湖行/
├── 前端 (微信小程序)
│   ├── pages/                 # 页面文件
│   │   ├── index/            # 主页面（江湖）
│   │   ├── backpack/         # 背包页面
│   │   ├── skills/           # 武功页面
│   │   ├── shop/             # 商店页面
│   │   ├── market/           # 市场页面
│   │   ├── guild/            # 门派页面
│   │   ├── healing/          # 疗伤页面
│   │   └── crafting/         # 打造页面
│   ├── utils/                # 工具类
│   │   ├── websocket.js      # WebSocket通信管理
│   │   ├── gameData.js       # 游戏数据模型
│   │   └── gameState.js      # 游戏状态管理
│   ├── static/               # 静态资源
│   ├── pages.json            # 页面配置
│   ├── manifest.json         # 应用配置
│   └── README.md             # 项目说明
└── 后端 (Python服务器)
    ├── server.py             # WebSocket服务器
    ├── requirements.txt      # Python依赖
    └── 后端启动说明.md        # 后端文档
```

## 🔌 通信协议

### WebSocket消息格式

```json
{
  "type": "消息类型",
  "data": {
    "参数": "值"
  },
  "timestamp": 1234567890
}
```

### 主要消息类型

| 类型 | 描述 | 方向 |
|------|------|------|
| auth | 用户认证 | 客户端→服务器 |
| get_player_data | 获取玩家数据 | 客户端→服务器 |
| player_data | 玩家数据响应 | 服务器→客户端 |
| adventure | 闯江湖事件 | 客户端→服务器 |
| game_event | 游戏事件响应 | 服务器→客户端 |
| meditation | 打坐恢复 | 客户端→服务器 |
| healing | 疗伤 | 客户端→服务器 |

## 🛠️ 技术栈

- **前端**：uniapp + Vue.js + WebSocket
- **后端**：Python + WebSocket + asyncio
- **通信**：WebSocket实时通信
- **存储**：服务器端数据存储
- **样式**：CSS3 + 武侠风格设计

## 🎨 界面特色

- 武侠风格的渐变背景
- 现代化的卡片式设计
- 流畅的动画效果
- 响应式布局适配
- 实时数据同步

## 🔧 开发说明

### 数据存储

游戏数据存储在服务器端，包括：
- 玩家属性
- 背包物品
- 装备信息
- 武功列表
- 事件日志

### 扩展功能

可以继续添加的功能：
- 数据库持久化
- 用户认证系统
- 门派系统
- 打造系统
- 市场交易
- 疗伤系统
- 更多武功和装备

## 📝 更新日志

### v2.0.0 (当前版本)
- ✅ WebSocket实时通信
- ✅ 服务器端数据存储
- ✅ 多用户支持
- ✅ 完整的事件系统
- ✅ 实时状态同步

### v1.0.0
- ✅ 基础游戏框架
- ✅ 闯江湖事件系统
- ✅ 背包和装备系统
- ✅ 武功修炼系统
- ✅ 商店交易系统
- ✅ 完整的UI界面

## 🤝 贡献

欢迎提交Issue和Pull Request来改进游戏！

## 📄 许可证

MIT License

---

**仗剑江湖行** - 体验不一样的武侠世界！ 