#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为wugong.json中的所有武功添加多条武侠MUD风格的攻击描述
"""

import json
import os

def enhance_wugong_descriptions():
    """为武功添加多条描述"""
    
    # 读取原始文件
    with open('wugong.json', 'r', encoding='utf-8') as f:
        wugong_data = json.load(f)
    
    # 武功类型对应的描述模板
    description_templates = {
        "剑法": {
            "基础": [
                "你手中长剑如{theme}，剑势{style}，剑光如虹。",
                "你脚踏{theme}步，长剑如臂使指，{name}运用自如。",
                "你身形如{theme}，长剑如电，{name}虽{complexity}，但胜在{advantage}。",
                "你手中长剑一抖，剑光如{theme}般{style}，{name}虽{complexity}，但效果显著。",
                "你脚踏七星步，长剑如风，{name}虽{complexity}，但胜在{advantage}。"
            ],
            "themes": {
                "流云": {"style": "飘逸", "complexity": "朴实无华", "advantage": "变化多端"},
                "金蛇": {"style": "灵动", "complexity": "变化多端", "advantage": "灵活"},
                "独孤": {"style": "纵横", "complexity": "变化无穷", "advantage": "无招胜有招"},
                "青锋": {"style": "锋利", "complexity": "朴实无华", "advantage": "锋利"},
                "紫电": {"style": "迅疾", "complexity": "变化多端", "advantage": "迅疾"},
                "寒光": {"style": "森寒", "complexity": "朴实无华", "advantage": "森寒"},
                "天外飞仙": {"style": "飘逸", "complexity": "变化多端", "advantage": "飘逸"},
                "碧海": {"style": "如潮", "complexity": "变化多端", "advantage": "连绵"},
                "落英": {"style": "如花", "complexity": "变化多端", "advantage": "绚烂"},
                "天山": {"style": "凌厉", "complexity": "朴实无华", "advantage": "凌厉"},
                "无名": {"style": "平实", "complexity": "朴实无华", "advantage": "平实"}
            }
        },
        "刀法": {
            "基础": [
                "你手中长刀如{theme}，刀势{style}，刀光如电。",
                "你脚踏{theme}步，长刀如臂使指，{name}运用自如。",
                "你身形如{theme}，长刀如电，{name}虽{complexity}，但胜在{advantage}。",
                "你手中长刀一抖，刀光如{theme}般{style}，{name}虽{complexity}，但效果显著。",
                "你脚踏马步，长刀如风，{name}虽{complexity}，但胜在{advantage}。"
            ],
            "themes": {
                "狂风": {"style": "猛烈", "complexity": "朴实无华", "advantage": "猛烈"},
                "血影": {"style": "诡异", "complexity": "变化多端", "advantage": "诡异"},
                "断水": {"style": "如水", "complexity": "朴实无华", "advantage": "连绵"},
                "烈焰": {"style": "如火", "complexity": "变化多端", "advantage": "炽热"},
                "寒冰": {"style": "如冰", "complexity": "朴实无华", "advantage": "寒气"},
                "风雷": {"style": "如风", "complexity": "朴实无华", "advantage": "雷霆"},
                "无影": {"style": "无影", "complexity": "变化多端", "advantage": "难以捉摸"},
                "烈阳": {"style": "如阳", "complexity": "变化多端", "advantage": "炽热"},
                "断岳": {"style": "如山", "complexity": "变化多端", "advantage": "威力无穷"},
                "无名": {"style": "平实", "complexity": "朴实无华", "advantage": "平实"}
            }
        },
        "拳法": {
            "基础": [
                "你双拳如{theme}，拳势{style}，拳风呼啸。",
                "你脚踏{theme}步，双拳如臂使指，{name}运用自如。",
                "你身形如{theme}，双拳如电，{name}虽{complexity}，但胜在{advantage}。",
                "你双拳一抖，拳光如{theme}般{style}，{name}虽{complexity}，但效果显著。",
                "你脚踏弓步，双拳如风，{name}虽{complexity}，但胜在{advantage}。"
            ],
            "themes": {
                "太极": {"style": "圆润", "complexity": "变化多端", "advantage": "刚柔并济"},
                "罗汉": {"style": "威猛", "complexity": "朴实无华", "advantage": "威猛"},
                "七伤": {"style": "刚猛", "complexity": "变化多端", "advantage": "伤敌伤己"},
                "大力金刚": {"style": "如雷", "complexity": "朴实无华", "advantage": "威猛"},
                "无影": {"style": "如风", "complexity": "朴实无华", "advantage": "难以捉摸"},
                "金刚": {"style": "刚猛", "complexity": "朴实无华", "advantage": "威不可挡"},
                "烈阳": {"style": "如阳", "complexity": "变化多端", "advantage": "炽热"},
                "断岳": {"style": "如山", "complexity": "变化多端", "advantage": "威力无穷"},
                "无名": {"style": "平实", "complexity": "朴实无华", "advantage": "平实"}
            }
        },
        "轻功": {
            "基础": [
                "你身形如{theme}，身法{style}，来去自如。",
                "你脚踏{theme}步，身形如臂使指，{name}运用自如。",
                "你身形如{theme}，身法如电，{name}虽{complexity}，但胜在{advantage}。",
                "你身形一抖，身法如{theme}般{style}，{name}虽{complexity}，但效果显著。",
                "你脚踏轻功步，身形如风，{name}虽{complexity}，但胜在{advantage}。"
            ],
            "themes": {
                "雪": {"style": "轻盈", "complexity": "朴实无华", "advantage": "轻盈"},
                "风": {"style": "如风", "complexity": "朴实无华", "advantage": "来去无踪"},
                "云": {"style": "如云", "complexity": "变化多端", "advantage": "虚实难测"},
                "龙虎": {"style": "雄健", "complexity": "朴实无华", "advantage": "气势如虹"},
                "影": {"style": "如影", "complexity": "变化多端", "advantage": "难以琢磨"},
                "游龙": {"style": "如龙", "complexity": "朴实无华", "advantage": "灵动自如"},
                "云": {"style": "如云", "complexity": "朴实无华", "advantage": "虚实难测"},
                "燕": {"style": "如燕", "complexity": "变化多端", "advantage": "穿云破雾"},
                "羽": {"style": "如羽", "complexity": "变化多端", "advantage": "飘然若仙"}
            }
        },
        "内功": {
            "基础": [
                "你真气如{theme}，真气{style}，内力充沛。",
                "你脚踏{theme}步，真气如臂使指，{name}运用自如。",
                "你身形如{theme}，真气如电，{name}虽{complexity}，但胜在{advantage}。",
                "你真气一抖，真气如{theme}般{style}，{name}虽{complexity}，但效果显著。",
                "你盘膝而坐，真气如风，{name}虽{complexity}，但胜在{advantage}。"
            ],
            "themes": {
                "混元": {"style": "浑厚", "complexity": "朴实无华", "advantage": "浑厚"},
                "纯阳": {"style": "阳刚", "complexity": "变化多端", "advantage": "刚猛"},
                "小无相": {"style": "无形", "complexity": "变化多端", "advantage": "变化无常"},
                "九阴": {"style": "深邃", "complexity": "变化多端", "advantage": "深邃"},
                "紫霞": {"style": "东来", "complexity": "变化多端", "advantage": "内力澎湃"},
                "天罡": {"style": "刚猛", "complexity": "朴实无华", "advantage": "气势如虹"},
                "北冥": {"style": "浩瀚", "complexity": "变化多端", "advantage": "浩瀚"},
                "金刚不坏": {"style": "护体", "complexity": "变化多端", "advantage": "刀枪不入"},
                "无名": {"style": "流转", "complexity": "朴实无华", "advantage": "充沛"}
            }
        },
        "暗器": {
            "基础": [
                "你暗器如{theme}，暗器{style}，伤人无形。",
                "你脚踏{theme}步，暗器如臂使指，{name}运用自如。",
                "你身形如{theme}，暗器如电，{name}虽{complexity}，但胜在{advantage}。",
                "你暗器一抖，暗器如{theme}般{style}，{name}虽{complexity}，但效果显著。",
                "你手腕翻转，暗器如风，{name}虽{complexity}，但胜在{advantage}。"
            ],
            "themes": {
                "花叶": {"style": "轻盈", "complexity": "朴实无华", "advantage": "轻盈"},
                "袖箭": {"style": "无声", "complexity": "朴实无华", "advantage": "无声无息"},
                "孔雀翎": {"style": "如虹", "complexity": "变化多端", "advantage": "绚烂夺目"},
                "流星": {"style": "如流星", "complexity": "变化多端", "advantage": "迅疾"},
                "毒砂": {"style": "含毒", "complexity": "朴实无华", "advantage": "伤人无形"},
                "梅花": {"style": "如梅", "complexity": "变化多端", "advantage": "精准"},
                "乾坤": {"style": "无形", "complexity": "变化多端", "advantage": "无形无踪"},
                "神针": {"style": "无影", "complexity": "变化多端", "advantage": "无影无踪"},
                "毒龙": {"style": "含毒", "complexity": "变化多端", "advantage": "伤人无形"},
                "无名": {"style": "平实", "complexity": "朴实无华", "advantage": "平实"}
            }
        }
    }
    
    # 处理每个武功
    for wugong in wugong_data:
        wugong_name = wugong["武功名"]
        wugong_type = wugong["类型"]
        
        # 如果已经是数组格式，跳过
        if isinstance(wugong["攻击描述"], list):
            continue
            
        # 获取主题词
        theme = None
        for key in description_templates.get(wugong_type, {}).get("themes", {}):
            if key in wugong_name:
                theme = key
                break
        
        if not theme:
            # 如果没有找到主题词，使用默认描述
            if wugong_type == "剑法":
                theme = "无名"
            elif wugong_type == "刀法":
                theme = "无名"
            elif wugong_type == "拳法":
                theme = "无名"
            elif wugong_type == "轻功":
                theme = "风"
            elif wugong_type == "内功":
                theme = "无名"
            elif wugong_type == "暗器":
                theme = "无名"
        
        # 获取描述模板
        templates = description_templates.get(wugong_type, {}).get("基础", [])
        theme_info = description_templates.get(wugong_type, {}).get("themes", {}).get(theme, {})
        
        if templates and theme_info:
            # 生成多条描述
            descriptions = []
            for template in templates:
                description = template.format(
                    theme=theme,
                    name=wugong_name,
                    style=theme_info.get("style", "平实"),
                    complexity=theme_info.get("complexity", "朴实无华"),
                    advantage=theme_info.get("advantage", "平实")
                )
                descriptions.append(description)
            
            wugong["攻击描述"] = descriptions
    
    # 保存修改后的文件
    with open('wugong_enhanced.json', 'w', encoding='utf-8') as f:
        json.dump(wugong_data, f, ensure_ascii=False, indent=2)
    
    print("武功描述增强完成！已保存到 wugong_enhanced.json")

if __name__ == "__main__":
    enhance_wugong_descriptions() 