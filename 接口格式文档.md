# 仗剑江湖行 WebSocket 接口格式文档

## 1. 注册/登录/认证

- **注册**
  - 请求：
    ```json
    { "type": "register", "data": { "username": "", "password": "", "character_name": "", "gender": "" } }
    ```
  - 响应成功：
    ```json
    { "type": "register_success", "data": { "token": "", "userInfo": { "username": "", "characterName": "", "userId": "", "gender": "", "talent": {}, "fortune": 1 } } }
    ```
  - 响应失败：
    ```json
    { "type": "register_failed", "data": { "message": "" } }
    ```

- **登录**
  - 请求：
    ```json
    { "type": "login", "data": { "username": "", "password": "" } }
    ```
  - 响应成功：
    ```json
    { "type": "login_success", "data": { "token": "", "userInfo": { ...同上 } } }
    ```
  - 响应失败：
    ```json
    { "type": "login_failed", "data": { "message": "" } }
    ```

- **认证**
  - 请求：
    ```json
    { "type": "auth", "data": { "token": "" } }
    ```
  - 响应成功：
    ```json
    { "type": "auth_success", "data": { "player_id": "", "player": {}, "userInfo": { ...同上 } } }
    ```
  - 响应失败：
    ```json
    { "type": "auth_failed", "data": { "message": "" } }
    ```

---

## 2. 玩家数据

- **获取玩家数据**
  - 请求：`{ "type": "get_player_data" }`
  - 响应：
    ```json
    { "type": "player_data", "data": { ...玩家完整数据... } }
    ```

- **获取背包数据**
  - 请求：`{ "type": "get_inventory_data" }`
  - 响应：
    ```json
    { "type": "inventory_data", "data": { "inventory": [...], "capacity": 50 } }
    ```

- **获取武功数据**
  - 请求：`{ "type": "get_skills_data" }`
  - 响应：
    ```json
    { "type": "skills_data", "data": [ ...技能列表... ] }
    ```

---

## 3. 地图与物品配置

- **获取地图配置**
  - 请求：`{ "type": "get_maps_config" }`
  - 响应：
    ```json
    { "type": "maps_config", "data": { "changan": { ... }, "heifengzhai": { ... }, ... } }
    ```
    - data 可能是对象（推荐，当前实现）或数组（历史兼容）

- **获取物品配置**
  - 请求：`{ "type": "get_items_config" }`
  - 响应：
    ```json
    { "type": "items_config", "data": { "item_id1": { ... }, "item_id2": { ... }, ... } }
    ```

---

## 4. 地图与NPC

- **获取当前地图NPC**
  - 请求：`{ "type": "get_map_npcs" }`
  - 响应：
    ```json
    { "type": "map_npcs", "data": { "npcs": [ { "id": "npc_0", "name": "", "avatar": "", "desc": "", "functions": [ ... ] }, ... ] } }
    ```

- **切换地图**
  - 请求：`{ "type": "select_map", "data": { "map_id": "" } }`
  - 响应成功：
    ```json
    { "type": "select_map_success", "data": { "map_id": "" } }
    ```
  - 响应失败：
    ```json
    { "type": "error", "data": { "message": "" } }
    ```

---

## 5. 闯江湖/事件/战斗

- **闯江湖**
  - 请求：`{ "type": "adventure" }`
  - 响应：
    ```json
    { "type": "game_event", "data": { "type": 5, "content": "", "rewards": { ... } } }
    ```

- **战斗回合**
  - 推送：
    ```json
    { "type": "battle_round", "data": { "round": 1, "attacker": "", "defender": "", "damage": 0, "desc": "", "move": "", "martial": "", "player_hp": 0, "enemy_hp": 0, ... } }
    ```

- **战斗结果**
  - 推送：
    ```json
    { "type": "battle_result", "data": { "win": true, "player_hp": 0, "enemy_hp": 0, "battle_log": [ ... ] } }
    ```

---

## 6. 采集/打造/商店/市场

- **采集**
  - 请求：`{ "type": "gather_action", "data": { "gatherType": "" } }`
  - 响应：
    ```json
    { "type": "game_event", "data": { ... } }
    ```

- **打造**
  - 请求：`{ "type": "crafting_action", "data": { "action": "", ... } }`
  - 响应成功：
    ```json
    { "type": "get_craftable_success", "data": { "craftable_items": [ ... ], "energy": 0 } }
    ```
  - 响应失败：
    ```json
    { "type": "get_craftable_failed", "data": { "message": "" } }
    ```

- **商店操作**
  - 请求：`{ "type": "shop_action", "data": { "action": "", "npc_id": "", ... } }`
  - 响应：
    ```json
    { "type": "shop_items", "data": { "npc_id": "", "items": [ ... ] } }
    ```

- **市场操作**
  - 请求：`{ "type": "market_action", "data": { ... } }`
  - 响应：`{ "type": "...", "data": { ... } }`（详见 market_system）

---

## 7. 境界/增益/其他

- **获取增益摘要**
  - 请求：`{ "type": "get_bonus_summary" }`
  - 响应：
    ```json
    { "type": "bonus_summary", "data": { "strength": { ... }, "intelligence": { ... }, "agility": { ... }, "constitution": { ... } } }
    ```

- **心跳包**
  - 请求：`{ "type": "ping" }`
  - 响应：
    ```json
    { "type": "pong", "data": { "timestamp": 0 } }
    ```

---

## 8. 错误通用格式

- `{ "type": "error", "data": { "message": "" } }`

---

> 如需详细字段说明或某个接口的完整示例数据，可随时补充！ 