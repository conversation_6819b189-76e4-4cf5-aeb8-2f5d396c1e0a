{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script>\r\n\texport default {\r\n\t\tonLaunch: function() {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function() {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\t/* 自定义底部导航栏样式 */\r\n\t.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__icon {\r\n\t\twidth: 80rpx !important;\r\n\t\theight: 80rpx !important;\r\n\t\tborder-radius: 50% !important;\r\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2) !important;\r\n\t\tdisplay: flex !important;\r\n\t\talign-items: center !important;\r\n\t\tjustify-content: center !important;\r\n\t\tmargin-bottom: 8rpx !important;\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3) !important;\r\n\t}\r\n\t\r\n\t.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__label {\r\n\t\tcolor: #667eea !important;\r\n\t\tfont-weight: bold !important;\r\n\t}\r\n\r\n\t/* 确保其他按钮正常显示 */\r\n\t.uni-tabbar__item .uni-tabbar__icon {\r\n\t\twidth: 48rpx;\r\n\t\theight: 48rpx;\r\n\t}\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nexport function createApp() {\n  const app = createSSRApp(App)\n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "createSSRApp", "App"], "mappings": ";;;;;;;;;;;;;AACC,MAAK,YAAU;AAAA,EACd,UAAU,WAAW;AACpBA,kBAAAA,MAAA,MAAA,OAAA,gBAAY,YAAY;AAAA,EACxB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,gBAAA,UAAU;AAAA,EACtB;AAAA,EACD,QAAQ,WAAW;AAClBA,kBAAAA,MAAY,MAAA,OAAA,iBAAA,UAAU;AAAA,EACvB;AACD;ACIM,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAACC,SAAG;AAC5B,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}