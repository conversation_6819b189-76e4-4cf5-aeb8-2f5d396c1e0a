<template>
	<view class="container">
		<!-- 门派信息 -->
		<view class="guild-info" v-if="playerGuild">
			<view class="guild-header">
				<text class="guild-name">{{ playerGuild.name }}</text>
				<text class="guild-level">等级 {{ playerGuild.level }}</text>
			</view>
			<view class="guild-stats">
				<view class="stat-item">
					<text class="stat-label">声望:</text>
					<text class="stat-value">{{ player.reputation || 0 }}</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">贡献:</text>
					<text class="stat-value">{{ playerGuild.contribution }}</text>
				</view>
				<view class="stat-item">
					<text class="stat-label">职位:</text>
					<text class="stat-value">{{ getPositionName(playerGuild.position) }}</text>
				</view>
			</view>
			<view class="guild-actions" v-if="sectInfo">
				<button
					class="daily-reward-btn"
					@click="claimDailyReward"
					:disabled="!sectInfo.can_claim_daily"
				>
					{{ sectInfo.can_claim_daily ? '领取每日奖励' : '今日已领取' }}
				</button>
			</view>
		</view>

		<!-- 未加入门派 -->
		<view class="no-guild" v-else>
			<text class="no-guild-title">尚未加入门派</text>
			<text class="no-guild-desc">加入门派可以获得专属武功和任务</text>
			<button class="join-guild-btn" @click="showGuildList">加入门派</button>
		</view>

		<!-- 门派功能 -->
		<view class="guild-functions" v-if="playerGuild">
			<view class="function-grid">
				<view class="function-item" @click="showTasks">
					<text class="function-icon">📋</text>
					<text class="function-name">门派任务</text>
				</view>
				<view class="function-item" @click="showSkills">
					<text class="function-icon">⚔️</text>
					<text class="function-name">门派武功</text>
				</view>
				<view class="function-item" @click="showMembers">
					<text class="function-icon">👥</text>
					<text class="function-name">门派成员</text>
				</view>
				<view class="function-item" @click="showShop">
					<text class="function-icon">🏪</text>
					<text class="function-name">门派商店</text>
				</view>
			</view>
		</view>

		<!-- 门派任务 -->
		<view class="tasks-section" v-if="showTasksSection">
			<view class="section-header">
				<text class="section-title">门派任务</text>
				<text class="section-close" @click="hideTasks">×</text>
			</view>
			<scroll-view class="tasks-list" scroll-y="true">
				<view 
					class="task-item" 
					v-for="(task, index) in availableTasks" 
					:key="index"
					@click="showTaskDetail(task)"
				>
					<view class="task-info">
						<text class="task-name">{{ task.name }}</text>
						<text class="task-desc">{{ task.description }}</text>
						<text class="task-reward">奖励: {{ task.reward }}</text>
					</view>
					<view class="task-status">
						<text class="task-difficulty" :class="getDifficultyClass(task.difficulty)">
							{{ getDifficultyName(task.difficulty) }}
						</text>
						<button 
							class="accept-task-btn" 
							@click.stop="acceptTask(task)"
							:disabled="!canAcceptTask(task)"
						>
							接受
						</button>
					</view>
				</view>
				<view class="empty-tasks" v-if="availableTasks.length === 0">
					<text>暂无可接任务</text>
				</view>
			</scroll-view>
		</view>

		<!-- 门派武功 -->
		<view class="skills-section" v-if="showSkillsSection">
			<view class="section-header">
				<text class="section-title">门派武功</text>
				<text class="section-close" @click="hideSkills">×</text>
			</view>
			<scroll-view class="skills-list" scroll-y="true">
				<view 
					class="skill-item" 
					v-for="(skill, index) in guildSkills" 
					:key="index"
					@click="showSkillDetail(skill)"
				>
					<view class="skill-info">
						<text class="skill-name">{{ skill.name }}</text>
						<text class="skill-type">{{ getSkillTypeName(skill.type) }}</text>
						<text class="skill-desc">{{ skill.description }}</text>
					</view>
					<view class="skill-status">
						<text class="skill-level" v-if="skill.level">等级 {{ skill.level }}</text>
						<button 
							class="learn-skill-btn" 
							@click.stop="learnSkill(skill)"
							:disabled="!canLearnSkill(skill)"
						>
							{{ skill.learned ? '修炼' : '学习' }}
						</button>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 门派成员 -->
		<view class="members-section" v-if="showMembersSection">
			<view class="section-header">
				<text class="section-title">门派成员</text>
				<text class="section-close" @click="hideMembers">×</text>
			</view>
			<scroll-view class="members-list" scroll-y="true">
				<view 
					class="member-item" 
					v-for="(member, index) in guildMembers" 
					:key="index"
				>
					<view class="member-info">
						<text class="member-name">{{ member.name }}</text>
						<text class="member-position">{{ getPositionName(member.position) }}</text>
						<text class="member-level">等级 {{ member.level }}</text>
					</view>
					<view class="member-contribution">
						<text class="contribution-label">贡献:</text>
						<text class="contribution-value">{{ member.contribution }}</text>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 门派商店 -->
		<view class="shop-section" v-if="showShopSection">
			<view class="section-header">
				<text class="section-title">门派商店</text>
				<text class="section-close" @click="hideShop">×</text>
			</view>
			<scroll-view class="shop-list" scroll-y="true">
				<view 
					class="shop-item" 
					v-for="(item, index) in guildShopItems" 
					:key="index"
					@click="showShopItemDetail(item)"
				>
					<view class="item-info">
						<text class="item-name">{{ item.name }}</text>
						<text class="item-desc">{{ item.description }}</text>
					</view>
					<view class="item-price">
						<text class="price-value">{{ item.price }}</text>
						<text class="price-unit">贡献</text>
					</view>
					<button 
						class="buy-item-btn" 
						@click.stop="buyShopItem(item)"
						:disabled="!canBuyItem(item)"
					>
						购买
					</button>
				</view>
			</scroll-view>
		</view>

		<!-- 门派列表弹窗 -->
		<view class="modal-overlay" v-if="showGuildListModal" @click="closeGuildList">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">选择门派</text>
					<text class="modal-close" @click="closeGuildList">×</text>
				</view>
				<view class="modal-body">
					<view
						class="guild-option"
						v-for="(guild, index) in availableGuilds"
						:key="index"
						@click="selectGuild(guild)"
						:class="{ 'guild-option-disabled': !guild.can_join }"
					>
						<text class="guild-option-name">{{ guild.name }}</text>
						<text class="guild-option-desc">{{ guild.description }}</text>
						<text class="guild-option-requirement" v-if="guild.reasons && guild.reasons.length > 0">
							要求: {{ guild.reasons.join('，') }}
						</text>
						<text class="guild-option-available" v-else>可以加入</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 任务详情弹窗 -->
		<view class="modal-overlay" v-if="showTaskDetail" @click="closeTaskDetail">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">任务详情</text>
					<text class="modal-close" @click="closeTaskDetail">×</text>
				</view>
				<view class="modal-body" v-if="selectedTask">
					<text class="detail-name">{{ selectedTask.name }}</text>
					<text class="detail-desc">{{ selectedTask.description }}</text>
					<text class="detail-requirement">要求: {{ selectedTask.requirement }}</text>
					<text class="detail-reward">奖励: {{ selectedTask.reward }}</text>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeTaskDetail">关闭</button>
					<button 
						class="modal-btn confirm-btn" 
						@click="acceptTask(selectedTask)"
						:disabled="!canAcceptTask(selectedTask)"
					>
						接受任务
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import gameState from '../../utils/gameState.js'
import { gameUtils } from '../../utils/gameData.js'

export default {
	data() {
		return {
			player: {},
			playerGuild: null,
			sectInfo: null,
			showTasksSection: false,
			showSkillsSection: false,
			showMembersSection: false,
			showShopSection: false,
			showGuildListModal: false,
			showTaskDetail: false,
			selectedTask: null,
			availableGuilds: [],
			loading: false,
			availableTasks: [],
			guildSkills: [],
			guildMembers: [],
			guildShopItems: []
		}
	},
	
	onLoad() {
		this.updateData()
		this.loadSectInfo()
	},

	onShow() {
		this.updateData()
		this.loadSectInfo()
	},
	
	methods: {
		updateData() {
			this.player = { ...gameState.player }
		},

		// 加载门派信息
		async loadSectInfo() {
			if (!gameState.isAuthed) {
				return
			}

			try {
				this.loading = true

				// 获取玩家门派信息
				const sectInfoResponse = await gameUtils.sendMessage({
					type: 'sect_action',
					data: { action: 'get_sect_info' }
				})

				if (sectInfoResponse.type === 'sect_info_success') {
					if (sectInfoResponse.data.has_sect) {
						this.sectInfo = sectInfoResponse.data
						this.playerGuild = {
							id: sectInfoResponse.data.sect_id,
							name: sectInfoResponse.data.sect_name,
							level: sectInfoResponse.data.rank,
							contribution: sectInfoResponse.data.contribution,
							position: sectInfoResponse.data.rank_name
						}
					} else {
						this.sectInfo = null
						this.playerGuild = null
					}
				}

				// 如果没有门派，获取可用门派列表
				if (!this.playerGuild) {
					const availableResponse = await gameUtils.sendMessage({
						type: 'sect_action',
						data: { action: 'get_available_sects' }
					})

					if (availableResponse.type === 'available_sects_success') {
						this.availableGuilds = availableResponse.data.sects.map(sect => ({
							id: sect.sect_id,
							name: sect.sect_name,
							description: sect.description,
							requirement: sect.requirements.level || 1,
							can_join: sect.can_join,
							reasons: sect.reasons
						}))
					}
				}

			} catch (error) {
				console.error('加载门派信息失败:', error)
				uni.showToast({
					title: '加载门派信息失败',
					icon: 'none'
				})
			} finally {
				this.loading = false
			}
		},

		// 领取每日奖励
		async claimDailyReward() {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}

			try {
				const response = await gameUtils.sendMessage({
					type: 'sect_action',
					data: { action: 'claim_daily_reward' }
				})

				if (response.type === 'daily_reward_success') {
					uni.showToast({
						title: response.data.message,
						icon: 'success'
					})
					// 重新加载门派信息
					await this.loadSectInfo()
					// 更新玩家数据
					this.updateData()
				} else {
					uni.showToast({
						title: response.data?.message || '领取失败',
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('领取每日奖励失败:', error)
				uni.showToast({
					title: '领取失败',
					icon: 'none'
				})
			}
		},

		// 加载门派武功
		async loadSectSkills() {
			if (!gameState.isAuthed || !this.playerGuild) {
				return
			}

			try {
				const response = await gameUtils.sendMessage({
					type: 'sect_action',
					data: { action: 'get_sect_skills' }
				})

				if (response.type === 'sect_skills_success') {
					this.guildSkills = response.data.skills.map(skill => ({
						id: skill.skill_name,
						name: skill.skill_name,
						type: skill.skill_type,
						quality: skill.quality,
						weapon: skill.weapon,
						description: `攻击+${skill.attack} 防御+${skill.defense} 内力+${skill.internal_power}`,
						rank_requirement: skill.rank_requirement,
						contribution_requirement: skill.contribution_requirement,
						can_learn: skill.can_learn,
						reason: skill.reason.join('，'),
						learned: false // TODO: 检查是否已学会
					}))
				} else {
					console.error('加载门派武功失败:', response.data?.message)
				}
			} catch (error) {
				console.error('加载门派武功失败:', error)
			}
		},

		getPositionName(position) {
			const positions = {
				'master': '掌门',
				'elder': '长老',
				'disciple': '弟子',
				'outer': '外门弟子'
			}
			return positions[position] || '弟子'
		},
		
		showGuildList() {
			this.showGuildListModal = true
		},
		
		closeGuildList() {
			this.showGuildListModal = false
		},
		
		async selectGuild(guild) {
			if (!guild.can_join) {
				const reasons = guild.reasons.join('，')
				uni.showToast({
					title: `无法加入：${reasons}`,
					icon: 'none'
				})
				return
			}

			uni.showModal({
				title: '确认加入',
				content: `确定要加入 ${guild.name} 吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							const response = await gameUtils.sendMessage({
								type: 'sect_action',
								data: {
									action: 'join_sect',
									sect_id: guild.id
								}
							})

							if (response.type === 'join_sect_success') {
								this.closeGuildList()
								uni.showToast({
									title: response.data.message,
									icon: 'success'
								})
								// 重新加载门派信息
								await this.loadSectInfo()
							} else {
								uni.showToast({
									title: response.data?.message || '加入门派失败',
									icon: 'none'
								})
							}
						} catch (error) {
							console.error('加入门派失败:', error)
							uni.showToast({
								title: '加入门派失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},
		
		showTasks() {
			this.showTasksSection = true
			this.hideOtherSections()
		},
		
		hideTasks() {
			this.showTasksSection = false
		},
		
		async showSkills() {
			this.showSkillsSection = true
			this.hideOtherSections()
			await this.loadSectSkills()
		},
		
		hideSkills() {
			this.showSkillsSection = false
		},
		
		showMembers() {
			this.showMembersSection = true
			this.hideOtherSections()
		},
		
		hideMembers() {
			this.showMembersSection = false
		},
		
		showShop() {
			this.showShopSection = true
			this.hideOtherSections()
		},
		
		hideShop() {
			this.showShopSection = false
		},
		
		hideOtherSections() {
			this.showTasksSection = false
			this.showSkillsSection = false
			this.showMembersSection = false
			this.showShopSection = false
		},
		
		getDifficultyClass(difficulty) {
			const classes = {
				'easy': 'difficulty-easy',
				'medium': 'difficulty-medium',
				'hard': 'difficulty-hard'
			}
			return classes[difficulty] || 'difficulty-easy'
		},
		
		getDifficultyName(difficulty) {
			const names = {
				'easy': '简单',
				'medium': '中等',
				'hard': '困难'
			}
			return names[difficulty] || '简单'
		},
		
		getSkillTypeName(type) {
			const types = {
				'external': '外功',
				'internal': '内功',
				'light': '轻功',
				'heart': '心法',
				'special': '特技'
			}
			return types[type] || '武功'
		},
		
		canAcceptTask(task) {
			// 检查其他要求（如果有的话）
			return true
		},
		
		canLearnSkill(skill) {
			return skill.can_learn
		},
		
		canBuyItem(item) {
			return this.playerGuild.contribution >= item.price
		},
		
		showTaskDetail(task) {
			this.selectedTask = task
			this.showTaskDetail = true
		},
		
		closeTaskDetail() {
			this.showTaskDetail = false
			this.selectedTask = null
		},
		
		acceptTask(task) {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}
			if (!this.canAcceptTask(task)) {
				uni.showToast({
					title: '不满足任务要求',
					icon: 'none'
				})
				return
			}
			
			uni.showModal({
				title: '接受任务',
				content: `确定要接受任务 "${task.name}" 吗？`,
				success: (res) => {
					if (res.confirm) {
						// 这里应该将任务添加到玩家的任务列表中
						uni.showToast({
							title: '任务已接受',
							icon: 'success'
						})
						this.closeTaskDetail()
					}
				}
			})
		},
		
		showSkillDetail(skill) {
			// 显示武功详情
		},
		
		async learnSkill(skill) {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}
			if (!skill.can_learn) {
				uni.showToast({
					title: skill.reason || '无法学习',
					icon: 'none'
				})
				return
			}

			if (skill.learned) {
				// 修炼武功
				uni.showToast({
					title: '开始修炼',
					icon: 'success'
				})
			} else {
				// 学习武功
				uni.showModal({
					title: '学习武功',
					content: `确定要学习 ${skill.name} 吗？\n消耗贡献: ${skill.contribution_requirement}`,
					success: async (res) => {
						if (res.confirm) {
							try {
								const response = await gameUtils.sendMessage({
									type: 'sect_action',
									data: {
										action: 'learn_sect_skill',
										skill_name: skill.name
									}
								})

								if (response.type === 'learn_skill_success') {
									uni.showToast({
										title: response.data.message,
										icon: 'success'
									})
									// 重新加载门派信息和武功列表
									await this.loadSectInfo()
									await this.loadSectSkills()
								} else {
									uni.showToast({
										title: response.data?.message || '学习失败',
										icon: 'none'
									})
								}
							} catch (error) {
								console.error('学习武功失败:', error)
								uni.showToast({
									title: '学习失败',
									icon: 'none'
								})
							}
						}
					}
				})
			}
		},
		
		showShopItemDetail(item) {
			// 显示商店物品详情
		},
		
		buyShopItem(item) {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}
			if (!this.canBuyItem(item)) {
				uni.showToast({
					title: '贡献不足',
					icon: 'none'
				})
				return
			}
			uni.showModal({
				title: '购买物品',
				content: `确定要购买 ${item.name} 吗？\n消耗贡献: ${item.price}`,
				success: (res) => {
					if (res.confirm) {
						this.playerGuild.contribution -= item.price
						// 适配字段
						const type = item.type || '';
						const sellable = (typeof item.sellable !== 'undefined' ? item.sellable : true) ? true : false;
						const unique_id = item.unique_id || `${item.id}_${Date.now()}_${Math.floor(Math.random()*10000)}`;
						gameState.addItem({ ...item, type, sellable, unique_id })
						gameState.save()
						uni.showToast({
							title: '购买成功！',
							icon: 'success'
						})
					}
				}
			})
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	padding-bottom: 140rpx; /* 为tabBar留出空间 */
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}

.guild-info {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.guild-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.guild-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.guild-level {
	font-size: 28rpx;
	color: #667eea;
	background: #f0f4ff;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.guild-stats {
	display: flex;
	gap: 30rpx;
}

.stat-item {
	display: flex;
	align-items: center;
}

.stat-label {
	font-size: 26rpx;
	color: #666;
	margin-right: 10rpx;
}

.stat-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.guild-actions {
	margin-top: 20rpx;
	text-align: center;
}

.daily-reward-btn {
	background: linear-gradient(135deg, #f39c12, #e67e22);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 15rpx 30rpx;
	font-size: 26rpx;
}

.daily-reward-btn[disabled] {
	background: #ccc;
	color: #666;
}

.no-guild {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 60rpx 30rpx;
	text-align: center;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.no-guild-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.no-guild-desc {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 40rpx;
}

.join-guild-btn {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.guild-functions {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.function-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.function-item {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
	text-align: center;
	transition: all 0.3s;
}

.function-item:active {
	background: #e9ecef;
	transform: scale(0.98);
}

.function-icon {
	font-size: 48rpx;
	display: block;
	margin-bottom: 15rpx;
}

.function-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.tasks-section,
.skills-section,
.members-section,
.shop-section {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.98);
	z-index: 1000;
	padding: 20rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.section-close {
	font-size: 40rpx;
	color: #999;
	line-height: 1;
}

.tasks-list,
.skills-list,
.members-list,
.shop-list {
	height: calc(100vh - 120rpx);
}

.task-item,
.skill-item,
.member-item,
.shop-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: white;
	border-radius: 15rpx;
	margin-bottom: 15rpx;
}

.task-info,
.skill-info,
.member-info,
.item-info {
	flex: 1;
}

.task-name,
.skill-name,
.member-name,
.item-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}

.task-desc,
.skill-desc,
.item-desc {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}

.task-reward {
	font-size: 24rpx;
	color: #f39c12;
	display: block;
}

.skill-type {
	font-size: 24rpx;
	color: #667eea;
	display: block;
	margin-bottom: 8rpx;
}

.member-position,
.member-level {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.task-status,
.skill-status,
.member-contribution {
	text-align: center;
}

.task-difficulty {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	display: block;
	margin-bottom: 10rpx;
}

.difficulty-easy {
	background: #d4edda;
	color: #155724;
}

.difficulty-medium {
	background: #fff3cd;
	color: #856404;
}

.difficulty-hard {
	background: #f8d7da;
	color: #721c24;
}

.skill-level {
	font-size: 24rpx;
	color: #667eea;
	display: block;
	margin-bottom: 10rpx;
}

.contribution-label {
	font-size: 24rpx;
	color: #666;
	margin-right: 10rpx;
}

.contribution-value {
	font-size: 28rpx;
	font-weight: bold;
	color: #f39c12;
}

.accept-task-btn,
.learn-skill-btn,
.buy-item-btn {
	background: #27ae60;
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 12rpx 24rpx;
	font-size: 26rpx;
}

.accept-task-btn[disabled],
.learn-skill-btn[disabled],
.buy-item-btn[disabled] {
	opacity: 0.5;
	background: #ccc;
}

.empty-tasks {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}

.modal-content {
	background: white;
	border-radius: 20rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 40rpx;
	color: #999;
	line-height: 1;
}

.modal-body {
	padding: 30rpx;
	max-height: 400rpx;
	overflow-y: auto;
}

.guild-option {
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.guild-option:last-child {
	border-bottom: none;
}

.guild-option-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.guild-option-desc {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}

.guild-option-requirement {
	font-size: 24rpx;
	color: #e74c3c;
	display: block;
}

.guild-option-available {
	font-size: 24rpx;
	color: #27ae60;
	display: block;
}

.guild-option-disabled {
	opacity: 0.6;
	background: #f8f9fa;
}

.detail-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.detail-desc,
.detail-requirement,
.detail-reward {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.modal-footer {
	display: flex;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
	gap: 20rpx;
}

.modal-btn {
	flex: 1;
	padding: 20rpx;
	border: none;
	border-radius: 15rpx;
	font-size: 28rpx;
}

.cancel-btn {
	background: #f0f0f0;
	color: #666;
}

.confirm-btn {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}

.confirm-btn[disabled] {
	opacity: 0.5;
	background: #ccc;
}
</style> 