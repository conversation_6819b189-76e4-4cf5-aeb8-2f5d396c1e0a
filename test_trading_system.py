#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易系统测试脚本
测试玩家上架物品、购买物品以及持久化功能
"""

import asyncio
import json
import sqlite3
import os
import sys
import websockets
from datetime import datetime

# 添加backend目录到路径
sys.path.append('backend')

from market_system import MarketSystem
from server import GameServer
from shop_system import ShopSystem

class TradingSystemTester:
    def __init__(self):
        self.server = None
        self.market_system = None
        self.shop_system = None
        
    async def setup(self):
        """初始化测试环境"""
        print("=== 初始化测试环境 ===")

        # 创建服务器实例
        self.server = GameServer()
        # 等待数据库初始化完成
        await self.server.init_db_task

        # 市场系统和商店系统已经在服务器初始化时创建
        self.market_system = self.server.market_system
        self.shop_system = self.server.shop_system if hasattr(self.server, 'shop_system') else ShopSystem(self.server)

        print("✓ 服务器和系统初始化完成")
        
    def check_database_structure(self):
        """检查数据库结构"""
        print("\n=== 检查数据库结构 ===")
        
        try:
            conn = sqlite3.connect('backend/game.db')
            cursor = conn.cursor()
            
            # 检查transactions表结构
            cursor.execute("PRAGMA table_info(transactions)")
            columns = cursor.fetchall()
            print("✓ transactions表结构:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
                
            # 检查是否有交易记录
            cursor.execute("SELECT COUNT(*) FROM transactions")
            count = cursor.fetchone()[0]
            print(f"✓ 当前交易记录数量: {count}")
            
            conn.close()
            
        except Exception as e:
            print(f"✗ 数据库检查失败: {e}")
            
    def check_market_persistence(self):
        """检查市场订单持久化"""
        print("\n=== 检查市场订单持久化 ===")
        
        try:
            # 检查market_orders.json文件
            orders_file = 'backend/market_orders.json'
            if os.path.exists(orders_file):
                with open(orders_file, 'r', encoding='utf-8') as f:
                    orders = json.load(f)
                print(f"✓ 市场订单文件存在，包含 {len(orders)} 个订单")
                
                # 显示前3个订单
                for i, order in enumerate(orders[:3]):
                    print(f"  订单{i+1}: {order['item']['name']} - {order['price']}银两 (卖家: {order['seller']})")
                    
            else:
                print("✗ 市场订单文件不存在")
                
        except Exception as e:
            print(f"✗ 市场订单检查失败: {e}")
            
    async def test_market_listing(self):
        """测试上架物品功能"""
        print("\n=== 测试上架物品功能 ===")
        
        try:
            # 创建模拟websocket对象
            class MockWebSocket:
                def __init__(self, user_id):
                    self.user_id = user_id
                    
            # 创建测试玩家数据
            test_user_id = 999
            test_player = {
                'name': '测试玩家',
                'money': 10000,
                'inventory': [
                    {
                        'id': 'chitie_jian',
                        'name': '赤铁剑',
                        'quality': 'common',
                        'quantity': 1,
                        'unique_id': 'test_sword_001'
                    }
                ]
            }
            
            # 保存测试玩家数据
            self.server.player_data[str(test_user_id)] = test_player
            
            # 模拟上架请求
            mock_ws = MockWebSocket(test_user_id)
            list_data = {
                'action': 'list',
                'item_id': 'chitie_jian',
                'price': 500,
                'quantity': 1
            }
            
            result = await self.market_system.handle_market_action(list_data, mock_ws)
            
            if result['type'] == 'success':
                print("✓ 上架物品成功")
                print(f"  消息: {result['data']['message']}")
                
                # 检查玩家银两是否扣除手续费
                updated_player = self.server.player_data[str(test_user_id)]
                print(f"  玩家银两: {updated_player['money']} (应该扣除了手续费)")
                
                # 检查物品是否从背包移除
                print(f"  背包物品数量: {len(updated_player['inventory'])}")
                
            else:
                print(f"✗ 上架物品失败: {result['data']['message']}")
                
        except Exception as e:
            print(f"✗ 上架测试失败: {e}")
            
    async def test_market_buying(self):
        """测试购买物品功能"""
        print("\n=== 测试购买物品功能 ===")
        
        try:
            # 创建买家
            buyer_id = 998
            buyer_player = {
                'name': '买家玩家',
                'money': 20000,
                'inventory': []
            }
            
            self.server.player_data[str(buyer_id)] = buyer_player
            
            # 获取市场订单列表
            class MockWebSocket:
                def __init__(self, user_id):
                    self.user_id = user_id
                    
            mock_ws = MockWebSocket(buyer_id)
            
            # 获取市场列表
            list_result = await self.market_system.handle_market_action(
                {'action': 'get_market_list'}, mock_ws
            )
            
            if list_result['type'] == 'market_list' and list_result['data']['list']:
                orders = list_result['data']['list']
                print(f"✓ 获取到 {len(orders)} 个市场订单")
                
                # 尝试购买第一个订单
                first_order = orders[0]
                print(f"  尝试购买: {first_order['item']['name']} - {first_order['price']}银两")
                
                buy_result = await self.market_system.handle_market_action(
                    {'action': 'buy', 'order_id': first_order['id']}, mock_ws
                )
                
                if buy_result['type'] == 'success':
                    print("✓ 购买成功")
                    print(f"  消息: {buy_result['data']['message']}")
                    
                    # 检查买家银两和背包
                    updated_buyer = self.server.player_data[str(buyer_id)]
                    print(f"  买家银两: {updated_buyer['money']}")
                    print(f"  买家背包物品数量: {len(updated_buyer['inventory'])}")
                    
                else:
                    print(f"✗ 购买失败: {buy_result['data']['message']}")
                    
            else:
                print("✗ 无法获取市场订单列表")
                
        except Exception as e:
            print(f"✗ 购买测试失败: {e}")
            
    async def test_shop_buying(self):
        """测试商店购买功能"""
        print("\n=== 测试商店购买功能 ===")
        
        try:
            # 创建测试玩家
            test_user_id = 997
            test_player = {
                'name': '商店测试玩家',
                'money': 5000,
                'inventory': []
            }
            
            self.server.player_data[str(test_user_id)] = test_player
            
            # 测试从NPC商店购买物品
            result = await self.shop_system.buy_item(
                test_player, str(test_user_id), 'changan_coalboss', 'chitie_jian', 1
            )
            
            if result['type'] == 'success':
                print("✓ 商店购买成功")
                print(f"  消息: {result['data']['message']}")
                
                # 检查玩家数据
                print(f"  玩家银两: {test_player['money']}")
                print(f"  背包物品数量: {len(test_player['inventory'])}")
                
            else:
                print(f"✗ 商店购买失败: {result['data']['message']}")
                
        except Exception as e:
            print(f"✗ 商店购买测试失败: {e}")
            
    def check_data_consistency(self):
        """检查数据一致性"""
        print("\n=== 检查数据一致性 ===")
        
        try:
            # 重新加载市场订单，检查持久化
            self.market_system.load_orders()
            print(f"✓ 重新加载后市场订单数量: {len(self.market_system.market_orders)}")
            
            # 检查订单ID计数器
            print(f"✓ 下一个订单ID: {self.market_system.order_id_counter}")
            
        except Exception as e:
            print(f"✗ 数据一致性检查失败: {e}")
            
    async def run_all_tests(self):
        """运行所有测试"""
        print("开始交易系统测试...")
        
        await self.setup()
        self.check_database_structure()
        self.check_market_persistence()
        await self.test_market_listing()
        await self.test_market_buying()
        await self.test_shop_buying()
        self.check_data_consistency()
        
        print("\n=== 测试完成 ===")

async def main():
    tester = TradingSystemTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
