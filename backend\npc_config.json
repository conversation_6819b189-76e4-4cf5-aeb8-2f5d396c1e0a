{"npcs": {"药店老板": {"id": "pharmacy_owner", "name": "药店老板", "avatar": "static/npc/pharmacy_owner.png", "description": "经营药店多年的老板，精通各种药材的功效。", "type": "merchant", "functions": [{"key": "shop", "label": "购买药品"}, {"key": "sell", "label": "出售物品"}, {"key": "talk", "label": "对话"}], "shop_items": [{"item_id": "heal_potion", "stock": 999}], "dialogues": ["欢迎光临我的药店！这里有各种疗伤圣药。", "江湖险恶，多备些药品总是好的。", "我这里的药品都是上等货色，童叟无欺！"], "quests": ["pharmacy_daily"]}, "铁匠": {"id": "blacksmith", "name": "铁匠", "avatar": "static/npc/blacksmith.png", "description": "技艺精湛的铁匠，能够打造和修理各种武器装备。", "type": "craftsman", "functions": [{"key": "shop", "label": "购买装备"}, {"key": "repair", "label": "修理装备"}, {"key": "upgrade", "label": "强化装备"}, {"key": "sell", "label": "出售物品"}, {"key": "talk", "label": "对话"}], "shop_items": [{"item_id": "chitie_jian", "stock": 999}, {"item_id": "qingtong_jian", "stock": 999}, {"item_id": "chitie_dao", "stock": 999}, {"item_id": "qingtong_dao", "stock": 999}, {"item_id": "tiejia_dunpai", "stock": 999}, {"item_id": "qingshan_hufu", "stock": 999}, {"item_id": "liumuguan", "stock": 999}, {"item_id": "qiaomuguan", "stock": 999}, {"item_id": "kucao_jiezhi", "stock": 999}, {"item_id": "bai<PERSON>_jiezhi", "stock": 999}], "dialogues": ["需要武器装备吗？我这里应有尽有！", "好的装备需要好的材料，价格自然不菲。", "我的手艺在这一带可是出了名的！"], "services": {"repair": {"cost_rate": 0.1, "description": "修理装备耐久度"}, "upgrade": {"cost_rate": 0.5, "success_rate": 0.7, "description": "强化装备属性"}}, "quests": ["blacksmith_daily"]}, "杂货商人": {"id": "general_merchant", "name": "杂货商人", "avatar": "static/npc/merchant.png", "description": "走南闯北的商人，售卖各种日用品和稀奇物件。", "type": "merchant", "functions": [{"key": "shop", "label": "购买物品"}, {"key": "sell", "label": "出售物品"}, {"key": "info", "label": "打听消息"}, {"key": "talk", "label": "对话"}], "shop_items": [{"item_id": "pojin<PERSON><PERSON>", "stock": 50}, {"item_id": "sickle_1", "stock": 999}, {"item_id": "axe_1", "stock": 999}, {"item_id": "pickaxe_1", "stock": 999}, {"item_id": "knife_1", "stock": 999}], "dialogues": ["客官要买点什么？我这里什么都有！", "我走遍大江南北，见过不少奇人异事。", "做生意讲究的是诚信，我从不坑人！"], "info_services": [{"type": "map_info", "price": 50, "description": "获取地图信息"}, {"type": "monster_info", "price": 30, "description": "获取怪物信息"}, {"type": "treasure_info", "price": 100, "description": "获取宝藏线索"}], "quests": ["merchant_daily"]}, "教书先生": {"id": "jiaoshuxiansheng", "name": "教书先生", "avatar": "static/npc/escort.png", "description": "远近闻名的老秀才，可以教你如何读书识字，只不过学费听说很贵。", "type": "service", "functions": [{"key": "learn", "label": "学习"}, {"key": "talk", "label": "对话"}], "dialogues": ["想和我一样成为读书人吗?", "江湖路远，学好数理化助你走遍全江湖！", "我明码标价，童叟无欺！"], "services": {"escort": {"destinations": [{"map": "heifengzhai", "price": 100, "time": 300}, {"map": "yaowanggu", "price": 200, "time": 600}], "description": "护送到指定地点"}, "transport": {"destinations": [{"map": "heifengzhai", "price": 50}, {"map": "yaowanggu", "price": 100}], "description": "快速传送到指定地点"}, "storage": {"slots": 20, "price_per_day": 10, "description": "寄存物品服务"}}, "quests": ["escort_daily"]}, "药王": {"id": "medicine_king", "name": "药王", "avatar": "static/npc/medicine_king.png", "description": "药王谷的主人，医术高超，能够炼制各种珍贵丹药。", "type": "master", "functions": [{"key": "shop", "label": "购买丹药"}, {"key": "alchemy", "label": "炼药服务"}, {"key": "heal", "label": "治疗服务"}, {"key": "talk", "label": "对话"}], "shop_items": [{"item_id": "great_heal_potion", "stock": 50}, {"item_id": "exp_pill", "stock": 20}, {"item_id": "breakthrough_pill", "stock": 10}, {"item_id": "immortal_pill", "stock": 3}], "dialogues": ["年轻人，你的内息有些紊乱，需要调理一下。", "炼药之道，在于心静如水，急躁不得。", "这些丹药都是我亲手炼制，效果绝佳。"], "services": {"alchemy": {"recipes": [{"result": "heal_potion", "materials": ["herb", "water"], "price": 20}, {"result": "strength_pill", "materials": ["ginseng", "tiger_bone"], "price": 100}], "description": "代为炼制丹药"}, "heal": {"full_heal_price": 100, "description": "完全恢复生命值"}}, "quests": ["medicine_master"]}, "采药人": {"id": "herb_gatherer", "name": "采药人", "avatar": "static/npc/gatherer.png", "description": "常年在药王谷采集草药的老人，对各种药材了如指掌。", "type": "gatherer", "functions": [{"key": "shop", "label": "购买药材"}, {"key": "sell", "label": "出售药材"}, {"key": "identify", "label": "鉴定药材"}, {"key": "talk", "label": "对话"}], "shop_items": [{"item_id": "common_herb", "stock": 999}, {"item_id": "rare_herb", "stock": 100}, {"item_id": "ginseng", "stock": 20}, {"item_id": "lingzhi", "stock": 10}], "dialogues": ["这山里的药材可多着呢，就看你有没有眼力。", "采药要趁早，露水未干时药效最佳。", "我在这山里采药几十年了，什么草药都见过。"], "services": {"identify": {"price": 20, "description": "鉴定未知药材"}}, "quests": ["gatherer_daily"]}}}