#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理工具测试脚本
验证管理工具的基本功能
"""

import asyncio
import os
from admin_tool import GameAdminTool

async def test_admin_tool():
    """测试管理工具功能"""
    print("🧪 开始测试管理工具...")
    
    # 初始化管理工具
    admin = GameAdminTool()
    
    # 测试1：获取玩家列表
    print("\n📋 测试1：获取玩家列表")
    try:
        players = await admin.get_player_list()
        print(f"✅ 成功获取 {len(players)} 个玩家")
        if players:
            first_player = players[0]
            print(f"   第一个玩家: {first_player['character_name']} (ID: {first_player['user_id']})")
    except Exception as e:
        print(f"❌ 获取玩家列表失败: {e}")
    
    # 测试2：搜索物品
    print("\n🔍 测试2：搜索物品")
    try:
        results = admin.search_items("镰刀")
        print(f"✅ 搜索'镰刀'找到 {len(results)} 个结果")
        for item in results[:3]:
            print(f"   {item['id']}: {item['name']}")
    except Exception as e:
        print(f"❌ 搜索物品失败: {e}")
    
    # 测试3：获取玩家详细信息（如果有玩家的话）
    if players:
        print(f"\n👤 测试3：获取玩家详细信息")
        try:
            user_id = players[0]['user_id']
            player_data = await admin.get_player_data(user_id)
            if player_data:
                print(f"✅ 成功获取玩家 {player_data['character_name']} 的详细信息")
                print(f"   等级: {player_data['level']}")
                print(f"   金钱: {player_data['money']}")
                print(f"   血量: {player_data['hp']}/{player_data['max_hp']}")
                
                inventory = player_data['player_data'].get('inventory', {})
                print(f"   背包物品数量: {len(inventory)}")
            else:
                print(f"❌ 获取玩家数据失败")
        except Exception as e:
            print(f"❌ 获取玩家详细信息失败: {e}")
    
    # 测试4：验证物品数据加载
    print(f"\n📦 测试4：验证物品数据")
    print(f"✅ 加载了 {len(admin.items_data)} 个物品")
    
    # 显示一些示例物品
    sample_items = ['sickle_1', 'herb_wildgrass', 'wudang_token']
    for item_id in sample_items:
        if item_id in admin.items_data:
            item = admin.items_data[item_id]
            print(f"   {item_id}: {item['name']} ({item['type']})")
        else:
            print(f"   ❌ 物品 {item_id} 未找到")
    
    print("\n🎉 管理工具测试完成！")

async def test_database_connection():
    """测试数据库连接"""
    print("🔗 测试数据库连接...")
    
    db_path = "game.db"
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件 {db_path} 不存在")
        return False
    
    try:
        import aiosqlite
        async with aiosqlite.connect(db_path) as db:
            cursor = await db.execute("SELECT COUNT(*) FROM users")
            count = await cursor.fetchone()
            print(f"✅ 数据库连接成功，共有 {count[0]} 个用户")
            return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_item_files():
    """测试物品文件是否存在"""
    print("📁 测试物品文件...")
    
    item_files = [
        'items_weapon.json', 'items_armor.json', 'items_helmet.json',
        'items_accessory.json', 'items_consumable.json', 'items_book.json',
        'items_gather_tool.json', 'items_gather_item.json', 'items_sect_token.json'
    ]
    
    existing_files = 0
    for file_name in item_files:
        if os.path.exists(file_name):
            print(f"✅ {file_name}")
            existing_files += 1
        else:
            print(f"❌ {file_name} 不存在")
    
    print(f"📊 物品文件检查完成: {existing_files}/{len(item_files)} 个文件存在")
    return existing_files > 0

async def main():
    """主测试函数"""
    print("🎮 游戏管理工具测试程序")
    print("=" * 50)
    
    # 测试物品文件
    if not test_item_files():
        print("⚠️  警告：缺少物品文件，某些功能可能无法正常工作")
    
    print()
    
    # 测试数据库连接
    if not await test_database_connection():
        print("⚠️  警告：数据库连接失败，某些功能可能无法正常工作")
    
    print()
    
    # 测试管理工具
    await test_admin_tool()
    
    print("\n" + "=" * 50)
    print("测试完成！如果所有测试都通过，管理工具应该可以正常使用。")
    print("运行 'python admin_tool.py' 启动管理工具。")

if __name__ == '__main__':
    asyncio.run(main())
