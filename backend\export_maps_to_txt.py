# -*- coding: utf-8 -*-
"""
自动导出所有地图静态内容到maps.txt
"""
import os
import importlib.util

spec = importlib.util.spec_from_file_location('map_data', os.path.join(os.path.dirname(__file__), 'map_data.py'))
map_data = importlib.util.module_from_spec(spec)
spec.loader.exec_module(map_data)

lines = []
for m in map_data.MAP_DATA:
    lines.append(f"{m['id']}={m['name']}")
    lines.append(f"{m['id']}_等级={m.get('level','')}")
    lines.append(f"{m['id']}_描述={m.get('description','')}")
    lines.append(f"{m['id']}_奖励={m.get('experience_reward','')}")
    lines.append(f"{m['id']}_进入条件={m.get('enter_requirements','')}")
    lines.append(f"{m['id']}_NPC={' '.join(m.get('functional_npcs', [])) if m.get('functional_npcs') else ''}")
    lines.append(f"{m['id']}_资源={' '.join([r['name'] for r in m.get('resources', [])]) if m.get('resources') else ''}")
    lines.append('')

with open(os.path.join(os.path.dirname(__file__), 'maps.txt'), 'w', encoding='utf-8') as f:
    f.write('\n'.join(lines))

print('地图配置已导出到 maps.txt') 