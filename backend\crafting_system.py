import uuid
from item_system import item_system
import logging
logger = logging.getLogger(__name__)

class CraftingSystem:
    def __init__(self, game_server):
        self.game_server = game_server

    async def handle_crafting_action(self, data, websocket):
        logger.info(f"[CraftingSystem] 收到打造请求: data={data}, user_id={getattr(websocket, 'user_id', None)}")
        action = data.get('action')
        if action == 'craft_item':
            logger.info(f"[CraftingSystem] 进入 craft_item 分支")
            result = await self.handle_craft_item(data, websocket)
            logger.info(f"[CraftingSystem] craft_item 返回: {result}")
            return result
        elif action == 'get_craftable_items':
            logger.info(f"[CraftingSystem] 进入 get_craftable_items 分支")
            result = await self.handle_get_craftable_items(websocket)
            logger.info(f"[CraftingSystem] get_craftable_items 返回: {result}")
            return result
        else:
            logger.warning(f"[CraftingSystem] 未知打造操作: {action}")
            return {'type': 'crafting_failed', 'data': {'message': '未知的打造操作'}}

    async def handle_craft_item(self, data, websocket):
        user_id = getattr(websocket, 'user_id', None)
        if not user_id:
            return {'type': 'craft_failed', 'data': {'message': '未认证'}}
        player = self.game_server.player_data.get(str(user_id))
        if not player:
            return {'type': 'craft_failed', 'data': {'message': '玩家数据不存在'}}
        target_item_id = data.get('item_id')
        if not target_item_id:
            return {'type': 'craft_failed', 'data': {'message': '请指定要合成的物品'}}
        target_item = item_system.get_item(target_item_id)
        if not target_item:
            return {'type': 'craft_failed', 'data': {'message': '物品不存在'}}
        if not target_item.craftable:
            return {'type': 'craft_failed', 'data': {'message': '该物品无法合成'}}
        craft_recipe = target_item.recipe
        if not craft_recipe:
            return {'type': 'craft_failed', 'data': {'message': '该物品没有合成配方'}}
        try:
            materials_needed = {}
            # 如果recipe是字典格式，直接使用
            if isinstance(craft_recipe, dict):
                materials_needed = craft_recipe.copy()
            else:
                # 如果是字符串格式，解析它
                recipe_str = str(craft_recipe)
                recipe_parts = recipe_str.split(',')
                for part in recipe_parts:
                    if ':' in part:
                        material_name, quantity = part.split(':', 1)
                        materials_needed[material_name.strip()] = int(quantity.strip())
        except Exception as e:
            logger.error(f"解析合成配方失败: {craft_recipe}, 错误: {e}")
            return {'type': 'craft_failed', 'data': {'message': '合成配方格式错误'}}
        inventory = player.get('inventory', [])
        # 支持name和id双重索引
        materials_available = {}
        for item in inventory:
            item_name = item.get('name', '')
            item_id = item.get('id', '')
            if item_name:
                materials_available[item_name] = materials_available.get(item_name, 0) + item.get('quantity', 1)
            if item_id:
                materials_available[item_id] = materials_available.get(item_id, 0) + item.get('quantity', 1)
        missing_materials = []
        for material_name, needed_quantity in materials_needed.items():
            available_quantity = materials_available.get(material_name, 0)
            # 如果name查不到，再用id查
            if available_quantity == 0:
                for item_obj in item_system.items.values():
                    if item_obj.name == material_name:
                        available_quantity = materials_available.get(item_obj.id, 0)
                        break
            if available_quantity < needed_quantity:
                missing_materials.append(f"{material_name}(需要{needed_quantity}，现有{available_quantity})")
        if missing_materials:
            return {'type': 'craft_failed', 'data': {'message': f'材料不足: {', '.join(missing_materials)}'}}
        if player.get('energy', 0) < 10:
            return {'type': 'craft_failed', 'data': {'message': '体力不足，需要10点体力'}}
        try:
            for material_name, needed_quantity in materials_needed.items():
                remaining_quantity = needed_quantity
                for i in range(len(inventory) - 1, -1, -1):
                    item = inventory[i]
                    if item.get('name', '') == material_name:
                        item_quantity = item.get('quantity', 1)
                        if item_quantity <= remaining_quantity:
                            remaining_quantity -= item_quantity
                            inventory.pop(i)
                        else:
                            item['quantity'] = item_quantity - remaining_quantity
                            remaining_quantity = 0
                        if remaining_quantity <= 0:
                            break
            player['energy'] -= 10
            crafted_item = {
                'id': target_item_id,
                'name': target_item.name,
                'type': target_item.type.value,
                'quality': target_item.quality.value,
                'description': target_item.description,
                'icon': target_item.icon,
                'price': target_item.price,
                'sell_price': target_item.sell_price,
                'quantity': 1,
                'unique_id': str(uuid.uuid4())
            }
            success = await self.game_server.add_item_to_inventory(user_id, player, crafted_item)
            if success:
                await self.game_server.save_player_data(str(user_id), player)
                return {'type': 'craft_success', 'data': {'message': f'成功合成 {target_item.name}', 'crafted_item': crafted_item, 'inventory': player.get('inventory', []), 'energy': player.get('energy', 0)}}
            else:
                return {'type': 'craft_failed', 'data': {'message': '背包已满，无法获得合成物品'}}
        except Exception as e:
            logger.error(f"合成物品失败: {e}")
            return {'type': 'craft_failed', 'data': {'message': f'合成失败: {str(e)}'}}

    async def handle_get_craftable_items(self, websocket):
        user_id = getattr(websocket, 'user_id', None)
        logger.info(f"[CraftingSystem] handle_get_craftable_items user_id={user_id}")
        if not user_id:
            logger.warning(f"[CraftingSystem] 未认证，无法获取可合成物品")
            return {'type': 'get_craftable_failed', 'data': {'message': '未认证'}}
        player = self.game_server.player_data.get(str(user_id))
        if not player:
            logger.warning(f"[CraftingSystem] 玩家数据不存在: {user_id}")
            return {'type': 'get_craftable_failed', 'data': {'message': '玩家数据不存在'}}
        try:
            craftable_items = []
            inventory = player.get('inventory', [])
            # 支持name和id双重索引
            materials_available = {}
            for item in inventory:
                item_name = item.get('name', '')
                item_id = item.get('id', '')
                if item_name:
                    materials_available[item_name] = materials_available.get(item_name, 0) + item.get('quantity', 1)
                if item_id:
                    materials_available[item_id] = materials_available.get(item_id, 0) + item.get('quantity', 1)
            for item_id, item_obj in item_system.items.items():
                if getattr(item_obj, "craftable", False) and hasattr(item_obj, "recipe") and item_obj.recipe:
                    try:
                        materials_needed = {}
                        # 如果recipe是字典格式，直接使用
                        if isinstance(item_obj.recipe, dict):
                            materials_needed = item_obj.recipe.copy()
                        else:
                            # 如果是字符串格式，解析它
                            recipe_str = str(item_obj.recipe)
                            recipe_parts = recipe_str.split(',')
                            for part in recipe_parts:
                                if ':' in part:
                                    material_name, quantity = part.split(':', 1)
                                    materials_needed[material_name.strip()] = int(quantity.strip())
                        can_craft = True
                        missing_materials = []
                        for material_name, needed_quantity in materials_needed.items():
                            available_quantity = materials_available.get(material_name, 0)
                            if available_quantity == 0:
                                for obj in item_system.items.values():
                                    if obj.name == material_name:
                                        available_quantity = materials_available.get(obj.id, 0)
                                        break
                            if available_quantity < needed_quantity:
                                can_craft = False
                                missing_materials.append(f"{material_name}(需要{needed_quantity}，现有{available_quantity})")
                        craftable_items.append({'id': item_id, 'name': item_obj.name, 'type': item_obj.type.value, 'quality': item_obj.quality.value, 'description': item_obj.description, 'icon': item_obj.icon, 'craft_recipe': str(item_obj.recipe), 'can_craft': can_craft, 'missing_materials': missing_materials if not can_craft else []})
                    except Exception as e:
                        logger.error(f"解析物品 {item_id} 的合成配方失败: {e}")
                        continue
            logger.info(f"[CraftingSystem] 返回可合成物品列表: {len(craftable_items)} 个")
            return {'type': 'get_craftable_success', 'data': {'craftable_items': craftable_items, 'energy': player.get('energy', 0)}}
        except Exception as e:
            logger.error(f"获取可合成物品失败: {e}")
            return {'type': 'get_craftable_failed', 'data': {'message': f'获取失败: {str(e)}'}} 