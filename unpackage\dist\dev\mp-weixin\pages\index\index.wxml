<view class="container data-v-1cf27b2a"><view class="map-bar data-v-1cf27b2a"><view class="map-bar-left data-v-1cf27b2a"><text class="user-name data-v-1cf27b2a">{{a}}</text><text class="user-label data-v-1cf27b2a">｜</text><text class="map-label data-v-1cf27b2a">当前地图：</text><text class="map-name data-v-1cf27b2a">{{b}}</text></view><view class="map-bar-right data-v-1cf27b2a"><text class="{{['connection-status', 'data-v-1cf27b2a', d]}}">{{c}}</text><button class="map-btn data-v-1cf27b2a" bindtap="{{e}}">切换地图</button></view></view><view class="announcement-bar data-v-1cf27b2a"><view class="announcement-icon data-v-1cf27b2a">📢</view><view class="announcement-content data-v-1cf27b2a"><view class="announcement-scroll data-v-1cf27b2a"><text class="announcement-text data-v-1cf27b2a">{{f}}</text><text class="announcement-text data-v-1cf27b2a">{{g}}</text></view></view></view><view class="character-card data-v-1cf27b2a"><view class="progress-bar data-v-1cf27b2a"><text class="progress-label data-v-1cf27b2a">气血</text><view class="progress-bg data-v-1cf27b2a"><view class="progress-fill hp-fill data-v-1cf27b2a" style="{{'width:' + h}}"></view></view><text class="progress-text data-v-1cf27b2a">{{i}}/{{j}}</text></view><view class="progress-bar data-v-1cf27b2a"><text class="progress-label data-v-1cf27b2a">内力</text><view class="progress-bg data-v-1cf27b2a"><view class="progress-fill mp-fill data-v-1cf27b2a" style="{{'width:' + k}}"></view></view><text class="progress-text data-v-1cf27b2a">{{l}}/{{m}}</text></view><view class="progress-bar data-v-1cf27b2a"><text class="progress-label data-v-1cf27b2a">体力</text><view class="progress-bg data-v-1cf27b2a"><view class="progress-fill stamina-fill data-v-1cf27b2a" style="{{'width:' + n}}"></view></view><text class="progress-text data-v-1cf27b2a">{{o}}/{{p}}</text></view><view class="progress-bar data-v-1cf27b2a"><text class="progress-label data-v-1cf27b2a">精力</text><view class="progress-bg data-v-1cf27b2a"><view class="progress-fill energy-fill data-v-1cf27b2a" style="{{'width:' + q}}"></view></view><text class="progress-text data-v-1cf27b2a">{{r}}/{{s}}</text></view></view><view wx:if="{{t}}" class="talent-bonus-card data-v-1cf27b2a"><view class="talent-bonus-title data-v-1cf27b2a">天赋增益</view><view class="talent-bonus-grid data-v-1cf27b2a"><view wx:if="{{v}}" class="talent-bonus-item data-v-1cf27b2a"><text class="talent-bonus-label data-v-1cf27b2a">力量</text><text class="talent-bonus-value data-v-1cf27b2a">+{{w}}%攻击</text></view><view wx:if="{{x}}" class="talent-bonus-item data-v-1cf27b2a"><text class="talent-bonus-label data-v-1cf27b2a">悟性</text><text class="talent-bonus-value data-v-1cf27b2a">+{{y}}%经验</text></view><view wx:if="{{z}}" class="talent-bonus-item data-v-1cf27b2a"><text class="talent-bonus-label data-v-1cf27b2a">身法</text><text class="talent-bonus-value data-v-1cf27b2a">+{{A}}%防御</text></view><view wx:if="{{B}}" class="talent-bonus-item data-v-1cf27b2a"><text class="talent-bonus-label data-v-1cf27b2a">根骨</text><text class="talent-bonus-value data-v-1cf27b2a">+{{C}}%气血</text></view></view></view><view class="jianghu-section-flex-opt data-v-1cf27b2a"><button class="main-btn data-v-1cf27b2a" bindtap="{{D}}" disabled="{{E}}"><text class="main-btn-text data-v-1cf27b2a">闯</text></button></view><view wx:if="{{F}}" class="npc-menu-modal data-v-1cf27b2a" bindtap="{{M}}"><view class="npc-menu-content data-v-1cf27b2a" catchtap="{{L}}"><view class="npc-menu-header data-v-1cf27b2a"><image class="npc-menu-avatar data-v-1cf27b2a" src="{{G}}" mode="aspectFill"/><text class="npc-menu-name data-v-1cf27b2a">{{H}}</text></view><text class="npc-menu-desc data-v-1cf27b2a">{{I}}</text><view class="npc-menu-list data-v-1cf27b2a"><button wx:for="{{J}}" wx:for-item="func" wx:key="b" class="npc-menu-btn data-v-1cf27b2a" bindtap="{{func.c}}">{{func.a}}</button></view><button class="npc-menu-close data-v-1cf27b2a" bindtap="{{K}}">关闭</button></view></view><view wx:if="{{N}}" class="buy-modal-mask data-v-1cf27b2a" bindtap="{{ac}}"><view class="buy-modal data-v-1cf27b2a" catchtap="{{ab}}"><view class="buy-modal-header data-v-1cf27b2a"><text class="buy-modal-title data-v-1cf27b2a">购买物品</text><button class="buy-modal-close data-v-1cf27b2a" bindtap="{{O}}">×</button></view><view class="buy-modal-content data-v-1cf27b2a"><view class="item-info data-v-1cf27b2a"><view class="item-name data-v-1cf27b2a">{{P}}</view><view class="item-details data-v-1cf27b2a"><text class="item-price data-v-1cf27b2a">单价: {{Q}} 银两</text><text class="item-stock data-v-1cf27b2a">库存: {{R}}</text></view></view><view class="quantity-section data-v-1cf27b2a"><text class="quantity-label data-v-1cf27b2a">购买数量:</text><view class="quantity-input-group data-v-1cf27b2a"><button class="quantity-btn data-v-1cf27b2a" bindtap="{{S}}">-</button><input class="quantity-input data-v-1cf27b2a" type="number" bindinput="{{T}}" value="{{U}}"/><button class="quantity-btn data-v-1cf27b2a" bindtap="{{V}}">+</button></view></view><view class="total-section data-v-1cf27b2a"><text class="total-label data-v-1cf27b2a">总价: {{W}} 银两</text><text class="money-info data-v-1cf27b2a">当前银两: {{X}}</text></view></view><view class="buy-modal-actions data-v-1cf27b2a"><button class="buy-btn-cancel data-v-1cf27b2a" bindtap="{{Y}}">取消</button><button class="buy-btn-confirm data-v-1cf27b2a" bindtap="{{Z}}" disabled="{{aa}}">确认购买</button></view></view></view><view wx:if="{{ad}}" class="status-display data-v-1cf27b2a"><text class="{{['status-text', 'data-v-1cf27b2a', af]}}">{{ae}}</text></view><view class="event-log data-v-1cf27b2a"><view class="log-header data-v-1cf27b2a"><text class="log-title data-v-1cf27b2a">江湖日志</text><text class="log-clear data-v-1cf27b2a" bindtap="{{ag}}">清空</text></view><scroll-view class="log-content data-v-1cf27b2a" scroll-y="true"><view wx:for="{{ah}}" wx:for-item="event" wx:key="d" class="log-item data-v-1cf27b2a"><view class="log-header-line data-v-1cf27b2a"><text class="log-time data-v-1cf27b2a">{{event.a}}</text><text class="log-event data-v-1cf27b2a">{{event.b}}</text></view><view class="log-content-line data-v-1cf27b2a"><view class="log-desc data-v-1cf27b2a"><rich-text class="data-v-1cf27b2a" nodes="{{event.c}}"/></view></view></view><view wx:if="{{ai}}" class="log-empty data-v-1cf27b2a"><text class="data-v-1cf27b2a">暂无江湖记录</text></view></scroll-view></view><gathering-popup wx:if="{{aj}}" class="data-v-1cf27b2a" bindclose="{{ak}}" binddoGather="{{al}}" u-i="1cf27b2a-0" bind:__l="__l" u-p="{{am}}"/><view wx:if="{{an}}" class="map-popup-mask data-v-1cf27b2a" bindtap="{{ar}}"><view class="map-popup data-v-1cf27b2a" catchtap="{{aq}}"><view class="map-popup-title data-v-1cf27b2a">切换地图</view><view class="map-list data-v-1cf27b2a"><view wx:for="{{ao}}" wx:for-item="map" wx:key="k" class="{{['map-item', 'data-v-1cf27b2a', map.l && 'active', map.m && 'locked']}}" bindtap="{{map.n}}"><view class="map-item-title data-v-1cf27b2a">{{map.a}}</view><view class="map-item-desc data-v-1cf27b2a">{{map.b}}</view><view wx:if="{{map.c}}" class="map-item-npc data-v-1cf27b2a">NPC：{{map.d}}</view><view wx:if="{{map.e}}" class="map-item-monster data-v-1cf27b2a">怪物：{{map.f}}</view><view wx:if="{{map.g}}" class="map-item-gather data-v-1cf27b2a">采集物品：{{map.h}}</view><view wx:if="{{map.i}}" class="map-item-req data-v-1cf27b2a">进入条件：{{map.j}}</view></view></view><button class="close-btn data-v-1cf27b2a" bindtap="{{ap}}">关闭</button></view></view><battle-popup wx:if="{{as}}" class="data-v-1cf27b2a" bindattack="{{at}}" bindescape="{{av}}" bindclose="{{aw}}" u-i="1cf27b2a-1" bind:__l="__l" u-p="{{ax}}"/><view wx:if="{{ay}}" class="healing-meditation-popup-centered data-v-1cf27b2a"><view wx:if="{{az}}" class="healing-meditation-loading data-v-1cf27b2a"><view class="spinner data-v-1cf27b2a"></view></view><view class="healing-meditation-msg-list data-v-1cf27b2a" style="max-height:180px;overflow-y:auto"><view wx:for="{{aA}}" wx:for-item="msg" wx:key="b" class="healing-meditation-msg-centered data-v-1cf27b2a">{{msg.a}}</view></view></view><view wx:if="{{aB}}" class="chat-popup-mask data-v-1cf27b2a" bindtap="{{aT}}"><view class="chat-popup data-v-1cf27b2a" catchtap="{{aS}}"><view class="chat-popup-header data-v-1cf27b2a"><text class="chat-popup-title data-v-1cf27b2a">全服聊天</text><button class="chat-popup-close data-v-1cf27b2a" bindtap="{{aC}}">×</button></view><view class="chat-messages data-v-1cf27b2a" ref="chatMessages"><view wx:for="{{aD}}" wx:for-item="msg" wx:key="i" class="{{['data-v-1cf27b2a', 'chat-message', msg.j, msg.k]}}"><view class="chat-message-header data-v-1cf27b2a"><view class="chat-message-info data-v-1cf27b2a"><text class="chat-time data-v-1cf27b2a">{{msg.a}}</text><text class="{{['chat-sender', 'data-v-1cf27b2a', msg.c, msg.d]}}">{{msg.b}}</text></view><view wx:if="{{msg.e}}" class="{{['chat-type-badge', 'data-v-1cf27b2a', msg.g]}}">{{msg.f}}</view></view><view class="chat-content data-v-1cf27b2a">{{msg.h}}</view></view><view wx:if="{{aE}}" class="no-messages data-v-1cf27b2a"><text class="data-v-1cf27b2a">暂无聊天消息</text></view></view><view class="chat-input-area data-v-1cf27b2a"><view class="chat-type-selector data-v-1cf27b2a"><picker value="{{aG}}" range="{{aH}}" bindchange="{{aI}}" class="chat-type-picker data-v-1cf27b2a"><view class="chat-type-display data-v-1cf27b2a"><text class="data-v-1cf27b2a">{{aF}}</text><text class="picker-arrow data-v-1cf27b2a">▼</text></view></picker></view><view class="chat-input-row data-v-1cf27b2a"><input wx:if="{{aJ}}" class="chat-target-input data-v-1cf27b2a" placeholder="目标玩家名" maxlength="20" value="{{aK}}" bindinput="{{aL}}"/><input class="chat-input data-v-1cf27b2a" placeholder="{{aM}}" bindconfirm="{{aN}}" maxlength="100" value="{{aO}}" bindinput="{{aP}}"/><button class="chat-send-btn data-v-1cf27b2a" bindtap="{{aQ}}" disabled="{{aR}}">发送</button></view></view></view></view><view wx:if="{{aU}}" class="player-menu-modal-mask data-v-1cf27b2a" bindtap="{{be}}"><view class="player-menu-modal data-v-1cf27b2a" catchtap="{{bd}}"><view class="player-menu-header data-v-1cf27b2a"><text class="player-menu-title data-v-1cf27b2a">{{aV}}</text><button class="player-menu-close data-v-1cf27b2a" bindtap="{{aW}}">×</button></view><view class="player-menu-info data-v-1cf27b2a"><text class="player-menu-level data-v-1cf27b2a">等级: {{aX}}</text><text class="player-menu-status data-v-1cf27b2a">状态: {{aY}}</text></view><view class="player-menu-actions data-v-1cf27b2a"><button class="player-action-btn attack data-v-1cf27b2a" bindtap="{{aZ}}">偷袭</button><button class="player-action-btn give data-v-1cf27b2a" bindtap="{{ba}}">给与</button><button class="player-action-btn steal data-v-1cf27b2a" bindtap="{{bb}}">偷窃</button><button class="player-action-btn view data-v-1cf27b2a" bindtap="{{bc}}">查看</button></view></view></view><view wx:if="{{bf}}" class="npc-sidebar-btn data-v-1cf27b2a" style="{{'top:' + bg}}" bindtouchstart="{{bh}}" bindtouchmove="{{bi}}" bindtouchend="{{bj}}" bindlongpress="{{bk}}" bindtap="{{bl}}"><text class="data-v-1cf27b2a">NPC</text></view><view class="player-sidebar-btn data-v-1cf27b2a" style="{{'top:' + bm}}" bindtouchstart="{{bn}}" bindtouchmove="{{bo}}" bindtouchend="{{bp}}" bindlongpress="{{bq}}" bindtap="{{br}}"><text class="data-v-1cf27b2a">侠客</text></view><view wx:if="{{bs}}" class="npc-sidebar-mask data-v-1cf27b2a" bindtap="{{bt}}"></view><view class="{{['npc-sidebar', 'data-v-1cf27b2a', bx && 'npc-sidebar-show']}}"><view class="npc-sidebar-header data-v-1cf27b2a"><text class="data-v-1cf27b2a">功能NPC</text><button class="npc-sidebar-close data-v-1cf27b2a" bindtap="{{bv}}">×</button></view><view class="npc-sidebar-list data-v-1cf27b2a"><view wx:for="{{bw}}" wx:for-item="npc" wx:key="e" class="npc-item data-v-1cf27b2a" bindtap="{{npc.f}}"><image class="npc-avatar data-v-1cf27b2a" src="{{npc.a}}" mode="aspectFill"/><text class="npc-name data-v-1cf27b2a">{{npc.b}}</text><text wx:if="{{npc.c}}" class="npc-desc data-v-1cf27b2a">{{npc.d}}</text></view></view></view><view wx:if="{{by}}" class="player-sidebar-mask data-v-1cf27b2a" bindtap="{{bz}}"></view><view class="{{['player-sidebar', 'data-v-1cf27b2a', bE && 'player-sidebar-show']}}"><view class="player-sidebar-header data-v-1cf27b2a"><text class="data-v-1cf27b2a">在线侠客 ({{bA}})</text><button class="player-sidebar-close data-v-1cf27b2a" bindtap="{{bB}}">×</button></view><view class="player-sidebar-list data-v-1cf27b2a"><view wx:for="{{bC}}" wx:for-item="player" wx:key="i" class="{{['player-item', 'data-v-1cf27b2a', player.h && 'player-item-self']}}" bindtap="{{player.j}}"><image class="player-avatar data-v-1cf27b2a" src="{{player.a}}" mode="aspectFill"/><view class="player-info data-v-1cf27b2a"><text class="{{['player-name', 'data-v-1cf27b2a', player.d && 'player-name-self']}}">{{player.b}} <text wx:if="{{player.c}}" class="self-tag data-v-1cf27b2a">（我）</text></text><text class="player-level data-v-1cf27b2a">等级: {{player.e}}</text><text class="{{['player-status', 'data-v-1cf27b2a', player.g]}}">{{player.f}}</text></view></view><view wx:if="{{bD}}" class="no-players data-v-1cf27b2a"><text class="data-v-1cf27b2a">当前地图暂无其他侠客</text></view></view></view><view class="left-menu-btn data-v-1cf27b2a" style="{{'top:' + bF}}" bindtouchstart="{{bG}}" bindtouchmove="{{bH}}" bindtouchend="{{bI}}" bindlongpress="{{bJ}}" bindtap="{{bK}}"><text class="data-v-1cf27b2a">快捷</text></view><view class="left-chat-btn data-v-1cf27b2a" style="{{'top:' + bL}}" bindtouchstart="{{bM}}" bindtouchmove="{{bN}}" bindtouchend="{{bO}}" bindlongpress="{{bP}}" bindtap="{{bQ}}"><text class="data-v-1cf27b2a">💬</text></view><view wx:if="{{bR}}" class="left-menu-bar data-v-1cf27b2a" style="{{'top:' + bW}}" catchtap="{{bX}}"><button class="left-menu-btn-item data-v-1cf27b2a" bindtap="{{bS}}"><text class="left-menu-btn-icon data-v-1cf27b2a">🩺</text><text class="left-menu-btn-text data-v-1cf27b2a">疗伤</text></button><button class="left-menu-btn-item data-v-1cf27b2a" bindtap="{{bT}}"><text class="left-menu-btn-icon data-v-1cf27b2a">🧘</text><text class="left-menu-btn-text data-v-1cf27b2a">打坐</text></button><button class="left-menu-btn-item data-v-1cf27b2a" bindtap="{{bU}}"><text class="left-menu-btn-icon data-v-1cf27b2a">🏆</text><text class="left-menu-btn-text data-v-1cf27b2a">排行榜</text></button><button class="left-menu-btn-item data-v-1cf27b2a" bindtap="{{bV}}"><text class="left-menu-btn-icon data-v-1cf27b2a">🎁</text><text class="left-menu-btn-text data-v-1cf27b2a">兑换码</text></button></view><view wx:if="{{bY}}" class="left-menu-mask data-v-1cf27b2a" bindtap="{{bZ}}"></view></view><view wx:if="{{ca}}" class="modal-overlay data-v-1cf27b2a" bindtap="{{cy}}"><view class="ranking-modal data-v-1cf27b2a" catchtap="{{cx}}"><view class="modal-header data-v-1cf27b2a"><text class="modal-title data-v-1cf27b2a">🏆 排行榜</text><text class="modal-close data-v-1cf27b2a" bindtap="{{cb}}">×</text></view><view class="ranking-tabs data-v-1cf27b2a"><view class="{{['ranking-tab', 'data-v-1cf27b2a', cc && 'active']}}" bindtap="{{cd}}"><text class="data-v-1cf27b2a">💰 富豪榜</text></view><view class="{{['ranking-tab', 'data-v-1cf27b2a', ce && 'active']}}" bindtap="{{cf}}"><text class="data-v-1cf27b2a">⭐ 经验榜</text></view><view class="{{['ranking-tab', 'data-v-1cf27b2a', cg && 'active']}}" bindtap="{{ch}}"><text class="data-v-1cf27b2a">🗡️ 肝帝榜</text></view></view><view class="ranking-content data-v-1cf27b2a"><view wx:if="{{ci}}" class="loading-text data-v-1cf27b2a">加载中...</view><view wx:elif="{{cj}}" class="empty-text data-v-1cf27b2a">暂无排行数据</view><view wx:else class="ranking-list data-v-1cf27b2a"><view wx:for="{{ck}}" wx:for-item="item" wx:key="j" class="{{['ranking-item', 'data-v-1cf27b2a', item.k && 'current-player', item.l && 'top-rank', item.m && 'first-place', item.n && 'second-place', item.o && 'third-place']}}"><view class="{{['rank-number', 'data-v-1cf27b2a', item.e && 'top-three']}}"><text wx:if="{{item.a}}" class="rank-icon gold data-v-1cf27b2a">🥇</text><text wx:elif="{{item.b}}" class="rank-icon silver data-v-1cf27b2a">🥈</text><text wx:elif="{{item.c}}" class="rank-icon bronze data-v-1cf27b2a">🥉</text><text wx:else class="rank-text data-v-1cf27b2a">{{item.d}}</text></view><view class="player-info data-v-1cf27b2a"><text class="player-name data-v-1cf27b2a">{{item.f}}</text></view><view class="ranking-value data-v-1cf27b2a"><text wx:if="{{cl}}" class="data-v-1cf27b2a">{{item.g}}</text><text wx:elif="{{cm}}" class="data-v-1cf27b2a">{{item.h}}</text><text wx:elif="{{cn}}" class="data-v-1cf27b2a">{{item.i}}次</text></view></view></view></view><view wx:if="{{co}}" class="my-ranking data-v-1cf27b2a"><text class="my-ranking-title data-v-1cf27b2a">我的排名：第{{cp}}名</text><text class="my-ranking-value data-v-1cf27b2a"><text wx:if="{{cq}}" class="data-v-1cf27b2a">{{cr}}</text><text wx:elif="{{cs}}" class="data-v-1cf27b2a">{{ct}}</text><text wx:elif="{{cv}}" class="data-v-1cf27b2a">{{cw}}次</text></text></view></view></view><view wx:if="{{cz}}" class="modal-overlay data-v-1cf27b2a" bindtap="{{cJ}}"><view class="redeem-modal data-v-1cf27b2a" catchtap="{{cI}}"><view class="modal-header data-v-1cf27b2a"><text class="modal-title data-v-1cf27b2a">🎁 兑换码</text><text class="modal-close data-v-1cf27b2a" bindtap="{{cA}}">×</text></view><view class="redeem-input-section data-v-1cf27b2a"><input class="redeem-input data-v-1cf27b2a" placeholder="请输入兑换码" disabled="{{cB}}" value="{{cC}}" bindinput="{{cD}}"/><button class="{{['redeem-btn', 'data-v-1cf27b2a', cF && 'disabled']}}" bindtap="{{cG}}" disabled="{{cH}}"><text wx:if="{{cE}}" class="data-v-1cf27b2a">兑换中...</text><text wx:else class="data-v-1cf27b2a">兑换</text></button></view><view class="redeem-tips data-v-1cf27b2a"><text class="tips-item data-v-1cf27b2a">• 每个兑换码只能使用一次</text><text class="tips-item data-v-1cf27b2a">• 兑换码有有效期限制</text><text class="tips-item data-v-1cf27b2a">• 请确保输入正确的兑换码</text></view></view></view>