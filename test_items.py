import sys
sys.path.append('backend')

from item_system import ItemSystem

def test_items():
    print("=== 测试物品系统 ===")
    try:
        item_system = ItemSystem()
        
        # 测试铁匠商店物品
        shop_items = ['chitie_jian', 'qingtong_jian', 'chitie_dao', 'qingtong_dao', 'tiejia_dunpai', 'qingshan_hufu']
        
        print("铁匠商店物品检查:")
        for item_id in shop_items:
            if item_id in item_system.items:
                item_obj = item_system.items[item_id]
                print(f"✓ {item_id}: 存在, 名称: {item_obj.name}, 价格: {item_obj.price}")
            else:
                print(f"✗ {item_id}: 不存在")
        
        print(f"\n总共加载了 {len(item_system.items)} 个物品")
        
        # 测试访问price属性
        if 'chitie_jian' in item_system.items:
            item = item_system.items['chitie_jian']
            print(f"\n测试访问price属性:")
            print(f"item.price = {item.price}")
            print(f"hasattr(item, 'price') = {hasattr(item, 'price')}")
            print(f"type(item) = {type(item)}")
            
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_items()
