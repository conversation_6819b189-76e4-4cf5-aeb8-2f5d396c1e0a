
.gathering-popup-mask.data-v-a12f92ec {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gathering-popup.data-v-a12f92ec {
  background: #fff;
  border-radius: 18rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  min-width: 480rpx;
  max-width: 90vw;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.gather-title.data-v-a12f92ec {
  font-size: 32rpx;
  font-weight: bold;
  color: #4b3fa7;
  margin-bottom: 18rpx;
}
.gather-desc.data-v-a12f92ec {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 18rpx;
  text-align: center;
}
.gather-info.data-v-a12f92ec {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.tool-type.data-v-a12f92ec {
  color: #27ae60;
  font-weight: bold;
  margin-left: 8rpx;
}
.gather-times.data-v-a12f92ec {
  color: #e67e22;
  font-weight: bold;
  margin-left: 8rpx;
}
.gather-result.data-v-a12f92ec {
  color: #e74c3c;
  font-size: 24rpx;
  margin: 12rpx 0 0 0;
  text-align: center;
}
.gather-btns.data-v-a12f92ec {
  display: flex;
  gap: 24rpx;
  margin-top: 24rpx;
}
.gather-btn.data-v-a12f92ec {
  background: linear-gradient(90deg, #27ae60, #4bfa7b);
  color: #fff;
  font-size: 26rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.close-btn.data-v-a12f92ec {
  background: #eee;
  color: #888;
  font-size: 24rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.gather-btn[disabled].data-v-a12f92ec {
  opacity: 0.5;
}
