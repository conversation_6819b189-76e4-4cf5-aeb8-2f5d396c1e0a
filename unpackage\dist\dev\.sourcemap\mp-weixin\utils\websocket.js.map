{"version": 3, "file": "websocket.js", "sources": ["utils/websocket.js"], "sourcesContent": ["/**\r\n * WebSocket通信管理器\r\n * 负责与Python后端进行实时通信\r\n */\r\n\r\nclass WebSocketManager {\r\n\tconstructor() {\r\n\t\tthis.ws = null\r\n\t\tthis.isConnected = false\r\n\t\tthis.isAuthed = false // 认证状态\r\n\t\tthis.reconnectAttempts = 0\r\n\t\tthis.maxReconnectAttempts = -1  // -1表示无限重连\r\n\t\tthis.reconnectInterval = 3000 // 3秒\r\n\t\tthis.maxReconnectInterval = 30000 // 最大重连间隔30秒\r\n\t\tthis.messageQueue = [] // 消息队列\r\n\t\tthis.eventHandlers = {} // 事件处理器\r\n\t\tthis.connecting = false // 防止重复连接\r\n\t\tthis.heartbeatInterval = null // 心跳定时器\r\n\t\tthis.heartbeatTimeout = null // 心跳超时定时器\r\n\t\tthis.manualDisconnect = false // 标记是否为手动断开\r\n\t\t// 根据环境设置不同的服务器地址\r\n\t\t// 开发环境使用本地地址，生产环境使用实际服务器地址\r\n\t\t// 微信小程序需要使用 wss:// 或 ws:// 格式\r\n\t\tthis.serverUrl = 'ws://localhost:8080' // 后端WebSocket地址（使用本地地址）\r\n\t\tthis.disableAutoAuth = false // 默认启用自动认证功能\r\n\t\tthis.isLoginPage = false // 标记是否在登录页面\r\n\t\tthis.debug = true // 调试模式\r\n\r\n\t\t// 认证/登录成功回调\r\n\t\tthis.on('login_success', (data) => {\r\n\t\t\tthis.isAuthed = true;\r\n\t\t\tthis.log('WebSocket: 登录成功，认证状态已更新');\r\n\t\t});\r\n\t\tthis.on('auth_success', (data) => {\r\n\t\t\tthis.isAuthed = true;\r\n\t\t\tthis.log('WebSocket: 认证成功，认证状态已更新');\r\n\t\t});\r\n\t\tthis.on('login_failed', (data) => { \r\n\t\t\tthis.isAuthed = false; \r\n\t\t\tthis.log('WebSocket: 登录失败，认证状态已重置');\r\n\t\t});\r\n\t\tthis.on('auth_failed', (data) => { \r\n\t\t\tthis.isAuthed = false; \r\n\t\t\tthis.log('WebSocket: 认证失败，认证状态已重置');\r\n\t\t});\r\n\t}\r\n\r\n\t// 日志方法\r\n\tlog(message, ...args) {\r\n\t\tif (this.debug) {\r\n\t\t\tconsole.log(`[WebSocket] ${message}`, ...args);\r\n\t\t}\r\n\t}\r\n\t\r\n\t// 错误日志方法\r\n\terror(message, ...args) {\r\n\t\tconsole.error(`[WebSocket错误] ${message}`, ...args);\r\n\t}\r\n\r\n\t/**\r\n\t * 连接到WebSocket服务器（防止重复连接，彻底断开旧连接）\r\n\t */\r\n\tconnect() {\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t// 如果已经连接，直接返回\r\n\t\t\tif (this.isConnected) {\r\n\t\t\t\tthis.log('WebSocket已连接，无需重新连接');\r\n\t\t\t\tresolve();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 防止重复连接\r\n\t\t\tif (this.connecting) {\r\n\t\t\t\tthis.log('WebSocket正在连接中，请等待...');\r\n\t\t\t\treject(new Error('正在连接中'));\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\ttry {\r\n\t\t\t\tthis.connecting = true;\r\n\t\t\t\tthis.log('开始连接WebSocket服务器:', this.serverUrl);\r\n\r\n\t\t\t\tthis.ws = uni.connectSocket({\r\n\t\t\t\t\turl: this.serverUrl,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tthis.log('WebSocket连接请求已发送');\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\tthis.error('WebSocket连接请求失败:', error);\r\n\t\t\t\t\t\tthis.connecting = false;\r\n\t\t\t\t\t\treject(error);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\tuni.onSocketOpen((res) => {\r\n\t\t\t\t\tthis.isConnected = true;\r\n\t\t\t\t\tthis.connecting = false;\r\n\t\t\t\t\tthis.reconnectAttempts = 0;\r\n\t\t\t\t\tthis.log('WebSocket连接已建立');\r\n\t\t\t\t\tthis.processMessageQueue();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 启动心跳机制\r\n\t\t\t\t\tthis.startHeartbeat();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 触发连接成功事件\r\n\t\t\t\t\tif (this.eventHandlers['connected']) {\r\n\t\t\t\t\t\tthis.eventHandlers['connected'].forEach(fn => fn());\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果不在登录页面且没有禁用自动认证，则进行自动认证\r\n\t\t\t\t\tif (!this.isLoginPage && !this.disableAutoAuth) {\r\n\t\t\t\t\t\t// 延迟执行自动认证，避免阻塞UI\r\n\t\t\t\t\t\tthis.log('准备执行自动认证...');\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.autoAuthenticate();\r\n\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.log('当前在登录页面或自动认证已禁用，跳过自动认证');\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tresolve(res);\r\n\t\t\t\t});\r\n\r\n\t\t\t\tuni.onSocketClose((res) => {\r\n\t\t\t\t\tthis.isConnected = false;\r\n\t\t\t\t\tthis.connecting = false;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 停止心跳机制\r\n\t\t\t\t\tthis.stopHeartbeat();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 触发连接断开事件\r\n\t\t\t\t\tif (this.eventHandlers['disconnected']) {\r\n\t\t\t\t\t\tthis.eventHandlers['disconnected'].forEach(fn => fn());\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.log('WebSocket连接断开，code:', res.code, 'reason:', res.reason);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果不是手动断开且不是正常关闭，则尝试重连\r\n\t\t\t\t\tif (!this.manualDisconnect && res.code !== 1000) {\r\n\t\t\t\t\t\tthis.log('检测到异常断开，开始重连...');\r\n\t\t\t\t\t\tthis.handleDisconnect();\r\n\t\t\t\t\t} else if (this.manualDisconnect) {\r\n\t\t\t\t\t\tthis.log('手动断开连接，不进行重连');\r\n\t\t\t\t\t\tthis.manualDisconnect = false; // 重置标记\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\tuni.onSocketError((res) => {\r\n\t\t\t\t\tthis.isConnected = false;\r\n\t\t\t\t\tthis.connecting = false;\r\n\t\t\t\t\tthis.error('WebSocket连接错误:', res);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 触发连接错误事件\r\n\t\t\t\t\tif (this.eventHandlers['error']) {\r\n\t\t\t\t\t\tthis.eventHandlers['error'].forEach(fn => fn(res));\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.handleDisconnect();\r\n\t\t\t\t});\r\n\r\n\t\t\t\tuni.onSocketMessage((res) => {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\tconst message = JSON.parse(res.data);\r\n\t\t\t\t\t\tthis.log('收到消息:', message);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 处理心跳响应\r\n\t\t\t\t\t\tif (message.type === 'pong') {\r\n\t\t\t\t\t\t\tthis.handlePong();\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 处理不同类型的消息\r\n\t\t\t\t\t\tthis.triggerEvent(message.type, message.data);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t// 消息解析失败\r\n\t\t\t\t\t\tthis.error('消息解析失败:', error, res.data);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tif (!this.isConnected && this.connecting) {\r\n\t\t\t\t\t\tthis.connecting = false;\r\n\t\t\t\t\t\tthis.error('WebSocket连接超时');\r\n\t\t\t\t\t\treject(new Error('连接超时'));\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 15000);\r\n\r\n\t\t\t} catch (error) {\r\n\t\t\t\tthis.connecting = false;\r\n\t\t\t\tthis.error('WebSocket连接异常:', error);\r\n\t\t\t\treject(error);\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n\t\r\n\t/**\r\n\t * 设置当前是否在登录页面\r\n\t */\r\n\tsetLoginPageStatus(isLoginPage) {\r\n\t\tthis.isLoginPage = isLoginPage;\r\n\t\tthis.log('设置登录页面状态:', isLoginPage ? '当前在登录页面' : '当前不在登录页面');\r\n\t}\r\n\r\n\t/**\r\n\t * 断开WebSocket连接\r\n\t */\r\n\tdisconnect() {\r\n\t\tif (this.ws && this.isConnected) {\r\n\t\t\tthis.log('手动断开WebSocket连接');\r\n\t\t\tuni.closeSocket();\r\n\t\t\tthis.isConnected = false;\r\n\t\t\tthis.connecting = false;\r\n\t\t\tthis.manualDisconnect = true; // 标记为手动断开\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 处理连接断开\r\n\t */\r\n\thandleDisconnect() {\r\n\t\tif (this.reconnectAttempts >= this.maxReconnectAttempts && this.maxReconnectAttempts !== -1) {\r\n\t\t\tthis.log('达到最大重连次数，停止重连');\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst delay = Math.min(\r\n\t\t\tthis.reconnectInterval * Math.pow(1.5, this.reconnectAttempts),\r\n\t\t\tthis.maxReconnectInterval\r\n\t\t);\r\n\r\n\t\tthis.reconnectAttempts++;\r\n\t\tthis.log(`第${this.reconnectAttempts}次重连，延迟${delay}ms`);\r\n\r\n\t\tsetTimeout(() => {\r\n\t\t\tif (!this.isConnected && !this.connecting) {\r\n\t\t\t\tthis.log('执行重连...');\r\n\t\t\t\tthis.connect().catch(error => {\r\n\t\t\t\t\tthis.error('重连失败:', error);\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}, delay);\r\n\t}\r\n\r\n\t/**\r\n\t * 发送消息\r\n\t */\r\n\tsendMessage(type, data = {}) {\r\n\t\tconst message = JSON.stringify({ type, data });\r\n\t\t\r\n\t\t// 如果WebSocket未连接，加入队列\r\n\t\tif (!this.isConnected) {\r\n\t\t\tthis.log('WebSocket未连接，消息加入队列:', { type, data });\r\n\t\t\tthis.messageQueue.push(message);\r\n\t\t\treturn Promise.reject(new Error('WebSocket未连接'));\r\n\t\t}\r\n\t\t\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tthis.log('发送消息:', { type, data });\r\n\t\t\tuni.sendSocketMessage({\r\n\t\t\t\tdata: message,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tthis.log('消息发送成功:', { type, data });\r\n\t\t\t\t\tresolve({ type, data });\r\n\t\t\t\t},\r\n\t\t\t\tfail: (error) => {\r\n\t\t\t\t\tthis.error('消息发送失败:', error, { type, data });\r\n\t\t\t\t\tthis.messageQueue.push(message);\r\n\t\t\t\t\treject(error);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * 处理消息队列\r\n\t */\r\n\tprocessMessageQueue() {\r\n\t\tif (this.messageQueue.length === 0) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\tthis.log(`处理消息队列，共${this.messageQueue.length}条消息`);\r\n\t\t\r\n\t\t// 复制队列并清空原队列\r\n\t\tconst queue = [...this.messageQueue];\r\n\t\tthis.messageQueue = [];\r\n\t\t\r\n\t\t// 逐条发送消息\r\n\t\tqueue.forEach(message => {\r\n\t\t\ttry {\r\n\t\t\t\tuni.sendSocketMessage({\r\n\t\t\t\t\tdata: message,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tthis.log('队列消息发送成功:', message);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\tthis.error('队列消息发送失败:', error, message);\r\n\t\t\t\t\t\tthis.messageQueue.unshift(message);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} catch (error) {\r\n\t\t\t\tthis.error('队列消息发送失败:', error, message);\r\n\t\t\t\tthis.messageQueue.unshift(message);\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * 触发事件\r\n\t */\r\n\ttriggerEvent(eventType, data) {\r\n\t\t// 特殊处理game_event事件，确保它总是被处理\r\n\t\tif (eventType === 'game_event') {\r\n\t\t\t// 导入gameState模块（避免循环引用问题）\r\n\t\t\ttry {\r\n\t\t\t\tconst gameState = require('./gameState').default;\r\n\t\t\t\tif (gameState && typeof gameState.handleGameEvent === 'function') {\r\n\t\t\t\t\tthis.log(`特殊处理game_event事件`, data);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 确保数据格式正确\r\n\t\t\t\t\tconst eventData = {\r\n\t\t\t\t\t\ttype: data.type || 5, // 默认为奇遇事件\r\n\t\t\t\t\t\tcontent: data.content || data.description || '你遇到了一个江湖事件',\r\n\t\t\t\t\t\trewards: data.rewards || {},\r\n\t\t\t\t\t\trealm_breakthrough: data.realm_breakthrough || null\r\n\t\t\t\t\t};\r\n\t\t\t\t\t\r\n\t\t\t\t\tgameState.handleGameEvent(eventData);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tthis.error('处理game_event事件失败:', error);\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\tif (this.eventHandlers[eventType]) {\r\n\t\t\tthis.log(`触发事件: ${eventType}`, data);\r\n\t\t\tthis.eventHandlers[eventType].forEach(handler => handler(data));\r\n\t\t} else {\r\n\t\t\tthis.log(`没有处理程序的事件: ${eventType}`, data);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 注册事件处理程序\r\n\t */\r\n\ton(eventType, handler) {\r\n\t\tif (!this.eventHandlers[eventType]) {\r\n\t\t\tthis.eventHandlers[eventType] = [];\r\n\t\t}\r\n\t\t\r\n\t\tthis.eventHandlers[eventType].push(handler);\r\n\t\tthis.log(`注册事件处理程序: ${eventType}`);\r\n\t}\r\n\r\n\t/**\r\n\t * 移除事件处理程序\r\n\t */\r\n\toff(eventType, handler) {\r\n\t\tif (!this.eventHandlers[eventType]) {\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\tconst idx = this.eventHandlers[eventType].indexOf(handler);\r\n\t\t\r\n\t\tif (idx > -1) {\r\n\t\t\tthis.eventHandlers[eventType].splice(idx, 1);\r\n\t\t\tthis.log(`移除事件处理程序: ${eventType}`);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 自动认证\r\n\t */\r\n\tautoAuthenticate() {\r\n\t\tthis.log('开始自动认证...');\r\n\t\tconst token = uni.getStorageSync('token');\r\n\t\t\r\n\t\tthis.log('认证信息检查:');\r\n\t\tthis.log('- token存在:', !!token);\r\n\t\t\r\n\t\tif (token) {\r\n\t\t\tthis.log('✅ 找到登录信息，发送认证消息');\r\n\t\t\t\r\n\t\t\tthis.sendMessage('auth', {\r\n\t\t\t\ttoken: token\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tthis.log('✅ 认证消息已发送');\r\n\t\t} else {\r\n\t\t\tthis.log('❌ 未找到登录信息，无法自动认证');\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 发送认证消息\r\n\t */\r\n\tauthenticate() {\r\n\t\t// 获取用户信息或token\r\n\t\tconst token = uni.getStorageSync('token') || '';\r\n\t\t\r\n\t\tthis.log('手动发送认证消息');\r\n\t\tthis.sendMessage('auth', {\r\n\t\t\ttoken: token\r\n\t\t});\r\n\t}\r\n\r\n\t/**\r\n\t * 发送游戏动作\r\n\t */\r\n\tsendGameAction(action, data = {}) {\r\n\t\tthis.sendMessage('game_action', {\r\n\t\t\taction: action,\r\n\t\t\t...data\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 请求玩家数据\r\n\t */\r\n\trequestPlayerData() {\r\n\t\tthis.log('WebSocket: 请求玩家数据');\r\n\t\tthis.sendMessage('get_player_data')\r\n\t}\r\n\r\n\t/**\r\n\t * 请求背包数据\r\n\t */\r\n\trequestInventoryData() {\r\n\t\tthis.sendMessage('get_inventory_data')\r\n\t}\r\n\r\n\t/**\r\n\t * 请求武功数据\r\n\t */\r\n\trequestSkillsData() {\r\n\t\tthis.sendMessage('get_skills_data')\r\n\t}\r\n\r\n\t/**\r\n\t * 发送闯江湖请求\r\n\t */\r\n\tsendAdventureRequest() {\r\n\t\tthis.log('=== WebSocket: 发送闯江湖请求 ===');\r\n\t\tthis.log('当前时间:', new Date().toLocaleString());\r\n\t\tthis.log('WebSocket连接状态:', this.isConnected);\r\n\t\tthis.log('服务器地址:', this.serverUrl);\r\n\t\t\r\n\t\tif (!this.isConnected) {\r\n\t\t\tthis.log('❌ WebSocket未连接，无法发送请求');\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\tthis.log('✅ 发送adventure消息');\r\n\t\tthis.sendMessage('adventure')\r\n\t\tthis.log('✅ WebSocket: 闯江湖请求已发送');\r\n\t}\r\n\r\n\t/**\r\n\t * 发送打坐请求\r\n\t */\r\n\tsendMeditationRequest() {\r\n\t\tthis.sendMessage('meditation')\r\n\t}\r\n\r\n\t/**\r\n\t * 发送疗伤请求\r\n\t */\r\n\tsendHealingRequest() {\r\n\t\tthis.sendMessage('healing')\r\n\t}\r\n\r\n\t/**\r\n\t * 发送装备操作\r\n\t */\r\n\tsendEquipmentAction(action, itemId) {\r\n\t\tthis.sendMessage('equipment_action', {\r\n\t\t\taction: action,\r\n\t\t\titemId: itemId\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 发送武功操作\r\n\t */\r\n\tsendSkillAction(action, skillId) {\r\n\t\tthis.sendMessage('skill_action', {\r\n\t\t\taction: action,\r\n\t\t\tskillId: skillId\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 发送商店操作\r\n\t */\r\n\tsendShopAction(action, data) {\r\n\t\tthis.sendMessage('shop_action', {\r\n\t\t\taction: action,\r\n\t\t\t...data\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 发送市场操作\r\n\t */\r\n\tsendMarketAction(action, data) {\r\n\t\tthis.sendMessage('market_action', {\r\n\t\t\taction: action,\r\n\t\t\t...data\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 发送门派操作\r\n\t */\r\n\tsendGuildAction(action, data) {\r\n\t\tthis.sendMessage('guild_action', {\r\n\t\t\taction: action,\r\n\t\t\t...data\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 发送打造操作\r\n\t */\r\n\tsendCraftingAction(action, data) {\r\n\t\tthis.sendMessage('crafting_action', {\r\n\t\t\taction: action,\r\n\t\t\t...data\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * 启动心跳机制\r\n\t */\r\n\tstartHeartbeat() {\r\n\t\t// 清除之前的心跳\r\n\t\tthis.stopHeartbeat();\r\n\t\t\r\n\t\t// 每30秒发送一次心跳\r\n\t\tthis.heartbeatInterval = setInterval(() => {\r\n\t\t\tif (this.isConnected) {\r\n\t\t\t\tthis.sendMessage('ping');\r\n\t\t\t\t\r\n\t\t\t\t// 设置心跳超时\r\n\t\t\t\tthis.heartbeatTimeout = setTimeout(() => {\r\n\t\t\t\t\tthis.log('心跳超时，断开连接');\r\n\t\t\t\t\tthis.disconnect();\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 尝试重连\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.connect();\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}, 10000); // 10秒超时\r\n\t\t\t}\r\n\t\t}, 30000); // 30秒间隔\r\n\t}\r\n\r\n\t/**\r\n\t * 停止心跳机制\r\n\t */\r\n\tstopHeartbeat() {\r\n\t\tif (this.heartbeatInterval) {\r\n\t\t\tclearInterval(this.heartbeatInterval);\r\n\t\t\tthis.heartbeatInterval = null;\r\n\t\t}\r\n\t\t\r\n\t\tif (this.heartbeatTimeout) {\r\n\t\t\tclearTimeout(this.heartbeatTimeout);\r\n\t\t\tthis.heartbeatTimeout = null;\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * 处理心跳响应\r\n\t */\r\n\thandlePong() {\r\n\t\t// 收到心跳响应，清除超时\r\n\t\tif (this.heartbeatTimeout) {\r\n\t\t\tclearTimeout(this.heartbeatTimeout);\r\n\t\t\tthis.heartbeatTimeout = null;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 创建单例\r\nconst wsManager = new WebSocketManager();\r\n\r\nexport default wsManager; "], "names": ["uni"], "mappings": ";;AAKA,MAAM,iBAAiB;AAAA,EACtB,cAAc;AACb,SAAK,KAAK;AACV,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,SAAK,oBAAoB;AACzB,SAAK,uBAAuB;AAC5B,SAAK,oBAAoB;AACzB,SAAK,uBAAuB;AAC5B,SAAK,eAAe,CAAE;AACtB,SAAK,gBAAgB,CAAE;AACvB,SAAK,aAAa;AAClB,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;AACxB,SAAK,mBAAmB;AAIxB,SAAK,YAAY;AACjB,SAAK,kBAAkB;AACvB,SAAK,cAAc;AACnB,SAAK,QAAQ;AAGb,SAAK,GAAG,iBAAiB,CAAC,SAAS;AAClC,WAAK,WAAW;AAChB,WAAK,IAAI,yBAAyB;AAAA,IACrC,CAAG;AACD,SAAK,GAAG,gBAAgB,CAAC,SAAS;AACjC,WAAK,WAAW;AAChB,WAAK,IAAI,yBAAyB;AAAA,IACrC,CAAG;AACD,SAAK,GAAG,gBAAgB,CAAC,SAAS;AACjC,WAAK,WAAW;AAChB,WAAK,IAAI,yBAAyB;AAAA,IACrC,CAAG;AACD,SAAK,GAAG,eAAe,CAAC,SAAS;AAChC,WAAK,WAAW;AAChB,WAAK,IAAI,yBAAyB;AAAA,IACrC,CAAG;AAAA,EACD;AAAA;AAAA,EAGD,IAAI,YAAY,MAAM;AACrB,QAAI,KAAK,OAAO;AACfA,0BAAA,MAAA,OAAA,4BAAY,eAAe,OAAO,IAAI,GAAG,IAAI;AAAA,IAC7C;AAAA,EACD;AAAA;AAAA,EAGD,MAAM,YAAY,MAAM;AACvBA,wBAAc,MAAA,SAAA,4BAAA,iBAAiB,OAAO,IAAI,GAAG,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU;AACT,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvC,UAAI,KAAK,aAAa;AACrB,aAAK,IAAI,qBAAqB;AAC9B;AACA;AAAA,MACA;AAGD,UAAI,KAAK,YAAY;AACpB,aAAK,IAAI,uBAAuB;AAChC,eAAO,IAAI,MAAM,OAAO,CAAC;AACzB;AAAA,MACA;AAED,UAAI;AACH,aAAK,aAAa;AAClB,aAAK,IAAI,qBAAqB,KAAK,SAAS;AAE5C,aAAK,KAAKA,cAAG,MAAC,cAAc;AAAA,UAC3B,KAAK,KAAK;AAAA,UACV,SAAS,MAAM;AACd,iBAAK,IAAI,kBAAkB;AAAA,UAC3B;AAAA,UACD,MAAM,CAAC,UAAU;AAChB,iBAAK,MAAM,oBAAoB,KAAK;AACpC,iBAAK,aAAa;AAClB,mBAAO,KAAK;AAAA,UACZ;AAAA,QACN,CAAK;AAEDA,4BAAI,aAAa,CAAC,QAAQ;AACzB,eAAK,cAAc;AACnB,eAAK,aAAa;AAClB,eAAK,oBAAoB;AACzB,eAAK,IAAI,gBAAgB;AACzB,eAAK,oBAAmB;AAGxB,eAAK,eAAc;AAGnB,cAAI,KAAK,cAAc,WAAW,GAAG;AACpC,iBAAK,cAAc,WAAW,EAAE,QAAQ,QAAM,GAAE,CAAE;AAAA,UAClD;AAGD,cAAI,CAAC,KAAK,eAAe,CAAC,KAAK,iBAAiB;AAE/C,iBAAK,IAAI,aAAa;AACtB,uBAAW,MAAM;AAChB,mBAAK,iBAAgB;AAAA,YACrB,GAAE,GAAG;AAAA,UACZ,OAAY;AACN,iBAAK,IAAI,wBAAwB;AAAA,UACjC;AAED,kBAAQ,GAAG;AAAA,QAChB,CAAK;AAEDA,4BAAI,cAAc,CAAC,QAAQ;AAC1B,eAAK,cAAc;AACnB,eAAK,aAAa;AAGlB,eAAK,cAAa;AAGlB,cAAI,KAAK,cAAc,cAAc,GAAG;AACvC,iBAAK,cAAc,cAAc,EAAE,QAAQ,QAAM,GAAE,CAAE;AAAA,UACrD;AAED,eAAK,IAAI,uBAAuB,IAAI,MAAM,WAAW,IAAI,MAAM;AAG/D,cAAI,CAAC,KAAK,oBAAoB,IAAI,SAAS,KAAM;AAChD,iBAAK,IAAI,iBAAiB;AAC1B,iBAAK,iBAAgB;AAAA,UAC3B,WAAgB,KAAK,kBAAkB;AACjC,iBAAK,IAAI,cAAc;AACvB,iBAAK,mBAAmB;AAAA,UACxB;AAAA,QACN,CAAK;AAEDA,4BAAI,cAAc,CAAC,QAAQ;AAC1B,eAAK,cAAc;AACnB,eAAK,aAAa;AAClB,eAAK,MAAM,kBAAkB,GAAG;AAGhC,cAAI,KAAK,cAAc,OAAO,GAAG;AAChC,iBAAK,cAAc,OAAO,EAAE,QAAQ,QAAM,GAAG,GAAG,CAAC;AAAA,UACjD;AAED,eAAK,iBAAgB;AAAA,QAC1B,CAAK;AAEDA,4BAAI,gBAAgB,CAAC,QAAQ;AAC5B,cAAI;AACH,kBAAM,UAAU,KAAK,MAAM,IAAI,IAAI;AACnC,iBAAK,IAAI,SAAS,OAAO;AAGzB,gBAAI,QAAQ,SAAS,QAAQ;AAC5B,mBAAK,WAAU;AACf;AAAA,YACA;AAGD,iBAAK,aAAa,QAAQ,MAAM,QAAQ,IAAI;AAAA,UAE5C,SAAQ,OAAO;AAEf,iBAAK,MAAM,WAAW,OAAO,IAAI,IAAI;AAAA,UACrC;AAAA,QACN,CAAK;AAED,mBAAW,MAAM;AAChB,cAAI,CAAC,KAAK,eAAe,KAAK,YAAY;AACzC,iBAAK,aAAa;AAClB,iBAAK,MAAM,eAAe;AAC1B,mBAAO,IAAI,MAAM,MAAM,CAAC;AAAA,UACxB;AAAA,QACD,GAAE,IAAK;AAAA,MAER,SAAQ,OAAO;AACf,aAAK,aAAa;AAClB,aAAK,MAAM,kBAAkB,KAAK;AAClC,eAAO,KAAK;AAAA,MACZ;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB,aAAa;AAC/B,SAAK,cAAc;AACnB,SAAK,IAAI,aAAa,cAAc,YAAY,UAAU;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa;AACZ,QAAI,KAAK,MAAM,KAAK,aAAa;AAChC,WAAK,IAAI,iBAAiB;AAC1BA,oBAAG,MAAC,YAAW;AACf,WAAK,cAAc;AACnB,WAAK,aAAa;AAClB,WAAK,mBAAmB;AAAA,IACxB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB;AAClB,QAAI,KAAK,qBAAqB,KAAK,wBAAwB,KAAK,yBAAyB,IAAI;AAC5F,WAAK,IAAI,eAAe;AACxB;AAAA,IACA;AAED,UAAM,QAAQ,KAAK;AAAA,MAClB,KAAK,oBAAoB,KAAK,IAAI,KAAK,KAAK,iBAAiB;AAAA,MAC7D,KAAK;AAAA,IACR;AAEE,SAAK;AACL,SAAK,IAAI,IAAI,KAAK,iBAAiB,SAAS,KAAK,IAAI;AAErD,eAAW,MAAM;AAChB,UAAI,CAAC,KAAK,eAAe,CAAC,KAAK,YAAY;AAC1C,aAAK,IAAI,SAAS;AAClB,aAAK,QAAO,EAAG,MAAM,WAAS;AAC7B,eAAK,MAAM,SAAS,KAAK;AAAA,QAC9B,CAAK;AAAA,MACD;AAAA,IACD,GAAE,KAAK;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY,MAAM,OAAO,IAAI;AAC5B,UAAM,UAAU,KAAK,UAAU,EAAE,MAAM,KAAI,CAAE;AAG7C,QAAI,CAAC,KAAK,aAAa;AACtB,WAAK,IAAI,wBAAwB,EAAE,MAAM,KAAM,CAAA;AAC/C,WAAK,aAAa,KAAK,OAAO;AAC9B,aAAO,QAAQ,OAAO,IAAI,MAAM,cAAc,CAAC;AAAA,IAC/C;AAED,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,WAAK,IAAI,SAAS,EAAE,MAAM,KAAM,CAAA;AAChCA,oBAAAA,MAAI,kBAAkB;AAAA,QACrB,MAAM;AAAA,QACN,SAAS,MAAM;AACd,eAAK,IAAI,WAAW,EAAE,MAAM,KAAM,CAAA;AAClC,kBAAQ,EAAE,MAAM,KAAI,CAAE;AAAA,QACtB;AAAA,QACD,MAAM,CAAC,UAAU;AAChB,eAAK,MAAM,WAAW,OAAO,EAAE,MAAM,KAAI,CAAE;AAC3C,eAAK,aAAa,KAAK,OAAO;AAC9B,iBAAO,KAAK;AAAA,QACZ;AAAA,MACL,CAAI;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,sBAAsB;AACrB,QAAI,KAAK,aAAa,WAAW,GAAG;AACnC;AAAA,IACA;AAED,SAAK,IAAI,WAAW,KAAK,aAAa,MAAM,KAAK;AAGjD,UAAM,QAAQ,CAAC,GAAG,KAAK,YAAY;AACnC,SAAK,eAAe;AAGpB,UAAM,QAAQ,aAAW;AACxB,UAAI;AACHA,sBAAAA,MAAI,kBAAkB;AAAA,UACrB,MAAM;AAAA,UACN,SAAS,MAAM;AACd,iBAAK,IAAI,aAAa,OAAO;AAAA,UAC7B;AAAA,UACD,MAAM,CAAC,UAAU;AAChB,iBAAK,MAAM,aAAa,OAAO,OAAO;AACtC,iBAAK,aAAa,QAAQ,OAAO;AAAA,UACjC;AAAA,QACN,CAAK;AAAA,MACD,SAAQ,OAAO;AACf,aAAK,MAAM,aAAa,OAAO,OAAO;AACtC,aAAK,aAAa,QAAQ,OAAO;AAAA,MACjC;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa,WAAW,MAAM;AAE7B,QAAI,cAAc,cAAc;AAE/B,UAAI;AACH,cAAM,YAAY,QAAQ,aAAa,EAAE;AACzC,YAAI,aAAa,OAAO,UAAU,oBAAoB,YAAY;AACjE,eAAK,IAAI,oBAAoB,IAAI;AAGjC,gBAAM,YAAY;AAAA,YACjB,MAAM,KAAK,QAAQ;AAAA;AAAA,YACnB,SAAS,KAAK,WAAW,KAAK,eAAe;AAAA,YAC7C,SAAS,KAAK,WAAW,CAAE;AAAA,YAC3B,oBAAoB,KAAK,sBAAsB;AAAA,UACrD;AAEK,oBAAU,gBAAgB,SAAS;AAAA,QACnC;AAAA,MACD,SAAQ,OAAO;AACf,aAAK,MAAM,qBAAqB,KAAK;AAAA,MACrC;AAAA,IACD;AAED,QAAI,KAAK,cAAc,SAAS,GAAG;AAClC,WAAK,IAAI,SAAS,SAAS,IAAI,IAAI;AACnC,WAAK,cAAc,SAAS,EAAE,QAAQ,aAAW,QAAQ,IAAI,CAAC;AAAA,IACjE,OAAS;AACN,WAAK,IAAI,cAAc,SAAS,IAAI,IAAI;AAAA,IACxC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,GAAG,WAAW,SAAS;AACtB,QAAI,CAAC,KAAK,cAAc,SAAS,GAAG;AACnC,WAAK,cAAc,SAAS,IAAI;IAChC;AAED,SAAK,cAAc,SAAS,EAAE,KAAK,OAAO;AAC1C,SAAK,IAAI,aAAa,SAAS,EAAE;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKD,IAAI,WAAW,SAAS;AACvB,QAAI,CAAC,KAAK,cAAc,SAAS,GAAG;AACnC;AAAA,IACA;AAED,UAAM,MAAM,KAAK,cAAc,SAAS,EAAE,QAAQ,OAAO;AAEzD,QAAI,MAAM,IAAI;AACb,WAAK,cAAc,SAAS,EAAE,OAAO,KAAK,CAAC;AAC3C,WAAK,IAAI,aAAa,SAAS,EAAE;AAAA,IACjC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB;AAClB,SAAK,IAAI,WAAW;AACpB,UAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AAExC,SAAK,IAAI,SAAS;AAClB,SAAK,IAAI,cAAc,CAAC,CAAC,KAAK;AAE9B,QAAI,OAAO;AACV,WAAK,IAAI,iBAAiB;AAE1B,WAAK,YAAY,QAAQ;AAAA,QACxB;AAAA,MACJ,CAAI;AAED,WAAK,IAAI,WAAW;AAAA,IACvB,OAAS;AACN,WAAK,IAAI,kBAAkB;AAAA,IAC3B;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AAEd,UAAM,QAAQA,cAAG,MAAC,eAAe,OAAO,KAAK;AAE7C,SAAK,IAAI,UAAU;AACnB,SAAK,YAAY,QAAQ;AAAA,MACxB;AAAA,IACH,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,QAAQ,OAAO,IAAI;AACjC,SAAK,YAAY,eAAe;AAAA,MAC/B;AAAA,MACA,GAAG;AAAA,IACN,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AACnB,SAAK,IAAI,mBAAmB;AAC5B,SAAK,YAAY,iBAAiB;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKD,uBAAuB;AACtB,SAAK,YAAY,oBAAoB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AACnB,SAAK,YAAY,iBAAiB;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKD,uBAAuB;AACtB,SAAK,IAAI,4BAA4B;AACrC,SAAK,IAAI,UAAS,oBAAI,KAAM,GAAC,eAAc,CAAE;AAC7C,SAAK,IAAI,kBAAkB,KAAK,WAAW;AAC3C,SAAK,IAAI,UAAU,KAAK,SAAS;AAEjC,QAAI,CAAC,KAAK,aAAa;AACtB,WAAK,IAAI,uBAAuB;AAChC;AAAA,IACA;AAED,SAAK,IAAI,iBAAiB;AAC1B,SAAK,YAAY,WAAW;AAC5B,SAAK,IAAI,uBAAuB;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA,EAKD,wBAAwB;AACvB,SAAK,YAAY,YAAY;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAKD,qBAAqB;AACpB,SAAK,YAAY,SAAS;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB,QAAQ,QAAQ;AACnC,SAAK,YAAY,oBAAoB;AAAA,MACpC;AAAA,MACA;AAAA,IACH,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB,QAAQ,SAAS;AAChC,SAAK,YAAY,gBAAgB;AAAA,MAChC;AAAA,MACA;AAAA,IACH,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe,QAAQ,MAAM;AAC5B,SAAK,YAAY,eAAe;AAAA,MAC/B;AAAA,MACA,GAAG;AAAA,IACN,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB,QAAQ,MAAM;AAC9B,SAAK,YAAY,iBAAiB;AAAA,MACjC;AAAA,MACA,GAAG;AAAA,IACN,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB,QAAQ,MAAM;AAC7B,SAAK,YAAY,gBAAgB;AAAA,MAChC;AAAA,MACA,GAAG;AAAA,IACN,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB,QAAQ,MAAM;AAChC,SAAK,YAAY,mBAAmB;AAAA,MACnC;AAAA,MACA,GAAG;AAAA,IACN,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AAEhB,SAAK,cAAa;AAGlB,SAAK,oBAAoB,YAAY,MAAM;AAC1C,UAAI,KAAK,aAAa;AACrB,aAAK,YAAY,MAAM;AAGvB,aAAK,mBAAmB,WAAW,MAAM;AACxC,eAAK,IAAI,WAAW;AACpB,eAAK,WAAU;AAGf,qBAAW,MAAM;AAChB,iBAAK,QAAO;AAAA,UACZ,GAAE,GAAI;AAAA,QACP,GAAE,GAAK;AAAA,MACR;AAAA,IACD,GAAE,GAAK;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB;AACf,QAAI,KAAK,mBAAmB;AAC3B,oBAAc,KAAK,iBAAiB;AACpC,WAAK,oBAAoB;AAAA,IACzB;AAED,QAAI,KAAK,kBAAkB;AAC1B,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB;AAAA,IACxB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa;AAEZ,QAAI,KAAK,kBAAkB;AAC1B,mBAAa,KAAK,gBAAgB;AAClC,WAAK,mBAAmB;AAAA,IACxB;AAAA,EACD;AACF;AAGK,MAAC,YAAY,IAAI,iBAAgB;;"}