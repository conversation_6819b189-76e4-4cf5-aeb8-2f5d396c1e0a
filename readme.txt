# 🎮 武侠点击式游戏开发文档 v1.0

---

## 一、项目概述

**游戏类型**：点击式武侠轻游戏  
**目标平台**：微信小程序  
**后端语言**：Python  
**通信方式**：WebSocket 实时通信  
**核心玩法**：点击“闯江湖”触发随机事件，通过战斗、采集、奇遇等方式获得资源、历练、成长武功，建设江湖人生。

---

## 二、系统架构

| 模块       | 描述                                         |
|------------|----------------------------------------------|
| 前端       | 微信小程序，负责界面展示与用户交互         |
| 通信       | WebSocket 实现前后端实时通信                |
| 后端       | Python 服务器，事件判定、状态处理、数据存储 |
| 数据库     | 存储玩家信息、道具、武功、事件记录等       |
| 日志与运维 | 玩家行为记录、调试信息、反作弊监控等       |

---

## 三、核心玩法：闯江湖系统

### 1. 事件类型

| 编号 | 类型       | 描述                                       |
|------|------------|--------------------------------------------|
| 1    | 好运事件   | 获得随机资源、装备、道具                   |
| 2    | 遭遇NPC    | 战斗或躲避，胜利得奖励，失败重伤10分钟     |
| 3    | 采集事件   | 采集资源：木材、矿石、动物皮毛、草药       |
| 4    | 空事件     | 显示一段武侠文案，获得1点历练              |
| 5    | 奇遇事件   | 触发门派任务、秘籍、神秘商人等稀有事件     |
| 6    | 恩怨事件   | 触发仇敌/知己事件，影响因果值             |
| 7    | 组队事件   | 多人共斗，战胜强敌或采集稀有资源           |

### 2. 触发方式

- 每点击一次“闯江湖”，触发一次事件（通过概率控制）
- 特殊事件可由剧情、状态、NPC记录等影响

---

## 四、玩家属性系统

### 1. 战斗类属性

| 属性名     | 描述                     |
|------------|--------------------------|
| 气血       | 当前生命值               |
| 内力       | 使用武功所需能量         |
| 攻击       | 物理攻击伤害基础         |
| 防御       | 降低所受伤害能力         |
| 命中       | 攻击命中概率             |
| 闪避       | 躲避攻击概率             |
| 暴击率     | 暴击触发概率             |
| 暴击伤害   | 暴击附加伤害             |
| 招架率     | 格挡攻击几率             |
| 格挡值     | 招架成功时减伤值         |

### 2. 武学类属性

| 属性名         | 描述                         |
|----------------|------------------------------|
| 身法           | 提升出招、闪避速度           |
| 内功修为       | 增强内功效果                 |
| 招式熟练度     | 每招独立熟练度               |
| 经脉通畅度     | 决定属性成长上限和技能效率   |

### 3. 成长类属性

| 属性名     | 描述                     |
|------------|--------------------------|
| 等级       | 玩家主等级               |
| 历练值     | 提升等级的经验值         |
| 体力值     | 打造/采集等消耗          |
| 精力值     | 修炼/打坐消耗            |
| 悟性       | 武功学习/成长效率影响    |

### 4. 江湖类属性

| 属性名   | 描述                         |
|----------|------------------------------|
| 声望     | 门派地位、任务门槛判定       |
| 因果值   | 善恶行为系统                 |
| 杀气     | 杀敌过多后产生，被追杀       |
| 友好度   | 与NPC关系值，影响互动        |

---

## 五、装备系统

### 1. 装备部位

- 武器
- 护甲
- 饰品：项链、手镯
- 坐骑
- 生活工具：斧头、镐头、捕兽器、镰刀

### 2. 装备属性

| 类型       | 描述                                           |
|------------|------------------------------------------------|
| 品质       | 普通→精良→稀有→传说→神品                      |
| 强化等级   | +1 ~ +10，增加基础属性                         |
| 耐久度     | 使用后降低，需维修                             |
| 特效       | 附加技能或属性，如吸血、灼烧、暴击加成等      |

---

## 六、武功系统

### 1. 类型划分

| 类型   | 功能                         |
|--------|------------------------------|
| 外功   | 攻击技能，拳脚/兵器         |
| 内功   | 增益状态、回复类             |
| 轻功   | 增加闪避、速度               |
| 心法   | 提升武学理解、经脉强化       |
| 特技   | 限定招式、奇遇获得           |

### 2. 武功机制

- 武功品级：黄 → 玄 → 地 → 天 → 绝世  
- 武功熟练度：使用或修炼提高  
- 连招系统：自定义武功组合，组合有冷却与连携加成

---

## 七、打造系统

| 项目         | 说明                                             |
|--------------|--------------------------------------------------|
| 打造等级     | 决定可打造装备品质                               |
| 材料来源     | 木材、矿物、皮毛等采集资源                       |
| 打造消耗     | 材料 + 体力值                                    |
| 打造成功率   | 可失败，但失败有经验                             |
| 附魔系统     | 高阶装备可镶嵌符文，增加特殊属性                 |
| 图纸机制     | 稀有装备需图纸才能打造                           |

---

## 八、采集系统

- 需要对应采集工具（斧、镐、捕兽器、镰刀）
- 工具等级决定采集成功率与资源数量
- 可采集资源：
  - 木材类：乌木、铁杉等
  - 矿石类：赤铁、黑金等
  - 动物类：狼皮、熊胆等
  - 草药类：金银花、人参等

---

## 九、商店系统

| 商店         | 功能                               |
|--------------|------------------------------------|
| 装备商       | 售卖基础装备                       |
| 药材铺       | 售卖草药及炼药工具                 |
| 神秘商人     | 刷新稀有道具/秘籍，限时出现       |
| 门派商店     | 需门派声望解锁                     |

- 玩家可出售物品
- 不同商店价格策略不同，可受声望影响

---

## 十、自由交易系统（市场）

| 功能       | 描述                                   |
|------------|----------------------------------------|
| 摆摊       | 玩家在城内设摊售卖                     |
| 拍卖       | 设置底价+时间，其他玩家竞价           |
| 搜索筛选   | 按名称、等级、价格等条件筛选物品     |
| 手续费     | 系统抽取部分手续费（银两或金币）       |

---

## 十一、疗伤系统

| 状态   | 恢复方式                                |
|--------|------------------------------------------|
| 重伤   | 打坐 / 金创药 / 门派治疗                |
| 内伤   | 内功调息 / 中品内丹                    |
| 中毒   | 解毒草 / 解毒丸                         |
| 疲劳   | 休息点 / 恢复丹药                       |

- 药品通过草药炼制系统合成，需配方+精力+经验

---

## 十二、门派系统

| 内容         | 描述                                 |
|--------------|--------------------------------------|
| 加入门派     | 玩家可拜入门派，获得专属技能        |
| 门派任务     | 完成获取贡献值与声望                 |
| 门派技能     | 专属武功与心法                       |
| 门派战       | 后期推出多人对战、阵营活动等玩法     |
| 叛门系统     | 玩家可脱离门派，引发追杀或奇遇事件   |

---

## 十三、前端页面结构

| 页面名称 | 功能说明                                                   |
|----------|------------------------------------------------------------|
| 主界面   | 展示角色、状态、地图、按钮「好运事件」「闯江湖」事件滚动 |
| 背包页   | 背包分类、装备使用、查看道具                              |
| 武功页   | 查看武学、配置连招、修炼                                   |
| 打造页   | 查看材料图纸、打造装备、附魔                               |
| 商场页   | 系统商店功能（购买/出售）                                 |
| 市场页   | 玩家自由交易、拍卖系统                                     |
| 门派页   | 加入门派、完成任务、学习武功                               |
| 疗伤页   | 显示状态、使用药物、打坐疗伤                               |

---

## 十四、数据库结构建议（简要）

| 表名           | 描述                             |
|----------------|----------------------------------|
| users          | 玩家主信息                       |
| stats          | 玩家属性表                       |
| inventory      | 玩家背包                         |
| equipment      | 所有装备信息                     |
| skills         | 武功与招式表                     |
| crafting       | 打造与炼药记录                   |
| events         | 历史事件记录                     |
| shops          | 商店商品信息                     |
| trades         | 玩家交易记录                     |
| npcs           | NPC数据与掉落表                 |
| guilds         | 门派信息与成员列表               |
| logs           | 玩家行为日志（审计用途）         |

---

## ✅ 下一步建议

1. 搭建 WebSocket 登录通信框架  
2. 开发“闯江湖”事件模块  
3. 搭建玩家属性模型（含装备/武功）  
4. 初始背包与商店功能实现  
5. 后续逐步开发战斗系统、打造系统、自由市场系统

---
