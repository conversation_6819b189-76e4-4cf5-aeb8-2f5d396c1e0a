#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
境界系统模块
负责管理境界配置、增益效果和境界计算
"""

class RealmSystem:
    """境界系统管理器"""
    
    # 境界配置：历练值范围（百万为单位）-> 境界名称
    REALM_CONFIGS = [
        (0, 1000000, "初出茅庐"),
        (1000000, 5000000, "不堪一击"),
        (5000000, 55000000, "初窥门径"),
        (55000000, 100000000, "豁然贯通"),
        (100000000, 150000000, "出类拔萃"),
        (150000000, 200000000, "无可匹敌"),
        (200000000, 250000000, "技冠群雄"),
        (250000000, 300000000, "神乎其技"),
        (300000000, 400000000, "出神入化"),
        (400000000, 600000000, "一代宗师"),
        (600000000, 800000000, "神功盖世"),
        (800000000, 1000000000, "物我两忘"),
        (1000000000, 1500000000, "至尊无敌"),
        (1500000000, 2000000000, "惊世骇俗"),
        (2000000000, 3000000000, "神鬼莫测"),
        (3000000000, 5000000000, "陆地神仙"),
        (5000000000, float('inf'), "大罗金仙"),
    ]
    
    # 境界增益配置
    REALM_BONUSES = {
        "不堪一击": {
            "description": "武学点获取+1",
            "effects": {
                "skill_points_gain": 1
            }
        },
        "初窥门径": {
            "description": "历练值获取+1",
            "effects": {
                "experience_gain": 1
            }
        },
        "豁然贯通": {
            "description": "生命、精力、内力分别+10%",
            "effects": {
                "hp_bonus": 0.1,
                "energy_bonus": 0.1,
                "mp_bonus": 0.1
            }
        },
        "出类拔萃": {
            "description": "攻击+5%",
            "effects": {
                "attack_bonus": 0.05
            }
        },
        "无可匹敌": {
            "description": "防御+5%",
            "effects": {
                "defense_bonus": 0.05
            }
        },
        "技冠群雄": {
            "description": "闪避+5%",
            "effects": {
                "dodge_bonus": 0.05
            }
        },
        "神乎其技": {
            "description": "暴击+5%",
            "effects": {
                "crit_bonus": 0.05
            }
        },
        "出神入化": {
            "description": "力量、悟性、身份、根骨+5",
            "effects": {
                "talent_bonus": 5
            }
        },
        "一代宗师": {
            "description": "武学点获取+1",
            "effects": {
                "skill_points_gain": 1
            }
        },
        "神功盖世": {
            "description": "历练值获取+1",
            "effects": {
                "experience_gain": 1
            }
        },
        "物我两忘": {
            "description": "富源+5",
            "effects": {
                "fortune_bonus": 5
            }
        },
        "至尊无敌": {
            "description": "生命、精力、内力分别+10%",
            "effects": {
                "hp_bonus": 0.1,
                "energy_bonus": 0.1,
                "mp_bonus": 0.1
            }
        },
        "惊世骇俗": {
            "description": "力量、悟性、身份、根骨+5",
            "effects": {
                "talent_bonus": 5
            }
        },
        "神鬼莫测": {
            "description": "暴击+5%",
            "effects": {
                "crit_bonus": 0.05
            }
        },
        "陆地神仙": {
            "description": "生命、精力、内力分别+20%",
            "effects": {
                "hp_bonus": 0.2,
                "energy_bonus": 0.2,
                "mp_bonus": 0.2
            }
        },
        "大罗金仙": {
            "description": "攻击、防御、闪避、暴击分别+30%",
            "effects": {
                "attack_bonus": 0.3,
                "defense_bonus": 0.3,
                "dodge_bonus": 0.3,
                "crit_bonus": 0.3
            }
        }
    }
    
    @classmethod
    def get_realm_info(cls, experience: int) -> dict:
        """根据历练值计算境界信息"""
        # 找到当前境界
        current_realm = "初出茅庐"
        next_realm = "不堪一击"
        current_min = 0
        current_max = 1000000
        progress = 0
        
        for i, (min_exp, max_exp, realm_name) in enumerate(cls.REALM_CONFIGS):
            if experience >= min_exp and experience < max_exp:
                current_realm = realm_name
                current_min = min_exp
                current_max = max_exp
                progress = (experience - min_exp) / (max_exp - min_exp) if max_exp > min_exp else 0
                
                # 获取下一个境界
                if i + 1 < len(cls.REALM_CONFIGS):
                    next_realm = cls.REALM_CONFIGS[i + 1][2]
                else:
                    next_realm = "大罗金仙"
                break
        
        return {
            'current_realm': current_realm,
            'next_realm': next_realm,
            'current_min': current_min,
            'current_max': current_max,
            'progress': progress,
            'experience': experience,
            'current_bonus': cls.get_realm_bonus(current_realm),
            'next_bonus': cls.get_realm_bonus(next_realm)
        }
    
    @classmethod
    def get_realm_bonus(cls, realm_name: str) -> dict:
        """获取境界增益信息"""
        if realm_name not in cls.REALM_BONUSES:
            return {
                "description": "无增益",
                "effects": {}
            }
        return cls.REALM_BONUSES[realm_name]
    
    @classmethod
    def apply_realm_bonuses(cls, player_data: dict) -> dict:
        """应用境界增益到玩家数据"""
        realm_info = cls.get_realm_info(player_data.get('experience', 0))
        current_realm = realm_info['current_realm']
        bonus = cls.get_realm_bonus(current_realm)
        
        # 应用增益效果
        for effect_type, value in bonus['effects'].items():
            if effect_type == 'skill_points_gain':
                # 武学点获取增益在获取时应用
                player_data['skill_points_gain_bonus'] = value
            elif effect_type == 'experience_gain':
                # 历练值获取增益在获取时应用
                player_data['experience_gain_bonus'] = value
            elif effect_type == 'hp_bonus':
                # 生命值百分比增益
                base_hp = player_data.get('base_hp', player_data.get('max_hp', 100))
                player_data['max_hp'] = int(base_hp * (1 + value))
            elif effect_type == 'energy_bonus':
                # 精力值百分比增益
                base_energy = player_data.get('base_energy', player_data.get('max_energy', 100))
                player_data['max_energy'] = int(base_energy * (1 + value))
            elif effect_type == 'mp_bonus':
                # 内力值百分比增益
                base_mp = player_data.get('base_mp', player_data.get('max_mp', 50))
                player_data['max_mp'] = int(base_mp * (1 + value))
            elif effect_type == 'attack_bonus':
                # 攻击力百分比增益
                base_attack = player_data.get('base_attack', player_data.get('attack', 10))
                player_data['attack'] = int(base_attack * (1 + value))
            elif effect_type == 'defense_bonus':
                # 防御力百分比增益
                base_defense = player_data.get('base_defense', player_data.get('defense', 5))
                player_data['defense'] = int(base_defense * (1 + value))
            elif effect_type == 'dodge_bonus':
                # 闪避百分比增益
                base_dodge = player_data.get('base_dodge', player_data.get('dodge', 1))
                dodge_bonus = base_dodge * value
                player_data['dodge'] = base_dodge + dodge_bonus
                # 限制闪避最大值（不超过40%）
                player_data['dodge'] = min(player_data['dodge'], 40.0)
            elif effect_type == 'crit_bonus':
                # 暴击百分比增益
                base_crit = player_data.get('base_crit', player_data.get('crit', 1))
                player_data['crit'] = base_crit + int(base_crit * value)
            elif effect_type == 'talent_bonus':
                # 天赋属性增益
                if 'talent' in player_data:
                    for talent_key in player_data['talent']:
                        player_data['talent'][talent_key] += value
            elif effect_type == 'fortune_bonus':
                # 富源增益
                player_data['fortune'] = player_data.get('fortune', 1) + value
        
        return player_data
    
    @classmethod
    def get_all_realms(cls) -> list:
        """获取所有境界列表"""
        return [realm[2] for realm in cls.REALM_CONFIGS]
    
    @classmethod
    def get_realm_by_name(cls, realm_name: str) -> dict:
        """根据境界名称获取境界信息"""
        for min_exp, max_exp, name in cls.REALM_CONFIGS:
            if name == realm_name:
                return {
                    'name': name,
                    'min_exp': min_exp,
                    'max_exp': max_exp,
                    'bonus': cls.get_realm_bonus(name)
                }
        return None 