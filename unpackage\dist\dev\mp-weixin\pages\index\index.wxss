
.container.data-v-1cf27b2a {
	padding: 15rpx;
	background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
	min-height: 100vh;
	height: 100vh;
	display: flex;
	flex-direction: column;
	gap: 10rpx;
	overflow: hidden;
	box-sizing: border-box;
}
.top-info-bar.data-v-1cf27b2a {
	display: flex;
		align-items: center;
		justify-content: center;
	flex-wrap: wrap;
	padding: 8rpx 0 0 0;
	font-size: 26rpx;
	color: #333;
	background: transparent;
	margin-bottom: 8rpx;
	line-height: 1.7;
}
.player-name.data-v-1cf27b2a {
	font-weight: bold;
	font-size: 32rpx;
	color: #4b3fa7;
	margin-right: 4rpx;
}
.player-exp.data-v-1cf27b2a, .conn-text.data-v-1cf27b2a {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}
.info-sep.data-v-1cf27b2a {
	margin: 0 8rpx;
	color: #bbb;
	font-size: 22rpx;
}
.conn-dot.data-v-1cf27b2a {
	display: inline-block;
	width: 14rpx;
	height: 14rpx;
	border-radius: 50%;
	margin-right: 4rpx;
	background: #bbb;
}
.conn-dot.connected.data-v-1cf27b2a { background: #27ae60;
}
.conn-dot.disconnected.data-v-1cf27b2a, .conn-dot.failed.data-v-1cf27b2a { background: #e74c3c;
}
.player-attr.data-v-1cf27b2a {
	font-size: 22rpx;
	color: #666;
	margin: 0 2rpx;
}
.attr-sep.data-v-1cf27b2a {
	color: #ccc;
	font-size: 20rpx;
	margin: 0 2rpx;
}
.character-card.data-v-1cf27b2a {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 15rpx;
	margin-bottom: 10rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
}
.character-header.data-v-1cf27b2a {
	display: none;
}
.progress-bar.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.progress-label.data-v-1cf27b2a {
	width: 80rpx;
	font-size: 28rpx;
	color: #666;
}
.progress-bg.data-v-1cf27b2a {
	flex: 1;
	height: 20rpx;
	background: #f0f0f0;
	border-radius: 10rpx;
	margin: 0 20rpx;
	overflow: hidden;
}
.progress-fill.data-v-1cf27b2a {
	height: 100%;
	border-radius: 10rpx;
	transition: width 0.3s ease;
}
.hp-fill.data-v-1cf27b2a {
	background: linear-gradient(90deg, #ff6b6b, #ee5a52);
}
.mp-fill.data-v-1cf27b2a {
	background: linear-gradient(90deg, #4ecdc4, #44a08d);
}
.stamina-fill.data-v-1cf27b2a {
	background: linear-gradient(90deg, #ffe66d, #f7b731);
}
.energy-fill.data-v-1cf27b2a {
	background: linear-gradient(90deg, #a8edea, #fed6e3);
}
.progress-text.data-v-1cf27b2a {
	width: 120rpx;
	font-size: 24rpx;
	color: #666;
	text-align: right;
}
.status-display.data-v-1cf27b2a {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 15rpx;
	margin-bottom: 10rpx;
	text-align: center;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
}
.status-text.data-v-1cf27b2a {
	font-size: 32rpx;
	font-weight: bold;
}
.status-injured.data-v-1cf27b2a {
	color: #e74c3c;
}
.status-internal_injury.data-v-1cf27b2a {
	color: #9b59b6;
}
.status-poisoned.data-v-1cf27b2a {
	color: #27ae60;
}
.status-tired.data-v-1cf27b2a {
	color: #95a5a6;
}
.event-log.data-v-1cf27b2a {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
	flex: 1;
	margin-bottom: 0;
	height: 0;
	min-height: 0;
	max-height: none;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}
.log-header.data-v-1cf27b2a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.log-title.data-v-1cf27b2a {
	font-size: 32rpx;
	font-weight: bold;
	color: #667eea;
}
.log-clear.data-v-1cf27b2a {
	font-size: 28rpx;
	color: #764ba2;
	font-weight: 500;
}
.log-content.data-v-1cf27b2a {
	height: calc(100% - 60rpx);
	flex: 1;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
}
.log-item.data-v-1cf27b2a {
	padding: 15rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s ease;
}
.log-item.data-v-1cf27b2a:hover {
	background-color: rgba(102, 126, 234, 0.05);
}
.log-item.data-v-1cf27b2a:last-child {
	border-bottom: none;
}
.log-header-line.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}
.log-content-line.data-v-1cf27b2a {
	margin-left: 0;
}
.log-time.data-v-1cf27b2a {
	font-size: 24rpx;
	color: #999;
	margin-right: 15rpx;
}
.log-event.data-v-1cf27b2a {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}
.log-desc.data-v-1cf27b2a {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}
.log-empty.data-v-1cf27b2a {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}
.map-bar.data-v-1cf27b2a {
	background: linear-gradient(135deg, #667eea, #764ba2);
	padding: 12px 15px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-radius: 0 0 15px 15px;
	box-shadow: 0 4px 15px rgba(0,0,0,0.2);
	margin-bottom: 5px;
	white-space: nowrap;
	overflow: hidden;
}
.map-bar-left.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	flex: 1;
	min-width: 0;
	gap: 6px;
}
.map-bar-right.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	gap: 8px;
	flex-shrink: 0;
}
.user-name.data-v-1cf27b2a {
	color: #ffffff;
	font-weight: bold;
	font-size: 14px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 150px;
	flex-shrink: 0;
}
.user-label.data-v-1cf27b2a {
	color: #e8e8e8;
	margin: 0 6px;
	font-size: 12px;
}
.map-label.data-v-1cf27b2a {
	color: #e8e8e8;
	font-size: 12px;
}
.map-name.data-v-1cf27b2a {
	color: #ffd700;
	font-weight: bold;
	margin: 0 6px;
	font-size: 14px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 80px;
	flex-shrink: 0;
}
.map-btn.data-v-1cf27b2a {
	font-size: 12px;
	padding: 4px 12px;
	border-radius: 15px;
	background: rgba(255, 255, 255, 0.2);
	color: #ffffff;
	border: 1px solid rgba(255, 255, 255, 0.3);
	-webkit-backdrop-filter: blur(10px);
	        backdrop-filter: blur(10px);
	white-space: nowrap;
}
.connection-status.data-v-1cf27b2a {
	font-size: 12px;
	font-weight: 500;
	padding: 3px 8px;
	border-radius: 12px;
	background: rgba(255, 255, 255, 0.1);
	white-space: nowrap;
}
.connection-status.connected.data-v-1cf27b2a {
	color: #4ade80;
	background: rgba(74, 222, 128, 0.1);
	border: 1px solid rgba(74, 222, 128, 0.3);
}
.connection-status.disconnected.data-v-1cf27b2a {
	color: #f87171;
	background: rgba(248, 113, 113, 0.1);
	border: 1px solid rgba(248, 113, 113, 0.3);
}
.map-popup-mask.data-v-1cf27b2a {
	position: fixed; left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.25); z-index: 9999; display: flex; align-items: center; justify-content: center;
}
.map-popup.data-v-1cf27b2a {
	background: #fff; border-radius: 18rpx; padding: 32rpx 24rpx; min-width: 540rpx; max-width: 90vw; box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18); display: flex; flex-direction: column; align-items: center;
}
.map-popup-title.data-v-1cf27b2a { font-size: 32rpx; font-weight: bold; color: #4b3fa7; margin-bottom: 18rpx;
}
.map-list.data-v-1cf27b2a { max-height: 600rpx; min-width: 480rpx; overflow-y: auto;
}
.map-item.data-v-1cf27b2a { background: #f7f7f7; border-radius: 12rpx; margin-bottom: 16rpx; padding: 18rpx 16rpx; cursor: pointer; transition: background 0.2s;
}
.map-item.active.data-v-1cf27b2a { background: #e6f7ff; border: 2rpx solid #4b3fa7;
}
.map-item.locked.data-v-1cf27b2a { opacity: 0.5; pointer-events: none;
}
.map-item-title.data-v-1cf27b2a { font-size: 28rpx; font-weight: bold; color: #333; margin-bottom: 6rpx;
}
.map-item-desc.data-v-1cf27b2a { font-size: 24rpx; color: #666; margin-bottom: 4rpx;
}
.map-item-npc.data-v-1cf27b2a { font-size: 22rpx; color: #27ae60; margin-bottom: 2rpx;
}
.map-item-monster.data-v-1cf27b2a { font-size: 22rpx; color: #e74c3c; margin-bottom: 2rpx;
}
.map-item-gather.data-v-1cf27b2a { font-size: 22rpx; color: #2980b9; margin-bottom: 2rpx;
}
.map-item-req.data-v-1cf27b2a { font-size: 20rpx; color: #888;
}
.close-btn.data-v-1cf27b2a { background: #eee; color: #888; font-size: 24rpx; border-radius: 24rpx; padding: 12rpx 36rpx; border: none; margin-top: 18rpx;
}
.announcement-bar.data-v-1cf27b2a {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 15rpx 20rpx;
	margin-bottom: 10rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
	display: flex;
	align-items: center;
	gap: 15rpx;
	height: 60rpx;
	overflow: hidden;
}
.announcement-icon.data-v-1cf27b2a {
	font-size: 28rpx;
	flex-shrink: 0;
	animation: announcement-bounce 1.5s ease-in-out infinite;
}
.announcement-content.data-v-1cf27b2a {
	flex: 1;
	overflow: hidden;
}
.announcement-scroll.data-v-1cf27b2a {
		display: flex;
	align-items: center;
	height: 100%;
	animation: announcement-scroll-1cf27b2a 25s linear infinite;
	white-space: nowrap;
	animation-play-state: running;
}
.announcement-scroll.data-v-1cf27b2a:hover {
	animation-play-state: paused;
}
@keyframes announcement-scroll-1cf27b2a {
0% {
		transform: translateX(100%);
}
100% {
		transform: translateX(-100%);
}
}
.announcement-text.data-v-1cf27b2a {
	font-size: 26rpx;
	font-weight: 500;
	color: #333;
	white-space: nowrap;
	margin-right: 50rpx;
}
.npc-menu-modal.data-v-1cf27b2a {
	position: fixed;
	left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.3);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}
.npc-menu-content.data-v-1cf27b2a {
	background: #fff;
	border-radius: 12px;
	padding: 24px 20px 16px 20px;
	min-width: 220px;
	max-width: 80vw;
	box-shadow: 0 2px 12px rgba(0,0,0,0.12);
	display: flex;
	flex-direction: column;
	align-items: center;
}
.npc-menu-header.data-v-1cf27b2a {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 8px;
}
.npc-menu-avatar.data-v-1cf27b2a {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	margin-bottom: 6px;
}
.npc-menu-name.data-v-1cf27b2a {
	font-size: 18px;
	font-weight: bold;
	color: #222;
}
.npc-menu-desc.data-v-1cf27b2a {
	font-size: 14px;
	color: #666;
	margin-bottom: 12px;
	text-align: center;
}
.npc-menu-list.data-v-1cf27b2a {
	width: 100%;
	margin-bottom: 10px;
}
.npc-menu-btn.data-v-1cf27b2a {
	width: 100%;
	margin-bottom: 8px;
	background: #409eff;
	color: #fff;
	border: none;
	border-radius: 6px;
	padding: 8px 0;
	font-size: 15px;
}
.npc-menu-close.data-v-1cf27b2a {
	margin-top: 6px;
	background: #eee;
	color: #333;
	border: none;
	border-radius: 6px;
	padding: 7px 0;
	width: 100%;
	font-size: 15px;
}

/* 购买弹窗样式 */
.buy-modal-mask.data-v-1cf27b2a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1002;
}
.buy-modal.data-v-1cf27b2a {
	background: white;
	border-radius: 20rpx;
	width: 90vw;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}
.buy-modal-header.data-v-1cf27b2a {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.buy-modal-title.data-v-1cf27b2a {
	font-size: 36rpx;
	font-weight: bold;
}
.buy-modal-close.data-v-1cf27b2a {
	background: none;
	border: none;
	color: white;
	font-size: 40rpx;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.buy-modal-content.data-v-1cf27b2a {
	padding: 40rpx 30rpx;
}
.item-info.data-v-1cf27b2a {
	margin-bottom: 30rpx;
}
.item-name.data-v-1cf27b2a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}
.item-details.data-v-1cf27b2a {
	display: flex;
	justify-content: space-between;
	font-size: 28rpx;
	color: #666;
}
.quantity-section.data-v-1cf27b2a {
	margin-bottom: 30rpx;
}
.quantity-label.data-v-1cf27b2a {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}
.quantity-input-group.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20rpx;
}
.quantity-btn.data-v-1cf27b2a {
	width: 60rpx;
	height: 60rpx;
	background: #f0f0f0;
	border: none;
	border-radius: 50%;
	font-size: 32rpx;
	color: #333;
	display: flex;
	align-items: center;
	justify-content: center;
}
.quantity-btn.data-v-1cf27b2a:active {
	background: #e0e0e0;
}
.quantity-input.data-v-1cf27b2a {
	width: 120rpx;
	height: 60rpx;
	text-align: center;
	border: 2rpx solid #e0e0e0;
	border-radius: 10rpx;
	font-size: 28rpx;
}
.total-section.data-v-1cf27b2a {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 15rpx;
	margin-bottom: 30rpx;
}
.total-label.data-v-1cf27b2a {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.money-info.data-v-1cf27b2a {
	font-size: 26rpx;
	color: #666;
}
.buy-modal-actions.data-v-1cf27b2a {
	display: flex;
	gap: 20rpx;
	padding: 0 30rpx 30rpx;
}
.buy-btn-cancel.data-v-1cf27b2a, .buy-btn-confirm.data-v-1cf27b2a {
	flex: 1;
	height: 80rpx;
	border: none;
	border-radius: 15rpx;
	font-size: 30rpx;
	font-weight: bold;
}
.buy-btn-cancel.data-v-1cf27b2a {
	background: #f0f0f0;
	color: #666;
}
.buy-btn-confirm.data-v-1cf27b2a {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.buy-btn-confirm.data-v-1cf27b2a:disabled {
	background: #ccc;
	color: #999;
}

/* 天赋增益卡片样式 */
.talent-bonus-card.data-v-1cf27b2a {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 16rpx;
	padding: 16rpx;
	margin-bottom: 10rpx;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
}
.talent-bonus-title.data-v-1cf27b2a {
	font-size: 28rpx;
	font-weight: bold;
	color: #4b3fa7;
	margin-bottom: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}
.talent-bonus-title.data-v-1cf27b2a::before {
	content: "⭐";
	font-size: 28rpx;
}
.talent-bonus-grid.data-v-1cf27b2a {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}
.talent-bonus-item.data-v-1cf27b2a {
	background: linear-gradient(135deg, #e8f4ff, #f0f8ff);
	border-radius: 8rpx;
	padding: 8rpx 12rpx;
	border: 1px solid rgba(102, 126, 234, 0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 80rpx;
}
.talent-bonus-label.data-v-1cf27b2a {
	font-size: 22rpx;
	font-weight: bold;
	color: #4b3fa7;
	margin-bottom: 4rpx;
}
.talent-bonus-value.data-v-1cf27b2a {
	font-size: 20rpx;
	color: #27ae60;
	font-weight: 500;
}

/* 江湖按钮样式 */
.jianghu-section.data-v-1cf27b2a {
	position: fixed;
	bottom: 20rpx;
	left: 20rpx;
	right: 20rpx;
	z-index: 100;
}
.jianghu-btn.data-v-1cf27b2a {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50rpx;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}
.jianghu-btn.data-v-1cf27b2a:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}
.jianghu-btn.data-v-1cf27b2a:disabled {
	background: linear-gradient(135deg, #ccc 0%, #999 100%);
	box-shadow: none;
	opacity: 0.6;
}
.jianghu-btn-text.data-v-1cf27b2a {
	font-size: 32rpx;
	font-weight: bold;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.jianghu-btn.data-v-1cf27b2a::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s;
}
.jianghu-btn.data-v-1cf27b2a:not(:disabled):hover::before {
	left: 100%;
}
.jianghu-section-flex.data-v-1cf27b2a {
  position: fixed;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  z-index: 100;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
}
.healing-btn.data-v-1cf27b2a {
  height: 100rpx;
  min-width: 160rpx;
  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
  border-radius: 50rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(67, 206, 162, 0.2);
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
  margin-right: 0;
}
.healing-btn.data-v-1cf27b2a:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(67, 206, 162, 0.3);
}
.healing-btn.data-v-1cf27b2a:disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: none;
  opacity: 0.6;
}
.healing-btn-text.data-v-1cf27b2a {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.meditation-btn.data-v-1cf27b2a {
  height: 100rpx;
  min-width: 160rpx;
  background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
  border-radius: 50rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.15);
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
  margin-left: 0;
}
.meditation-btn.data-v-1cf27b2a:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.25);
}
.meditation-btn.data-v-1cf27b2a:disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: none;
  opacity: 0.6;
}
.meditation-btn-text.data-v-1cf27b2a {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.jianghu-section-flex-opt.data-v-1cf27b2a {
  position: fixed;
  bottom: 20rpx;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
  gap: 48rpx;
}
.side-btn.data-v-1cf27b2a {
  width: 90rpx;
  height: 90rpx;
  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
  border-radius: 50%;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(67, 206, 162, 0.15);
  transition: box-shadow 0.2s, transform 0.2s;
}
.side-btn.data-v-1cf27b2a:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(67, 206, 162, 0.25);
}
.side-btn.data-v-1cf27b2a:disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: none;
  opacity: 0.6;
}
.side-btn-icon.data-v-1cf27b2a {
  font-size: 36rpx;
  line-height: 1;
}
.side-btn-text.data-v-1cf27b2a {
  font-size: 20rpx;
  margin-top: 2rpx;
  line-height: 1;
}
.main-btn.data-v-1cf27b2a {
  width: 160rpx;
  height: 90rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 45rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);
  margin: 0 8rpx;
  transition: box-shadow 0.2s, transform 0.2s;
}
.main-btn.data-v-1cf27b2a:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.main-btn.data-v-1cf27b2a:disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: none;
  opacity: 0.6;
}
.main-btn-text.data-v-1cf27b2a {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}
.healing-meditation-popup-centered.data-v-1cf27b2a {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  min-width: 520rpx;
  max-width: 90vw;
  min-height: 80rpx;
  max-height: 420rpx;
  background: rgba(255,255,255,0.85);
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);
  border: 2rpx solid #e0cda2;
  padding: 24rpx 28rpx 16rpx 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #3e2c13;
  text-align: center;
  pointer-events: auto;
  font-family: 'STKaiti', 'KaiTi', 'FZKai-Z03', '楷体', serif;
  letter-spacing: 1.5rpx;
  animation: fadeInScale-1cf27b2a 0.3s;
}
.healing-meditation-loading.data-v-1cf27b2a {
  margin-bottom: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.spinner.data-v-1cf27b2a {
  width: 64rpx;
  height: 64rpx;
  border: 8rpx solid #e0cda2;
  border-top: 8rpx solid #bfa76a;
  border-radius: 50%;
  animation: spin-1cf27b2a 1s linear infinite;
}
@keyframes spin-1cf27b2a {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.healing-meditation-msg-centered.data-v-1cf27b2a {
  margin-bottom: 8rpx;
  line-height: 1.8;
  word-break: break-all;
  font-size: 24rpx;
}
@keyframes fadeInScale-1cf27b2a {
0% { opacity: 0; transform: scale(0.8) translate(-50%, -50%);
}
100% { opacity: 1; transform: scale(1) translate(-50%, -50%);
}
}
.npc-sidebar-btn.data-v-1cf27b2a {
  position: fixed;
  right: 0;
  z-index: 1001;
  background: #409eff;
  color: #fff;
  border-radius: 16rpx 0 0 16rpx;
  padding: 18rpx 12rpx;
  font-size: 28rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
  transition: top 0.15s;
  -webkit-user-select: none;
          user-select: none;
}
.npc-sidebar-mask.data-v-1cf27b2a {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 1000;
}
.npc-sidebar.data-v-1cf27b2a {
  position: fixed;
  top: 0; right: -340rpx;
  width: 340rpx;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 60%, #e0e7ef 100%);
  z-index: 1002;
  box-shadow: -8rpx 0 32rpx rgba(102,126,234,0.12);
  border-radius: 24rpx 0 0 24rpx;
  transition: right 0.35s cubic-bezier(.4,0,.2,1), opacity 0.2s;
  opacity: 0.98;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.npc-sidebar-show.data-v-1cf27b2a {
  right: 0 !important;
}
.npc-sidebar-header.data-v-1cf27b2a {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 32rpx 18rpx 18rpx 18rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #4b3fa7;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx 0 0 0;
  box-shadow: 0 2rpx 8rpx rgba(102,126,234,0.08);
}
.npc-sidebar-close.data-v-1cf27b2a {
  position: absolute;
  right: 18rpx;
  top: 32rpx;
  background: none;
  border: none;
  font-size: 36rpx;
  color: #888;
  cursor: pointer;
  z-index: 2;
}
.npc-sidebar-list.data-v-1cf27b2a {
  flex: 1;
  overflow-y: auto;
  padding: 18rpx 12rpx;
}
.npc-item.data-v-1cf27b2a {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(102,126,234,0.06);
  margin-bottom: 18rpx;
  padding: 18rpx 0 12rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s, transform 0.2s;
}
.npc-item.data-v-1cf27b2a:active {
  box-shadow: 0 4rpx 16rpx rgba(102,126,234,0.18);
  transform: scale(0.97);
}
.npc-avatar.data-v-1cf27b2a {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  border: 3rpx solid #667eea;
  box-shadow: 0 2rpx 8rpx rgba(102,126,234,0.12);
  margin-bottom: 8rpx;
  background: #f0f0f0;
}
.npc-name.data-v-1cf27b2a {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 4rpx;
}
.npc-desc.data-v-1cf27b2a {
  font-size: 22rpx;
  color: #888;
  text-align: center;
  margin-bottom: 6rpx;
}
.left-menu-btn.data-v-1cf27b2a {
  position: fixed;
  left: 0;
  z-index: 1001;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 0 16rpx 16rpx 0;
  padding: 18rpx 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
  border: none;
  transition: top 0.15s;
  -webkit-user-select: none;
          user-select: none;
}
.left-menu-btn.data-v-1cf27b2a:active {
  transform: scale(0.95);
}
.left-menu-bar.data-v-1cf27b2a {
  position: fixed;
  left: 90rpx;
  z-index: 1002;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  border-radius: 25rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  padding: 8rpx 16rpx;
  height: 80rpx;
  min-width: 280rpx;
  animation: leftMenuPop-1cf27b2a 0.3s cubic-bezier(.4,0,.2,1);
  transition: top 0.15s;
}
@keyframes leftMenuPop-1cf27b2a {
0% { opacity: 0; transform: scale(0.92) translateX(-24rpx);}
100% { opacity: 1; transform: scale(1) translateX(0);}
}
.left-menu-btn-item.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8rpx;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  outline: none;
  transition: all 0.3s ease;
  height: 56rpx;
  min-width: 80rpx;
  box-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.3);
  flex-shrink: 0;
}
.left-menu-btn-item.data-v-1cf27b2a:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(102, 126, 234, 0.4);
}
.left-menu-btn-icon.data-v-1cf27b2a {
  font-size: 28rpx;
  margin-right: 6rpx;
}
.left-menu-btn-text.data-v-1cf27b2a {
  font-size: 24rpx;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.left-menu-mask.data-v-1cf27b2a {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 1000;
  background: transparent;
}
.healing-meditation-msg-list.data-v-1cf27b2a {
  max-height: 180px;
  overflow-y: auto;
}

/* 弹窗样式 */
.modal-overlay.data-v-1cf27b2a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.ranking-modal.data-v-1cf27b2a, .redeem-modal.data-v-1cf27b2a {
	background: #fff;
	border-radius: 20rpx;
	padding: 0;
	margin: 20rpx;
	width: calc(100vw - 40rpx);
	max-width: 90vw;
	max-height: 90vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}
.modal-header.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}
.modal-title.data-v-1cf27b2a {
	font-size: 32rpx;
	font-weight: bold;
}
.modal-close.data-v-1cf27b2a {
	font-size: 40rpx;
	font-weight: bold;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
}

/* 排行榜样式 */
.ranking-tabs.data-v-1cf27b2a {
	display: flex;
	background: #f8f9fa;
	padding: 10rpx;
}
.ranking-tab.data-v-1cf27b2a {
	flex: 1;
	text-align: center;
	padding: 20rpx 10rpx;
	border-radius: 10rpx;
	font-size: 26rpx;
	color: #666;
	transition: all 0.3s ease;
}
.ranking-tab.active.data-v-1cf27b2a {
	background: #667eea;
	color: #fff;
}
.ranking-content.data-v-1cf27b2a {
	flex: 1;
	padding: 20rpx;
	overflow-y: auto;
	max-height: 1200rpx;
	min-height: 800rpx;
}
.loading-text.data-v-1cf27b2a, .empty-text.data-v-1cf27b2a {
	text-align: center;
	color: #999;
	padding: 60rpx 0;
	font-size: 28rpx;
}
.ranking-list.data-v-1cf27b2a {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}
.ranking-item.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	border-left: 6rpx solid #e9ecef;
}
.ranking-item.current-player.data-v-1cf27b2a {
	background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
	border-left-color: #17a2b8;
}
.ranking-item.top-rank.data-v-1cf27b2a {
	border-left-width: 8rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.ranking-item.first-place.data-v-1cf27b2a {
	background: linear-gradient(135deg, #fff9c4 0%, #fff3a0 100%);
	border-left-color: #ffd700;
}
.ranking-item.second-place.data-v-1cf27b2a {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-left-color: #c0c0c0;
}
.ranking-item.third-place.data-v-1cf27b2a {
	background: linear-gradient(135deg, #fdf2e9 0%, #fce4d6 100%);
	border-left-color: #cd7f32;
}
.rank-number.data-v-1cf27b2a {
	width: 80rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}
.rank-number.top-three.data-v-1cf27b2a {
	width: 90rpx;
}
.rank-icon.data-v-1cf27b2a {
	font-size: 32rpx;
}
.rank-icon.gold.data-v-1cf27b2a {
	filter: drop-shadow(0 0 8rpx rgba(255, 215, 0, 0.6));
}
.rank-icon.silver.data-v-1cf27b2a {
	filter: drop-shadow(0 0 8rpx rgba(192, 192, 192, 0.6));
}
.rank-icon.bronze.data-v-1cf27b2a {
	filter: drop-shadow(0 0 8rpx rgba(205, 127, 50, 0.6));
}
.rank-text.data-v-1cf27b2a {
	color: #666;
	font-size: 26rpx;
}
.player-info.data-v-1cf27b2a {
	flex: 1;
	margin-left: 20rpx;
}
.player-name.data-v-1cf27b2a {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}
.ranking-value.data-v-1cf27b2a {
	font-size: 26rpx;
	font-weight: bold;
	color: #e74c3c;
}
.my-ranking.data-v-1cf27b2a {
	padding: 20rpx;
	background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
	color: #fff;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.my-ranking-title.data-v-1cf27b2a {
	font-size: 26rpx;
}
.my-ranking-value.data-v-1cf27b2a {
	font-size: 28rpx;
	font-weight: bold;
}

/* 兑换码样式 */
.redeem-input-section.data-v-1cf27b2a {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
}
.redeem-input.data-v-1cf27b2a {
	flex: 1;
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 15rpx;
	font-size: 28rpx;
	background: #fff;
}
.redeem-input.data-v-1cf27b2a:focus {
	border-color: #667eea;
}
.redeem-btn.data-v-1cf27b2a {
	width: 160rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	border: none;
	border-radius: 15rpx;
	font-size: 26rpx;
	font-weight: bold;
}
.redeem-btn.disabled.data-v-1cf27b2a {
	background: #ccc;
	color: #999;
}
.redeem-tips.data-v-1cf27b2a {
	padding: 0 30rpx 20rpx;
	background: #f8f9fa;
}
.tips-item.data-v-1cf27b2a {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}
.reward-tag.data-v-1cf27b2a {
	font-size: 22rpx;
	color: #28a745;
	background: rgba(40, 167, 69, 0.1);
	padding: 5rpx 10rpx;
	border-radius: 8rpx;
}

/* 侠客按钮样式 - 与NPC按钮保持一致 */
.player-sidebar-btn.data-v-1cf27b2a {
	position: fixed;
	right: 0;
	z-index: 1001;
	background: #ff6b6b;
	color: #fff;
	border-radius: 16rpx 0 0 16rpx;
	padding: 18rpx 12rpx;
	font-size: 28rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
	transition: top 0.15s;
	-webkit-user-select: none;
	        user-select: none;
}
.player-sidebar-btn.data-v-1cf27b2a:active {
	transform: scale(0.95);
}

/* 侠客侧边栏样式 */
.player-sidebar-mask.data-v-1cf27b2a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
}
.player-sidebar.data-v-1cf27b2a {
	position: fixed;
	top: 0;
	right: -600rpx;
	width: 600rpx;
	height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	transition: right 0.3s ease;
	z-index: 1001;
	display: flex;
	flex-direction: column;
}
.player-sidebar-show.data-v-1cf27b2a {
	right: 0;
}
.player-sidebar-header.data-v-1cf27b2a {
	padding: 40rpx 30rpx 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.player-sidebar-close.data-v-1cf27b2a {
	background: none;
	border: none;
	color: white;
	font-size: 40rpx;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.player-sidebar-list.data-v-1cf27b2a {
	flex: 1;
	padding: 20rpx;
	overflow-y: auto;
}
.player-item.data-v-1cf27b2a {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
}
.player-avatar.data-v-1cf27b2a {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin-right: 20rpx;
}
.player-info.data-v-1cf27b2a {
	flex: 1;
}
.player-name.data-v-1cf27b2a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}
.player-level.data-v-1cf27b2a {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 5rpx;
}
.player-status.data-v-1cf27b2a {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	display: inline-block;
}
.player-status.online.data-v-1cf27b2a {
	background: #e8f5e8;
	color: #52c41a;
}
.player-status.busy.data-v-1cf27b2a {
	background: #fff7e6;
	color: #fa8c16;
}
.player-status.battle.data-v-1cf27b2a {
	background: #fff1f0;
	color: #f5222d;
}

/* 自己的特殊样式 */
.player-item-self.data-v-1cf27b2a {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
.player-item-self .player-name.data-v-1cf27b2a,
.player-item-self .player-level.data-v-1cf27b2a {
	color: white;
}
.player-name-self.data-v-1cf27b2a {
	position: relative;
}
.self-tag.data-v-1cf27b2a {
	font-size: 24rpx;
	color: #ffd700;
	font-weight: normal;
	margin-left: 10rpx;
}
.no-players.data-v-1cf27b2a {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
}

/* 玩家交互弹窗样式 */
.player-menu-modal-mask.data-v-1cf27b2a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1002;
}
.player-menu-modal.data-v-1cf27b2a {
	background: white;
	border-radius: 20rpx;
	padding: 0;
	margin: 40rpx;
	width: calc(100vw - 80rpx);
	max-width: 600rpx;
}
.player-menu-header.data-v-1cf27b2a {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 30rpx;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.player-menu-title.data-v-1cf27b2a {
	font-size: 36rpx;
	font-weight: bold;
}
.player-menu-close.data-v-1cf27b2a {
	background: none;
	border: none;
	color: white;
	font-size: 40rpx;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
}
.player-menu-info.data-v-1cf27b2a {
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.player-menu-level.data-v-1cf27b2a, .player-menu-status.data-v-1cf27b2a {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
}
.player-menu-actions.data-v-1cf27b2a {
	padding: 30rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}
.player-action-btn.data-v-1cf27b2a {
	flex: 1;
	min-width: 120rpx;
	height: 80rpx;
	border: none;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: white;
	font-weight: bold;
}
.player-action-btn.attack.data-v-1cf27b2a {
	background: linear-gradient(135deg, #f5222d 0%, #cf1322 100%);
}
.player-action-btn.give.data-v-1cf27b2a {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}
.player-action-btn.steal.data-v-1cf27b2a {
	background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
}
.player-action-btn.view.data-v-1cf27b2a {
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

/* 聊天弹窗样式 */
.chat-popup-mask.data-v-1cf27b2a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1003;
}
.chat-popup.data-v-1cf27b2a {
	background: white;
	border-radius: 20rpx;
	margin: 40rpx;
	width: calc(100vw - 80rpx);
	height: calc(100vh - 200rpx);
	max-width: 800rpx;
	display: flex;
	flex-direction: column;
}
.chat-popup-header.data-v-1cf27b2a {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 30rpx;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.chat-popup-title.data-v-1cf27b2a {
	font-size: 36rpx;
	font-weight: bold;
}
.chat-popup-close.data-v-1cf27b2a {
	background: none;
	border: none;
	color: white;
	font-size: 40rpx;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
}
.chat-messages.data-v-1cf27b2a {
	flex: 1;
	padding: 20rpx;
	overflow-y: auto;
	background: #f8f9fa;
}
.chat-message.data-v-1cf27b2a {
	background: white;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 15rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.chat-message-own.data-v-1cf27b2a {
	background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
	border-left: 4rpx solid #667eea;
}

/* 不同聊天类型的样式 */
.chat-message-world.data-v-1cf27b2a {
	border-left: 4rpx solid #409eff;
}
.chat-message-private.data-v-1cf27b2a {
	background: linear-gradient(135deg, #fff3e0, #fce4ec);
	border-left: 4rpx solid #ff9800;
}
.chat-message-rumor.data-v-1cf27b2a {
	background: linear-gradient(135deg, #f3e5f5, #e8f5e8);
	border-left: 4rpx solid #9c27b0;
}
.chat-message-system.data-v-1cf27b2a {
	background: linear-gradient(135deg, #fff8e1, #f1f8e9);
	border-left: 4rpx solid #ffc107;
}
.chat-message-header.data-v-1cf27b2a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}
.chat-message-info.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	gap: 10rpx;
}
.chat-sender.data-v-1cf27b2a {
	font-size: 28rpx;
	font-weight: bold;
	color: #667eea;
}
.chat-sender-own.data-v-1cf27b2a {
	color: #ff6b6b;
}

/* 不同聊天类型的发送者颜色 */
.chat-sender-world.data-v-1cf27b2a {
	color: #409eff;
}
.chat-sender-private.data-v-1cf27b2a {
	color: #ff9800;
}
.chat-sender-rumor.data-v-1cf27b2a {
	color: #9c27b0;
}
.chat-sender-system.data-v-1cf27b2a {
	color: #ffc107;
}
.chat-type-badge.data-v-1cf27b2a {
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	color: white;
	font-weight: bold;
}
.chat-type-private.data-v-1cf27b2a {
	background: #ff9800;
}
.chat-type-rumor.data-v-1cf27b2a {
	background: #9c27b0;
}
.chat-type-system.data-v-1cf27b2a {
	background: #ffc107;
}
.chat-time.data-v-1cf27b2a {
	font-size: 24rpx;
	color: #999;
}
.chat-content.data-v-1cf27b2a {
	font-size: 30rpx;
	color: #333;
	line-height: 1.5;
}
.no-messages.data-v-1cf27b2a {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
}
.chat-input-area.data-v-1cf27b2a {
	padding: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}
.chat-type-selector.data-v-1cf27b2a {
	display: flex;
	justify-content: center;
	margin-bottom: 10rpx;
}
.chat-type-picker.data-v-1cf27b2a {
	width: 200rpx;
}
.chat-type-display.data-v-1cf27b2a {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	border: 2rpx solid #667eea;
	background: white;
	color: #667eea;
	font-size: 24rpx;
}
.picker-arrow.data-v-1cf27b2a {
	font-size: 20rpx;
	color: #667eea;
}
.chat-input-row.data-v-1cf27b2a {
	display: flex;
	gap: 10rpx;
	align-items: center;
}
.chat-target-input.data-v-1cf27b2a {
	width: 150rpx;
	height: 80rpx;
	border: 2rpx solid #ff9800;
	border-radius: 12rpx;
	padding: 0 15rpx;
	font-size: 26rpx;
	color: #ff9800;
}
.chat-input.data-v-1cf27b2a {
	flex: 1;
	height: 80rpx;
	border: 2rpx solid #d9d9d9;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}
.chat-input.data-v-1cf27b2a:focus {
	border-color: #667eea;
}
.chat-send-btn.data-v-1cf27b2a {
	width: 120rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: bold;
}
.chat-send-btn.data-v-1cf27b2a:disabled {
	background: #d9d9d9;
	color: #999;
}

/* 左侧聊天按钮样式 - 与NPC按钮大小一致 */
.left-chat-btn.data-v-1cf27b2a {
	position: fixed;
	left: 0;
	z-index: 1001;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	color: white;
	border-radius: 0 16rpx 16rpx 0;
	padding: 18rpx 12rpx;
	font-size: 28rpx;
	font-weight: bold;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
	border: none;
	transition: top 0.15s;
	-webkit-user-select: none;
	        user-select: none;
}
.left-chat-btn.data-v-1cf27b2a:active {
	transform: scale(0.95);
}
