import sqlite3
import json
import os

db_path = os.path.join(os.path.dirname(__file__), 'game.db')
conn = sqlite3.connect(db_path)
cursor = conn.cursor()

cursor.execute("SELECT id, data FROM players")
rows = cursor.fetchall()
for player_id, data_json in rows:
    data = json.loads(data_json)
    martial_skills = data.get('martial_skills')
    equipped_martial = data.get('equipped_martial')
    changed = False
    if isinstance(martial_skills, list):
        for skill in martial_skills:
            if equipped_martial and skill.get('name') == equipped_martial:
                if not skill.get('equipped'):
                    skill['equipped'] = True
                    changed = True
            else:
                if skill.get('equipped'):
                    skill['equipped'] = False
                    changed = True
    if 'equipped_martial' in data:
        del data['equipped_martial']
        changed = True
    if changed:
        print(f'修正玩家ID {player_id} 的武功装备字段')
        cursor.execute("UPDATE players SET data=? WHERE id=?", (json.dumps(data, ensure_ascii=False), player_id))
conn.commit()
conn.close()
print('所有玩家武功已补全 equipped 字段，并删除 equipped_martial 字段') 