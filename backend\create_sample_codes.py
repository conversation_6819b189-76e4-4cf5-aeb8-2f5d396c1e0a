#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建示例兑换码
用于测试兑换码功能
"""

import asyncio
from redeem_code_manager import RedeemCodeManager

async def create_sample_codes():
    """创建示例兑换码"""
    manager = RedeemCodeManager()
    await manager.init_db()
    
    print("🎁 创建示例兑换码...")
    print("=" * 50)
    
    # 示例1：新手礼包
    await manager.create_code(
        name="新手礼包",
        description="新玩家专属礼包，包含银两和经验",
        rewards=[
            {"type": "money", "name": "银两", "quantity": 1000},
            {"type": "experience", "name": "历练值", "quantity": 500}
        ],
        max_uses=100,
        expires_days=30,
        created_by="system"
    )
    
    # 示例2：每日签到奖励
    await manager.create_code(
        name="每日签到",
        description="每日签到奖励",
        rewards=[
            {"type": "money", "name": "银两", "quantity": 200},
            {"type": "experience", "name": "历练值", "quantity": 100}
        ],
        max_uses=1000,
        expires_days=1,
        created_by="system"
    )
    
    # 示例3：VIP礼包
    await manager.create_code(
        name="VIP礼包",
        description="VIP玩家专属豪华礼包",
        rewards=[
            {"type": "money", "name": "银两", "quantity": 5000},
            {"type": "experience", "name": "历练值", "quantity": 2000}
        ],
        max_uses=50,
        expires_days=7,
        created_by="admin"
    )
    
    # 示例4：节日活动
    await manager.create_code(
        name="春节活动",
        description="春节特别活动奖励",
        rewards=[
            {"type": "money", "name": "银两", "quantity": 8888},
            {"type": "experience", "name": "历练值", "quantity": 1888}
        ],
        max_uses=200,
        expires_days=14,
        created_by="event"
    )
    
    # 示例5：永久兑换码
    await manager.create_code(
        name="永久福利",
        description="永久有效的福利兑换码",
        rewards=[
            {"type": "money", "name": "银两", "quantity": 666},
            {"type": "experience", "name": "历练值", "quantity": 333}
        ],
        max_uses=999,
        expires_days=None,  # 永久有效
        created_by="system"
    )
    
    print("\n✅ 示例兑换码创建完成！")
    print("📋 查看所有兑换码：")
    await manager.list_codes()

if __name__ == "__main__":
    asyncio.run(create_sample_codes())
