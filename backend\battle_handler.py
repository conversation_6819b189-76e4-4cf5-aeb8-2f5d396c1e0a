#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
战斗系统处理模块
负责战斗相关的业务逻辑处理
"""

import random
import logging

try:
    from enhanced_martial_system import get_martial_move, format_battle_description
except ImportError:
    # 如果模块不存在，提供默认实现
    def get_martial_move(martial_name):
        return "普通攻击", f"使用{martial_name}攻击"

    def format_battle_description(desc, attacker, defender, damage=0):
        return f"{attacker}对{defender}{desc}，造成{damage}点伤害"

logger = logging.getLogger(__name__)

class BattleHandler:
    """战斗系统处理器"""
    
    def __init__(self, game_data: dict):
        self.game_data = game_data
    
    def simulate_battle(self, player: dict, enemy: dict) -> dict:
        """模拟战斗，支持敌人武功出招描述"""
        player_hp = player['hp']
        enemy_hp = enemy.get('hp', 100)  # 支持怪物自定义血量
        player_name = player.get('name', '玩家')
        enemy_name = enemy.get('name', enemy.get('id', '敌人'))
        
        # 玩家武功（如有装备武学，否则用基础拳法）
        player_martial = player.get('equipped_martial', '基本拳法')
        
        # 敌人武功（如为人类且有martials则随机选一个，否则用基础拳法）
        enemy_martial = '基本拳法'
        if enemy.get('type') == '人类' and enemy.get('martials'):
            martials = [m.split(':')[0] for m in enemy['martials'].split(',') if m]
            if martials:
                enemy_martial = random.choice(martials)
        
        rounds = []
        
        while player_hp > 0 and enemy_hp > 0:
            # 玩家攻击
            if random.random() < player.get('hit_rate', 0.8):
                # 命中
                move_name, desc = get_martial_move(player_martial)
                player_damage = max(1, player.get('attack', 10) - enemy.get('defense', 5) + random.randint(-3, 3))
                enemy_hp -= player_damage
                desc = format_battle_description(desc, player_name, enemy_name, player_damage)
                rounds.append({
                    'attacker': player_name, 
                    'defender': enemy_name, 
                    'damage': player_damage, 
                    'desc': desc, 
                    'move': move_name, 
                    'martial': player_martial, 
                    'player_hp': max(0, player_hp), 
                    'enemy_hp': max(0, enemy_hp)
                })
            else:
                # 未命中
                rounds.append({
                    'attacker': player_name, 
                    'defender': enemy_name, 
                    'damage': 0, 
                    'desc': f'{player_name}攻击落空了！', 
                    'move': '普通攻击', 
                    'martial': player_martial, 
                    'player_hp': max(0, player_hp), 
                    'enemy_hp': max(0, enemy_hp)
                })
            
            if enemy_hp <= 0:
                break
            
            # 敌人攻击
            if random.random() < enemy.get('hit_rate', 0.7):
                # 命中
                move_name, desc = get_martial_move(enemy_martial)
                enemy_damage = max(1, enemy.get('attack', 8) - player.get('defense', 3) + random.randint(-2, 2))
                player_hp -= enemy_damage
                desc = format_battle_description(desc, enemy_name, player_name, enemy_damage)
                rounds.append({
                    'attacker': enemy_name, 
                    'defender': player_name, 
                    'damage': enemy_damage, 
                    'desc': desc, 
                    'move': move_name, 
                    'martial': enemy_martial, 
                    'player_hp': max(0, player_hp), 
                    'enemy_hp': max(0, enemy_hp)
                })
            else:
                # 未命中
                rounds.append({
                    'attacker': enemy_name, 
                    'defender': player_name, 
                    'damage': 0, 
                    'desc': f'{enemy_name}攻击落空了！', 
                    'move': '普通攻击', 
                    'martial': enemy_martial, 
                    'player_hp': max(0, player_hp), 
                    'enemy_hp': max(0, enemy_hp)
                })
        
        return {
            'win': player_hp > 0,
            'player_hp': max(0, player_hp),
            'enemy_hp': max(0, enemy_hp),
            'battle_log': rounds
        }
    
    def calculate_battle_rewards(self, enemy: dict, player: dict) -> dict:
        """计算战斗奖励"""
        rewards = {}
        
        # 基础经验值奖励
        base_exp = enemy.get('experience_reward', 50)
        experience_gain_bonus = player.get('experience_gain_bonus', 0)
        exp_reward = base_exp + experience_gain_bonus
        rewards['历练值'] = exp_reward
        
        # 基础银两奖励
        base_money = enemy.get('money_reward', 20)
        money_gain_bonus = player.get('money_gain_bonus', 0)
        money_reward = base_money + money_gain_bonus
        rewards['银两'] = money_reward
        
        # 物品掉落
        if enemy.get('drops'):
            for drop in enemy['drops']:
                if random.random() < drop.get('chance', 0.1):
                    if '物品' not in rewards:
                        rewards['物品'] = []
                    rewards['物品'].append({
                        'id': drop['item_id'],
                        'quantity': drop.get('quantity', 1)
                    })
        
        return rewards
    
    def apply_battle_result(self, player: dict, battle_result: dict, rewards: dict):
        """应用战斗结果到玩家数据"""
        # 更新玩家血量
        player['hp'] = battle_result['player_hp']
        
        # 应用奖励
        if battle_result['win']:
            # 历练值
            if '历练值' in rewards:
                player['experience'] = player.get('experience', 0) + rewards['历练值']
            
            # 银两
            if '银两' in rewards:
                player['money'] = player.get('money', 0) + rewards['银两']
            
            # 物品奖励在上层处理
    
    def get_battle_summary(self, battle_result: dict, rewards: dict) -> str:
        """获取战斗总结"""
        if battle_result['win']:
            summary = "战斗胜利！"
            if '历练值' in rewards:
                summary += f" 获得{rewards['历练值']}点历练值"
            if '银两' in rewards:
                summary += f"，{rewards['银两']}两银子"
            if '物品' in rewards:
                items = rewards['物品']
                if isinstance(items, list):
                    item_names = [f"{item.get('name', item.get('id'))} x{item.get('quantity', 1)}" for item in items]
                    summary += f"，物品：{', '.join(item_names)}"
        else:
            summary = "战斗失败！"
        
        return summary
    
    def check_player_can_battle(self, player: dict) -> tuple:
        """检查玩家是否可以战斗
        
        Returns:
            tuple: (can_battle: bool, message: str)
        """
        # 检查血量
        if player.get('hp', 0) <= 0:
            return False, "血量不足，无法战斗！请先疗伤。"
        
        # 检查体力
        if player.get('energy', 0) <= 0:
            return False, "体力不足，无法战斗！请先休息。"
        
        return True, ""
    
    def calculate_escape_success_rate(self, player: dict, enemy: dict) -> float:
        """计算逃跑成功率"""
        # 基础逃跑成功率
        base_rate = 0.6
        
        # 根据玩家轻功等级调整
        player_agility = player.get('dodge', 0)
        enemy_agility = enemy.get('dodge', 0)
        
        agility_bonus = (player_agility - enemy_agility) * 0.1
        
        # 根据血量比例调整（血量越少越容易逃跑）
        hp_ratio = player.get('hp', 100) / player.get('max_hp', 100)
        hp_bonus = (1 - hp_ratio) * 0.2
        
        final_rate = base_rate + agility_bonus + hp_bonus
        
        # 限制在0.1-0.9之间
        return max(0.1, min(0.9, final_rate))
