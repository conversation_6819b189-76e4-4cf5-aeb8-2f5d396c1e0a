# -*- coding: utf-8 -*-
"""
自动导出所有武功静态内容到wugong.txt
包括基础、武器、高级、绝学、轻功等，保留是否秘籍字段
"""
import os
import sys
import importlib.util

# 动态导入模块
MODULES = [
    'martial_system',
    'enhanced_martial_system',
]

martials = {}

# 读取martial_system.py
spec1 = importlib.util.spec_from_file_location('martial_system', os.path.join(os.path.dirname(__file__), 'martial_system.py'))
martial_system = importlib.util.module_from_spec(spec1)
spec1.loader.exec_module(martial_system)

# 读取enhanced_martial_system.py
spec2 = importlib.util.spec_from_file_location('enhanced_martial_system', os.path.join(os.path.dirname(__file__), 'enhanced_martial_system.py'))
enhanced_martial_system = importlib.util.module_from_spec(spec2)
spec2.loader.exec_module(enhanced_martial_system)

# 统一收集所有武功
# 1. martial_system.py
if hasattr(martial_system, 'MARTIAL_SKILLS'):
    for skill in martial_system.MARTIAL_SKILLS:
        name = skill['name']
        martials[name] = {
            'name': name,
            'school': skill.get('school', '无门派'),
            'level': skill.get('level', ''),
            'is_secret': '是' if skill.get('is_secret', False) else '否',
            'desc': skill.get('desc', ''),
            'moves': [],
            'move_descs': {},
        }

# 2. enhanced_martial_system.py
for martial_dict in [
    getattr(enhanced_martial_system, 'BASIC_MARTIALS', {}),
    getattr(enhanced_martial_system, 'WEAPON_MARTIALS', {}),
    getattr(enhanced_martial_system, 'ADVANCED_MARTIALS', {}),
    getattr(enhanced_martial_system, 'LIGHTNESS_MARTIALS', {}),
]:
    for name, info in martial_dict.items():
        martials[name] = {
            'name': name,
            'school': info.get('school', '无门派'),
            'level': info.get('level', ''),
            'is_secret': '是' if info.get('is_secret', False) or info.get('level', '') in ['绝学', '高级'] else '否',
            'desc': info.get('description', ''),
            'moves': info.get('moves', []),
            'move_descs': info.get('move_descriptions', {}),
        }

# 导出为txt
lines = []
for name, info in martials.items():
    lines.append(f"{name}={info['name']}")
    lines.append(f"{name}_门派={info['school']}")
    lines.append(f"{name}_品阶={info['level']}")
    lines.append(f"{name}_是否秘籍={info['is_secret']}")
    lines.append(f"{name}_描述={info['desc']}")
    if info['moves']:
        lines.append(f"{name}_招式={' '.join(info['moves'])}")
        for move in info['moves']:
            desc = info['move_descs'].get(move, '')
            lines.append(f"{move}={desc}")
    lines.append('')

with open(os.path.join(os.path.dirname(__file__), '../wugong.txt'), 'w', encoding='utf-8') as f:
    f.write('\n'.join(lines))

print('武功配置已导出到 wugong.txt') 