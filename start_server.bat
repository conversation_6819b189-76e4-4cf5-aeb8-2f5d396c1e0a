@echo off
echo 🎮 仗剑江湖行 - 启动后端服务器
echo ======================================

cd backend

echo 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo 安装依赖包...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo 启动WebSocket服务器...
echo 服务器地址: ws://localhost:8080/ws
echo 按 Ctrl+C 停止服务器
echo.

python server.py

pause 