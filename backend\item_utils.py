from item_system import item_system
import uuid

def standardize_item(item):
    """
    物品字段标准化：type字段为英文，unique_id补全
    """
    if not item or 'id' not in item:
        return item
    item_obj = item_system.get_item(item['id'])
    if item_obj:
        item['type'] = item_obj.type.value
    if 'unique_id' not in item or not item['unique_id']:
        item['unique_id'] = str(uuid.uuid4())
    return item 