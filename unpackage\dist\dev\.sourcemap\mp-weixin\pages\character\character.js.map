{"version": 3, "file": "character.js", "sources": ["pages/character/character.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2hhcmFjdGVyL2NoYXJhY3Rlci52dWU"], "sourcesContent": ["<template>\n\t<view class=\"character-compact\">\n\t\t<!-- 加载状态 -->\n\t\t<view v-if=\"isLoading\" class=\"loading-overlay\">\n\t\t\t<view class=\"loading-content\">\n\t\t\t\t<text class=\"loading-text\">{{ loadingText }}</text>\n\t\t\t</view>\n\t\t</view>\n\t\t<!-- 头部信息 - 更紧凑 -->\n\t\t<view class=\"header-section\">\n\t\t\t<view class=\"avatar-section\">\n\t\t\t\t<image class=\"avatar\" src=\"/static/logo.png\" />\n\t\t\t\t<view class=\"level-badge\">Lv.{{ playerData.level || 1 }}</view>\n\t\t\t</view>\n\t\t\t<view class=\"info-section\">\n\t\t\t\t<view class=\"name-row\">\n\t\t\t\t\t<text class=\"name\">{{ playerData.character_name || playerData.name || '未知' }}</text>\n\t\t\t\t\t<text class=\"gender\">{{ playerData.gender || '男' }}</text>\n\t\t\t\t\t<text class=\"experience\">📈 历练 {{ formatExperience(playerData.experience || 0) }}</text>\n\t\t\t\t\t<text class=\"fortune\">💎 富源 {{ playerData.fortune || 1 }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"stats-row\">\n\t\t\t\t\t<text class=\"money\">💰 {{ formatMoney(playerData.money || 0) }}</text>\n\t\t\t\t\t<text class=\"skill-points\">🎯 武学 {{ playerData.skill_points || 0 }}</text>\n\t\t\t\t\t<text class=\"realm\">⚔️ {{ playerData.realm_info?.current_realm || '初出茅庐' }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 属性网格 - 更紧凑 -->\n\t\t<view class=\"attributes-grid\">\n\t\t\t<text class=\"section-title\">📊 基础属性</text>\n\t\t\t<view class=\"attr-item\">\n\t\t\t\t<text class=\"attr-label\">气血</text>\n\t\t\t\t<text class=\"attr-value\">{{ Math.floor(playerData.max_hp || 0) }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"attr-item\">\n\t\t\t\t<text class=\"attr-label\">内力</text>\n\t\t\t\t<text class=\"attr-value\">{{ Math.floor(playerData.max_mp || 0) }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"attr-item\">\n\t\t\t\t<text class=\"attr-label\">体力</text>\n\t\t\t\t<text class=\"attr-value\">{{ Math.floor(playerData.max_energy || 0) }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"attr-item\">\n\t\t\t\t<text class=\"attr-label\">精力</text>\n\t\t\t\t<text class=\"attr-value\">{{ Math.floor(playerData.max_spirit || playerData.max_energy || 0) }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"attr-item\">\n\t\t\t\t<text class=\"attr-label\">攻击</text>\n\t\t\t\t<text class=\"attr-value\">{{ Math.floor(playerData.attack || 0) }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"attr-item\">\n\t\t\t\t<text class=\"attr-label\">防御</text>\n\t\t\t\t<text class=\"attr-value\">{{ Math.floor(playerData.defense || 0) }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"attr-item\">\n\t\t\t\t<text class=\"attr-label\">闪避</text>\n\t\t\t\t<text class=\"attr-value\">{{ (playerData.dodge || 0).toFixed(1) }}%</text>\n\t\t\t</view>\n\t\t\t<view class=\"attr-item\">\n\t\t\t\t<text class=\"attr-label\">暴击</text>\n\t\t\t\t<text class=\"attr-value\">{{ (playerData.crit || 0).toFixed(1) }}%</text>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 增益摘要展示 -->\n\t\t<view v-if=\"bonusSummary.total\" style=\"background:#e8f4ff;padding:10rpx;margin:10rpx 0;font-size:22rpx;color:#333;\">\n\t\t\t<text>装备加成：攻击{{ Math.floor(bonusSummary.equipment.attack || 0) }}，防御{{ Math.floor(bonusSummary.equipment.defense || 0) }}，气血{{ Math.floor(bonusSummary.equipment.hp || 0) }}</text>\n\t\t\t<br/>\n\t\t\t<text>武功加成：攻击{{ Math.floor(bonusSummary.martial.attack || 0) }}，防御{{ Math.floor(bonusSummary.martial.defense || 0) }}，气血{{ Math.floor(bonusSummary.martial.hp || 0) }}</text>\n\t\t\t<br/>\n\t\t\t<text>总加成：攻击{{ Math.floor(bonusSummary.total.attack || 0) }}，防御{{ Math.floor(bonusSummary.total.defense || 0) }}，气血{{ Math.floor(bonusSummary.total.hp || 0) }}</text>\n\t\t</view>\n\n\t\t<!-- 调试信息 -->\n\t\t<view style=\"background:#f0f0f0;padding:10rpx;margin:10rpx;font-size:20rpx;color:#666;\">\n\t\t\t<text>调试信息 - 增益后属性:</text>\n\t\t\t<view>max_hp: {{ Math.floor(playerData.max_hp || 0) }} <!-- 增益后最大气血 --></view>\n\t\t\t<view>max_mp: {{ Math.floor(playerData.max_mp || 0) }} <!-- 增益后最大内力 --></view>\n\t\t\t<view>max_energy: {{ Math.floor(playerData.max_energy || 0) }} <!-- 增益后最大体力 --></view>\n\t\t\t<view>max_spirit: {{ Math.floor(playerData.max_spirit || 0) }} <!-- 增益后最大精力 --></view>\n\t\t\t<view>attack: {{ Math.floor(playerData.attack || 0) }} <!-- 增益后攻击 --></view>\n\t\t\t<view>defense: {{ Math.floor(playerData.defense || 0) }} <!-- 增益后防御 --></view>\n\t\t\t<view>dodge: {{ (playerData.dodge || 0).toFixed(1) }} <!-- 增益后闪避 --></view>\n\t\t\t<view>crit: {{ (playerData.crit || 0).toFixed(1) }} <!-- 增益后暴击 --></view>\n\t\t</view>\n\n\t\t<!-- 天赋属性 - 紧凑展示 -->\n\t\t<view class=\"talent-section\">\n\t\t\t<text class=\"section-title\">天赋属性</text>\n\t\t\t<view class=\"talent-list\" style=\"display: flex; flex-direction: row; justify-content: space-between; align-items: center;\">\n\t\t\t\t<view class=\"attr-item\" style=\"flex: 1 1 0; text-align: center;\" v-for=\"item in talentArrNoFortune\" :key=\"item.label\" @click=\"showTalentDetail(item)\">\n\t\t\t\t\t<text class=\"attr-label\">{{ item.label }}</text>\n\t\t\t\t\t<text class=\"attr-value\">{{ Math.floor(item.value || 0) }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 天赋详情弹窗 -->\n\t\t<view v-if=\"showTalentModal\" class=\"talent-modal-mask\" @click=\"closeTalentModal\">\n\t\t\t<view class=\"talent-modal-content\" @click.stop>\n\t\t\t\t<view class=\"talent-modal-header\">\n\t\t\t\t\t<text class=\"talent-modal-title\">{{ selectedTalent.label }}详情</text>\n\t\t\t\t\t<text class=\"talent-modal-close\" @click=\"closeTalentModal\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"talent-modal-body\">\n\t\t\t\t\t<view class=\"talent-current\">\n\t\t\t\t\t\t<text class=\"talent-current-label\">当前{{ selectedTalent.label }}：</text>\n\t\t\t\t\t\t<text class=\"talent-current-value\">{{ Math.floor(selectedTalent.value || 0) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"talent-effects\" v-if=\"selectedTalent.label === '根骨'\">\n\t\t\t\t\t\t<text class=\"talent-effects-title\">根骨增益效果：</text>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">气血加成：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">+{{ (getConstitutionHpBonus() || 0).toFixed(1) }}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">实际提升：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">+{{ (getConstitutionHpBonusValue() || 0).toFixed(1) }}点</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">体力恢复加成：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">+{{ Math.floor(getConstitutionBonus() || 0) }}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">基础根骨值：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">15点</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">每点根骨增加：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">0.5%气血，2%恢复速度</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">最大加成：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">100%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">当前恢复速度：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">{{ Number(getCurrentEnergyRegen() || 0).toFixed(1) }}/10秒</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"talent-effects\" v-else-if=\"selectedTalent.label === '力量'\">\n\t\t\t\t\t\t<text class=\"talent-effects-title\">力量增益效果：</text>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">攻击力加成：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">+{{ (getStrengthBonus() || 0).toFixed(1) }}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">基础力量值：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">15点</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">每点力量增加：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">0.3%攻击力</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">实际提升：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">+{{ (getStrengthAttackBonus() || 0).toFixed(1) }}点</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"talent-effects\" v-else-if=\"selectedTalent.label === '悟性'\">\n\t\t\t\t\t\t<text class=\"talent-effects-title\">悟性增益效果：</text>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">经验获取加成：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">+{{ (getIntelligenceBonus() || 0).toFixed(1) }}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">基础悟性值：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">15点</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">每点悟性增加：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">0.5%经验获取</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">经验倍率：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">{{ (getIntelligenceMultiplier() || 1.0).toFixed(2) }}x</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"talent-effects\" v-else-if=\"selectedTalent.label === '身法'\">\n\t\t\t\t\t\t<text class=\"talent-effects-title\">身法增益效果：</text>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">防御力加成：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">+{{ (getAgilityDefenseBonus() || 0).toFixed(1) }}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">实际提升：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">+{{ (getAgilityDefenseBonusValue() || 0).toFixed(1) }}点</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">闪避加成：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">+{{ (getAgilityDodgeBonus() || 0).toFixed(1) }}%</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">基础身法值：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">15点</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"talent-effect-item\">\n\t\t\t\t\t\t\t<text class=\"effect-label\">每点身法增加：</text>\n\t\t\t\t\t\t\t<text class=\"effect-value\">0.5%防御力，0.5%闪避</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"talent-modal-footer\">\n\t\t\t\t\t<button class=\"talent-modal-btn\" @click=\"closeTalentModal\">关闭</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 境界进度 - 紧凑 -->\n\t\t<view class=\"realm-section\">\n\t\t\t<view class=\"realm-header\">\n\t\t\t\t<text class=\"realm-title\">下一境界：{{ playerData.realm_info?.next_realm || '不堪一击' }}</text>\n\t\t\t\t<text class=\"realm-progress\">\n\t\t\t\t\t{{ formatExperience(playerData.experience || 0) }} / {{ formatExperience(playerData.realm_info?.current_max || 0) }}\n\t\t\t\t</text>\n\t\t\t</view>\n\t\t\t<view class=\"progress-bar\">\n\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: getRealmProgressPercent() + '%' }\"></view>\n\t\t\t</view>\n\t\t\t<text class=\"realm-bonus\">{{ playerData.realm_info?.next_bonus?.description || '无增益' }}</text>\n\t\t</view>\n\n\t\t<!-- 装备展示 - 紧凑网格 -->\n\t\t<view class=\"equipment-section\">\n\t\t\t<text class=\"section-title\">装备</text>\n\t\t\t<view class=\"equipment-grid\">\n\t\t\t\t<view v-for=\"slot in ['main_hand', 'off_hand', 'helmet', 'armor', 'necklace', 'ring1', 'ring2', 'medal']\" :key=\"slot\" class=\"equip-slot\" @click=\"handleEquipmentClick(slot)\">\n\t\t\t\t\t<template v-if=\"mainEquipments[slot] && mainEquipments[slot].id\">\n\t\t\t\t\t\t<text class=\"equip-icon\">{{ mainEquipments[slot].icon || '📦' }}</text>\n\t\t\t\t\t\t<text class=\"equip-name\">{{ mainEquipments[slot].name }}</text>\n\t\t\t\t\t</template>\n\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t<text class=\"empty-text\">{{ getSlotLabel(slot) }}</text>\n\t\t\t\t\t</template>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 装备详情弹窗 -->\n\t\t<view v-if=\"showEquipmentModal\" class=\"equipment-modal-mask\" @click=\"closeEquipmentModal\">\n\t\t\t<view class=\"equipment-modal-content\" @click.stop>\n\t\t\t\t<view class=\"equipment-modal-header\">\n\t\t\t\t\t<text class=\"equipment-modal-title\">{{ selectedEquipment.name }}</text>\n\t\t\t\t\t<text class=\"equipment-modal-close\" @click=\"closeEquipmentModal\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"equipment-modal-body\">\n\t\t\t\t\t<view class=\"equipment-info\">\n\t\t\t\t\t\t<view class=\"equipment-basic\">\n\t\t\t\t\t\t\t<text class=\"equipment-icon\">{{ selectedEquipment.icon || '📦' }}</text>\n\t\t\t\t\t\t\t<view class=\"equipment-details\">\n\t\t\t\t\t\t\t\t<text class=\"equipment-type\">类型：{{ getTypeText(selectedEquipment.type) }}</text>\n\t\t\t\t\t\t\t\t<text class=\"equipment-quality\">品质：{{ getQualityText(selectedEquipment.quality || selectedEquipment.品质) }}</text>\n\t\t\t\t\t\t\t\t<text class=\"equipment-slot\">槽位：{{ getSlotLabel(selectedSlot) }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"equipment-stats\">\n\t\t\t\t\t\t\t<text class=\"stats-title\">战斗属性</text>\n\t\t\t\t\t\t\t<view class=\"stat-item\" v-if=\"selectedEquipment.attack || selectedEquipment.攻击\">\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">攻击：</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-value\">+{{ selectedEquipment.attack || selectedEquipment.攻击 }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-item\" v-if=\"selectedEquipment.defense || selectedEquipment.防御\">\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">防御：</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-value\">+{{ selectedEquipment.defense || selectedEquipment.防御 }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-item\" v-if=\"selectedEquipment.hp || selectedEquipment.气血\">\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">气血：</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-value\">+{{ selectedEquipment.hp || selectedEquipment.气血 }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-item\" v-if=\"selectedEquipment.mp || selectedEquipment.内力\">\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">内力：</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-value\">+{{ selectedEquipment.mp || selectedEquipment.内力 }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-item\" v-if=\"selectedEquipment.energy || selectedEquipment.精力\">\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">精力：</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-value\">+{{ selectedEquipment.energy || selectedEquipment.精力 }}</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"stat-item\" v-if=\"selectedEquipment.energy_regen\">\n\t\t\t\t\t\t\t\t<text class=\"stat-label\">体力恢复：</text>\n\t\t\t\t\t\t\t\t<text class=\"stat-value\">+{{ selectedEquipment.energy_regen }}/10秒</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\n\t\t\t\t\t\t<view class=\"equipment-description\" v-if=\"selectedEquipment.description || selectedEquipment.描述\">\n\t\t\t\t\t\t\t<text class=\"description-title\">描述</text>\n\t\t\t\t\t\t\t<text class=\"description-text\">{{ selectedEquipment.description || selectedEquipment.描述 }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"equipment-modal-actions\">\n\t\t\t\t\t<button class=\"equipment-action-btn unequip-btn\" @click=\"unequipSelectedEquipment\">\n\t\t\t\t\t\t<text>卸下装备</text>\n\t\t\t\t\t</button>\n\t\t\t\t\t<button class=\"equipment-action-btn close-btn\" @click=\"closeEquipmentModal\">\n\t\t\t\t\t\t<text>关闭</text>\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 操作按钮 - 紧凑 -->\n\t\t<view class=\"action-section\">\n\t\t\t<button class=\"action-btn healing-btn\" @click=\"handleHealing\">\n\t\t\t\t<text>🏥 疗伤</text>\n\t\t\t</button>\n\t\t\t<button class=\"action-btn crafting-btn\" @click=\"handleCrafting\">\n\t\t\t\t<text>⚒️ 打造</text>\n\t\t\t</button>\n\t\t</view>\n\t\t<view class=\"action-section\">\n\t\t\t<button class=\"action-btn breakthrough-btn\" @click=\"handleBreakthrough\" :disabled=\"playerData.experience < (playerData.realm_info?.current_max || 0)\">\n\t\t\t\t<text>🌟 境界突破</text>\n\t\t\t</button>\n\t\t\t<button class=\"action-btn backpack-btn\" @click=\"handleBackpack\">\n\t\t\t\t<text>🎒 背包</text>\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport wsManager from '../../utils/websocket.js'\nimport gameState from '../../utils/gameState.js'\nimport { gameUtils } from '@/utils/gameData.js'\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tisLoading: true,\n\t\t\tloadingText: '正在加载角色数据...',\n\t\t\tplayerData: {\n\t\t\t\tcharacter_name: '',\n\t\t\t\tname: '',\n\t\t\t\tgender: '男',\n\t\t\t\tlevel: 1,\n\t\t\t\thp: 0,\n\t\t\t\tmax_hp: 0,\n\t\t\t\tmp: 0,\n\t\t\t\tmax_mp: 0,\n\t\t\t\tenergy: 0,\n\t\t\t\tmax_energy: 0,\n\t\t\t\tspirit: 0,\n\t\t\t\tmax_spirit: 0,\n\t\t\t\texperience: 0,\n\t\t\t\tmoney: 0,\n\t\t\t\ttalent: {},\n\t\t\t\tdodge: 1,\n\t\t\t\tcrit: 1,\n\t\t\t\tfortune: 1,\n\t\t\t\tequipment: {},\n\t\t\t\tinventory_capacity: 50,\n\t\t\t\trealm_info: {\n\t\t\t\t\tcurrent_realm: '初出茅庐',\n\t\t\t\t\tnext_realm: '不堪一击',\n\t\t\t\t\tcurrent_min: 0,\n\t\t\t\t\tcurrent_max: 5,\n\t\t\t\t\tprogress: 0,\n\t\t\t\t\texperience: 0\n\t\t\t\t},\n\t\t\t\tskill_points: 0,\n\t\t\t\tcurrent_map: null, // 新增：当前地图\n\t\t\t},\n\t\t\tinventoryData: [],\n\t\t\tactiveTab: 'attributes',\n\t\t\tisExpanding: false,\n\t\t\tbonusSummary: {}, // 新增：增益摘要\n\t\t\t// 新增：物品配置\n\t\t\titemsConfig: {},\n\t\t\tmapsConfig: {}, // 新增：地图配置\n\t\t\t// 新增：天赋详情弹窗\n\t\t\tshowTalentModal: false,\n\t\t\tselectedTalent: {},\n\t\t\t// 新增：装备详情弹窗\n\t\t\tshowEquipmentModal: false,\n\t\t\tselectedEquipment: {},\n\t\t\tselectedSlot: '',\n\t\t}\n\t},\n\t\n\tcomputed: {\n\t\tmainEquipments() {\n\t\t\tconst eq = this.playerData.equipment || {};\n\t\t\t// 深拷贝，去除 Proxy 响应式影响\n\t\t\ttry {\n\t\t\t\treturn JSON.parse(JSON.stringify({\n\t\t\t\t\tmain_hand: eq.main_hand || null,\n\t\t\t\t\toff_hand: eq.off_hand || null,\n\t\t\t\t\tarmor: eq.armor || null,\n\t\t\t\t\thelmet: eq.helmet || null,\n\t\t\t\t\tnecklace: eq.necklace || null,\n\t\t\t\t\tring1: eq.ring1 || null,\n\t\t\t\t\tring2: eq.ring2 || null,\n\t\t\t\t\tmedal: eq.medal || null\n\t\t\t\t}));\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('装备数据解析失败:', error);\n\t\t\t\treturn {\n\t\t\t\t\tmain_hand: null,\n\t\t\t\t\toff_hand: null,\n\t\t\t\t\tarmor: null,\n\t\t\t\t\thelmet: null,\n\t\t\t\t\tnecklace: null,\n\t\t\t\t\tring1: null,\n\t\t\t\t\tring2: null,\n\t\t\t\t\tmedal: null\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\t\ttalentArr() {\n\t\t\treturn [\n\t\t\t\t{ label: '力量', value: this.playerData.talent?.力量 ?? 0 },\n\t\t\t\t{ label: '悟性', value: this.playerData.talent?.悟性 ?? 0 },\n\t\t\t\t{ label: '身法', value: this.playerData.talent?.身法 ?? 0 },\n\t\t\t\t{ label: '根骨', value: this.playerData.talent?.根骨 ?? 0 },\n\t\t\t\t{ label: '富源', value: this.playerData.fortune ?? 1 }\n\t\t\t];\n\t\t},\n\t\ttalentArrNoFortune() {\n\t\t\treturn [\n\t\t\t\t{ label: '力量', value: this.playerData.talent?.力量 ?? 0 },\n\t\t\t\t{ label: '悟性', value: this.playerData.talent?.悟性 ?? 0 },\n\t\t\t\t{ label: '身法', value: this.playerData.talent?.身法 ?? 0 },\n\t\t\t\t{ label: '根骨', value: this.playerData.talent?.根骨 ?? 0 }\n\t\t\t];\n\t\t},\n\t\tcurrentMapName() {\n\t\t\tif (!this.mapsConfig || !gameState.player.current_map) return '未知';\n\t\t\tconst map = this.mapsConfig[gameState.player.current_map];\n\t\t\treturn map ? map.name : '未知';\n\t\t},\n\t\tenergyRegenDetails() {\n\t\t\t// 使用后端推送的体力恢复详情数据\n\t\t\treturn this.playerData.energy_regen_details || null;\n\t\t}\n\t},\n\t\n\tonLoad() {\n\t\tconsole.log('角色页面onLoad被调用');\n\t\tthis.setupEventListeners();\n\t\t// 延迟初始化，确保页面完全加载\n\t\tsetTimeout(async () => {\n\t\t\tconsole.log('开始初始化游戏状态');\n\t\t\tawait this.loadMapsConfigSafe(); // 先加载地图配置\n\t\t\tthis.initGameState();\n\t\t}, 100);\n\t\tthis.loadItemsConfig(); // 新增\n\t},\n\t\n\tonShow() {\n\t\tconsole.log('角色页面onShow被调用');\n\t\tconsole.log('WebSocket连接状态:', wsManager.isConnected);\n\t\tconsole.log('gameState认证状态:', gameState.isAuthed);\n\t\tthis.updatePlayerData();\n\t\tthis.fetchBonusSummary(); // 新增：在 onShow 中调用\n\t\t// 如果gameState中没有数据，重新请求\n\t\tif (!gameState.getPlayer()) {\n\t\t\tconsole.log('gameState中没有玩家数据，重新请求');\n\t\t\tif (wsManager.isConnected && gameState.isAuthed) {\n\t\t\t\tgameState.requestAllData();\n\t\t\t} else {\n\t\t\t\tconsole.log('WebSocket未连接或未认证，尝试重新初始化');\n\t\t\t\tthis.initGameState();\n\t\t\t}\n\t\t}\n\t},\n\t\n\tonUnload() {\n\t\tthis.cleanupEventListeners();\n\t\t// 移除gameState更新回调\n\t\tgameState.offUpdate(this.handleStateUpdate);\n\t},\n\t\n\tmethods: {\n\t\t\tasync initGameState() {\n\t\ttry {\n\t\t\tconsole.log('角色页面开始初始化游戏状态...');\n\t\t\tthis.isLoading = true;\n\t\t\tthis.loadingText = '正在初始化游戏状态...';\n\t\t\t\n\t\t\t// 注册状态更新回调\n\t\t\tgameState.onUpdate(this.handleStateUpdate);\n\t\t\tconsole.log('已注册状态更新回调');\n\t\t\t\n\t\t\t// 初始化游戏状态\n\t\t\tawait gameState.init();\n\t\t\tconsole.log('游戏状态初始化完成');\n\t\t\t\n\t\t\t// 检查认证状态\n\t\t\tif (gameState.isAuthed) {\n\t\t\t\tconsole.log('已认证，请求数据');\n\t\t\t\t// 请求所有数据\n\t\t\t\tgameState.requestAllData();\n\t\t\t\tconsole.log('已请求所有数据');\n\t\t\t} else {\n\t\t\t\tconsole.log('未认证，等待认证完成');\n\t\t\t}\n\t\t\t\n\t\t\t// 不在这里停止加载状态，等待数据更新回调\n\t\t\tconsole.log('角色页面初始化完成，等待数据更新...');\n\t\t\t\n\t\t\t// 设置超时机制\n\t\t\tsetTimeout(() => {\n\t\t\t\tif (this.isLoading) {\n\t\t\t\t\tconsole.warn('数据加载超时');\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '数据加载超时，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}, 10000); // 10秒超时\n\t\t} catch (error) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.error('初始化游戏状态失败:', error);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '初始化失败: ' + error.message,\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t}\n\t},\n\t\t\n\t\tupdatePlayerData() {\n\t\t\t// 从gameState获取最新数据\n\t\t\tconst player = gameState.getPlayer();\n\t\t\tif (player) {\n\t\t\t\tthis.playerData = { ...player };\n\t\t\t\tconsole.log('从gameState更新玩家数据:', this.playerData);\n\t\t\t} else {\n\t\t\t\tconsole.warn('gameState中没有玩家数据');\n\t\t\t\t// 如果没有数据，尝试重新请求\n\t\t\t\tif (gameState.isAuthed) {\n\t\t\t\t\tconsole.log('已认证但无数据，重新请求');\n\t\t\t\t\tgameState.requestAllData();\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\tconst inventory = gameState.getInventory();\n\t\t\tif (inventory && Array.isArray(inventory)) {\n\t\t\t\tthis.inventoryData = [...inventory];\n\t\t\t\tconsole.log('从gameState更新背包数据:', this.inventoryData);\n\t\t\t} else {\n\t\t\t\tconsole.warn('gameState中没有背包数据或数据格式错误:', inventory);\n\t\t\t\tthis.inventoryData = [];\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleStateUpdate(type, gameStateInstance) {\n\t\t\tconsole.log('角色页面收到状态更新:', type);\n\t\t\tswitch (type) {\n\t\t\t\tcase 'player':\n\t\t\t\t\tif (gameStateInstance.player) {\n\t\t\t\t\t\tthis.playerData = { ...gameStateInstance.player };\n\t\t\t\t\t\tthis.isLoading = false; // 停止加载状态\n\t\t\t\t\t\tconsole.log('角色数据已更新:', this.playerData);\n\t\t\t\t\t\tconsole.log('基础属性检查:');\n\t\t\t\t\t\tconsole.log('- max_hp:', this.playerData.max_hp);\n\t\t\t\t\t\tconsole.log('- max_mp:', this.playerData.max_mp);\n\t\t\t\t\t\tconsole.log('- max_energy:', this.playerData.max_energy);\n\t\t\t\t\t\tconsole.log('- max_spirit:', this.playerData.max_spirit);\n\t\t\t\t\t\tconsole.log('- attack:', this.playerData.attack);\n\t\t\t\t\t\tconsole.log('- defense:', this.playerData.defense);\n\t\t\t\t\t\tconsole.log('- dodge:', this.playerData.dodge);\n\t\t\t\t\t\tconsole.log('- crit:', this.playerData.crit);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查数据是否为空或undefined\n\t\t\t\t\t\tif (!this.playerData.max_hp && this.playerData.max_hp !== 0) {\n\t\t\t\t\t\t\tconsole.warn('max_hp 为空或undefined');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (!this.playerData.max_mp && this.playerData.max_mp !== 0) {\n\t\t\t\t\t\t\tconsole.warn('max_mp 为空或undefined');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (!this.playerData.max_energy && this.playerData.max_energy !== 0) {\n\t\t\t\t\t\t\tconsole.warn('max_energy 为空或undefined');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (!this.playerData.attack && this.playerData.attack !== 0) {\n\t\t\t\t\t\t\tconsole.warn('attack 为空或undefined');\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (!this.playerData.defense && this.playerData.defense !== 0) {\n\t\t\t\t\t\t\tconsole.warn('defense 为空或undefined');\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 检查数据是否完整\n\t\t\t\t\t\tthis.checkDataCompleteness();\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'inventory':\n\t\t\t\t\tif (gameStateInstance.inventory && Array.isArray(gameStateInstance.inventory)) {\n\t\t\t\t\t\tthis.inventoryData = [...gameStateInstance.inventory];\n\t\t\t\t\t\tconsole.log('背包数据已更新:', this.inventoryData);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('背包数据格式错误:', gameStateInstance.inventory);\n\t\t\t\t\t\tthis.inventoryData = [];\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t},\n\t\t\n\t\tcheckDataCompleteness() {\n\t\t\tconst requiredFields = ['max_hp', 'max_mp', 'max_energy', 'max_spirit', 'attack', 'defense', 'dodge', 'crit'];\n\t\t\tconst missingFields = requiredFields.filter(field => {\n\t\t\t\tconst value = this.playerData[field];\n\t\t\t\treturn value === undefined || value === null || value === '';\n\t\t\t});\n\t\t\t\n\t\t\tif (missingFields.length > 0) {\n\t\t\t\tconsole.warn('缺少基础属性字段:', missingFields);\n\t\t\t\tconsole.warn('当前玩家数据:', this.playerData);\n\t\t\t\t// 如果缺少数据，尝试重新请求\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tconsole.log('尝试重新请求玩家数据');\n\t\t\t\t\tgameState.requestAllData();\n\t\t\t\t}, 1000);\n\t\t\t} else {\n\t\t\t\tconsole.log('基础属性数据完整');\n\t\t\t}\n\t\t},\n\t\t\n\t\tformatMoney(money) {\n\t\t\tif (money >= 1000000000000) {\n\t\t\t\treturn (money / 1000000000000).toFixed(1) + '万亿';\n\t\t\t} else if (money >= 100000000) {\n\t\t\t\treturn (money / 100000000).toFixed(1) + '亿';\n\t\t\t} else if (money >= 10000) {\n\t\t\t\treturn (money / 10000).toFixed(1) + '万';\n\t\t\t} else if (money >= 1000) {\n\t\t\t\treturn money.toLocaleString();\n\t\t\t} else {\n\t\t\t\treturn money.toString();\n\t\t\t}\n\t\t},\n\t\t\n\t\tformatExperience(exp) {\n\t\t\tif (exp >= 1000000000000) {\n\t\t\t\treturn (exp / 1000000000000).toFixed(1) + '万亿';\n\t\t\t} else if (exp >= 100000000) {\n\t\t\t\treturn (exp / 100000000).toFixed(1) + '亿';\n\t\t\t} else if (exp >= 10000) {\n\t\t\t\treturn (exp / 10000).toFixed(1) + '万';\n\t\t\t} else if (exp >= 1000) {\n\t\t\t\treturn exp.toLocaleString();\n\t\t\t} else {\n\t\t\t\treturn exp.toString();\n\t\t\t}\n\t\t},\n\t\t\n\t\tsetupEventListeners() {\n\t\t\tthis.cleanupEventListeners();\n\t\t\t// 用箭头函数缓存，保证this指向组件\n\t\t\tthis._errorHandler = (err) => this.handleError(err);\n\t\t\tthis._authSuccessHandler = (data) => this.handleAuthSuccess(data);\n\t\t\tthis._unequipSuccessHandler = (data) => this.handleUnequipSuccess(data);\n\t\t\tthis._unequipFailedHandler = (data) => this.handleUnequipFailed(data);\n\t\t\tthis._equipSuccessHandler = (data) => this.handleEquipSuccess(data);\n\t\t\tthis._equipFailedHandler = (data) => this.handleEquipFailed(data);\n\t\t\tthis._healingSuccessHandler = (data) => this.handleHealingSuccess(data);\n\t\t\tthis._healingFailedHandler = (data) => this.handleHealingFailed(data);\n\t\t\tthis._expandSuccessHandler = (data) => this.handleExpandSuccess(data);\n\t\t\tthis._expandFailedHandler = (data) => this.handleExpandFailed(data);\n\t\t\tthis._breakthroughSuccessHandler = (data) => this.handleBreakthroughSuccess(data);\n\t\t\tthis._breakthroughFailedHandler = (data) => this.handleBreakthroughFailed(data);\n\t\t\t\n\t\t\t// 移除直接使用wsManager.on的方式，改为在各方法中使用gameUtils.sendMessage\n\t\t},\n\t\t\n\t\tcleanupEventListeners() {\n\t\t\t// 不再需要移除监听器，因为我们不再使用wsManager.on\n\t\t},\n\t\t\n\t\tasync fetchBonusSummary() {\n\t\t\ttry {\n\t\t\t\tconst res = await gameUtils.sendMessage({\n\t\t\t\t\ttype: 'get_bonus_summary',\n\t\t\t\t\tdata: {}\n\t\t\t\t});\n\t\t\t\tif (res.type === 'bonus_summary') {\n\t\t\t\t\tthis.bonusSummary = res.data;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.warn('获取增益摘要失败', e);\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleAuthSuccess(data) {\n\t\t\tconsole.log('角色页面收到认证成功:', data);\n\t\t\t// 认证成功后请求数据\n\t\t\tif (gameState.isAuthed) {\n\t\t\t\tconsole.log('认证成功，请求玩家数据');\n\t\t\t\tgameState.requestAllData();\n\t\t\t} else {\n\t\t\t\tconsole.log('认证成功但gameState未更新，等待状态更新');\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleError(error) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.error('角色页面收到错误:', error);\n\t\t\t// 处理后端返回的错误格式\n\t\t\tconst errorMessage = error.message || '网络错误，请重试';\n\t\t\tuni.showToast({\n\t\t\t\ttitle: errorMessage,\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 处理装备槽点击\n\t\thandleEquipmentClick(slot) {\n\t\t\tconst equipment = this.mainEquipments[slot];\n\t\t\tif (equipment && equipment.id) {\n\t\t\t\t// 有装备，显示详情弹窗\n\t\t\t\tthis.selectedEquipment = { ...equipment };\n\t\t\t\tthis.selectedSlot = slot;\n\t\t\t\tthis.showEquipmentModal = true;\n\t\t\t} else {\n\t\t\t\t// 空槽位，显示提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: `${this.getSlotLabel(slot)}为空`,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 关闭装备详情弹窗\n\t\tcloseEquipmentModal() {\n\t\t\tthis.showEquipmentModal = false;\n\t\t\tthis.selectedEquipment = {};\n\t\t\tthis.selectedSlot = '';\n\t\t},\n\t\t\n\t\t// 卸下选中的装备\n\t\tunequipSelectedEquipment() {\n\t\t\tif (this.selectedSlot) {\n\t\t\t\tconst slot = this.selectedSlot; // 保存槽位信息\n\t\t\t\tthis.closeEquipmentModal();\n\t\t\t\tthis.unequipItem(slot);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 获取装备类型文本\n\t\tgetTypeText(type) {\n\t\t\tconst typeMap = {\n\t\t\t\t'weapon': '武器',\n\t\t\t\t'helmet': '头盔',\n\t\t\t\t'necklace': '项链',\n\t\t\t\t'armor': '衣服',\n\t\t\t\t'cloak': '披风',\n\t\t\t\t'pants': '裤子',\n\t\t\t\t'shoes': '鞋子',\n\t\t\t\t'bracelet': '手镯',\n\t\t\t\t'ring': '戒指',\n\t\t\t\t'shield': '盾牌',\n\t\t\t\t'medal': '勋章',\n\t\t\t\t'accessory': '饰品'\n\t\t\t};\n\t\t\treturn typeMap[type] || type;\n\t\t},\n\t\t\n\t\t// 获取装备品质文本\n\t\tgetQualityText(quality) {\n\t\t\tconst qualityMap = {\n\t\t\t\t'common': '普通',\n\t\t\t\t'uncommon': '优秀',\n\t\t\t\t'rare': '稀有',\n\t\t\t\t'epic': '史诗',\n\t\t\t\t'legendary': '传说',\n\t\t\t\t'mythic': '神话'\n\t\t\t};\n\t\t\treturn qualityMap[quality] || '普通';\n\t\t},\n\t\t\n\t\tselectEquipment(slot) {\n\t\t\tconst item = this.equipment[slot];\n\t\t\tif (item) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: item.name,\n\t\t\t\t\tcontent: `类型：${item.type}\\n品质：${item.quality}\\n攻击：${item.attack || 0}\\n防御：${item.defense || 0}`,\n\t\t\t\t\tshowCancel: true,\n\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\tconfirmText: '卸下',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tthis.unequipItem(slot);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '该槽位为空',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\tselectInventoryItem(index) {\n\t\t\tconst item = this.inventoryData[index];\n\t\t\tif (item) {\n\t\t\t\t// 检查是否为装备类型\n\t\t\t\tconst isEquipment = ['weapon', 'helmet', 'necklace', 'armor', 'cloak', 'pants', 'shoes', 'bracelet', 'ring'].includes(item.type);\n\t\t\t\t\n\t\t\t\tif (isEquipment) {\n\t\t\t\t\t// 装备类型，显示装备选项\n\t\t\t\t\tuni.showActionSheet({\n\t\t\t\t\t\titemList: ['装备', '使用', '取消'],\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.tapIndex === 0) {\n\t\t\t\t\t\t\t\tthis.equipItem(index);\n\t\t\t\t\t\t\t} else if (res.tapIndex === 1) {\n\t\t\t\t\t\t\t\tthis.useItem(index);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\t// 非装备类型，直接使用\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: item.name,\n\t\t\t\t\t\tcontent: `类型：${item.type}\\n品质：${item.quality}\\n数量：${item.quantity}`,\n\t\t\t\t\t\tshowCancel: true,\n\t\t\t\t\t\tcancelText: '取消',\n\t\t\t\t\t\tconfirmText: '使用',\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t\tthis.useItem(index);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\tasync unequipItem(slot) {\n\t\t\ttry {\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tthis.loadingText = '正在卸下装备...';\n\t\t\t\t\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\n\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\ttype: 'unequip',\n\t\t\t\t\tdata: { slot }\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tthis.isLoading = false;\n\t\t\t\t\n\t\t\t\tif (response.type === 'unequip_success') {\n\t\t\t\t\tconsole.log('卸下装备成功:', response.data);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.data.message || '卸下装备成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t// 更新玩家数据\n\t\t\t\t\tthis.updatePlayerData();\n\t\t\t\t} else if (response.type === 'unequip_failed') {\n\t\t\t\t\tconsole.error('卸下装备失败:', response.data);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.data.message || '卸下装备失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else if (response.type === 'error') {\n\t\t\t\t\tthis.handleError(response.data);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('卸下装备失败:', error);\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '卸下装备失败: ' + (error.message || '未知错误'),\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\tasync equipItem(index) {\n\t\t\tconst item = this.inventoryData[index];\n\t\t\tif (!item) return;\n\t\t\t\n\t\t\t// 根据物品类型确定装备槽\n\t\t\tconst slotMapping = {\n\t\t\t\t'weapon': ['main_hand', 'off_hand'],\n\t\t\t\t'helmet': ['helmet'],\n\t\t\t\t'necklace': ['necklace'],\n\t\t\t\t'armor': ['armor'],\n\t\t\t\t'cloak': ['cloak'],\n\t\t\t\t'pants': ['pants'],\n\t\t\t\t'shoes': ['shoes'],\n\t\t\t\t'ring': ['ring1', 'ring2']\n\t\t\t};\n\t\t\t\n\t\t\tconst possibleSlots = slotMapping[item.type] || [];\n\t\t\tif (possibleSlots.length === 0) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '无法装备此物品',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 如果有多个可能的槽位，让用户选择\n\t\t\tif (possibleSlots.length > 1) {\n\t\t\t\tconst slotNames = {\n\t\t\t\t\t'main_hand': '主手',\n\t\t\t\t\t'off_hand': '副手',\n\t\t\t\t\t'ring1': '戒指1',\n\t\t\t\t\t'ring2': '戒指2'\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tconst slotOptions = possibleSlots.map(slot => slotNames[slot] || slot);\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: slotOptions,\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tconst selectedSlot = possibleSlots[res.tapIndex];\n\t\t\t\t\t\tthis.doEquipItem(index, selectedSlot);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tthis.doEquipItem(index, possibleSlots[0]);\n\t\t\t}\n\t\t},\n\t\t\n\t\tasync doEquipItem(index, slot) {\n\t\t\ttry {\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tthis.loadingText = '正在装备...';\n\t\t\t\t\n\t\t\t\t// 发送装备请求\n\t\t\t\twsManager.sendMessage('equip_item', {\n\t\t\t\t\titem_index: index,\n\t\t\t\t\tslot_type: slot\n\t\t\t\t});\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('装备失败:', error);\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '装备失败: ' + error.message,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\tuseItem(index) {\n\t\t\t// 使用物品的逻辑\n\t\t\tconsole.log('使用物品:', index);\n\t\t\t// TODO: 发送使用物品的消息到后端\n\t\t},\n\t\t\n\t\tasync handleHealing() {\n\t\t\ttry {\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tthis.loadingText = '正在疗伤...';\n\t\t\t\t\n\t\t\t\t// 发送疗伤请求\n\t\t\t\twsManager.sendMessage('healing', {});\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('疗伤失败:', error);\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '疗伤失败: ' + error.message,\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleHealingSuccess(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.log('疗伤成功:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '疗伤成功',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t\t// 更新玩家数据\n\t\t\tthis.updatePlayerData();\n\t\t},\n\t\t\n\t\thandleHealingFailed(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.error('疗伤失败:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '疗伤失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\thandleUnequipSuccess(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.log('卸下装备成功:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '卸下装备成功',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t\t// 更新玩家数据\n\t\t\tthis.updatePlayerData();\n\t\t},\n\t\t\n\t\thandleUnequipFailed(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.error('卸下装备失败:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '卸下装备失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\thandleEquipSuccess(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.log('装备成功:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '装备成功',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t\t// 更新玩家数据\n\t\t\tthis.updatePlayerData();\n\t\t},\n\t\t\n\t\thandleEquipFailed(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.error('装备失败:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '装备失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\tasync expandInventory() {\n\t\t\ttry {\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tthis.loadingText = '正在扩充背包...';\n\t\t\t\t\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\n\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\ttype: 'expand_inventory',\n\t\t\t\t\tdata: {}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tthis.isLoading = false;\n\t\t\t\t\n\t\t\t\tif (response.type === 'expand_success') {\n\t\t\t\t\tconsole.log('扩充背包成功:', response.data);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.data.message || '扩充背包成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t// 更新背包容量\n\t\t\t\t\tif (response.data.capacity) {\n\t\t\t\t\t\tthis.inventoryCapacity = response.data.capacity;\n\t\t\t\t\t}\n\t\t\t\t\t// 更新玩家数据\n\t\t\t\t\tthis.updatePlayerData();\n\t\t\t\t} else if (response.type === 'expand_failed') {\n\t\t\t\t\tconsole.error('扩充背包失败:', response.data);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.data.message || '扩充背包失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else if (response.type === 'error') {\n\t\t\t\t\tthis.handleError(response.data);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('扩充背包失败:', error);\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '扩充背包失败: ' + (error.message || '未知错误'),\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleExpandSuccess(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.log('扩充背包成功:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '扩充背包成功',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t\t// 更新背包容量\n\t\t\tif (data.capacity) {\n\t\t\t\tthis.inventoryCapacity = data.capacity;\n\t\t\t}\n\t\t\t// 更新玩家数据\n\t\t\tthis.updatePlayerData();\n\t\t},\n\t\t\n\t\thandleExpandFailed(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.error('扩充背包失败:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '扩充背包失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\thandleBreakthroughSuccess(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.log('境界突破成功:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '境界突破成功！',\n\t\t\t\ticon: 'success'\n\t\t\t});\n\t\t\t// 更新玩家数据\n\t\t\tthis.updatePlayerData();\n\t\t},\n\t\t\n\t\thandleBreakthroughFailed(data) {\n\t\t\tthis.isLoading = false;\n\t\t\tconsole.error('境界突破失败:', data);\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '境界突破失败',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\thandleCrafting() {\n\t\t\t// 跳转到打造页面\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/crafting/crafting'\n\t\t\t});\n\t\t},\n\t\t\n\t\thandleBreakthrough() {\n\t\t\t// 境界突破功能\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '境界突破',\n\t\t\t\tcontent: '是否尝试突破当前境界？需要消耗大量历练值。',\n\t\t\t\tshowCancel: true,\n\t\t\t\tcancelText: '取消',\n\t\t\t\tconfirmText: '突破',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tthis.attemptBreakthrough();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\tasync attemptBreakthrough() {\n\t\t\ttry {\n\t\t\t\tthis.isLoading = true;\n\t\t\t\tthis.loadingText = '正在突破境界...';\n\t\t\t\t\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\n\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\ttype: 'realm_breakthrough',\n\t\t\t\t\tdata: {}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tthis.isLoading = false;\n\t\t\t\t\n\t\t\t\tif (response.type === 'breakthrough_success') {\n\t\t\t\t\tconsole.log('境界突破成功:', response.data);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.data.message || '境界突破成功！',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\t\t\t\t\t// 更新玩家数据\n\t\t\t\t\tthis.updatePlayerData();\n\t\t\t\t} else if (response.type === 'breakthrough_failed') {\n\t\t\t\t\tconsole.error('境界突破失败:', response.data);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: response.data.message || '境界突破失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t} else if (response.type === 'error') {\n\t\t\t\t\tthis.handleError(response.data);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('境界突破失败:', error);\n\t\t\t\tthis.isLoading = false;\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '境界突破失败: ' + (error.message || '未知错误'),\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleBackpack() {\n\t\t\t// 跳转到背包页面\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/character/backpack'\n\t\t\t});\n\t\t},\n\t\t\n\t\tgetItemQualityClass(item) {\n\t\t\tif (!item || !item.quality) return 'quality-normal';\n\t\t\tswitch (item.quality) {\n\t\t\t\tcase 'common': return 'quality-common';\n\t\t\t\tcase 'uncommon': return 'quality-uncommon';\n\t\t\t\tcase 'rare': return 'quality-rare';\n\t\t\t\tcase 'epic': return 'quality-epic';\n\t\t\t\tcase 'legendary': return 'quality-legendary';\n\t\t\t\tdefault: return 'quality-normal';\n\t\t\t}\n\t\t},\n\t\t\n\t\tswitchTab(tab) {\n\t\t\tthis.activeTab = tab;\n\t\t},\n\t\t\n\t\thandleItemClick(item, index) {\n\t\t\t// 处理物品点击的逻辑\n\t\t\tconsole.log('点击了物品:', item, index);\n\t\t},\n\t\t\n\t\tgetTalentLabel(key) {\n\t\t\tconst labels = {\n\t\t\t\t'力量': '力量',\n\t\t\t\t'悟性': '悟性',\n\t\t\t\t'身法': '身法',\n\t\t\t\t'根骨': '根骨'\n\t\t\t};\n\t\t\treturn labels[key] || key;\n\t\t},\n\t\t\n\t\tgetSlotLabel(slot) {\n\t\t\tconst slotMap = {\n\t\t\t\tmain_hand: '主手',\n\t\t\t\toff_hand: '副手',\n\t\t\t\tarmor: '衣服',\n\t\t\t\thelmet: '头盔',\n\t\t\t\tnecklace: '项链',\n\t\t\t\tring1: '戒指1',\n\t\t\t\tring2: '戒指2',\n\t\t\t\tmedal: '勋章'\n\t\t\t};\n\t\t\treturn slotMap[slot] || slot;\n\t\t},\n\t\t\n\t\tgetRealmProgressPercent() {\n\t\t\tconst realmInfo = this.playerData.realm_info;\n\t\t\tif (!realmInfo || !realmInfo.next_min) return 0;\n\t\t\t\n\t\t\tconst progress = realmInfo.progress || 0;\n\t\t\tconst nextMin = realmInfo.next_min || 0;\n\t\t\tconst currentMax = realmInfo.current_max || 0;\n\t\t\t\n\t\t\t// 计算当前境界内的进度\n\t\t\tconst currentRange = nextMin - currentMax;\n\t\t\tif (currentRange <= 0) return 100;\n\t\t\t\n\t\t\tconst currentProgress = progress - currentMax;\n\t\t\tconst percent = Math.min(100, Math.max(0, (currentProgress / currentRange) * 100));\n\t\t\treturn Math.round(percent);\n\t\t},\n\t\t\n\t\tasync loadItemsConfig() {\n\t\t\tthis.itemsConfig = await gameState.getItemsConfig();\n\t\t},\n\t\t// 可在需要时通过 this.itemsConfig[itemId] 获取物品详情\n\t\t\n\t\tasync loadMapsConfigSafe() {\n\t\t\ttry {\n\t\t\t\tthis.mapsConfig = await gameState.getMapsConfig();\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('地图信息加载失败', e);\n\t\t\t\tuni.showToast({ title: '地图信息错误', icon: 'none' });\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 天赋详情相关方法\n\t\tshowTalentDetail(talent) {\n\t\t\tthis.selectedTalent = talent;\n\t\t\tthis.showTalentModal = true;\n\t\t},\n\t\t\n\t\tcloseTalentModal() {\n\t\t\tthis.showTalentModal = false;\n\t\t\tthis.selectedTalent = {};\n\t\t},\n\t\t\n\t\tgetConstitutionBonus() {\n\t\t\t// 根骨百分比加成依然由后端体力恢复详情返回，或为0\n\t\t\treturn 0;\n\t\t},\n\t\t\n\t\tgetConstitutionHpBonus() {\n\t\t\treturn this.playerData?.talent_bonuses?.constitution?.hp_bonus_percentage || 0;\n\t\t},\n\t\t\n\t\tgetConstitutionHpBonusValue() {\n\t\t\treturn this.playerData?.talent_bonuses?.constitution?.hp_bonus || 0;\n\t\t},\n\t\t\n\t\tgetCurrentEnergyRegen() {\n\t\t\tif (this.energyRegenDetails) {\n\t\t\t\treturn this.energyRegenDetails.final_energy_regen.toFixed(2);\n\t\t\t}\n\t\t\t// 如果没有详情数据，手动计算\n\t\t\tconst baseRegen = this.playerData.energy_regen_rate || 0.1;\n\t\t\tconst constitution = this.playerData.talent?.根骨 || 15;\n\t\t\tconst bonus = Math.min(1.0, (constitution - 15) * 0.02);\n\t\t\tconst multiplier = 1.0 + bonus * Math.log(constitution / 15 + 1) / Math.log(2);\n\t\t\treturn (baseRegen * multiplier).toFixed(2);\n\t\t},\n\t\t\n\t\t// 力量增益计算方法\n\t\tgetStrengthBonus() {\n\t\t\treturn this.playerData?.talent_bonuses?.strength?.bonus_percentage || 0;\n\t\t},\n\t\t\n\t\tgetStrengthAttackBonus() {\n\t\t\treturn this.playerData?.talent_bonuses?.strength?.attack_bonus || 0;\n\t\t},\n\t\t\n\t\t// 悟性增益计算方法\n\t\tgetIntelligenceBonus() {\n\t\t\treturn this.playerData?.talent_bonuses?.intelligence?.bonus_percentage || 0;\n\t\t},\n\t\t\n\t\tgetIntelligenceMultiplier() {\n\t\t\treturn this.playerData?.talent_bonuses?.intelligence?.exp_multiplier || 1.0;\n\t\t},\n\t\t\n\t\t// 身法增益计算方法\n\t\tgetAgilityDefenseBonus() {\n\t\t\treturn this.playerData?.talent_bonuses?.agility?.defense_bonus_percentage || 0;\n\t\t},\n\t\t\n\t\tgetAgilityDodgeBonus() {\n\t\t\treturn this.playerData?.talent_bonuses?.agility?.dodge_bonus_percentage || 0;\n\t\t},\n\t\t\n\t\tgetAgilityDefenseBonusValue() {\n\t\t\treturn this.playerData?.talent_bonuses?.agility?.defense_bonus || 0;\n\t\t},\n\t\t\n\t\tgetAgilityDodgeBonusValue() {\n\t\t\treturn this.playerData?.talent_bonuses?.agility?.dodge_bonus || 0;\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.character-compact { \n\tpadding: 16rpx; \n\tbackground: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%); \n\tmin-height: 100vh; \n\tfont-size: 26rpx;\n}\n\n/* 头部区域 */\n.header-section { \n\tdisplay: flex; \n\talign-items: center; \n\tgap: 16rpx; \n\tmargin-bottom: 16rpx; \n\tbackground: rgba(255,255,255,0.9); \n\tborder-radius: 16rpx; \n\tpadding: 16rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\n}\n\n.avatar-section { \n\tposition: relative; \n}\n\n.avatar { \n\twidth: 80rpx; \n\theight: 80rpx; \n\tborder-radius: 50%; \n\tborder: 3rpx solid #667eea; \n\tbox-shadow: 0 4rpx 8rpx rgba(102,126,234,0.3);\n}\n\n.level-badge { \n\tposition: absolute; \n\ttop: -4rpx; \n\tright: -4rpx; \n\tbackground: linear-gradient(135deg, #FF5722, #F44336); \n\tcolor: white; \n\tfont-size: 22rpx; \n\tpadding: 4rpx 8rpx; \n\tborder-radius: 12rpx; \n\tfont-weight: bold;\n\tbox-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);\n}\n\n.info-section { \n\tflex: 1; \n}\n\n.name-row { \n\tdisplay: flex; \n\talign-items: center;\n\tgap: 12rpx; \n\tmargin-bottom: 8rpx; \n\tflex-wrap: wrap;\n}\n\n.name { \n\tfont-weight: bold; \n\tfont-size: 32rpx; \n\tcolor: #333; \n}\n\n.gender { \n\tfont-size: 22rpx; \n\tcolor: #888; \n\tbackground: linear-gradient(135deg, #f0f0f0, #e0e0e0); \n\tpadding: 4rpx 8rpx; \n\tborder-radius: 8rpx;\n\tborder: 1rpx solid #ddd;\n}\n\n.experience { \n\tcolor: #4CAF50; \n\tfont-weight: bold;\n\tfont-size: 24rpx;\n\tbackground: rgba(76,175,80,0.1);\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n}\n\n.fortune { \n\tcolor: #9C27B0; \n\tfont-weight: bold;\n\tbackground: rgba(156,39,176,0.1);\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n}\n\n.stats-row { \n\tdisplay: flex; \n\tgap: 12rpx; \n\tfont-size: 24rpx; \n\tflex-wrap: wrap;\n}\n\n.money { \n\tcolor: #ff9800; \n\tfont-weight: bold;\n\tbackground: rgba(255,152,0,0.1);\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n}\n\n.skill-points { \n\tcolor: #9C27B0; \n\tfont-weight: bold;\n\tbackground: rgba(156,39,176,0.1);\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n}\n\n.realm { \n\tcolor: #2196F3; \n\tfont-weight: bold;\n\tbackground: rgba(33,150,243,0.1);\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n}\n\n/* 属性网格 */\n.attributes-grid { \n\tdisplay: grid; \n\tgrid-template-columns: repeat(4, 1fr); \n\tgap: 12rpx; \n\tmargin: 16rpx 0; \n\tbackground: rgba(255,255,255,0.9); \n\tborder-radius: 16rpx; \n\tpadding: 16rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\n}\n\n.attributes-grid .section-title {\n\tgrid-column: 1 / -1;\n\tfont-size: 32rpx; \n\tfont-weight: bold; \n\tcolor: #333; \n\tmargin-bottom: 12rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n\tpadding: 8rpx 12rpx;\n\tborder-radius: 8rpx;\n\ttext-align: center;\n\tjustify-content: center;\n}\n\n.attr-item { \n\tdisplay: flex; \n\tflex-direction: column; \n\talign-items: center; \n\tpadding: 12rpx 8rpx;\n\tbackground: linear-gradient(135deg, #f8f9fa, #e9ecef);\n\tborder-radius: 12rpx;\n\tborder: 1rpx solid #dee2e6;\n\ttransition: all 0.3s ease;\n}\n\n.attr-item:hover {\n\ttransform: translateY(-2rpx);\n\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.attr-label { \n\tfont-size: 22rpx; \n\tcolor: #666; \n\tmargin-bottom: 6rpx;\n\tfont-weight: 500;\n}\n\n.attr-value { \n\tfont-size: 26rpx; \n\tfont-weight: bold; \n\tcolor: #333;\n}\n\n/* 天赋区域 */\n.talent-section { \n\tmargin: 16rpx 0; \n\tbackground: rgba(255,255,255,0.9); \n\tborder-radius: 16rpx; \n\tpadding: 16rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\n}\n\n.section-title { \n\tfont-size: 32rpx; \n\tfont-weight: bold; \n\tcolor: #333; \n\tmargin-bottom: 12rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.section-title::before {\n\tcontent: \"⭐\";\n\tfont-size: 32rpx;\n}\n\n.talent-list {\n\tdisplay: flex;\n\tflex-direction: row;\n\tflex-wrap: nowrap;\n\tgap: 16rpx;\n\toverflow-x: auto;\n}\n\n.talent-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tpadding: 8rpx 20rpx;\n\tbackground: #fffbe6;\n\tborder-radius: 16rpx;\n\tborder: 1rpx solid #ffeaa7;\n\ttransition: all 0.3s ease;\n}\n\n.talent-item:hover {\n\ttransform: translateY(-2rpx);\n\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n}\n\n.talent-label { \n\tfont-size: 22rpx; \n\tcolor: #856404; \n\tmargin-bottom: 6rpx;\n\tfont-weight: 500;\n}\n\n.talent-value { \n\tfont-size: 26rpx; \n\tfont-weight: bold; \n\tcolor: #333;\n}\n\n/* 境界区域 */\n.realm-section { \n\tmargin: 16rpx 0; \n\tbackground: rgba(255,255,255,0.9); \n\tborder-radius: 16rpx; \n\tpadding: 16rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\n}\n\n.realm-header { \n\tdisplay: flex; \n\tjustify-content: space-between; \n\talign-items: center; \n\tmargin-bottom: 12rpx;\n}\n\n.realm-title { \n\tfont-size: 28rpx; \n\tfont-weight: bold; \n\tcolor: #333;\n}\n\n.realm-progress { \n\tfont-size: 24rpx; \n\tcolor: #666;\n\tbackground: rgba(0,0,0,0.05);\n\tpadding: 4rpx 8rpx;\n\tborder-radius: 8rpx;\n}\n\n.progress-bar { \n\twidth: 100%; \n\theight: 12rpx; \n\tbackground: #e9ecef; \n\tborder-radius: 6rpx; \n\tmargin: 8rpx 0; \n\toverflow: hidden;\n\tbox-shadow: inset 0 2rpx 4rpx rgba(0,0,0,0.1);\n}\n\n.progress-fill { \n\theight: 100%; \n\tbackground: linear-gradient(90deg, #FFD700, #FFA500); \n\tborder-radius: 6rpx; \n\ttransition: width 0.3s ease;\n\tbox-shadow: 0 2rpx 4rpx rgba(255,215,0,0.3);\n}\n\n.realm-bonus { \n\tfont-size: 24rpx; \n\tcolor: #ff9800; \n\tfont-style: italic;\n\tbackground: rgba(255,152,0,0.1);\n\tpadding: 8rpx;\n\tborder-radius: 8rpx;\n\tborder-left: 4rpx solid #ff9800;\n}\n\n/* 装备区域 */\n.equipment-section { \n\tmargin: 16rpx 0; \n\tbackground: rgba(255,255,255,0.9); \n\tborder-radius: 16rpx; \n\tpadding: 16rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\n}\n\n.section-title { \n\tfont-size: 32rpx; \n\tfont-weight: bold; \n\tcolor: #333; \n\tmargin-bottom: 12rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.section-title::before {\n\tcontent: \"⚔️\";\n\tfont-size: 32rpx;\n}\n\n.equipment-grid { \n\tdisplay: grid; \n\tgrid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));\n\tgap: 12rpx;\n}\n\n.equip-slot { \n\tmin-width: 0;\n\tbox-sizing: border-box;\n\tdisplay: flex; \n\tflex-direction: column; \n\talign-items: center; \n\tpadding: 12rpx 8rpx;\n\tbackground: linear-gradient(135deg, #f8f9fa, #e9ecef);\n\tborder-radius: 12rpx;\n\tborder: 2rpx solid #dee2e6;\n\ttransition: all 0.3s ease;\n\tmin-height: 80rpx;\n\tjustify-content: center;\n}\n\n.equip-slot:hover {\n\ttransform: translateY(-2rpx);\n\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\n\tborder-color: #667eea;\n}\n\n.equip-icon { \n\tfont-size: 32rpx; \n\tcolor: #333; \n\tmargin-bottom: 6rpx;\n}\n\n.equip-name { \n\tfont-size: 22rpx; \n\tcolor: #333; \n\tmargin-top: 6rpx; \n\ttext-align: center;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n\tmax-width: 100%;\n\tfont-weight: 500;\n\tword-break: break-all;\n}\n\n.empty-text { \n\tfont-size: 22rpx; \n\tcolor: #ccc; \n\tmargin-top: 6rpx;\n\tfont-style: italic;\n}\n\n/* 操作按钮 */\n.action-section { \n\tdisplay: flex; \n\tgap: 16rpx; \n\tmargin-top: 16rpx;\n}\n\n.action-btn { \n\tflex: 1; \n\theight: 56rpx; \n\tborder: none; \n\tborder-radius: 28rpx; \n\tcolor: white; \n\tfont-size: 28rpx; \n\tfont-weight: bold; \n\ttransition: all 0.3s ease;\n\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.healing-btn { \n\tbackground: linear-gradient(135deg, #FF5722, #F44336);\n}\n\n.crafting-btn { \n\tbackground: linear-gradient(135deg, #2196F3, #1976D2);\n}\n\n.breakthrough-btn { \n\tbackground: linear-gradient(135deg, #9C27B0, #673AB7);\n}\n\n.backpack-btn { \n\tbackground: linear-gradient(135deg, #4CAF50, #388E3C);\n}\n\n.action-btn:active {\n\ttransform: scale(0.95);\n\tbox-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);\n}\n\n.action-btn:hover {\n\ttransform: translateY(-2rpx);\n\tbox-shadow: 0 6rpx 12rpx rgba(0,0,0,0.3);\n}\n\n/* 天赋详情弹窗样式 */\n.talent-modal-mask {\n\tposition: fixed;\n\tleft: 0;\n\ttop: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tz-index: 9999;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.talent-modal-content {\n\tbackground: white;\n\tborder-radius: 20rpx;\n\twidth: 80%;\n\tmax-width: 600rpx;\n\tmax-height: 80vh;\n\toverflow: hidden;\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);\n}\n\n.talent-modal-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 24rpx;\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n}\n\n.talent-modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.talent-modal-close {\n\tfont-size: 40rpx;\n\tcursor: pointer;\n\tpadding: 8rpx;\n\tborder-radius: 50%;\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: rgba(255, 255, 255, 0.2);\n}\n\n.talent-modal-body {\n\tpadding: 24rpx;\n\tmax-height: 60vh;\n\toverflow-y: auto;\n}\n\n.talent-current {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 24rpx;\n\tpadding: 16rpx;\n\tbackground: linear-gradient(135deg, #f8f9fa, #e9ecef);\n\tborder-radius: 12rpx;\n\tborder-left: 6rpx solid #667eea;\n}\n\n.talent-current-label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n}\n\n.talent-current-value {\n\tfont-size: 32rpx;\n\tcolor: #667eea;\n\tfont-weight: bold;\n}\n\n.talent-effects {\n\tmargin-top: 16rpx;\n}\n\n.talent-effects-title {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 16rpx;\n\tdisplay: block;\n}\n\n.talent-effect-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 12rpx;\n\tpadding: 12rpx;\n\tbackground: #f8f9fa;\n\tborder-radius: 8rpx;\n\tborder-left: 4rpx solid #28a745;\n}\n\n.effect-label {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.effect-value {\n\tfont-size: 26rpx;\n\tcolor: #28a745;\n\tfont-weight: bold;\n}\n\n.talent-modal-footer {\n\tpadding: 24rpx;\n\tborder-top: 2rpx solid #eee;\n\tdisplay: flex;\n\tjustify-content: center;\n}\n\n.talent-modal-btn {\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tcolor: white;\n\tborder: none;\n\tborder-radius: 25rpx;\n\tpadding: 16rpx 48rpx;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tbox-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.3);\n\ttransition: all 0.3s ease;\n}\n\n.talent-modal-btn:active {\n\ttransform: scale(0.95);\n}\n\n/* 天赋属性点击效果 */\n.attr-item {\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n\tborder-radius: 8rpx;\n\tpadding: 8rpx;\n}\n\n.attr-item:hover {\n\tbackground: rgba(102, 126, 234, 0.1);\n\ttransform: translateY(-2rpx);\n}\n\n.attr-item:active {\n\ttransform: scale(0.95);\n}\n\n/* 装备详情弹窗样式 */\n.equipment-modal-mask {\n\tposition: fixed;\n\tleft: 0;\n\ttop: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0, 0, 0, 0.5);\n\tz-index: 9999;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.equipment-modal-content {\n\tbackground: white;\n\tborder-radius: 20rpx;\n\twidth: 85%;\n\tmax-width: 650rpx;\n\tmax-height: 85vh;\n\toverflow: hidden;\n\tbox-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);\n}\n\n.equipment-modal-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 24rpx;\n\tbackground: linear-gradient(135deg, #4CAF50, #388E3C);\n\tcolor: white;\n}\n\n.equipment-modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.equipment-modal-close {\n\tfont-size: 40rpx;\n\tcursor: pointer;\n\tpadding: 8rpx;\n\tborder-radius: 50%;\n\twidth: 60rpx;\n\theight: 60rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: rgba(255, 255, 255, 0.2);\n}\n\n.equipment-modal-body {\n\tpadding: 24rpx;\n\tmax-height: 60vh;\n\toverflow-y: auto;\n}\n\n.equipment-info {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.equipment-basic {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 20rpx;\n\tpadding: 16rpx;\n\tbackground: linear-gradient(135deg, #f8f9fa, #e9ecef);\n\tborder-radius: 12rpx;\n\tborder-left: 6rpx solid #4CAF50;\n}\n\n.equipment-icon {\n\tfont-size: 48rpx;\n\twidth: 80rpx;\n\theight: 80rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbackground: white;\n\tborder-radius: 12rpx;\n\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n}\n\n.equipment-details {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 8rpx;\n}\n\n.equipment-type,\n.equipment-quality,\n.equipment-slot {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.equipment-stats {\n\tmargin-top: 16rpx;\n}\n\n.stats-title {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 16rpx;\n\tdisplay: block;\n}\n\n.stat-item {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 12rpx;\n\tpadding: 12rpx;\n\tbackground: #f8f9fa;\n\tborder-radius: 8rpx;\n\tborder-left: 4rpx solid #2196F3;\n}\n\n.stat-label {\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.stat-value {\n\tfont-size: 26rpx;\n\tcolor: #2196F3;\n\tfont-weight: bold;\n}\n\n.equipment-description {\n\tmargin-top: 16rpx;\n}\n\n.description-title {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 12rpx;\n\tdisplay: block;\n}\n\n.description-text {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n\tpadding: 12rpx;\n\tbackground: #f8f9fa;\n\tborder-radius: 8rpx;\n}\n\n.equipment-modal-actions {\n\tpadding: 24rpx;\n\tborder-top: 2rpx solid #eee;\n\tdisplay: flex;\n\tgap: 16rpx;\n}\n\n.equipment-action-btn {\n\tflex: 1;\n\theight: 56rpx;\n\tborder: none;\n\tborder-radius: 28rpx;\n\tcolor: white;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tbox-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);\n\ttransition: all 0.3s ease;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.unequip-btn {\n\tbackground: linear-gradient(135deg, #FF5722, #F44336);\n}\n\n.close-btn {\n\tbackground: linear-gradient(135deg, #9E9E9E, #757575);\n}\n\n.equipment-action-btn:active {\n\ttransform: scale(0.95);\n\tbox-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n}\n\n/* 装备槽点击效果 */\n.equip-slot {\n\tcursor: pointer;\n\ttransition: all 0.3s ease;\n\tborder-radius: 8rpx;\n}\n\n.equip-slot:hover {\n\tbackground: rgba(76, 175, 80, 0.1);\n\ttransform: translateY(-2rpx);\n}\n\n.equip-slot:active {\n\ttransform: scale(0.95);\n}\n</style> ", "import MiniProgramPage from 'D:/zjjhx/仗剑江湖行/pages/character/character.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "gameState", "wsManager", "gameUtils"], "mappings": ";;;;;;AAwUA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,WAAW;AAAA,MACX,aAAa;AAAA,MACb,YAAY;AAAA,QACX,gBAAgB;AAAA,QAChB,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,IAAI;AAAA,QACJ,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ,CAAE;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,SAAS;AAAA,QACT,WAAW,CAAE;AAAA,QACb,oBAAoB;AAAA,QACpB,YAAY;AAAA,UACX,eAAe;AAAA,UACf,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,aAAa;AAAA,UACb,UAAU;AAAA,UACV,YAAY;AAAA,QACZ;AAAA,QACD,cAAc;AAAA,QACd,aAAa;AAAA;AAAA,MACb;AAAA,MACD,eAAe,CAAE;AAAA,MACjB,WAAW;AAAA,MACX,aAAa;AAAA,MACb,cAAc,CAAE;AAAA;AAAA;AAAA,MAEhB,aAAa,CAAE;AAAA,MACf,YAAY,CAAE;AAAA;AAAA;AAAA,MAEd,iBAAiB;AAAA,MACjB,gBAAgB,CAAE;AAAA;AAAA,MAElB,oBAAoB;AAAA,MACpB,mBAAmB,CAAE;AAAA,MACrB,cAAc;AAAA,IACf;AAAA,EACA;AAAA,EAED,UAAU;AAAA,IACT,iBAAiB;AAChB,YAAM,KAAK,KAAK,WAAW,aAAa,CAAA;AAExC,UAAI;AACH,eAAO,KAAK,MAAM,KAAK,UAAU;AAAA,UAChC,WAAW,GAAG,aAAa;AAAA,UAC3B,UAAU,GAAG,YAAY;AAAA,UACzB,OAAO,GAAG,SAAS;AAAA,UACnB,QAAQ,GAAG,UAAU;AAAA,UACrB,UAAU,GAAG,YAAY;AAAA,UACzB,OAAO,GAAG,SAAS;AAAA,UACnB,OAAO,GAAG,SAAS;AAAA,UACnB,OAAO,GAAG,SAAS;AAAA,QACnB,CAAA,CAAC;AAAA,MACD,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,wCAAc,aAAa,KAAK;AAChC,eAAO;AAAA,UACN,WAAW;AAAA,UACX,UAAU;AAAA,UACV,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA;MAET;AAAA,IACA;AAAA,IACD,YAAY;;AACX,aAAO;AAAA,QACN,EAAE,OAAO,MAAM,SAAO,UAAK,WAAW,WAAhB,mBAAwB,OAAM,EAAG;AAAA,QACvD,EAAE,OAAO,MAAM,SAAO,UAAK,WAAW,WAAhB,mBAAwB,OAAM,EAAG;AAAA,QACvD,EAAE,OAAO,MAAM,SAAO,UAAK,WAAW,WAAhB,mBAAwB,OAAM,EAAG;AAAA,QACvD,EAAE,OAAO,MAAM,SAAO,UAAK,WAAW,WAAhB,mBAAwB,OAAM,EAAG;AAAA,QACvD,EAAE,OAAO,MAAM,OAAO,KAAK,WAAW,WAAW,EAAE;AAAA;IAEpD;AAAA,IACD,qBAAqB;;AACpB,aAAO;AAAA,QACN,EAAE,OAAO,MAAM,SAAO,UAAK,WAAW,WAAhB,mBAAwB,OAAM,EAAG;AAAA,QACvD,EAAE,OAAO,MAAM,SAAO,UAAK,WAAW,WAAhB,mBAAwB,OAAM,EAAG;AAAA,QACvD,EAAE,OAAO,MAAM,SAAO,UAAK,WAAW,WAAhB,mBAAwB,OAAM,EAAG;AAAA,QACvD,EAAE,OAAO,MAAM,SAAO,UAAK,WAAW,WAAhB,mBAAwB,OAAM,EAAE;AAAA;IAEvD;AAAA,IACD,iBAAiB;AAChB,UAAI,CAAC,KAAK,cAAc,CAACC,gBAAS,UAAC,OAAO;AAAa,eAAO;AAC9D,YAAM,MAAM,KAAK,WAAWA,gBAAAA,UAAU,OAAO,WAAW;AACxD,aAAO,MAAM,IAAI,OAAO;AAAA,IACxB;AAAA,IACD,qBAAqB;AAEpB,aAAO,KAAK,WAAW,wBAAwB;AAAA,IAChD;AAAA,EACA;AAAA,EAED,SAAS;AACRD,kBAAAA,MAAY,MAAA,OAAA,wCAAA,eAAe;AAC3B,SAAK,oBAAmB;AAExB,eAAW,YAAY;AACtBA,oBAAAA,MAAY,MAAA,OAAA,wCAAA,WAAW;AACvB,YAAM,KAAK;AACX,WAAK,cAAa;AAAA,IAClB,GAAE,GAAG;AACN,SAAK,gBAAe;AAAA,EACpB;AAAA,EAED,SAAS;AACRA,kBAAAA,MAAY,MAAA,OAAA,wCAAA,eAAe;AAC3BA,kBAAY,MAAA,MAAA,OAAA,wCAAA,kBAAkBE,gBAAAA,UAAU,WAAW;AACnDF,kBAAY,MAAA,MAAA,OAAA,wCAAA,kBAAkBC,gBAAAA,UAAU,QAAQ;AAChD,SAAK,iBAAgB;AACrB,SAAK,kBAAiB;AAEtB,QAAI,CAACA,gBAAAA,UAAU,aAAa;AAC3BD,oBAAAA,MAAA,MAAA,OAAA,wCAAY,uBAAuB;AACnC,UAAIE,0BAAU,eAAeD,gBAAS,UAAC,UAAU;AAChDA,wBAAS,UAAC,eAAc;AAAA,aAClB;AACND,sBAAAA,MAAA,MAAA,OAAA,wCAAY,0BAA0B;AACtC,aAAK,cAAa;AAAA,MACnB;AAAA,IACD;AAAA,EACA;AAAA,EAED,WAAW;AACV,SAAK,sBAAqB;AAE1BC,oBAAAA,UAAU,UAAU,KAAK,iBAAiB;AAAA,EAC1C;AAAA,EAED,SAAS;AAAA,IACP,MAAM,gBAAgB;AACvB,UAAI;AACHD,sBAAAA,MAAY,MAAA,OAAA,wCAAA,kBAAkB;AAC9B,aAAK,YAAY;AACjB,aAAK,cAAc;AAGnBC,wBAAAA,UAAU,SAAS,KAAK,iBAAiB;AACzCD,sBAAAA,MAAY,MAAA,OAAA,wCAAA,WAAW;AAGvB,cAAMC,gBAAAA,UAAU;AAChBD,sBAAAA,MAAY,MAAA,OAAA,wCAAA,WAAW;AAGvB,YAAIC,gBAAAA,UAAU,UAAU;AACvBD,wBAAAA,MAAA,MAAA,OAAA,wCAAY,UAAU;AAEtBC,0BAAS,UAAC,eAAc;AACxBD,wBAAAA,MAAA,MAAA,OAAA,wCAAY,SAAS;AAAA,eACf;AACNA,wBAAAA,MAAY,MAAA,OAAA,wCAAA,YAAY;AAAA,QACzB;AAGAA,sBAAAA,MAAA,MAAA,OAAA,wCAAY,qBAAqB;AAGjC,mBAAW,MAAM;AAChB,cAAI,KAAK,WAAW;AACnBA,0BAAAA,4DAAa,QAAQ;AACrB,iBAAK,YAAY;AACjBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACA,GAAE,GAAK;AAAA,MACP,SAAO,OAAO;AACf,aAAK,YAAY;AACjBA,sBAAc,MAAA,MAAA,SAAA,wCAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM;AAAA,UACzB,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAEA,mBAAmB;AAElB,YAAM,SAASC,0BAAU;AACzB,UAAI,QAAQ;AACX,aAAK,aAAa,EAAE,GAAG;AACvBD,iFAAY,qBAAqB,KAAK,UAAU;AAAA,aAC1C;AACNA,sBAAAA,MAAA,MAAA,QAAA,wCAAa,kBAAkB;AAE/B,YAAIC,gBAAAA,UAAU,UAAU;AACvBD,wBAAAA,2DAAY,cAAc;AAC1BC,0BAAS,UAAC,eAAc;AAAA,QACzB;AAAA,MACD;AAEA,YAAM,YAAYA,0BAAU;AAC5B,UAAI,aAAa,MAAM,QAAQ,SAAS,GAAG;AAC1C,aAAK,gBAAgB,CAAC,GAAG,SAAS;AAClCD,sBAAA,MAAA,MAAA,OAAA,wCAAY,qBAAqB,KAAK,aAAa;AAAA,aAC7C;AACNA,kFAAa,4BAA4B,SAAS;AAClD,aAAK,gBAAgB;MACtB;AAAA,IACA;AAAA,IAED,kBAAkB,MAAM,mBAAmB;AAC1CA,oBAAY,MAAA,MAAA,OAAA,wCAAA,eAAe,IAAI;AAC/B,cAAQ,MAAI;AAAA,QACX,KAAK;AACJ,cAAI,kBAAkB,QAAQ;AAC7B,iBAAK,aAAa,EAAE,GAAG,kBAAkB;AACzC,iBAAK,YAAY;AACjBA,0BAAY,MAAA,MAAA,OAAA,wCAAA,YAAY,KAAK,UAAU;AACvCA,0BAAAA,2DAAY,SAAS;AACrBA,gCAAA,MAAA,OAAA,wCAAY,aAAa,KAAK,WAAW,MAAM;AAC/CA,gCAAA,MAAA,OAAA,wCAAY,aAAa,KAAK,WAAW,MAAM;AAC/CA,gCAAY,MAAA,OAAA,wCAAA,iBAAiB,KAAK,WAAW,UAAU;AACvDA,gCAAY,MAAA,OAAA,wCAAA,iBAAiB,KAAK,WAAW,UAAU;AACvDA,gCAAA,MAAA,OAAA,wCAAY,aAAa,KAAK,WAAW,MAAM;AAC/CA,gCAAY,MAAA,OAAA,wCAAA,cAAc,KAAK,WAAW,OAAO;AACjDA,gCAAA,MAAA,OAAA,wCAAY,YAAY,KAAK,WAAW,KAAK;AAC7CA,qFAAY,WAAW,KAAK,WAAW,IAAI;AAG3C,gBAAI,CAAC,KAAK,WAAW,UAAU,KAAK,WAAW,WAAW,GAAG;AAC5DA,4BAAAA,MAAa,MAAA,QAAA,wCAAA,qBAAqB;AAAA,YACnC;AACA,gBAAI,CAAC,KAAK,WAAW,UAAU,KAAK,WAAW,WAAW,GAAG;AAC5DA,4BAAAA,MAAa,MAAA,QAAA,wCAAA,qBAAqB;AAAA,YACnC;AACA,gBAAI,CAAC,KAAK,WAAW,cAAc,KAAK,WAAW,eAAe,GAAG;AACpEA,4BAAAA,MAAA,MAAA,QAAA,wCAAa,yBAAyB;AAAA,YACvC;AACA,gBAAI,CAAC,KAAK,WAAW,UAAU,KAAK,WAAW,WAAW,GAAG;AAC5DA,4BAAAA,MAAa,MAAA,QAAA,wCAAA,qBAAqB;AAAA,YACnC;AACA,gBAAI,CAAC,KAAK,WAAW,WAAW,KAAK,WAAW,YAAY,GAAG;AAC9DA,4BAAAA,MAAa,MAAA,QAAA,wCAAA,sBAAsB;AAAA,YACpC;AAGA,iBAAK,sBAAqB;AAAA,UAC3B;AACA;AAAA,QACD,KAAK;AACJ,cAAI,kBAAkB,aAAa,MAAM,QAAQ,kBAAkB,SAAS,GAAG;AAC9E,iBAAK,gBAAgB,CAAC,GAAG,kBAAkB,SAAS;AACpDA,qFAAY,YAAY,KAAK,aAAa;AAAA,iBACpC;AACNA,0BAAa,MAAA,MAAA,QAAA,wCAAA,aAAa,kBAAkB,SAAS;AACrD,iBAAK,gBAAgB;UACtB;AACA;AAAA,MACF;AAAA,IACA;AAAA,IAED,wBAAwB;AACvB,YAAM,iBAAiB,CAAC,UAAU,UAAU,cAAc,cAAc,UAAU,WAAW,SAAS,MAAM;AAC5G,YAAM,gBAAgB,eAAe,OAAO,WAAS;AACpD,cAAM,QAAQ,KAAK,WAAW,KAAK;AACnC,eAAO,UAAU,UAAa,UAAU,QAAQ,UAAU;AAAA,MAC3D,CAAC;AAED,UAAI,cAAc,SAAS,GAAG;AAC7BA,sBAAA,MAAA,MAAA,QAAA,wCAAa,aAAa,aAAa;AACvCA,sBAAA,MAAA,MAAA,QAAA,wCAAa,WAAW,KAAK,UAAU;AAEvC,mBAAW,MAAM;AAChBA,wBAAAA,MAAY,MAAA,OAAA,wCAAA,YAAY;AACxBC,0BAAS,UAAC,eAAc;AAAA,QACxB,GAAE,GAAI;AAAA,aACD;AACND,sBAAAA,MAAA,MAAA,OAAA,wCAAY,UAAU;AAAA,MACvB;AAAA,IACA;AAAA,IAED,YAAY,OAAO;AAClB,UAAI,SAAS,MAAe;AAC3B,gBAAQ,QAAQ,MAAe,QAAQ,CAAC,IAAI;AAAA,iBAClC,SAAS,KAAW;AAC9B,gBAAQ,QAAQ,KAAW,QAAQ,CAAC,IAAI;AAAA,MACzC,WAAW,SAAS,KAAO;AAC1B,gBAAQ,QAAQ,KAAO,QAAQ,CAAC,IAAI;AAAA,MACrC,WAAW,SAAS,KAAM;AACzB,eAAO,MAAM;aACP;AACN,eAAO,MAAM;MACd;AAAA,IACA;AAAA,IAED,iBAAiB,KAAK;AACrB,UAAI,OAAO,MAAe;AACzB,gBAAQ,MAAM,MAAe,QAAQ,CAAC,IAAI;AAAA,iBAChC,OAAO,KAAW;AAC5B,gBAAQ,MAAM,KAAW,QAAQ,CAAC,IAAI;AAAA,MACvC,WAAW,OAAO,KAAO;AACxB,gBAAQ,MAAM,KAAO,QAAQ,CAAC,IAAI;AAAA,MACnC,WAAW,OAAO,KAAM;AACvB,eAAO,IAAI;aACL;AACN,eAAO,IAAI;MACZ;AAAA,IACA;AAAA,IAED,sBAAsB;AACrB,WAAK,sBAAqB;AAE1B,WAAK,gBAAgB,CAAC,QAAQ,KAAK,YAAY,GAAG;AAClD,WAAK,sBAAsB,CAAC,SAAS,KAAK,kBAAkB,IAAI;AAChE,WAAK,yBAAyB,CAAC,SAAS,KAAK,qBAAqB,IAAI;AACtE,WAAK,wBAAwB,CAAC,SAAS,KAAK,oBAAoB,IAAI;AACpE,WAAK,uBAAuB,CAAC,SAAS,KAAK,mBAAmB,IAAI;AAClE,WAAK,sBAAsB,CAAC,SAAS,KAAK,kBAAkB,IAAI;AAChE,WAAK,yBAAyB,CAAC,SAAS,KAAK,qBAAqB,IAAI;AACtE,WAAK,wBAAwB,CAAC,SAAS,KAAK,oBAAoB,IAAI;AACpE,WAAK,wBAAwB,CAAC,SAAS,KAAK,oBAAoB,IAAI;AACpE,WAAK,uBAAuB,CAAC,SAAS,KAAK,mBAAmB,IAAI;AAClE,WAAK,8BAA8B,CAAC,SAAS,KAAK,0BAA0B,IAAI;AAChF,WAAK,6BAA6B,CAAC,SAAS,KAAK,yBAAyB,IAAI;AAAA,IAG9E;AAAA,IAED,wBAAwB;AAAA,IAEvB;AAAA,IAED,MAAM,oBAAoB;AACzB,UAAI;AACH,cAAM,MAAM,MAAMG,eAAS,UAAC,YAAY;AAAA,UACvC,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,QACR,CAAC;AACD,YAAI,IAAI,SAAS,iBAAiB;AACjC,eAAK,eAAe,IAAI;AAAA,QACzB;AAAA,MACD,SAAS,GAAG;AACXH,kFAAa,YAAY,CAAC;AAAA,MAC3B;AAAA,IACA;AAAA,IAED,kBAAkB,MAAM;AACvBA,oBAAY,MAAA,MAAA,OAAA,wCAAA,eAAe,IAAI;AAE/B,UAAIC,gBAAAA,UAAU,UAAU;AACvBD,sBAAAA,2DAAY,aAAa;AACzBC,wBAAS,UAAC,eAAc;AAAA,aAClB;AACND,sBAAAA,MAAA,MAAA,OAAA,wCAAY,0BAA0B;AAAA,MACvC;AAAA,IACA;AAAA,IAED,YAAY,OAAO;AAClB,WAAK,YAAY;AACjBA,oBAAc,MAAA,MAAA,SAAA,wCAAA,aAAa,KAAK;AAEhC,YAAM,eAAe,MAAM,WAAW;AACtCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA;AAAA,IAGD,qBAAqB,MAAM;AAC1B,YAAM,YAAY,KAAK,eAAe,IAAI;AAC1C,UAAI,aAAa,UAAU,IAAI;AAE9B,aAAK,oBAAoB,EAAE,GAAG;AAC9B,aAAK,eAAe;AACpB,aAAK,qBAAqB;AAAA,aACpB;AAENA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,GAAG,KAAK,aAAa,IAAI,CAAC;AAAA,UACjC,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,sBAAsB;AACrB,WAAK,qBAAqB;AAC1B,WAAK,oBAAoB;AACzB,WAAK,eAAe;AAAA,IACpB;AAAA;AAAA,IAGD,2BAA2B;AAC1B,UAAI,KAAK,cAAc;AACtB,cAAM,OAAO,KAAK;AAClB,aAAK,oBAAmB;AACxB,aAAK,YAAY,IAAI;AAAA,MACtB;AAAA,IACA;AAAA;AAAA,IAGD,YAAY,MAAM;AACjB,YAAM,UAAU;AAAA,QACf,UAAU;AAAA,QACV,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa;AAAA;AAEd,aAAO,QAAQ,IAAI,KAAK;AAAA,IACxB;AAAA;AAAA,IAGD,eAAe,SAAS;AACvB,YAAM,aAAa;AAAA,QAClB,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,UAAU;AAAA;AAEX,aAAO,WAAW,OAAO,KAAK;AAAA,IAC9B;AAAA,IAED,gBAAgB,MAAM;AACrB,YAAM,OAAO,KAAK,UAAU,IAAI;AAChC,UAAI,MAAM;AACTA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,KAAK;AAAA,UACZ,SAAS,MAAM,KAAK,IAAI;AAAA,KAAQ,KAAK,OAAO;AAAA,KAAQ,KAAK,UAAU,CAAC;AAAA,KAAQ,KAAK,WAAW,CAAC;AAAA,UAC7F,YAAY;AAAA,UACZ,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChB,mBAAK,YAAY,IAAI;AAAA,YACtB;AAAA,UACD;AAAA,QACD,CAAC;AAAA,aACK;AACNA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,oBAAoB,OAAO;AAC1B,YAAM,OAAO,KAAK,cAAc,KAAK;AACrC,UAAI,MAAM;AAET,cAAM,cAAc,CAAC,UAAU,UAAU,YAAY,SAAS,SAAS,SAAS,SAAS,YAAY,MAAM,EAAE,SAAS,KAAK,IAAI;AAE/H,YAAI,aAAa;AAEhBA,wBAAAA,MAAI,gBAAgB;AAAA,YACnB,UAAU,CAAC,MAAM,MAAM,IAAI;AAAA,YAC3B,SAAS,CAAC,QAAQ;AACjB,kBAAI,IAAI,aAAa,GAAG;AACvB,qBAAK,UAAU,KAAK;AAAA,yBACV,IAAI,aAAa,GAAG;AAC9B,qBAAK,QAAQ,KAAK;AAAA,cACnB;AAAA,YACD;AAAA,UACD,CAAC;AAAA,eACK;AAENA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,KAAK;AAAA,YACZ,SAAS,MAAM,KAAK,IAAI;AAAA,KAAQ,KAAK,OAAO;AAAA,KAAQ,KAAK,QAAQ;AAAA,YACjE,YAAY;AAAA,YACZ,YAAY;AAAA,YACZ,aAAa;AAAA,YACb,SAAS,CAAC,QAAQ;AACjB,kBAAI,IAAI,SAAS;AAChB,qBAAK,QAAQ,KAAK;AAAA,cACnB;AAAA,YACD;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD;AAAA,IACA;AAAA,IAED,MAAM,YAAY,MAAM;AACvB,UAAI;AACH,aAAK,YAAY;AACjB,aAAK,cAAc;AAGnB,cAAM,WAAW,MAAMG,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,EAAE,KAAK;AAAA,QACd,CAAC;AAED,aAAK,YAAY;AAEjB,YAAI,SAAS,SAAS,mBAAmB;AACxCH,wBAAY,MAAA,MAAA,OAAA,wCAAA,WAAW,SAAS,IAAI;AACpCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAED,eAAK,iBAAgB;AAAA,QACtB,WAAW,SAAS,SAAS,kBAAkB;AAC9CA,wBAAc,MAAA,MAAA,SAAA,wCAAA,WAAW,SAAS,IAAI;AACtCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAAA,QACF,WAAW,SAAS,SAAS,SAAS;AACrC,eAAK,YAAY,SAAS,IAAI;AAAA,QAC/B;AAAA,MAEC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,wCAAc,WAAW,KAAK;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,cAAc,MAAM,WAAW;AAAA,UACtC,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,MAAM,UAAU,OAAO;AACtB,YAAM,OAAO,KAAK,cAAc,KAAK;AACrC,UAAI,CAAC;AAAM;AAGX,YAAM,cAAc;AAAA,QACnB,UAAU,CAAC,aAAa,UAAU;AAAA,QAClC,UAAU,CAAC,QAAQ;AAAA,QACnB,YAAY,CAAC,UAAU;AAAA,QACvB,SAAS,CAAC,OAAO;AAAA,QACjB,SAAS,CAAC,OAAO;AAAA,QACjB,SAAS,CAAC,OAAO;AAAA,QACjB,SAAS,CAAC,OAAO;AAAA,QACjB,QAAQ,CAAC,SAAS,OAAO;AAAA;AAG1B,YAAM,gBAAgB,YAAY,KAAK,IAAI,KAAK,CAAA;AAChD,UAAI,cAAc,WAAW,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAGA,UAAI,cAAc,SAAS,GAAG;AAC7B,cAAM,YAAY;AAAA,UACjB,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,SAAS;AAAA;AAGV,cAAM,cAAc,cAAc,IAAI,UAAQ,UAAU,IAAI,KAAK,IAAI;AACrEA,sBAAAA,MAAI,gBAAgB;AAAA,UACnB,UAAU;AAAA,UACV,SAAS,CAAC,QAAQ;AACjB,kBAAM,eAAe,cAAc,IAAI,QAAQ;AAC/C,iBAAK,YAAY,OAAO,YAAY;AAAA,UACrC;AAAA,QACD,CAAC;AAAA,aACK;AACN,aAAK,YAAY,OAAO,cAAc,CAAC,CAAC;AAAA,MACzC;AAAA,IACA;AAAA,IAED,MAAM,YAAY,OAAO,MAAM;AAC9B,UAAI;AACH,aAAK,YAAY;AACjB,aAAK,cAAc;AAGnBE,wBAAS,UAAC,YAAY,cAAc;AAAA,UACnC,YAAY;AAAA,UACZ,WAAW;AAAA,QACZ,CAAC;AAAA,MAEA,SAAO,OAAO;AACfF,mFAAc,SAAS,KAAK;AAC5B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,WAAW,MAAM;AAAA,UACxB,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,QAAQ,OAAO;AAEdA,oBAAA,MAAA,MAAA,OAAA,wCAAY,SAAS,KAAK;AAAA,IAE1B;AAAA,IAED,MAAM,gBAAgB;AACrB,UAAI;AACH,aAAK,YAAY;AACjB,aAAK,cAAc;AAGnBE,wBAAAA,UAAU,YAAY,WAAW,CAAA,CAAE;AAAA,MAElC,SAAO,OAAO;AACfF,mFAAc,SAAS,KAAK;AAC5B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,WAAW,MAAM;AAAA,UACxB,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,qBAAqB,MAAM;AAC1B,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,OAAA,wCAAY,SAAS,IAAI;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAED,WAAK,iBAAgB;AAAA,IACrB;AAAA,IAED,oBAAoB,MAAM;AACzB,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,SAAA,wCAAc,SAAS,IAAI;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IAED,qBAAqB,MAAM;AAC1B,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,OAAA,wCAAY,WAAW,IAAI;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAED,WAAK,iBAAgB;AAAA,IACrB;AAAA,IAED,oBAAoB,MAAM;AACzB,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,SAAA,wCAAc,WAAW,IAAI;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IAED,mBAAmB,MAAM;AACxB,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,OAAA,yCAAY,SAAS,IAAI;AACzBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAED,WAAK,iBAAgB;AAAA,IACrB;AAAA,IAED,kBAAkB,MAAM;AACvB,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,SAAA,yCAAc,SAAS,IAAI;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IAED,MAAM,kBAAkB;AACvB,UAAI;AACH,aAAK,YAAY;AACjB,aAAK,cAAc;AAGnB,cAAM,WAAW,MAAMG,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,QACR,CAAC;AAED,aAAK,YAAY;AAEjB,YAAI,SAAS,SAAS,kBAAkB;AACvCH,wBAAY,MAAA,MAAA,OAAA,yCAAA,WAAW,SAAS,IAAI;AACpCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAED,cAAI,SAAS,KAAK,UAAU;AAC3B,iBAAK,oBAAoB,SAAS,KAAK;AAAA,UACxC;AAEA,eAAK,iBAAgB;AAAA,QACtB,WAAW,SAAS,SAAS,iBAAiB;AAC7CA,wBAAc,MAAA,MAAA,SAAA,yCAAA,WAAW,SAAS,IAAI;AACtCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAAA,QACF,WAAW,SAAS,SAAS,SAAS;AACrC,eAAK,YAAY,SAAS,IAAI;AAAA,QAC/B;AAAA,MAEC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,KAAK;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,cAAc,MAAM,WAAW;AAAA,UACtC,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,oBAAoB,MAAM;AACzB,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,OAAA,yCAAY,WAAW,IAAI;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAED,UAAI,KAAK,UAAU;AAClB,aAAK,oBAAoB,KAAK;AAAA,MAC/B;AAEA,WAAK,iBAAgB;AAAA,IACrB;AAAA,IAED,mBAAmB,MAAM;AACxB,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,IAAI;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IAED,0BAA0B,MAAM;AAC/B,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,OAAA,yCAAY,WAAW,IAAI;AAC3BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAED,WAAK,iBAAgB;AAAA,IACrB;AAAA,IAED,yBAAyB,MAAM;AAC9B,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,IAAI;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IAED,iBAAiB;AAEhBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA,IAED,qBAAqB;AAEpBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,oBAAmB;AAAA,UACzB;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAED,MAAM,sBAAsB;AAC3B,UAAI;AACH,aAAK,YAAY;AACjB,aAAK,cAAc;AAGnB,cAAM,WAAW,MAAMG,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,QACR,CAAC;AAED,aAAK,YAAY;AAEjB,YAAI,SAAS,SAAS,wBAAwB;AAC7CH,wBAAY,MAAA,MAAA,OAAA,yCAAA,WAAW,SAAS,IAAI;AACpCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAED,eAAK,iBAAgB;AAAA,mBACX,SAAS,SAAS,uBAAuB;AACnDA,wBAAc,MAAA,MAAA,SAAA,yCAAA,WAAW,SAAS,IAAI;AACtCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAAA,QACF,WAAW,SAAS,SAAS,SAAS;AACrC,eAAK,YAAY,SAAS,IAAI;AAAA,QAC/B;AAAA,MAEC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,yCAAc,WAAW,KAAK;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,cAAc,MAAM,WAAW;AAAA,UACtC,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,iBAAiB;AAEhBA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACN,CAAC;AAAA,IACD;AAAA,IAED,oBAAoB,MAAM;AACzB,UAAI,CAAC,QAAQ,CAAC,KAAK;AAAS,eAAO;AACnC,cAAQ,KAAK,SAAO;AAAA,QACnB,KAAK;AAAU,iBAAO;AAAA,QACtB,KAAK;AAAY,iBAAO;AAAA,QACxB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAa,iBAAO;AAAA,QACzB;AAAS,iBAAO;AAAA,MACjB;AAAA,IACA;AAAA,IAED,UAAU,KAAK;AACd,WAAK,YAAY;AAAA,IACjB;AAAA,IAED,gBAAgB,MAAM,OAAO;AAE5BA,oBAAA,MAAA,MAAA,OAAA,yCAAY,UAAU,MAAM,KAAK;AAAA,IACjC;AAAA,IAED,eAAe,KAAK;AACnB,YAAM,SAAS;AAAA,QACd,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA;AAEP,aAAO,OAAO,GAAG,KAAK;AAAA,IACtB;AAAA,IAED,aAAa,MAAM;AAClB,YAAM,UAAU;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA;AAER,aAAO,QAAQ,IAAI,KAAK;AAAA,IACxB;AAAA,IAED,0BAA0B;AACzB,YAAM,YAAY,KAAK,WAAW;AAClC,UAAI,CAAC,aAAa,CAAC,UAAU;AAAU,eAAO;AAE9C,YAAM,WAAW,UAAU,YAAY;AACvC,YAAM,UAAU,UAAU,YAAY;AACtC,YAAM,aAAa,UAAU,eAAe;AAG5C,YAAM,eAAe,UAAU;AAC/B,UAAI,gBAAgB;AAAG,eAAO;AAE9B,YAAM,kBAAkB,WAAW;AACnC,YAAM,UAAU,KAAK,IAAI,KAAK,KAAK,IAAI,GAAI,kBAAkB,eAAgB,GAAG,CAAC;AACjF,aAAO,KAAK,MAAM,OAAO;AAAA,IACzB;AAAA,IAED,MAAM,kBAAkB;AACvB,WAAK,cAAc,MAAMC,gBAAS,UAAC,eAAc;AAAA,IACjD;AAAA;AAAA,IAGD,MAAM,qBAAqB;AAC1B,UAAI;AACH,aAAK,aAAa,MAAMA,gBAAS,UAAC,cAAa;AAAA,MAChD,SAAS,GAAG;AACXD,oFAAc,YAAY,CAAC;AAC3BA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAK,CAAG;AAAA,MAChD;AAAA,IACA;AAAA;AAAA,IAGD,iBAAiB,QAAQ;AACxB,WAAK,iBAAiB;AACtB,WAAK,kBAAkB;AAAA,IACvB;AAAA,IAED,mBAAmB;AAClB,WAAK,kBAAkB;AACvB,WAAK,iBAAiB;IACtB;AAAA,IAED,uBAAuB;AAEtB,aAAO;AAAA,IACP;AAAA,IAED,yBAAyB;;AACxB,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,iBAAjC,mBAA+C,wBAAuB;AAAA,IAC7E;AAAA,IAED,8BAA8B;;AAC7B,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,iBAAjC,mBAA+C,aAAY;AAAA,IAClE;AAAA,IAED,wBAAwB;;AACvB,UAAI,KAAK,oBAAoB;AAC5B,eAAO,KAAK,mBAAmB,mBAAmB,QAAQ,CAAC;AAAA,MAC5D;AAEA,YAAM,YAAY,KAAK,WAAW,qBAAqB;AACvD,YAAM,iBAAe,UAAK,WAAW,WAAhB,mBAAwB,OAAM;AACnD,YAAM,QAAQ,KAAK,IAAI,IAAM,eAAe,MAAM,IAAI;AACtD,YAAM,aAAa,IAAM,QAAQ,KAAK,IAAI,eAAe,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC;AAC7E,cAAQ,YAAY,YAAY,QAAQ,CAAC;AAAA,IACzC;AAAA;AAAA,IAGD,mBAAmB;;AAClB,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,aAAjC,mBAA2C,qBAAoB;AAAA,IACtE;AAAA,IAED,yBAAyB;;AACxB,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,aAAjC,mBAA2C,iBAAgB;AAAA,IAClE;AAAA;AAAA,IAGD,uBAAuB;;AACtB,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,iBAAjC,mBAA+C,qBAAoB;AAAA,IAC1E;AAAA,IAED,4BAA4B;;AAC3B,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,iBAAjC,mBAA+C,mBAAkB;AAAA,IACxE;AAAA;AAAA,IAGD,yBAAyB;;AACxB,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,YAAjC,mBAA0C,6BAA4B;AAAA,IAC7E;AAAA,IAED,uBAAuB;;AACtB,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,YAAjC,mBAA0C,2BAA0B;AAAA,IAC3E;AAAA,IAED,8BAA8B;;AAC7B,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,YAAjC,mBAA0C,kBAAiB;AAAA,IAClE;AAAA,IAED,4BAA4B;;AAC3B,eAAO,sBAAK,eAAL,mBAAiB,mBAAjB,mBAAiC,YAAjC,mBAA0C,gBAAe;AAAA,IACjE;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClzCA,GAAG,WAAW,eAAe;"}