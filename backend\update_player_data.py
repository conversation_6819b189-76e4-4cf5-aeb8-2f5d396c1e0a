#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新玩家数据脚本
为所有现有玩家补全新增字段
"""

import aiosqlite
import asyncio
import json
import os
from datetime import datetime

# 数据库路径
DB_PATH = os.path.join(os.path.dirname(__file__), 'game.db')

async def update_player_data():
    """更新所有玩家数据，补全缺失字段"""
    print("🔧 开始更新玩家数据...")
    print("=" * 50)
    
    try:
        async with aiosqlite.connect(DB_PATH) as db:
            # 获取所有玩家数据
            async with db.execute("""
                SELECT p.user_id, p.data, u.character_name
                FROM players p
                JOIN users u ON p.user_id = u.id
                WHERE u.status = 'active'
            """) as cursor:
                rows = await cursor.fetchall()
            
            print(f"📊 找到 {len(rows)} 个玩家需要更新")
            
            updated_count = 0
            for user_id, data_str, character_name in rows:
                try:
                    # 解析玩家数据
                    player_data = json.loads(data_str) if data_str else {}
                    
                    # 记录是否有更新
                    has_updates = False
                    
                    # 补全 adventure_count 字段
                    if 'adventure_count' not in player_data:
                        player_data['adventure_count'] = 0
                        has_updates = True
                        print(f"  ✅ {character_name}: 添加 adventure_count 字段")
                    
                    # 补全其他可能缺失的字段
                    if 'experience' not in player_data:
                        player_data['experience'] = 0
                        has_updates = True
                        print(f"  ✅ {character_name}: 添加 experience 字段")
                    
                    if 'skill_points' not in player_data:
                        player_data['skill_points'] = 0
                        has_updates = True
                        print(f"  ✅ {character_name}: 添加 skill_points 字段")
                    
                    if 'money' not in player_data:
                        player_data['money'] = 1000
                        has_updates = True
                        print(f"  ✅ {character_name}: 添加 money 字段")
                    
                    if 'level' not in player_data:
                        player_data['level'] = 1
                        has_updates = True
                        print(f"  ✅ {character_name}: 添加 level 字段")
                    
                    # 确保数值字段为整数
                    numeric_fields = ['adventure_count', 'experience', 'skill_points', 'money', 'level']
                    for field in numeric_fields:
                        if field in player_data:
                            try:
                                player_data[field] = int(player_data[field])
                            except (ValueError, TypeError):
                                player_data[field] = 0
                                has_updates = True
                                print(f"  🔧 {character_name}: 修复 {field} 字段数据类型")
                    
                    # 如果有更新，保存到数据库
                    if has_updates:
                        await db.execute("""
                            UPDATE players 
                            SET data = ? 
                            WHERE user_id = ?
                        """, (json.dumps(player_data, ensure_ascii=False), user_id))
                        updated_count += 1
                    
                except Exception as e:
                    print(f"  ❌ {character_name}: 更新失败 - {e}")
                    continue
            
            # 提交所有更改
            await db.commit()
            
            print(f"\n✅ 更新完成！")
            print(f"📊 总计处理: {len(rows)} 个玩家")
            print(f"🔧 实际更新: {updated_count} 个玩家")
            
    except Exception as e:
        print(f"❌ 更新过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

async def verify_data():
    """验证数据更新结果"""
    print("\n🔍 验证数据更新结果...")
    print("-" * 30)
    
    try:
        async with aiosqlite.connect(DB_PATH) as db:
            # 检查 adventure_count 字段
            async with db.execute("""
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN json_extract(data, '$.adventure_count') IS NOT NULL THEN 1 END) as has_adventure_count,
                    COUNT(CASE WHEN json_extract(data, '$.experience') IS NOT NULL THEN 1 END) as has_experience,
                    COUNT(CASE WHEN json_extract(data, '$.money') IS NOT NULL THEN 1 END) as has_money
                FROM players p
                JOIN users u ON p.user_id = u.id
                WHERE u.status = 'active'
            """) as cursor:
                row = await cursor.fetchone()
                total, has_adventure_count, has_experience, has_money = row
                
                print(f"📊 数据完整性检查:")
                print(f"  总玩家数: {total}")
                print(f"  有 adventure_count 字段: {has_adventure_count}/{total}")
                print(f"  有 experience 字段: {has_experience}/{total}")
                print(f"  有 money 字段: {has_money}/{total}")
                
                if has_adventure_count == total and has_experience == total and has_money == total:
                    print("✅ 所有字段完整！")
                else:
                    print("⚠️ 仍有字段缺失，可能需要重新运行更新")
            
            # 显示一些统计数据
            print(f"\n📈 排行榜数据预览:")
            
            # 富豪榜前3
            async with db.execute("""
                SELECT u.character_name, json_extract(p.data, '$.money') as money
                FROM users u 
                JOIN players p ON u.id = p.user_id 
                WHERE u.status = 'active' AND p.data IS NOT NULL
                ORDER BY CAST(COALESCE(json_extract(p.data, '$.money'), 0) AS INTEGER) DESC 
                LIMIT 3
            """) as cursor:
                rows = await cursor.fetchall()
                print("  💰 富豪榜前3:")
                for i, (name, money) in enumerate(rows, 1):
                    print(f"    {i}. {name} - {money}两")
            
            # 经验榜前3
            async with db.execute("""
                SELECT u.character_name, json_extract(p.data, '$.experience') as experience
                FROM users u 
                JOIN players p ON u.id = p.user_id 
                WHERE u.status = 'active' AND p.data IS NOT NULL
                ORDER BY CAST(COALESCE(json_extract(p.data, '$.experience'), 0) AS INTEGER) DESC 
                LIMIT 3
            """) as cursor:
                rows = await cursor.fetchall()
                print("  ⭐ 经验榜前3:")
                for i, (name, exp) in enumerate(rows, 1):
                    print(f"    {i}. {name} - {exp}点")
            
            # 肝帝榜前3
            async with db.execute("""
                SELECT u.character_name, json_extract(p.data, '$.adventure_count') as adventure_count
                FROM users u 
                JOIN players p ON u.id = p.user_id 
                WHERE u.status = 'active' AND p.data IS NOT NULL
                ORDER BY CAST(COALESCE(json_extract(p.data, '$.adventure_count'), 0) AS INTEGER) DESC 
                LIMIT 3
            """) as cursor:
                rows = await cursor.fetchall()
                print("  🗡️ 肝帝榜前3:")
                for i, (name, count) in enumerate(rows, 1):
                    count = count or 0
                    print(f"    {i}. {name} - {count}次")
                    
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")

async def main():
    """主函数"""
    print("🔧 玩家数据更新工具")
    print("=" * 50)
    
    # 检查数据库是否存在
    if not os.path.exists(DB_PATH):
        print("❌ 数据库文件不存在，请先启动游戏服务器初始化数据库")
        return
    
    # 更新数据
    await update_player_data()
    
    # 验证结果
    await verify_data()
    
    print("\n" + "=" * 50)
    print("🎮 数据更新完成！现在可以测试排行榜功能了")

if __name__ == "__main__":
    asyncio.run(main())
