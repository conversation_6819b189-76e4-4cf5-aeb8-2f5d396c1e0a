#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增功能：地图玩家列表、玩家交互、聊天系统
"""

import asyncio
import json
import websockets
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestClient:
    def __init__(self, username, password, character_name):
        self.username = username
        self.password = password
        self.character_name = character_name
        self.websocket = None
        self.token = None
        
    async def connect(self):
        """连接到服务器"""
        try:
            self.websocket = await websockets.connect("ws://localhost:8080")
            logger.info(f"客户端 {self.username} 连接成功")
            return True
        except Exception as e:
            logger.error(f"连接失败: {e}")
            return False
    
    async def send_message(self, message_type, data=None):
        """发送消息"""
        if not self.websocket:
            return None
            
        message = {
            "type": message_type,
            "data": data or {}
        }
        
        try:
            await self.websocket.send(json.dumps(message, ensure_ascii=False))
            response = await self.websocket.recv()
            return json.loads(response)
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return None
    
    async def register_and_login(self):
        """注册并登录"""
        # 尝试注册
        register_response = await self.send_message("register", {
            "username": self.username,
            "password": self.password,
            "character_name": self.character_name,
            "gender": "男"
        })
        
        if register_response and register_response.get("type") == "register_success":
            logger.info(f"注册成功: {self.username}")
        else:
            logger.info(f"注册失败或用户已存在: {self.username}")
        
        # 登录
        login_response = await self.send_message("login", {
            "username": self.username,
            "password": self.password
        })
        
        if login_response and login_response.get("type") == "login_success":
            self.token = login_response["data"]["token"]
            logger.info(f"登录成功: {self.username}, token: {self.token}")
            return True
        else:
            logger.error(f"登录失败: {self.username}")
            return False
    
    async def test_get_map_players(self):
        """测试获取地图玩家列表"""
        response = await self.send_message("get_map_players", {
            "map_id": "changan"
        })
        
        if response:
            logger.info(f"获取地图玩家列表: {response}")
            return response.get("data", {}).get("players", [])
        return []
    
    async def test_player_action(self, target_player_id, action):
        """测试玩家交互"""
        response = await self.send_message("player_action", {
            "action": action,
            "target_player_id": target_player_id,
            "target_player_name": "测试玩家"
        })
        
        if response:
            logger.info(f"玩家交互 {action}: {response}")
        return response
    
    async def test_send_chat_message(self, content):
        """测试发送聊天消息"""
        response = await self.send_message("send_chat_message", {
            "content": content
        })
        
        if response:
            logger.info(f"发送聊天消息: {response}")
        return response
    
    async def test_get_chat_messages(self):
        """测试获取聊天消息"""
        response = await self.send_message("get_chat_messages", {})
        
        if response:
            logger.info(f"获取聊天消息: {response}")
            return response.get("data", {}).get("messages", [])
        return []
    
    async def close(self):
        """关闭连接"""
        if self.websocket:
            await self.websocket.close()

async def test_new_features():
    """测试新功能"""
    logger.info("开始测试新增功能...")
    
    # 创建两个测试客户端
    client1 = TestClient("test_user1", "password123", "测试侠客1")
    client2 = TestClient("test_user2", "password123", "测试侠客2")
    
    try:
        # 连接并登录
        if not await client1.connect() or not await client1.register_and_login():
            logger.error("客户端1连接或登录失败")
            return
            
        if not await client2.connect() or not await client2.register_and_login():
            logger.error("客户端2连接或登录失败")
            return
        
        # 等待一下让服务器处理
        await asyncio.sleep(1)
        
        # 测试获取地图玩家列表
        logger.info("\n=== 测试获取地图玩家列表 ===")
        players1 = await client1.test_get_map_players()
        players2 = await client2.test_get_map_players()
        
        # 测试玩家交互
        if players1:
            logger.info("\n=== 测试玩家交互 ===")
            target_player = players1[0]
            await client2.test_player_action(target_player["id"], "view")
            await client2.test_player_action(target_player["id"], "attack")
            await client2.test_player_action(target_player["id"], "steal")
        
        # 测试聊天系统
        logger.info("\n=== 测试聊天系统 ===")
        await client1.test_send_chat_message("大家好，我是测试侠客1！")
        await client2.test_send_chat_message("你好，我是测试侠客2！")
        
        # 等待消息处理
        await asyncio.sleep(1)
        
        # 获取聊天历史
        messages1 = await client1.test_get_chat_messages()
        messages2 = await client2.test_get_chat_messages()
        
        logger.info("测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
    finally:
        # 关闭连接
        await client1.close()
        await client2.close()

if __name__ == "__main__":
    asyncio.run(test_new_features())
