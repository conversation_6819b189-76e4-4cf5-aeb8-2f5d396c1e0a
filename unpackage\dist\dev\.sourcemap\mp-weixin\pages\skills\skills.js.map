{"version": 3, "file": "skills.js", "sources": ["pages/skills/skills.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2tpbGxzL3NraWxscy52dWU"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 三大选项卡 -->\n\t\t<view class=\"category-tabs\">\n\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 'equip' }\" @click=\"switchTab('equip')\">\n\t\t\t\t<text class=\"tab-text\">使用武功</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 'skills' }\" @click=\"switchTab('skills')\">\n\t\t\t\t<text class=\"tab-text\">武功技能</text>\n\t\t\t</view>\n\t\t\t<view class=\"tab-item\" :class=\"{ active: activeTab === 'life' }\" @click=\"switchTab('life')\">\n\t\t\t\t<text class=\"tab-text\">生活技能</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 使用武功：分类型展示，每类可装备一个 -->\n\t\t<view v-if=\"Object.values(martialSkillsByType).some(arr => arr.length > 0) && activeTab === 'equip'\">\n\t\t\t<view v-for=\"typeObj in martialTypes\" :key=\"typeObj.key\" class=\"skill-group\">\n\t\t\t\t<view class=\"skill-item\">\n\t\t\t\t\t<view class=\"skill-info\">\n\t\t\t\t\t\t<view class=\"title-selector-row\">\n\t\t\t\t\t\t\t<text class=\"group-title\">{{ typeObj.label }}</text>\n\t\t\t\t\t\t\t<view class=\"equip-selector\">\n\t\t\t\t\t\t\t\t<button class=\"martial-select-tag\" @click=\"openMartialSelect(typeObj.key)\" :disabled=\"getMartialsByType(typeObj.key).length === 0\">\n\t\t\t\t\t\t\t\t\t<text class=\"martial-select-tag-text\">\n\t\t\t\t\t\t\t\t\t\t{{ getSelectedMartialName(typeObj.key) || (getMartialsByType(typeObj.key).length === 0 ? '暂无可选武功' : '请选择武功') }}\n\t\t\t\t\t\t\t\t\t\t{{ getSelectedMartial(typeObj.key) && isMartialEquipped(getSelectedMartial(typeObj.key)) ? '(已装备)' : '' }}\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t\t<button \n\t\t\t\t\t\t\t\t\tv-if=\"getSelectedMartial(typeObj.key) && isMartialEquipped(getSelectedMartial(typeObj.key))\"\n\t\t\t\t\t\t\t\t\tclass=\"martial-clear-btn\"\n\t\t\t\t\t\t\t\t\t@click=\"unequipMartial(typeObj.key)\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t×\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view v-else-if=\"activeTab === 'equip'\" style=\"text-align:center;color:#aaa;padding:32px 0;\">暂无武功数据</view>\n\n\t\t<!-- 武功技能：展示所有已学会武功 -->\n\t\t<view v-if=\"activeTab === 'skills'\">\n\t\t\t<view v-if=\"learnedMartials.length\">\n\t\t\t\t<view class=\"skill-group\">\n\t\t\t\t\t<view class=\"group-header\">\n\t\t\t\t\t\t<text class=\"group-title\">已学会武功技能（{{ learnedMartialCount }}）</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-for=\"skill in learnedMartials\" :key=\"skill.名称 || skill.name\" class=\"skill-item\">\n\t\t\t\t\t\t<view class=\"skill-info\">\n\t\t\t\t\t\t\t<text class=\"skill-name\">{{ skill.名称 || skill.name }}</text>\n\t\t\t\t\t\t\t<text class=\"skill-level\">Lv.{{ skill.等级 || skill.level }}</text>\n\t\t\t\t\t\t\t<text class=\"skill-category\">{{ skill.类别 || skill.category }}</text>\n\t\t\t\t\t\t\t<text class=\"skill-school\" v-if=\"skill.门派 || (skill.school && skill.school !== '无门派')\">{{ skill.门派 || skill.school }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"detail-btn\" @click=\"showSkillDetail(skill)\">详情</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"learnedMartials.length === 0\" class=\"empty-skills\">暂无已学会武功</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-else style=\"text-align:center;color:#aaa;padding:32px 0;\">暂无武功技能</view>\n\t\t</view>\n\n\t\t<!-- 生活技能：展示所有生活技能 -->\n\t\t<view v-if=\"activeTab === 'life'\">\n\t\t\t<view v-if=\"lifeSkills.length\">\n\t\t\t\t<view class=\"skill-group\">\n\t\t\t\t\t<view class=\"group-header\">\n\t\t\t\t\t\t<text class=\"group-title\">生活技能</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-for=\"skill in lifeSkills\" :key=\"skill.名称 || skill.name\" class=\"skill-item\">\n\t\t\t\t\t\t<view class=\"skill-info\">\n\t\t\t\t\t\t\t<text class=\"skill-name\">{{ skill.名称 || skill.name }}</text>\n\t\t\t\t\t\t\t<text class=\"skill-level\">Lv.{{ skill.等级 || skill.level }}</text>\n\t\t\t\t\t\t\t<text class=\"skill-category\">{{ skill.类别 || skill.category }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<button class=\"detail-btn\" @click=\"showSkillDetail(skill)\">详情</button>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"lifeSkills.length === 0\" class=\"empty-skills\">暂无生活技能</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view v-else style=\"text-align:center;color:#aaa;padding:32px 0;\">暂无生活技能</view>\n\t\t</view>\n\n\t\t<!-- 技能详情弹窗 -->\n\t\t<view class=\"modal-overlay\" v-if=\"showDetail\" @click=\"closeDetail\">\n\t\t\t<view class=\"modal-content\" @click.stop>\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">武学详情</text>\n\t\t\t\t\t<text class=\"modal-close\" @click=\"closeDetail\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-body\" v-if=\"selectedSkill\">\n\t\t\t\t\t<view class=\"detail-header\">\n\t\t\t\t\t\t<text class=\"detail-name\">{{ selectedSkill.名称 || selectedSkill.name }}</text>\n\t\t\t\t\t\t<view class=\"detail-status\">\n\t\t\t\t\t\t\t<text class=\"detail-level\">{{ selectedSkill.等级 || selectedSkill.level }}级</text>\n\t\t\t\t\t\t\t<text class=\"detail-school\" v-if=\"selectedSkill.门派 || (selectedSkill.school && selectedSkill.school !== '无门派')\">{{ selectedSkill.门派 || selectedSkill.school }}</text>\n\t\t\t\t\t\t\t<text class=\"detail-equipped\" v-if=\"selectedSkill.装备 || selectedSkill.equipped\">已装备</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"detail-desc\">{{ selectedSkill.描述 || selectedSkill.description }}</text>\n\t\t\t\t\t<view class=\"detail-progress\" v-if=\"selectedSkill.等级 > 0\">\n\t\t\t\t\t\t<text class=\"progress-title\">进度:</text>\n\t\t\t\t\t\t<view class=\"progress-bar\">\n\t\t\t\t\t\t\t<view class=\"progress-bg\">\n\t\t\t\t\t\t\t\t<view class=\"progress-fill\" :style=\"{ width: getSkillProgress(selectedSkill) + '%' }\"></view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<text class=\"progress-text\">{{ selectedSkill.经验 || selectedSkill.exp }}/{{ selectedSkill.最大经验 || selectedSkill.maxExp }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-effects\" v-if=\"selectedSkill.等级 > 0\">\n\t\t\t\t\t\t<text class=\"effects-title\">当前效果:</text>\n\t\t\t\t\t\t<text class=\"effects-text\">{{ getSkillEffects(selectedSkill) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-category\">\n\t\t\t\t\t\t<text class=\"category-title\">武学分类:</text>\n\t\t\t\t\t\t<text class=\"category-text\">{{ selectedSkill.类别 || selectedSkill.category }} - {{ selectedSkill.等级 || selectedSkill.level }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"detail-moves\" v-if=\"selectedSkill.招式 || selectedSkill.moves\">\n\t\t\t\t\t\t<text class=\"moves-title\">招式列表:</text>\n\t\t\t\t\t\t<view class=\"moves-list\">\n\t\t\t\t\t\t\t<view class=\"move-item\" v-for=\"move in selectedSkill.moves\" :key=\"move.name\">\n\t\t\t\t\t\t\t\t<text class=\"move-name\">{{ move.name }}</text>\n\t\t\t\t\t\t\t\t<text class=\"move-unlock-level\">（解锁等级: {{ move.unlock_level }}）</text>\n\t\t\t\t\t\t\t\t<text class=\"move-status\" :style=\"{color: move.unlocked ? '#4caf50' : '#aaa'}\">\n\t\t\t\t\t\t\t\t\t{{ move.unlocked ? '已解锁' : '未解锁' }}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button class=\"modal-btn cancel-btn\" @click=\"closeDetail\">关闭</button>\n\t\t\t\t\t<button \n\t\t\t\t\t\tclass=\"modal-btn confirm-btn\" \n\t\t\t\t\t\t@click=\"studyMartial(selectedSkill)\"\n\t\t\t\t\t\t:disabled=\"!isAuthed || !selectedSkill.解锁 || !selectedSkill.unlocked\"\n\t\t\t\t\t>\n\t\t\t\t\t\t学习\n\t\t\t\t\t</button>\n\t\t\t\t\t<button \n\t\t\t\t\t\tv-if=\"canUseMartial(selectedSkill)\"\n\t\t\t\t\t\tclass=\"modal-btn use-btn\" \n\t\t\t\t\t\t:class=\"{ 'unequip': selectedSkill.装备 || selectedSkill.equipped }\"\n\t\t\t\t\t\t@click=\"toggleMartialUse(selectedSkill)\"\n\t\t\t\t\t\t:disabled=\"!isAuthed\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{ selectedSkill.装备 || selectedSkill.equipped ? '卸下' : '使用' }}\n\t\t\t\t\t</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 招式弹窗 -->\n\t\t<view class=\"modal-overlay\" v-if=\"showMovesModal\" @click=\"closeMovesModal\">\n\t\t\t<view class=\"modal-content\" @click.stop>\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">{{ selectedSkill?.名称 || selectedSkill?.name }} - 招式</text>\n\t\t\t\t\t<text class=\"modal-close\" @click=\"closeMovesModal\">×</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-body\" v-if=\"selectedSkill\">\n\t\t\t\t\t<view class=\"moves-container\">\n\t\t\t\t\t\t<view class=\"move-item\" v-for=\"(move, index) in selectedSkill.招式 || selectedSkill.moves\" :key=\"index\">\n\t\t\t\t\t\t\t<text class=\"move-name\">{{ move }}</text>\n\t\t\t\t\t\t\t<text class=\"move-desc\">{{ getMoveDescription(selectedSkill.名称 || selectedSkill.name, move) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"modal-footer\">\n\t\t\t\t\t<button class=\"modal-btn cancel-btn\" @click=\"closeMovesModal\">关闭</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 武功选择弹窗 -->\n\t\t<view v-if=\"showMartialSelect\" class=\"martial-select-modal-mask\" @click=\"closeMartialSelect\">\n\t\t\t<view class=\"martial-select-modal\" @click.stop>\n\t\t\t\t<view class=\"martial-select-title\">选择{{ martialSelectTypeLabel }}武功</view>\n\t\t\t\t<scroll-view class=\"martial-select-list\" scroll-y=\"true\">\n\t\t\t\t\t<view v-for=\"(skill, idx) in getMartialsByType(martialSelectType)\" :key=\"skill.name\" class=\"martial-select-item\" :class=\"{ selected: skill.name === selectedMartials[martialSelectType] }\" @click=\"selectMartial(skill, martialSelectType)\">\n\t\t\t\t\t\t<view class=\"martial-select-header\">\n\t\t\t\t\t\t\t<text class=\"martial-select-name\">{{ skill.name }}</text>\n\t\t\t\t\t\t\t<view class=\"martial-select-status\">\n\t\t\t\t\t\t\t\t<text class=\"martial-select-level\">Lv.{{ skill.level || skill.等级 || 1 }}</text>\n\t\t\t\t\t\t\t\t<text class=\"martial-select-quality\">{{ skill.quality || skill.品质 || '普通' }}品质</text>\n\t\t\t\t\t\t\t\t<text v-if=\"isMartialEquipped(skill)\" class=\"martial-select-equipped\">已装备</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<text class=\"martial-select-desc\">{{ skill.desc || skill.描述 || '' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"getMartialsByType(martialSelectType).length === 0\" class=\"martial-select-empty\">暂无可选武功</view>\n\t\t\t\t</scroll-view>\n\t\t\t\t<button class=\"martial-select-cancel\" @click=\"closeMartialSelect\">取消</button>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport gameState from '../../utils/gameState.js'\nimport { gameUtils } from '../../utils/gameData.js'\nimport wsManager from '../../utils/websocket.js'\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tplayer: {},\n\t\t\tselectedMartials: {}, // 每种类型选中的武功\n\t\t\tmartialTypes: [\n\t\t\t\t{ key: '剑法', label: '剑法' },\n\t\t\t\t{ key: '刀法', label: '刀法' },\n\t\t\t\t{ key: '拳法', label: '拳法' },\n\t\t\t\t// { key: '空手', label: '空手' }, // 移除空手分类\n\t\t\t\t{ key: '招架', label: '招架' },\n\t\t\t\t{ key: '轻功', label: '轻功' },\n\t\t\t\t{ key: '内功', label: '内功' },\n\t\t\t\t{ key: '暗器', label: '暗器' }\n\t\t\t],\n\t\t\tactiveTab: 'equip', // 新增，默认显示\"使用武功\"tab\n\t\t\tshowDetail: false,\n\t\t\tshowMovesModal: false,\n\t\t\tselectedSkill: null,\n\t\t\tisAuthed: false,\n\t\t\tshowMartialSelect: false,\n\t\t\tmartialSelectType: '',\n\t\t\tmartialSelectTypeLabel: '',\n\t\t\tbonusSummary: {}, // 新增：后端增益摘要\n\t\t}\n\t},\n\t\n\tcomputed: {\n\t\t// \"使用武功\"tab：每类显示所有已解锁武功，且过滤掉基础武功\n\t\tmartialSkillsByType() {\n\t\t\tconst groups = {};\n\t\t\tif (!this.player || !this.player.martial_skills) return groups;\n\t\t\tthis.martialTypes.forEach(typeObj => {\n\t\t\t\tgroups[typeObj.key] = this.player.martial_skills.filter(skill => \n\t\t\t\t\t(skill.type === typeObj.key || skill.类型 === typeObj.key || skill.类别 === typeObj.key) && \n\t\t\t\t\t(skill.unlocked || skill.解锁) &&\n\t\t\t\t\t!this.isBasicMartial(skill)\n\t\t\t\t);\n\t\t\t});\n\t\t\treturn groups;\n\t\t},\n\t\t// 武功技能（不含生活技能）\n\t\tlearnedMartials() {\n\t\t\tconst arr = (this.player && this.player.martial_skills)\n\t\t\t\t? this.player.martial_skills.filter(skill => (skill.unlocked || skill.解锁) && !this.isLifeSkill(skill))\n\t\t\t\t: []\n\t\t\treturn arr\n\t\t},\n\t\t// 生活技能\n\t\tlifeSkills() {\n\t\t\tconst arr = (this.player && this.player.martial_skills)\n\t\t\t\t? this.player.martial_skills.filter(skill => (skill.unlocked || skill.解锁) && this.isLifeSkill(skill))\n\t\t\t\t: []\n\t\t\treturn arr\n\t\t},\n\t\tlearnedMartialCount() {\n\t\t\treturn this.learnedMartials.length;\n\t\t}\n\t},\n\t\n\tonLoad() {\n\t\tthis.updateData();\n\t\tthis.fetchBonusSummary();\n\t\t// 监听 gameState 变化，自动刷新\n\t\tif (!this._gameStateCallback) {\n\t\t\tthis._gameStateCallback = (type, state) => {\n\t\t\t\tif (type === 'player') {\n\t\t\t\t\tthis.updateData();\n\t\t\t\t}\n\t\t\t};\n\t\t\tgameState.updateCallbacks.push(this._gameStateCallback);\n\t\t}\n\t},\n\tonUnload() {\n\t\t// 页面卸载时移除监听，防止内存泄漏\n\t\tif (this._gameStateCallback) {\n\t\t\tconst idx = gameState.updateCallbacks.indexOf(this._gameStateCallback);\n\t\t\tif (idx >= 0) gameState.updateCallbacks.splice(idx, 1);\n\t\t\tthis._gameStateCallback = null;\n\t\t}\n\t},\n\t\n\tonShow() {\n\t\tthis.updateData();\n\t\tthis.fetchBonusSummary();\n\t},\n\t\n\tmethods: {\n\t\tupdateData() {\n\t\t\tconsole.log('开始更新数据...');\n\t\t\t// 如果本地已有 player 数据，优先使用本地数据（避免被 gameState 的旧数据覆盖）\n\t\t\tif (!this.player || Object.keys(this.player).length === 0) {\n\t\t\t\tthis.player = Object.assign({}, gameState.getPlayerData());\n\t\t\t\tconsole.log('从gameState获取玩家数据');\n\t\t\t} else {\n\t\t\t\tconsole.log('使用现有玩家数据');\n\t\t\t}\n\t\t\tthis.isAuthed = gameState.isAuthenticated ? gameState.isAuthenticated() : gameState.isAuthed;\n\n\t\t\t// 确保 martial_skills 为数组格式\n\t\t\tif (this.player && this.player.martial_skills && !Array.isArray(this.player.martial_skills)) {\n\t\t\t\tconsole.log('转换武功数据为数组格式');\n\t\t\t\tthis.player.martial_skills = Object.keys(this.player.martial_skills).map(name => {\n\t\t\t\t\treturn { name, ...this.player.martial_skills[name] };\n\t\t\t\t})\n\t\t\t}\n\t\t\tif (!this.player.martial_skills) this.player.martial_skills = [];\n\n\t\t\tif (this.player && this.player.martial_skills) {\n\t\t\t\tconsole.log('武功总数:', this.player.martial_skills.length);\n\t\t\t\t// 输出所有已装备武功\n\t\t\t\tconst equippedSkills = this.player.martial_skills.filter(skill => skill.equipped || skill.装备);\n\t\t\t\tconsole.log('已装备武功:', equippedSkills.map(s => s.name || s.名称));\n\t\t\t\t\n\t\t\t\t// 重新计算 selectedMartials，确保与当前装备状态一致\n\t\t\t\tconsole.log('更新前的selectedMartials:', JSON.stringify(this.selectedMartials));\n\t\t\t\tthis.selectedMartials = {}\n\t\t\t\tthis.martialTypes.forEach(typeObj => {\n\t\t\t\t\tconst equipped = this.player.martial_skills.find(skill => \n\t\t\t\t\t\t(skill.type === typeObj.key || skill.类型 === typeObj.key || skill.类别 === typeObj.key) && \n\t\t\t\t\t\t(skill.equipped || skill.装备)\n\t\t\t\t\t)\n\t\t\t\t\tif (equipped) {\n\t\t\t\t\t\tconsole.log(`类型 ${typeObj.key} 找到已装备武功:`, equipped.name || equipped.名称);\n\t\t\t\t\t\tthis.selectedMartials[typeObj.key] = equipped.name || equipped.名称;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 如果没有已装备的，选择第一个可用的武功\n\t\t\t\t\t\tconst available = this.getMartialsByType(typeObj.key);\n\t\t\t\t\t\tif (available.length > 0) {\n\t\t\t\t\t\t\tconsole.log(`类型 ${typeObj.key} 没有已装备武功，选择第一个可用武功:`, available[0].name || available[0].名称);\n\t\t\t\t\t\t\tthis.selectedMartials[typeObj.key] = available[0].name || available[0].名称;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconsole.log(`类型 ${typeObj.key} 没有可用武功`);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t\tconsole.log('更新后的selectedMartials:', JSON.stringify(this.selectedMartials));\n\t\t\t\t\n\t\t\t\t// 强制刷新视图\n\t\t\t\tthis.$forceUpdate();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 选中、装备等逻辑可直接用 this.player.martial_skills\n\t\t\n\t\tconvertMartialData(martialSkills) {\n\t\t\tconst allSkills = []\n\t\t\t\n\t\t\t// 遍历所有武功分类\n\t\t\tObject.keys(martialSkills).forEach(category => {\n\t\t\t\tconst skills = martialSkills[category] || []\n\t\t\t\tskills.forEach(skill => {\n\t\t\t\t\t// 添加武功分类信息\n\t\t\t\t\tallSkills.push({\n\t\t\t\t\t\t...skill,\n\t\t\t\t\t\t类别: this.getSkillCategory(skill.名称 || skill.name),\n\t\t\t\t\t\t门派: this.getSkillSchool(skill.名称 || skill.name),\n\t\t\t\t\t\t等级: this.getSkillLevel(skill.名称 || skill.name),\n\t\t\t\t\t\t描述: this.getSkillDescription(skill.名称 || skill.name),\n\t\t\t\t\t\t招式: this.getSkillMoves(skill.名称 || skill.name),\n\t\t\t\t\t\t效果: this.getSkillEffects(skill.名称 || skill.name)\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t})\n\t\t\t\n\t\t\treturn allSkills\n\t\t},\n\t\t\n\t\tgetMartialsByType(type) {\n\t\t\tif (!this.player || !this.player.martial_skills) return [];\n\t\t\treturn this.player.martial_skills.filter(skill =>\n\t\t\t\t(skill.type === type || skill.类型 === type || skill.类别 === type) &&\n\t\t\t\t(skill.unlocked || skill.解锁) &&\n\t\t\t\t!this.isBasicMartial(skill)\n\t\t\t);\n\t\t},\n\t\t\n\t\t// 获取选中武功的索引\n\t\tgetSelectedMartialIndex(type) {\n\t\t\tconst selectedName = this.selectedMartials[type]\n\t\t\tif (!selectedName) return 0\n\t\t\tconst martials = this.getMartialsByType(type)\n\t\t\tconst index = martials.findIndex(skill => skill.name === selectedName)\n\t\t\treturn index >= 0 ? index : 0\n\t\t},\n\t\t\n\t\t// 获取选中武功的名称\n\t\tgetSelectedMartialName(type) {\n\t\t\tconst selectedName = this.selectedMartials[type]\n\t\t\tif (!selectedName) return ''\n\t\t\tconst martial = this.getMartialsByType(type).find(skill => skill.name === selectedName)\n\t\t\treturn martial ? martial.name : ''\n\t\t},\n\t\t\n\t\t// 获取选中的武功对象\n\t\tgetSelectedMartial(type) {\n\t\t\tconst selectedName = this.selectedMartials[type]\n\t\t\tif (!selectedName) return null\n\t\t\tconst result = this.getMartialsByType(type).find(skill => skill.name === selectedName) || null\n\t\t\treturn result\n\t\t},\n\t\t\n\t\t// 检查武功是否已装备\n\t\tisMartialEquipped(skill) {\n\t\t\tif (!skill) {\n\t\t\t\tconsole.log('isMartialEquipped: skill为空');\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\t\n\t\t\tconst isEquipped = skill.equipped || skill.装备;\n\t\t\tconsole.log('isMartialEquipped检查:', skill.name || skill.名称, '装备状态:', isEquipped);\n\t\t\t\n\t\t\t// 检查数据类型，帮助调试\n\t\t\tif (typeof skill.equipped === 'boolean') {\n\t\t\t\tconsole.log('  equipped是布尔值:', skill.equipped);\n\t\t\t} else if (skill.equipped !== undefined) {\n\t\t\t\tconsole.log('  equipped不是布尔值:', typeof skill.equipped, skill.equipped);\n\t\t\t}\n\t\t\t\n\t\t\tif (typeof skill.装备 === 'boolean') {\n\t\t\t\tconsole.log('  装备是布尔值:', skill.装备);\n\t\t\t} else if (skill.装备 !== undefined) {\n\t\t\t\tconsole.log('  装备不是布尔值:', typeof skill.装备, skill.装备);\n\t\t\t}\n\t\t\t\n\t\t\treturn isEquipped;\n\t\t},\n\t\t\n\t\t// 处理武功选择\n\t\tonMartialSelect(event, type) {\n\t\t\tconst index = event.detail.value\n\t\t\tconst martials = this.getMartialsByType(type)\n\t\t\tif (martials[index]) {\n\t\t\t\tthis.selectedMartials[type] = martials[index].name\n\t\t\t}\n\t\t},\n\t\t\n\t\tasync fetchPlayerDataFromServer() {\n\t\t\tconst res = await gameUtils.sendMessage({ type: 'get_player_data', data: {} });\n\t\t\tif (res.type === 'player_data') {\n\t\t\t\t// 更新 gameState，触发通知机制\n\t\t\t\tgameState.setPlayerData(res.data);\n\t\t\t\t// 重新获取最新数据并更新界面\n\t\t\t\tthis.updateData();\n\t\t\t\t\n\t\t\t\t// 确保视图更新\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t}, 50);\n\t\t\t}\n\t\t},\n\n\t\tasync equipMartial(type, skill) {\n\t\t\tif (!this.isAuthed) {\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' })\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (!skill.unlocked) {\n\t\t\t\tuni.showToast({ title: '该武学尚未解锁', icon: 'none' })\n\t\t\t\treturn\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tconst isCurrentlyEquipped = this.isMartialEquipped(skill);\n\t\t\t\tconst action = isCurrentlyEquipped ? 'unequip_martial' : 'use_martial';\n\t\t\t\tconst skillName = skill.name || skill.名称;\n\t\t\t\t\n\t\t\t\t// 只用 gameUtils.sendMessage，避免重复监听\n\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\ttype: action,\n\t\t\t\t\tdata: { skill_name: skillName }\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tif (response && response.type === action + '_success') {\n\t\t\t\t\t// 直接使用后端返回的 player 数据，确保数据一致性\n\t\t\t\t\tif (response.data.player) {\n\t\t\t\t\t\t// 确保 player 中的 martial_skills 为 list 格式\n\t\t\t\t\t\tif (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {\n\t\t\t\t\t\t\tresponse.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map(name => {\n\t\t\t\t\t\t\t\treturn { name, ...response.data.player.martial_skills[name] };\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 直接更新本地数据，不触发 gameState 通知，避免循环调用\n\t\t\t\t\t\tthis.player = Object.assign({}, response.data.player);\n\t\t\t\t\t\tgameState.player = response.data.player;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 强制更新数据和视图\n\t\t\t\t\t\tthis.updateData();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 确保视图更新\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t}, 50);\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 如果没有返回 player 数据，则重新请求\n\t\t\t\t\t\tawait this.fetchPlayerDataFromServer();\n\t\t\t\t\t}\n\t\t\t\t\tawait this.fetchBonusSummary();\n\t\t\t\t\tuni.showToast({ title: response.data.message, icon: 'success' });\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({ title: (response && response.data && response.data.message) || '操作失败', icon: 'none' });\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.showToast({ title: '装备失败', icon: 'none' });\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 卸下指定类型的武功\n\t\tasync unequipMartial(type) {\n\t\t\tconsole.log('卸载武功，类型:', type);\n\t\t\tif (!this.isAuthed) {\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' })\n\t\t\t\treturn\n\t\t\t}\n\t\t\t\n\t\t\tconst skill = this.getSelectedMartial(type);\n\t\t\tif (!skill) {\n\t\t\t\tuni.showToast({ title: '未选择武功', icon: 'none' })\n\t\t\t\treturn\n\t\t\t}\n\t\t\t\n\t\t\tif (!this.isMartialEquipped(skill)) {\n\t\t\t\tuni.showToast({ title: '该武功未装备', icon: 'none' })\n\t\t\t\treturn\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst skillName = skill.name || skill.名称;\n\t\t\t\tconsole.log('准备卸载武功:', skillName);\n\t\t\t\t\n\t\t\t\t// 显示加载提示\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在卸载...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\n\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\ttype: 'unequip_martial',\n\t\t\t\t\tdata: { skill_name: skillName }\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 隐藏加载提示\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (response && response.type === 'unequip_martial_success') {\n\t\t\t\t\tconsole.log('卸载成功:', response.data);\n\t\t\t\t\t// 直接使用后端返回的player数据，确保数据一致性\n\t\t\t\t\tif (response.data.player) {\n\t\t\t\t\t\t// 确保player中的martial_skills为list格式\n\t\t\t\t\t\tif (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {\n\t\t\t\t\t\t\tresponse.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map(name => {\n\t\t\t\t\t\t\t\treturn { name, ...response.data.player.martial_skills[name] };\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 更新本地数据\n\t\t\t\t\t\tthis.player = Object.assign({}, response.data.player);\n\t\t\t\t\t\tgameState.player = response.data.player;\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 手动设置装备状态\n\t\t\t\t\t\tconst updatedSkill = this.player.martial_skills.find(s => s.name === skillName || s.名称 === skillName);\n\t\t\t\t\t\tif (updatedSkill) {\n\t\t\t\t\t\t\tconsole.log('更新武功装备状态为未装备:', skillName);\n\t\t\t\t\t\t\tupdatedSkill.equipped = false;\n\t\t\t\t\t\t\tif (updatedSkill.装备 !== undefined) {\n\t\t\t\t\t\t\t\tupdatedSkill.装备 = false;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 更新界面\n\t\t\t\t\t\tthis.updateData();\n\t\t\t\t\t\tawait this.fetchBonusSummary();\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 确保视图更新\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 显示成功提示\n\t\t\t\t\t\tuni.showToast({ \n\t\t\t\t\t\t\ttitle: response.data.message || `成功卸下${skillName}`, \n\t\t\t\t\t\t\ticon: 'success' \n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// 显示错误提示\n\t\t\t\t\tuni.showToast({ \n\t\t\t\t\t\ttitle: (response && response.data && response.data.message) || '卸下失败', \n\t\t\t\t\t\ticon: 'none' \n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('卸载武功失败:', error);\n\t\t\t\tuni.showToast({ title: '操作异常', icon: 'none' });\n\t\t\t}\n\t\t},\n\t\t\n\t\tgetSkillCategory(skillName) {\n\t\t\t// 根据武功名称判断分类\n\t\t\tconst categoryMap = {\n\t\t\t\t'基本拳法': '拳法',\n\t\t\t\t'太极拳': '拳法',\n\t\t\t\t'基本剑法': '剑法',\n\t\t\t\t'独孤九剑': '剑法',\n\t\t\t\t'凌波微步': '轻功',\n\t\t\t\t'神行百变': '轻功',\n\t\t\t\t'九阳神功': '内功',\n\t\t\t\t'九阴真经': '内功',\n\t\t\t\t'降龙十八掌': '掌法',\n\t\t\t\t'打狗棒法': '棍法',\n\t\t\t\t'采药': '生活技能',\n\t\t\t\t'伐木': '生活技能',\n\t\t\t\t'挖矿': '生活技能',\n\t\t\t\t'剥皮': '生活技能'\n\t\t\t}\n\t\t\treturn categoryMap[skillName] || '其他'\n\t\t},\n\t\t\n\t\tgetSkillSchool(skillName) {\n\t\t\t// 根据武功名称判断门派\n\t\t\tconst schoolMap = {\n\t\t\t\t'独孤九剑': '华山',\n\t\t\t\t'太极拳': '武当',\n\t\t\t\t'九阳神功': '少林',\n\t\t\t\t'九阴真经': '古墓派',\n\t\t\t\t'降龙十八掌': '丐帮',\n\t\t\t\t'打狗棒法': '丐帮',\n\t\t\t\t'凌波微步': '逍遥派',\n\t\t\t\t'神行百变': '逍遥派'\n\t\t\t}\n\t\t\treturn schoolMap[skillName] || '无门派'\n\t\t},\n\t\t\n\t\tgetSkillLevel(skillName) {\n\t\t\t// 根据武功名称判断等级\n\t\t\tconst levelMap = {\n\t\t\t\t'基本拳法': '基础',\n\t\t\t\t'基本剑法': '基础',\n\t\t\t\t'太极拳': '高级',\n\t\t\t\t'独孤九剑': '绝学',\n\t\t\t\t'凌波微步': '绝学',\n\t\t\t\t'神行百变': '绝学',\n\t\t\t\t'九阳神功': '绝学',\n\t\t\t\t'九阴真经': '绝学',\n\t\t\t\t'降龙十八掌': '绝学',\n\t\t\t\t'打狗棒法': '绝学'\n\t\t\t}\n\t\t\treturn levelMap[skillName] || '基础'\n\t\t},\n\t\t\n\t\tgetSkillDescription(skillName) {\n\t\t\t// 根据武功名称获取描述\n\t\t\tconst descMap = {\n\t\t\t\t'基本拳法': '最基础的拳法，习武之初必学',\n\t\t\t\t'太极拳': '武当派绝学，以柔克刚',\n\t\t\t\t'独孤九剑': '华山派绝学，破尽天下武功',\n\t\t\t\t'凌波微步': '逍遥派绝学，身法如鬼魅',\n\t\t\t\t'九阳神功': '少林派绝学，内力深厚',\n\t\t\t\t'九阴真经': '古墓派绝学，内力深厚',\n\t\t\t\t'降龙十八掌': '丐帮绝学，掌力雄浑',\n\t\t\t\t'打狗棒法': '丐帮绝学，棒法精妙',\n\t\t\t\t'采药': '采集草药，用于炼药',\n\t\t\t\t'伐木': '砍伐树木，用于制作装备',\n\t\t\t\t'挖矿': '挖掘矿石，用于制作装备',\n\t\t\t\t'剥皮': '剥取动物皮毛，用于制作装备'\n\t\t\t}\n\t\t\treturn descMap[skillName] || '武功描述待补充'\n\t\t},\n\t\t\n\t\tgetSkillMoves(skillName) {\n\t\t\t// 根据武功名称获取招式列表\n\t\t\tconst movesMap = {\n\t\t\t\t'基本拳法': ['基本拳法1', '基本拳法2', '基本拳法3', '基本拳法4', '基本拳法5'],\n\t\t\t\t'太极拳': ['白鹤亮翅', '野马分鬃', '搂膝拗步', '倒卷肱', '揽雀尾'],\n\t\t\t\t'独孤九剑': ['总诀式', '破剑式', '破刀式', '破枪式', '破鞭式', '破索式', '破掌式', '破箭式', '破气式'],\n\t\t\t\t'凌波微步': ['凌波微步1', '凌波微步2', '凌波微步3', '凌波微步4'],\n\t\t\t\t'九阳神功': ['九阳护体', '九阳真气', '九阳神功'],\n\t\t\t\t'九阴真经': ['九阴护体', '九阴真气', '九阴神功'],\n\t\t\t\t'降龙十八掌': ['亢龙有悔', '飞龙在天', '见龙在田', '鸿渐于陆', '潜龙勿用'],\n\t\t\t\t'打狗棒法': ['棒打双犬', '棒打狗头', '棒打狗腿', '棒打狗尾'],\n\t\t\t\t'采药': ['采药1', '采药2', '采药3'],\n\t\t\t\t'伐木': ['伐木1', '伐木2', '伐木3'],\n\t\t\t\t'挖矿': ['挖矿1', '挖矿2', '挖矿3'],\n\t\t\t\t'剥皮': ['剥皮1', '剥皮2', '剥皮3']\n\t\t\t}\n\t\t\treturn movesMap[skillName] || []\n\t\t},\n\t\t\n\t\tgetMoveDescription(skillName, moveName) {\n\t\t\t// 根据武功和招式名称获取描述\n\t\t\tconst descMap = {\n\t\t\t\t'基本拳法': {\n\t\t\t\t\t'基本拳法1': '对准敌人的胸口打出一拳！',\n\t\t\t\t\t'基本拳法2': '双拳齐出，敌人连连后退！',\n\t\t\t\t\t'基本拳法3': '对准敌人的腹部一拳！',\n\t\t\t\t\t'基本拳法4': '对准敌人的头部一拳！',\n\t\t\t\t\t'基本拳法5': '对准敌人的腿部一拳！'\n\t\t\t\t},\n\t\t\t\t'太极拳': {\n\t\t\t\t\t'白鹤亮翅': '一式「白鹤亮翅」，双手成白鹤亮翅之势，敌人连连后退！',\n\t\t\t\t\t'野马分鬃': '一式「野马分鬃」，双手成野马分鬃之势，敌人连连后退！',\n\t\t\t\t\t'搂膝拗步': '一式「搂膝拗步」，双手成搂膝拗步之势，敌人连连后退！',\n\t\t\t\t\t'倒卷肱': '一式「倒卷肱」，双手成倒卷肱之势，敌人连连后退！',\n\t\t\t\t\t'揽雀尾': '一式「揽雀尾」，双手成揽雀尾之势，敌人连连后退！'\n\t\t\t\t},\n\t\t\t\t'独孤九剑': {\n\t\t\t\t\t'总诀式': '一式「总诀式」，剑势如虹，敌人连连后退！',\n\t\t\t\t\t'破剑式': '一式「破剑式」，专门破解剑法，敌人连连后退！',\n\t\t\t\t\t'破刀式': '一式「破刀式」，专门破解刀法，敌人连连后退！',\n\t\t\t\t\t'破枪式': '一式「破枪式」，专门破解枪法，敌人连连后退！',\n\t\t\t\t\t'破鞭式': '一式「破鞭式」，专门破解鞭法，敌人连连后退！'\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn descMap[skillName]?.[moveName] || '招式描述待补充'\n\t\t},\n\t\t\n\t\tgetSkillEffects(skill) {\n\t\t\tif (!skill || skill.等级 === '基础') return '暂无效果'\n\t\t\t\n\t\t\tconst effects = []\n\t\t\tconst level = skill.等级\n\t\t\t\n\t\t\t// 根据武功类型和等级计算效果\n\t\t\tif (skill.类别 === '拳法') {\n\t\t\t\teffects.push(`攻击 +${Math.floor(level * 1.5)}`)\n\t\t\t\teffects.push(`力量 +${Math.floor(level / 10)}`)\n\t\t\t} else if (skill.类别 === '剑法') {\n\t\t\t\teffects.push(`攻击 +${Math.floor(level * 2)}`)\n\t\t\t\teffects.push(`悟性 +${Math.floor(level / 10)}`)\n\t\t\t} else if (skill.类别 === '轻功') {\n\t\t\t\teffects.push(`防御 +${Math.floor(level * 1.5)}`)\n\t\t\t\teffects.push(`闪避 +${Math.floor(level * 1.2)}`)\n\t\t\t\teffects.push(`身法 +${Math.floor(level / 10)}`)\n\t\t\t} else if (skill.类别 === '内功') {\n\t\t\t\teffects.push(`气血 +${Math.floor(level * 3)}`)\n\t\t\t\teffects.push(`内力 +${Math.floor(level * 2)}`)\n\t\t\t\teffects.push(`根骨 +${Math.floor(level / 10)}`)\n\t\t\t}\n\t\t\t\n\t\t\treturn effects.length > 0 ? effects.join(', ') : '暂无效果'\n\t\t},\n\t\t\n\t\tswitchTab(tab) {\n\t\t\tthis.activeTab = tab\n\t\t},\n\t\t\n\t\tonLevelChange(e) {\n\t\t\tthis.levelIndex = e.detail.value\n\t\t},\n\t\t\n\t\tonSchoolChange(e) {\n\t\t\tthis.schoolIndex = e.detail.value\n\t\t},\n\t\t\n\t\tgetCategoryTitle() {\n\t\t\tif (this.activeTab === 'all') return '全部武功'\n\t\t\treturn this.activeTab\n\t\t},\n\t\t\n\t\tgetCategoryDescription() {\n\t\t\tconst descriptions = {\n\t\t\t\t'all': '所有可学习的武功',\n\t\t\t\t'拳法': '拳法类武功，以拳为主',\n\t\t\t\t'剑法': '剑法类武功，以剑为主',\n\t\t\t\t'轻功': '轻功类武功，提升身法',\n\t\t\t\t'内功': '内功类武功，提升内力'\n\t\t\t}\n\t\t\treturn descriptions[this.activeTab] || '武功描述'\n\t\t},\n\t\t\n\t\tgetSkillProgress(skill) {\n\t\t\tif (skill.最大经验 === 0) return 0\n\t\t\treturn Math.min((skill.经验 / skill.最大经验) * 100, 100)\n\t\t},\n\t\t\n\t\tcanUseMartial(skill) {\n\t\t\t// 基础武功不能使用/装备\n\t\t\treturn skill.等级 !== '基础' && skill.解锁 || skill.unlocked\n\t\t},\n\t\t\n\t\tasync toggleMartialUse(skill) {\n\t\t\tif (!this.isAuthed) {\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' })\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (!skill.解锁 && !skill.unlocked) {\n\t\t\t\tuni.showToast({ title: '该武学尚未解锁', icon: 'none' })\n\t\t\t\treturn\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tconst action = (skill.装备 || skill.equipped) ? 'unequip_martial' : 'use_martial'\n\t\t\t\tconst skillName = skill.名称 || skill.name;\n\t\t\t\t\n\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\ttype: action,\n\t\t\t\t\tdata: { skill_name: skillName }\n\t\t\t\t})\n\t\t\t\tif (response.type === action + '_success') {\n\t\t\t\t\t// 直接使用后端返回的 player 数据，确保数据一致性\n\t\t\t\t\tif (response.data.player) {\n\t\t\t\t\t\t// 确保 player 中的 martial_skills 为 list 格式\n\t\t\t\t\t\tif (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {\n\t\t\t\t\t\t\tresponse.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map(name => {\n\t\t\t\t\t\t\t\treturn { name, ...response.data.player.martial_skills[name] }\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// 直接更新本地数据，不触发 gameState 通知，避免循环调用\n\t\t\t\t\t\tthis.player = Object.assign({}, response.data.player);\n\t\t\t\t\t\tgameState.player = response.data.player;\n\t\t\t\t\t\tthis.updateData();\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 如果没有返回 player 数据，则重新请求\n\t\t\t\t\t\tawait this.fetchPlayerDataFromServer();\n\t\t\t\t\t}\n\t\t\t\t\tawait this.fetchBonusSummary();\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\tuni.showToast({ title: response.data.message, icon: 'success' })\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({ title: response.data.message, icon: 'none' })\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('武学操作失败:', error)\n\t\t\t\tuni.showToast({ title: '操作失败', icon: 'none' })\n\t\t\t}\n\t\t},\n\t\t\n\t\tshowSkillDetail(skill) {\n\t\t\tthis.selectedSkill = skill\n\t\t\tthis.showDetail = true\n\t\t},\n\t\t\n\t\tcloseDetail() {\n\t\t\tthis.showDetail = false\n\t\t\tthis.selectedSkill = null\n\t\t},\n\t\t\n\t\tshowMoves(skill) {\n\t\t\tthis.selectedSkill = skill\n\t\t\tthis.showMovesModal = true\n\t\t},\n\t\t\n\t\tcloseMovesModal() {\n\t\t\tthis.showMovesModal = false\n\t\t\tthis.selectedSkill = null\n\t\t},\n\t\t\n\t\tasync studyMartial(skill) {\n\t\t\tif (!this.isAuthed) {\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' })\n\t\t\t\treturn\n\t\t\t}\n\t\t\tif (!skill.解锁 && !skill.unlocked) {\n\t\t\t\tuni.showToast({ title: '该武学尚未解锁', icon: 'none' })\n\t\t\t\treturn\n\t\t\t}\n\t\t\ttry {\n\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\ttype: 'study_martial',\n\t\t\t\t\tdata: { 名称: skill.名称 || skill.name }\n\t\t\t\t})\n\t\t\t\tif (response.type === 'study_martial_success') {\n\t\t\t\t\tthis.updateData() // 刷新数据\n\t\t\t\t\tuni.showToast({ title: response.data.message, icon: 'success' })\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({ title: response.data.message, icon: 'none' })\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('学习武学失败:', error)\n\t\t\t\tuni.showToast({ title: '学习失败', icon: 'none' })\n\t\t\t}\n\t\t},\n\t\tisLifeSkill(skill) {\n\t\t\t// 仅用于装备等过滤，实际展示已用lifeSkills\n\t\t\tconst skillName = skill.名称 || skill.name || ''\n\t\t\treturn skill.类别 === '生活技能' || skill.类别 === '生活类' || skillName.includes('采') || skillName.includes('药') || skillName.includes('伐木') || skillName.includes('挖矿') || skillName.includes('剥皮')\n\t\t},\n\t\topenMartialSelect(type) {\n\t\t\tthis.martialSelectType = type\n\t\t\tconst found = this.martialTypes.find(t => t.key === type)\n\t\t\tthis.martialSelectTypeLabel = found ? found.label : type\n\t\t\tthis.showMartialSelect = true\n\t\t},\n\t\tcloseMartialSelect() {\n\t\t\tthis.showMartialSelect = false\n\t\t},\n\t\tasync selectMartial(skill, type) {\n\t\t\tconsole.log('选择武功:', skill.name, '类型:', type);\n\t\t\tthis.selectedMartials[type] = skill.name;\n\t\t\tthis.showMartialSelect = false;\n\t\t\t\n\t\t\t// 选择武功后自动装备\n\t\t\tif (skill.unlocked || skill.解锁) {\n\t\t\t\t// 如果已装备，不需要重复装备\n\t\t\t\tif (this.isMartialEquipped(skill)) {\n\t\t\t\t\tconsole.log('武功已装备，无需重复装备');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\ttry {\n\t\t\t\t\tconst skillName = skill.name || skill.名称;\n\t\t\t\t\t\n\t\t\t\t\t// 显示加载提示\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '正在装备...',\n\t\t\t\t\t\tmask: true\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\n\t\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\t\ttype: 'use_martial',\n\t\t\t\t\t\tdata: { skill_name: skillName }\n\t\t\t\t\t});\n\t\t\t\t\t\n\t\t\t\t\t// 隐藏加载提示\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\t\n\t\t\t\t\tif (response && response.type === 'use_martial_success') {\n\t\t\t\t\t\tconsole.log('装备成功:', response.data);\n\t\t\t\t\t\t// 直接使用后端返回的player数据，确保数据一致性\n\t\t\t\t\t\tif (response.data.player) {\n\t\t\t\t\t\t\t// 确保player中的martial_skills为list格式\n\t\t\t\t\t\t\tif (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {\n\t\t\t\t\t\t\t\tresponse.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map(name => {\n\t\t\t\t\t\t\t\t\treturn { name, ...response.data.player.martial_skills[name] };\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// 更新本地数据\n\t\t\t\t\t\t\tthis.player = Object.assign({}, response.data.player);\n\t\t\t\t\t\t\tgameState.player = response.data.player;\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 手动设置装备状态\n\t\t\t\t\t\t\tconst updatedSkill = this.player.martial_skills.find(s => s.name === skillName || s.名称 === skillName);\n\t\t\t\t\t\t\tif (updatedSkill) {\n\t\t\t\t\t\t\t\tconsole.log('更新武功装备状态:', skillName);\n\t\t\t\t\t\t\t\tupdatedSkill.equipped = true;\n\t\t\t\t\t\t\t\tif (updatedSkill.装备 !== undefined) {\n\t\t\t\t\t\t\t\t\tupdatedSkill.装备 = true;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 更新界面\n\t\t\t\t\t\t\tthis.updateData();\n\t\t\t\t\t\t\tawait this.fetchBonusSummary();\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 确保视图更新\n\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 显示成功提示\n\t\t\t\t\t\t\tuni.showToast({ \n\t\t\t\t\t\t\t\ttitle: response.data.message || `成功装备${skillName}`, \n\t\t\t\t\t\t\t\ticon: 'success' \n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 显示错误提示\n\t\t\t\t\t\tuni.showToast({ \n\t\t\t\t\t\t\ttitle: (response && response.data && response.data.message) || '装备失败', \n\t\t\t\t\t\t\ticon: 'none' \n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t\tconsole.error('装备武功失败:', error);\n\t\t\t\t\tuni.showToast({ title: '操作异常', icon: 'none' });\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tuni.showToast({ title: '该武学尚未解锁', icon: 'none' });\n\t\t\t}\n\t\t},\n\t\tasync fetchBonusSummary() {\n\t\t\ttry {\n\t\t\t\tconst res = await gameUtils.sendMessage({\n\t\t\t\t\ttype: 'get_bonus_summary',\n\t\t\t\t\tdata: {}\n\t\t\t\t});\n\t\t\t\tif (res.type === 'bonus_summary') {\n\t\t\t\t\tthis.bonusSummary = res.data;\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.warn('获取增益摘要失败', e);\n\t\t\t}\n\t\t},\n\t\tisBasicMartial(skill) {\n\t\t\t// 判断是否为基础武功\n\t\t\tconst basicNames = ['基本剑法', '基本拳法', '基本刀法', '基本棍法', '基本招架', '基本轻功', '基础内功', '基础暗器', '基本暗器法'];\n\t\t\treturn basicNames.includes(skill.name) || basicNames.includes(skill.名称);\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.container {\n\tpadding: 20rpx;\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n\tbackground: #f5f7fa;\n}\n\n.header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tpadding: 30rpx;\n\tborder-radius: 20rpx;\n\tmargin-bottom: 30rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);\n}\n\n.energy-info {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.energy-label {\n\tcolor: #fff;\n\tfont-size: 28rpx;\n\tmargin-right: 10rpx;\n}\n\n.energy-value {\n\tcolor: #fff;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.skill-count {\n\tbackground: rgba(255,255,255,0.2);\n\tpadding: 10rpx 20rpx;\n\tborder-radius: 20rpx;\n}\n\n.count-text {\n\tcolor: #fff;\n\tfont-size: 24rpx;\n}\n\n.category-tabs {\n\tdisplay: flex;\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 10rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.tab-item {\n\tflex: 1;\n\ttext-align: center;\n\tpadding: 20rpx 10rpx;\n\tborder-radius: 15rpx;\n\ttransition: all 0.3s;\n}\n\n.tab-item.active {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.tab-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.tab-item.active .tab-text {\n\tcolor: #fff;\n}\n\n.filter-section {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 20rpx;\n\tmargin-bottom: 20rpx;\n\tbox-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.filter-row {\n\tdisplay: flex;\n\tgap: 20rpx;\n}\n\n.filter-item {\n\tflex: 1;\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.filter-label {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-right: 10rpx;\n}\n\n.picker-text {\n\tfont-size: 28rpx;\n\tcolor: #667eea;\n\tbackground: rgba(102, 126, 234, 0.1);\n\tpadding: 10rpx 20rpx;\n\tborder-radius: 10rpx;\n}\n\n.skills-list {\n\theight: calc(100vh - 400rpx);\n}\n\n.skill-group {\n\tmargin-bottom: 40rpx;\n}\n\n.group-header {\n\tbackground: #fff;\n\tpadding: 30rpx;\n\tborder-radius: 20rpx 20rpx 0 0;\n\tborder-bottom: 2rpx solid #f0f0f0;\n}\n\n.group-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 0;\n\tline-height: 60rpx;\n}\n\n.group-desc {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.skill-item {\n\tbackground: #fff;\n\tpadding: 30rpx;\n\tborder-bottom: 2rpx solid #f0f0f0;\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n}\n\n.skill-item:last-child {\n\tborder-bottom: none;\n\tborder-radius: 0 0 20rpx 20rpx;\n}\n\n.skill-info {\n\tflex: 1;\n\tmargin-right: 20rpx;\n}\n\n.skill-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 15rpx;\n}\n\n.skill-name {\n\tfont-size: 30rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.skill-status-group {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-end;\n}\n\n.skill-level-text {\n\tfont-size: 24rpx;\n\tcolor: #667eea;\n\tbackground: rgba(102, 126, 234, 0.1);\n\tpadding: 5rpx 15rpx;\n\tborder-radius: 15rpx;\n\tmargin-bottom: 5rpx;\n}\n\n.skill-school {\n\tfont-size: 20rpx;\n\tcolor: #1890ff;\n\tbackground: rgba(24, 144, 255, 0.1);\n\tpadding: 3rpx 10rpx;\n\tborder-radius: 10rpx;\n\tmargin-bottom: 5rpx;\n}\n\n.skill-equipped {\n\tfont-size: 20rpx;\n\tcolor: #52c41a;\n\tbackground: rgba(82, 196, 26, 0.1);\n\tpadding: 3rpx 10rpx;\n\tborder-radius: 10rpx;\n}\n\n.skill-desc {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tmargin-bottom: 15rpx;\n\tline-height: 1.4;\n}\n\n.skill-progress {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 10rpx;\n}\n\n.progress-bg {\n\tflex: 1;\n\theight: 20rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 10rpx;\n\tmargin-right: 15rpx;\n\toverflow: hidden;\n}\n\n.progress-fill {\n\theight: 100%;\n\tbackground: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n\tborder-radius: 10rpx;\n\ttransition: width 0.3s;\n}\n\n.progress-text {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tmin-width: 80rpx;\n}\n\n.skill-status {\n\tmargin-top: 10rpx;\n}\n\n.skill-unlock {\n\tfont-size: 22rpx;\n\tcolor: #52c41a;\n}\n\n.skill-locked {\n\tfont-size: 22rpx;\n\tcolor: #ff4d4f;\n}\n\n.skill-actions {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 10rpx;\n}\n\n.action-btn {\n\tpadding: 15rpx 30rpx;\n\tborder-radius: 15rpx;\n\tfont-size: 24rpx;\n\tborder: none;\n}\n\n.study-btn {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: #fff;\n}\n\n.study-btn:disabled {\n\tbackground: #ccc;\n\tcolor: #999;\n}\n\n.use-btn {\n\tbackground: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\n\tcolor: #fff;\n}\n\n.use-btn.unequip {\n\tbackground: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);\n}\n\n.use-btn:disabled {\n\tbackground: #ccc;\n\tcolor: #999;\n}\n\n.moves-btn {\n\tbackground: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);\n\tcolor: #fff;\n}\n\n.moves-btn:disabled {\n\tbackground: #ccc;\n\tcolor: #999;\n}\n\n.empty-skills {\n\ttext-align: center;\n\tpadding: 100rpx 0;\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n\n/* 弹窗样式 */\n.modal-overlay {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground: rgba(0,0,0,0.5);\n\tdisplay: flex;\n\tjustify-content: center;\n\talign-items: center;\n\tz-index: 1000;\n}\n\n.modal-content {\n\tbackground: #fff;\n\tborder-radius: 20rpx;\n\twidth: 80%;\n\tmax-width: 600rpx;\n\tmax-height: 80vh;\n\toverflow: hidden;\n}\n\n.modal-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 2rpx solid #f0f0f0;\n}\n\n.modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.modal-close {\n\tfont-size: 40rpx;\n\tcolor: #999;\n\tcursor: pointer;\n}\n\n.modal-body {\n\tpadding: 30rpx;\n\tmax-height: 60vh;\n\toverflow-y: auto;\n}\n\n.detail-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.detail-name {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.detail-status {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-end;\n}\n\n.detail-level {\n\tfont-size: 28rpx;\n\tcolor: #667eea;\n\tmargin-bottom: 5rpx;\n}\n\n.detail-school {\n\tfont-size: 24rpx;\n\tcolor: #1890ff;\n\tbackground: rgba(24, 144, 255, 0.1);\n\tpadding: 5rpx 15rpx;\n\tborder-radius: 10rpx;\n\tmargin-bottom: 5rpx;\n}\n\n.detail-equipped {\n\tfont-size: 24rpx;\n\tcolor: #52c41a;\n\tbackground: rgba(82, 196, 26, 0.1);\n\tpadding: 5rpx 15rpx;\n\tborder-radius: 10rpx;\n}\n\n.detail-desc {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n\tmargin-bottom: 30rpx;\n}\n\n.detail-progress {\n\tmargin-bottom: 30rpx;\n}\n\n.progress-title {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 15rpx;\n\tdisplay: block;\n}\n\n.progress-bar {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.detail-effects {\n\tmargin-bottom: 30rpx;\n\tpadding: 20rpx;\n\tbackground: #f6ffed;\n\tborder-radius: 10rpx;\n\tborder-left: 4rpx solid #52c41a;\n}\n\n.effects-title {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n\tdisplay: block;\n}\n\n.effects-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.detail-category {\n\tmargin-bottom: 30rpx;\n\tpadding: 20rpx;\n\tbackground: #f0f8ff;\n\tborder-radius: 10rpx;\n\tborder-left: 4rpx solid #1890ff;\n}\n\n.category-title {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n\tdisplay: block;\n}\n\n.category-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.detail-moves {\n\tmargin-bottom: 30rpx;\n\tpadding: 20rpx;\n\tbackground: #fff7e6;\n\tborder-radius: 10rpx;\n\tborder-left: 4rpx solid #faad14;\n}\n\n.moves-title {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\tmargin-bottom: 15rpx;\n\tdisplay: block;\n}\n\n.moves-list {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 10rpx;\n}\n\n.move-item {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tbackground: rgba(250, 173, 20, 0.1);\n\tpadding: 5rpx 15rpx;\n\tborder-radius: 10rpx;\n}\n\n.moves-container {\n\tdisplay: flex;\n\tflex-direction: column;\n\tgap: 20rpx;\n}\n\n.moves-container .move-item {\n\tbackground: #f8f9fa;\n\tpadding: 20rpx;\n\tborder-radius: 10rpx;\n\tborder-left: 4rpx solid #667eea;\n}\n\n.move-name {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n\tdisplay: block;\n}\n\n.move-desc {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tline-height: 1.4;\n}\n\n.modal-footer {\n\tdisplay: flex;\n\tpadding: 30rpx;\n\tborder-top: 2rpx solid #f0f0f0;\n\tgap: 10rpx;\n}\n\n.modal-btn {\n\tflex: 1;\n\tpadding: 20rpx;\n\tborder-radius: 15rpx;\n\tfont-size: 28rpx;\n\tborder: none;\n}\n\n.cancel-btn {\n\tbackground: #f0f0f0;\n\tcolor: #666;\n}\n\n.confirm-btn {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: #fff;\n}\n\n.confirm-btn:disabled {\n\tbackground: #ccc;\n}\n\n.use-btn {\n\tbackground: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\n\tcolor: #fff;\n}\n\n.use-btn.unequip {\n\tbackground: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);\n}\n\n/* 标题和选择器同行样式 */\n.title-selector-row {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tmargin-bottom: 15rpx;\n\tposition: relative;\n}\n\n.title-selector-row .group-title {\n\tflex: 0 0 auto;\n}\n\n.title-selector-row .equip-selector {\n\tflex: 1;\n\tdisplay: flex;\n\tjustify-content: center;\n\tmargin: 0 20rpx;\n}\n\n.title-selector-row .skill-actions {\n\tflex: 0 0 auto;\n}\n\n/* 使用武功选择器样式 */\n.equip-selector {\n\tmargin: 0;\n\tdisplay: flex;\n\talign-items: center;\n}\n.picker-display {\n\tdisplay: flex;\n\talign-items: center;\n\tborder: 1px solid #e0e0e0;\n\tborder-radius: 8px;\n\tpadding: 0 8px;\n\tfont-size: 12px;\n\tbackground: #fafbfc;\n\tmin-width: 60px;\n\theight: 28px;\n\ttransition: border 0.2s, box-shadow 0.2s, background 0.2s;\n}\n.picker-display:active, .picker-display:focus {\n\tborder: 1px solid #b6b6b6;\n\tbackground: #f5f7fa;\n}\n.picker-text {\n\tflex: 1;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n.picker-arrow {\n\tmargin-left: 2px;\n\tfont-size: 11px;\n\tcolor: #aaa;\n}\n/* 装备按钮样式 */\n.equip-btn {\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tcolor: #fff;\n\theight: 60rpx;\n\tpadding: 12rpx 24rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder-radius: 15rpx;\n\tfont-size: 24rpx;\n\tfont-weight: 600;\n\tborder: none;\n}\n\n.equip-btn.equipped {\n\tbackground: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);\n}\n.martial-info {\n\tdisplay: none;\n}\n/* 增益效果样式 */\n.martial-bonus {\n\tmargin-top: 15rpx;\n\tdisplay: flex;\n\talign-items: flex-start;\n}\n\n.martial-bonus-title {\n\tfont-size: 24rpx;\n\tcolor: #666;\n\tmargin-right: 15rpx;\n\twhite-space: nowrap;\n\tfont-weight: 600;\n}\n\n.martial-bonus-content {\n\tfont-size: 22rpx;\n\tcolor: #52c41a;\n\tline-height: 1.4;\n\tflex: 1;\n\tword-break: keep-all;\n\twhite-space: normal;\n}\n.martial-select-modal-mask {\n\tposition: fixed;\n\tleft: 0; top: 0; right: 0; bottom: 0;\n\tbackground: rgba(0,0,0,0.18);\n\tz-index: 9999;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n.martial-select-modal {\n\tbackground: #ffffff;\n\tborder-radius: 20rpx;\n\tmin-width: 600rpx;\n\tmax-width: 90vw;\n\tmax-height: 70vh;\n\tbox-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: stretch;\n\tpadding: 40rpx 30rpx 30rpx 30rpx;\n\tborder: 2rpx solid #e2e8f0;\n}\n.martial-select-title {\n\tfont-size: 32rpx;\n\tfont-weight: 700;\n\tcolor: #1e293b;\n\tmargin-bottom: 20rpx;\n\ttext-align: center;\n}\n.martial-select-list {\n\tflex: 1;\n\tmin-height: 120px;\n\tmax-height: 40vh;\n\toverflow-y: auto;\n\tmargin-bottom: 10px;\n}\n.martial-select-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\tpadding: 20rpx;\n\tborder-radius: 12rpx;\n\tmargin-bottom: 12rpx;\n\tbackground: #f8fafc;\n\tcursor: pointer;\n\ttransition: all 0.2s ease;\n\tborder: 2rpx solid #e2e8f0;\n}\n.martial-select-item.selected, .martial-select-item:hover {\n\tbackground: #e6f7ff;\n\tborder-color: #667eea;\n\ttransform: translateY(-2rpx);\n\tbox-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.1);\n}\n.martial-select-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: flex-start;\n\tmargin-bottom: 8rpx;\n}\n\n.martial-select-name {\n\tfont-size: 30rpx;\n\tfont-weight: 600;\n\tcolor: #2d3748;\n\tflex: 1;\n}\n\n.martial-select-status {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: flex-end;\n}\n\n.martial-select-level {\n\tfont-size: 24rpx;\n\tcolor: #667eea;\n\tfont-weight: 500;\n\tmargin-bottom: 4rpx;\n}\n\n.martial-select-quality {\n\tfont-size: 24rpx;\n\tcolor: #d69e2e;\n\tfont-weight: 500;\n\tmargin-bottom: 8rpx;\n}\n\n.martial-select-desc {\n\tfont-size: 24rpx;\n\tcolor: #4a5568;\n\tline-height: 1.5;\n\tfont-weight: 400;\n}\n\n.martial-select-equipped {\n\tfont-size: 22rpx;\n\tcolor: #52c41a;\n\tbackground: rgba(82, 196, 26, 0.1);\n\tpadding: 4rpx 12rpx;\n\tborder-radius: 10rpx;\n\tmargin-top: 8rpx;\n}\n.martial-select-empty {\n\tcolor: #aaa;\n\tfont-size: 13px;\n\ttext-align: center;\n\tmargin: 16px 0;\n}\n.martial-select-cancel {\n\tmargin-top: 20rpx;\n\tbackground: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);\n\tcolor: #4a5568;\n\tborder-radius: 50rpx;\n\tborder: 2rpx solid #e2e8f0;\n\tfont-size: 28rpx;\n\tfont-weight: 600;\n\tpadding: 20rpx 0;\n\ttransition: all 0.3s ease;\n}\n.martial-select-cancel:active {\n\tbackground: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);\n\ttransform: scale(0.98);\n}\n.martial-select-tag {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder: 2rpx solid #cbd5e0;\n\tborder-radius: 16rpx;\n\tbackground: #f8fafc;\n\tcolor: #4a5568;\n\tfont-size: 28rpx;\n\tfont-weight: 500;\n\tpadding: 12rpx 24rpx;\n\tmin-width: 280rpx;\n\tmax-width: 360rpx;\n\theight: 60rpx;\n\tcursor: pointer;\n\ttransition: all 0.2s ease;\n\tmargin: 0;\n}\n.martial-select-tag:active, .martial-select-tag:focus {\n\tbackground: #e2e8f0;\n\tborder-color: #667eea;\n\ttransform: scale(0.98);\n}\n\n.martial-select-tag:disabled {\n\tbackground: #f1f5f9;\n\tborder-color: #cbd5e0;\n\tcolor: #94a3b8;\n\tcursor: not-allowed;\n\ttransform: none;\n}\n\n.martial-select-tag:disabled:active, .martial-select-tag:disabled:focus {\n\tbackground: #f1f5f9;\n\tborder-color: #cbd5e0;\n\ttransform: none;\n}\n.martial-select-tag-text {\n\tflex: 1;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n\ttext-align: center;\n\tfont-weight: 500;\n}\n.martial-select-tag-arrow {\n\tmargin-left: 6px;\n\tfont-size: 13px;\n\tcolor: #aaa;\n}\n.martial-clear-btn {\n\twidth: 40rpx;\n\theight: 40rpx;\n\tline-height: 36rpx;\n\tborder-radius: 50%;\n\tbackground: rgba(255, 77, 79, 0.1);\n\tcolor: #ff4d4f;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tpadding: 0;\n\tmargin-left: 10rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tborder: 1rpx solid rgba(255, 77, 79, 0.3);\n\tflex-shrink: 0;\n}\n\n\n\n</style> ", "import MiniProgramPage from 'D:/zjjhx/仗剑江湖行/pages/skills/skills.vue'\nwx.createPage(MiniProgramPage)"], "names": ["gameState", "uni", "gameUtils"], "mappings": ";;;;;AA8MA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,QAAQ,CAAE;AAAA,MACV,kBAAkB,CAAE;AAAA;AAAA,MACpB,cAAc;AAAA,QACb,EAAE,KAAK,MAAM,OAAO,KAAM;AAAA,QAC1B,EAAE,KAAK,MAAM,OAAO,KAAM;AAAA,QAC1B,EAAE,KAAK,MAAM,OAAO,KAAM;AAAA;AAAA,QAE1B,EAAE,KAAK,MAAM,OAAO,KAAM;AAAA,QAC1B,EAAE,KAAK,MAAM,OAAO,KAAM;AAAA,QAC1B,EAAE,KAAK,MAAM,OAAO,KAAM;AAAA,QAC1B,EAAE,KAAK,MAAM,OAAO,KAAK;AAAA,MACzB;AAAA,MACD,WAAW;AAAA;AAAA,MACX,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,eAAe;AAAA,MACf,UAAU;AAAA,MACV,mBAAmB;AAAA,MACnB,mBAAmB;AAAA,MACnB,wBAAwB;AAAA,MACxB,cAAc,CAAE;AAAA;AAAA,IACjB;AAAA,EACA;AAAA,EAED,UAAU;AAAA;AAAA,IAET,sBAAsB;AACrB,YAAM,SAAS,CAAA;AACf,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAAgB,eAAO;AACxD,WAAK,aAAa,QAAQ,aAAW;AACpC,eAAO,QAAQ,GAAG,IAAI,KAAK,OAAO,eAAe;AAAA,UAAO,YACtD,MAAM,SAAS,QAAQ,OAAO,MAAM,OAAO,QAAQ,OAAO,MAAM,OAAO,QAAQ,SAC/E,MAAM,YAAY,MAAM,OACzB,CAAC,KAAK,eAAe,KAAK;AAAA;MAE5B,CAAC;AACD,aAAO;AAAA,IACP;AAAA;AAAA,IAED,kBAAkB;AACjB,YAAM,MAAO,KAAK,UAAU,KAAK,OAAO,iBACrC,KAAK,OAAO,eAAe,OAAO,YAAU,MAAM,YAAY,MAAM,OAAO,CAAC,KAAK,YAAY,KAAK,CAAC,IACnG,CAAC;AACJ,aAAO;AAAA,IACP;AAAA;AAAA,IAED,aAAa;AACZ,YAAM,MAAO,KAAK,UAAU,KAAK,OAAO,iBACrC,KAAK,OAAO,eAAe,OAAO,YAAU,MAAM,YAAY,MAAM,OAAO,KAAK,YAAY,KAAK,CAAC,IAClG,CAAC;AACJ,aAAO;AAAA,IACP;AAAA,IACD,sBAAsB;AACrB,aAAO,KAAK,gBAAgB;AAAA,IAC7B;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,WAAU;AACf,SAAK,kBAAiB;AAEtB,QAAI,CAAC,KAAK,oBAAoB;AAC7B,WAAK,qBAAqB,CAAC,MAAM,UAAU;AAC1C,YAAI,SAAS,UAAU;AACtB,eAAK,WAAU;AAAA,QAChB;AAAA;AAEDA,sBAAAA,UAAU,gBAAgB,KAAK,KAAK,kBAAkB;AAAA,IACvD;AAAA,EACA;AAAA,EACD,WAAW;AAEV,QAAI,KAAK,oBAAoB;AAC5B,YAAM,MAAMA,gBAAAA,UAAU,gBAAgB,QAAQ,KAAK,kBAAkB;AACrE,UAAI,OAAO;AAAGA,wBAAS,UAAC,gBAAgB,OAAO,KAAK,CAAC;AACrD,WAAK,qBAAqB;AAAA,IAC3B;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,WAAU;AACf,SAAK,kBAAiB;AAAA,EACtB;AAAA,EAED,SAAS;AAAA,IACR,aAAa;AACZC,oBAAAA,MAAY,MAAA,OAAA,kCAAA,WAAW;AAEvB,UAAI,CAAC,KAAK,UAAU,OAAO,KAAK,KAAK,MAAM,EAAE,WAAW,GAAG;AAC1D,aAAK,SAAS,OAAO,OAAO,CAAA,GAAID,gBAAS,UAAC,cAAa,CAAE;AACzDC,sBAAAA,MAAA,MAAA,OAAA,kCAAY,kBAAkB;AAAA,aACxB;AACNA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,UAAU;AAAA,MACvB;AACA,WAAK,WAAWD,gBAAAA,UAAU,kBAAkBA,gBAAS,UAAC,gBAAgB,IAAIA,gBAAS,UAAC;AAGpF,UAAI,KAAK,UAAU,KAAK,OAAO,kBAAkB,CAAC,MAAM,QAAQ,KAAK,OAAO,cAAc,GAAG;AAC5FC,sBAAAA,qDAAY,aAAa;AACzB,aAAK,OAAO,iBAAiB,OAAO,KAAK,KAAK,OAAO,cAAc,EAAE,IAAI,UAAQ;AAChF,iBAAO,EAAE,MAAM,GAAG,KAAK,OAAO,eAAe,IAAI;SACjD;AAAA,MACF;AACA,UAAI,CAAC,KAAK,OAAO;AAAgB,aAAK,OAAO,iBAAiB;AAE9D,UAAI,KAAK,UAAU,KAAK,OAAO,gBAAgB;AAC9CA,4BAAA,MAAA,OAAA,kCAAY,SAAS,KAAK,OAAO,eAAe,MAAM;AAEtD,cAAM,iBAAiB,KAAK,OAAO,eAAe,OAAO,WAAS,MAAM,YAAY,MAAM,EAAE;AAC5FA,sBAAAA,MAAY,MAAA,OAAA,kCAAA,UAAU,eAAe,IAAI,OAAK,EAAE,QAAQ,EAAE,EAAE,CAAC;AAG7DA,4BAAA,MAAA,OAAA,kCAAY,yBAAyB,KAAK,UAAU,KAAK,gBAAgB,CAAC;AAC1E,aAAK,mBAAmB,CAAC;AACzB,aAAK,aAAa,QAAQ,aAAW;AACpC,gBAAM,WAAW,KAAK,OAAO,eAAe;AAAA,YAAK,YAC/C,MAAM,SAAS,QAAQ,OAAO,MAAM,OAAO,QAAQ,OAAO,MAAM,OAAO,QAAQ,SAC/E,MAAM,YAAY,MAAM;AAAA,UAC1B;AACA,cAAI,UAAU;AACbA,0BAAAA,MAAA,MAAA,OAAA,kCAAY,MAAM,QAAQ,GAAG,aAAa,SAAS,QAAQ,SAAS,EAAE;AACtE,iBAAK,iBAAiB,QAAQ,GAAG,IAAI,SAAS,QAAQ,SAAS;AAAA,iBACzD;AAEN,kBAAM,YAAY,KAAK,kBAAkB,QAAQ,GAAG;AACpD,gBAAI,UAAU,SAAS,GAAG;AACzBA,iFAAY,MAAM,QAAQ,GAAG,uBAAuB,UAAU,CAAC,EAAE,QAAQ,UAAU,CAAC,EAAE,EAAE;AACxF,mBAAK,iBAAiB,QAAQ,GAAG,IAAI,UAAU,CAAC,EAAE,QAAQ,UAAU,CAAC,EAAE;AAAA,mBACjE;AACNA,kCAAA,MAAA,OAAA,kCAAY,MAAM,QAAQ,GAAG,SAAS;AAAA,YACvC;AAAA,UACD;AAAA,SACA;AACDA,4BAAA,MAAA,OAAA,kCAAY,yBAAyB,KAAK,UAAU,KAAK,gBAAgB,CAAC;AAG1E,aAAK,aAAY;AAAA,MAClB;AAAA,IACA;AAAA;AAAA,IAID,mBAAmB,eAAe;AACjC,YAAM,YAAY,CAAC;AAGnB,aAAO,KAAK,aAAa,EAAE,QAAQ,cAAY;AAC9C,cAAM,SAAS,cAAc,QAAQ,KAAK,CAAC;AAC3C,eAAO,QAAQ,WAAS;AAEvB,oBAAU,KAAK;AAAA,YACd,GAAG;AAAA,YACH,IAAI,KAAK,iBAAiB,MAAM,MAAM,MAAM,IAAI;AAAA,YAChD,IAAI,KAAK,eAAe,MAAM,MAAM,MAAM,IAAI;AAAA,YAC9C,IAAI,KAAK,cAAc,MAAM,MAAM,MAAM,IAAI;AAAA,YAC7C,IAAI,KAAK,oBAAoB,MAAM,MAAM,MAAM,IAAI;AAAA,YACnD,IAAI,KAAK,cAAc,MAAM,MAAM,MAAM,IAAI;AAAA,YAC7C,IAAI,KAAK,gBAAgB,MAAM,MAAM,MAAM,IAAI;AAAA,WAC/C;AAAA,SACD;AAAA,OACD;AAED,aAAO;AAAA,IACP;AAAA,IAED,kBAAkB,MAAM;AACvB,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,OAAO;AAAgB,eAAO;AACxD,aAAO,KAAK,OAAO,eAAe;AAAA,QAAO,YACvC,MAAM,SAAS,QAAQ,MAAM,OAAO,QAAQ,MAAM,OAAO,UACzD,MAAM,YAAY,MAAM,OACzB,CAAC,KAAK,eAAe,KAAK;AAAA;IAE3B;AAAA;AAAA,IAGD,wBAAwB,MAAM;AAC7B,YAAM,eAAe,KAAK,iBAAiB,IAAI;AAC/C,UAAI,CAAC;AAAc,eAAO;AAC1B,YAAM,WAAW,KAAK,kBAAkB,IAAI;AAC5C,YAAM,QAAQ,SAAS,UAAU,WAAS,MAAM,SAAS,YAAY;AACrE,aAAO,SAAS,IAAI,QAAQ;AAAA,IAC5B;AAAA;AAAA,IAGD,uBAAuB,MAAM;AAC5B,YAAM,eAAe,KAAK,iBAAiB,IAAI;AAC/C,UAAI,CAAC;AAAc,eAAO;AAC1B,YAAM,UAAU,KAAK,kBAAkB,IAAI,EAAE,KAAK,WAAS,MAAM,SAAS,YAAY;AACtF,aAAO,UAAU,QAAQ,OAAO;AAAA,IAChC;AAAA;AAAA,IAGD,mBAAmB,MAAM;AACxB,YAAM,eAAe,KAAK,iBAAiB,IAAI;AAC/C,UAAI,CAAC;AAAc,eAAO;AAC1B,YAAM,SAAS,KAAK,kBAAkB,IAAI,EAAE,KAAK,WAAS,MAAM,SAAS,YAAY,KAAK;AAC1F,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,kBAAkB,OAAO;AACxB,UAAI,CAAC,OAAO;AACXA,sBAAAA,MAAA,MAAA,OAAA,kCAAY,4BAA4B;AACxC,eAAO;AAAA,MACR;AAEA,YAAM,aAAa,MAAM,YAAY,MAAM;AAC3CA,oBAAAA,MAAY,MAAA,OAAA,kCAAA,wBAAwB,MAAM,QAAQ,MAAM,IAAI,SAAS,UAAU;AAG/E,UAAI,OAAO,MAAM,aAAa,WAAW;AACxCA,sBAAY,MAAA,MAAA,OAAA,kCAAA,mBAAmB,MAAM,QAAQ;AAAA,MAC9C,WAAW,MAAM,aAAa,QAAW;AACxCA,4BAAA,MAAA,OAAA,kCAAY,oBAAoB,OAAO,MAAM,UAAU,MAAM,QAAQ;AAAA,MACtE;AAEA,UAAI,OAAO,MAAM,OAAO,WAAW;AAClCA,sBAAY,MAAA,MAAA,OAAA,kCAAA,aAAa,MAAM,EAAE;AAAA,iBACvB,MAAM,OAAO,QAAW;AAClCA,4BAAA,MAAA,OAAA,kCAAY,cAAc,OAAO,MAAM,IAAI,MAAM,EAAE;AAAA,MACpD;AAEA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,gBAAgB,OAAO,MAAM;AAC5B,YAAM,QAAQ,MAAM,OAAO;AAC3B,YAAM,WAAW,KAAK,kBAAkB,IAAI;AAC5C,UAAI,SAAS,KAAK,GAAG;AACpB,aAAK,iBAAiB,IAAI,IAAI,SAAS,KAAK,EAAE;AAAA,MAC/C;AAAA,IACA;AAAA,IAED,MAAM,4BAA4B;AACjC,YAAM,MAAM,MAAMC,eAAS,UAAC,YAAY,EAAE,MAAM,mBAAmB,MAAM,CAAC,EAAA,CAAG;AAC7E,UAAI,IAAI,SAAS,eAAe;AAE/BF,wBAAAA,UAAU,cAAc,IAAI,IAAI;AAEhC,aAAK,WAAU;AAGf,mBAAW,MAAM;AAChB,eAAK,aAAY;AAAA,QACjB,GAAE,EAAE;AAAA,MACN;AAAA,IACA;AAAA,IAED,MAAM,aAAa,MAAM,OAAO;AAC/B,UAAI,CAAC,KAAK,UAAU;AACnBC,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C;AAAA,MACD;AACA,UAAI,CAAC,MAAM,UAAU;AACpBA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,QAAQ;AAChD;AAAA,MACD;AACA,UAAI;AACH,cAAM,sBAAsB,KAAK,kBAAkB,KAAK;AACxD,cAAM,SAAS,sBAAsB,oBAAoB;AACzD,cAAM,YAAY,MAAM,QAAQ,MAAM;AAGtC,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,EAAE,YAAY,UAAU;AAAA,QAC/B,CAAC;AAED,YAAI,YAAY,SAAS,SAAS,SAAS,YAAY;AAEtD,cAAI,SAAS,KAAK,QAAQ;AAEzB,gBAAI,SAAS,KAAK,OAAO,kBAAkB,CAAC,MAAM,QAAQ,SAAS,KAAK,OAAO,cAAc,GAAG;AAC/F,uBAAS,KAAK,OAAO,iBAAiB,OAAO,KAAK,SAAS,KAAK,OAAO,cAAc,EAAE,IAAI,UAAQ;AAClG,uBAAO,EAAE,MAAM,GAAG,SAAS,KAAK,OAAO,eAAe,IAAI;cAC3D,CAAC;AAAA,YACF;AAEA,iBAAK,SAAS,OAAO,OAAO,CAAE,GAAE,SAAS,KAAK,MAAM;AACpDF,4BAAAA,UAAU,SAAS,SAAS,KAAK;AAGjC,iBAAK,WAAU;AAGf,uBAAW,MAAM;AAChB,mBAAK,aAAY;AAAA,YACjB,GAAE,EAAE;AAAA,iBACC;AAEN,kBAAM,KAAK;UACZ;AACA,gBAAM,KAAK;AACXC,8BAAI,UAAU,EAAE,OAAO,SAAS,KAAK,SAAS,MAAM,UAAU,CAAC;AAAA,eACzD;AACNA,wBAAAA,MAAI,UAAU,EAAE,OAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,WAAY,QAAQ,MAAM,OAAQ,CAAA;AAAA,QACtG;AAAA,MACC,SAAO,OAAO;AACfA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAAA,MAC9C;AAAA,IACA;AAAA;AAAA,IAGD,MAAM,eAAe,MAAM;AAC1BA,oBAAY,MAAA,MAAA,OAAA,kCAAA,YAAY,IAAI;AAC5B,UAAI,CAAC,KAAK,UAAU;AACnBA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C;AAAA,MACD;AAEA,YAAM,QAAQ,KAAK,mBAAmB,IAAI;AAC1C,UAAI,CAAC,OAAO;AACXA,sBAAG,MAAC,UAAU,EAAE,OAAO,SAAS,MAAM,QAAQ;AAC9C;AAAA,MACD;AAEA,UAAI,CAAC,KAAK,kBAAkB,KAAK,GAAG;AACnCA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,QAAQ;AAC/C;AAAA,MACD;AAEA,UAAI;AACH,cAAM,YAAY,MAAM,QAAQ,MAAM;AACtCA,sBAAY,MAAA,MAAA,OAAA,kCAAA,WAAW,SAAS;AAGhCA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAGD,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,EAAE,YAAY,UAAU;AAAA,QAC/B,CAAC;AAGDD,sBAAG,MAAC,YAAW;AAEf,YAAI,YAAY,SAAS,SAAS,2BAA2B;AAC5DA,wBAAY,MAAA,MAAA,OAAA,kCAAA,SAAS,SAAS,IAAI;AAElC,cAAI,SAAS,KAAK,QAAQ;AAEzB,gBAAI,SAAS,KAAK,OAAO,kBAAkB,CAAC,MAAM,QAAQ,SAAS,KAAK,OAAO,cAAc,GAAG;AAC/F,uBAAS,KAAK,OAAO,iBAAiB,OAAO,KAAK,SAAS,KAAK,OAAO,cAAc,EAAE,IAAI,UAAQ;AAClG,uBAAO,EAAE,MAAM,GAAG,SAAS,KAAK,OAAO,eAAe,IAAI;cAC3D,CAAC;AAAA,YACF;AAEA,iBAAK,SAAS,OAAO,OAAO,CAAE,GAAE,SAAS,KAAK,MAAM;AACpDD,4BAAAA,UAAU,SAAS,SAAS,KAAK;AAGjC,kBAAM,eAAe,KAAK,OAAO,eAAe,KAAK,OAAK,EAAE,SAAS,aAAa,EAAE,OAAO,SAAS;AACpG,gBAAI,cAAc;AACjBC,4BAAA,MAAA,MAAA,OAAA,kCAAY,iBAAiB,SAAS;AACtC,2BAAa,WAAW;AACxB,kBAAI,aAAa,OAAO,QAAW;AAClC,6BAAa,KAAK;AAAA,cACnB;AAAA,YACD;AAGA,iBAAK,WAAU;AACf,kBAAM,KAAK;AAGX,uBAAW,MAAM;AAChB,mBAAK,aAAY;AAAA,YACjB,GAAE,EAAE;AAGLA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,SAAS,KAAK,WAAW,OAAO,SAAS;AAAA,cAChD,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,eACM;AAENA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,WAAY;AAAA,YAC/D,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9BA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAAA,MAC9C;AAAA,IACA;AAAA,IAED,iBAAiB,WAAW;AAE3B,YAAM,cAAc;AAAA,QACnB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AACA,aAAO,YAAY,SAAS,KAAK;AAAA,IACjC;AAAA,IAED,eAAe,WAAW;AAEzB,YAAM,YAAY;AAAA,QACjB,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,MACT;AACA,aAAO,UAAU,SAAS,KAAK;AAAA,IAC/B;AAAA,IAED,cAAc,WAAW;AAExB,YAAM,WAAW;AAAA,QAChB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,MACT;AACA,aAAO,SAAS,SAAS,KAAK;AAAA,IAC9B;AAAA,IAED,oBAAoB,WAAW;AAE9B,YAAM,UAAU;AAAA,QACf,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AACA,aAAO,QAAQ,SAAS,KAAK;AAAA,IAC7B;AAAA,IAED,cAAc,WAAW;AAExB,YAAM,WAAW;AAAA,QAChB,QAAQ,CAAC,SAAS,SAAS,SAAS,SAAS,OAAO;AAAA,QACpD,OAAO,CAAC,QAAQ,QAAQ,QAAQ,OAAO,KAAK;AAAA,QAC5C,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;AAAA,QACtE,QAAQ,CAAC,SAAS,SAAS,SAAS,OAAO;AAAA,QAC3C,QAAQ,CAAC,QAAQ,QAAQ,MAAM;AAAA,QAC/B,QAAQ,CAAC,QAAQ,QAAQ,MAAM;AAAA,QAC/B,SAAS,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAChD,QAAQ,CAAC,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QACvC,MAAM,CAAC,OAAO,OAAO,KAAK;AAAA,QAC1B,MAAM,CAAC,OAAO,OAAO,KAAK;AAAA,QAC1B,MAAM,CAAC,OAAO,OAAO,KAAK;AAAA,QAC1B,MAAM,CAAC,OAAO,OAAO,KAAK;AAAA,MAC3B;AACA,aAAO,SAAS,SAAS,KAAK,CAAC;AAAA,IAC/B;AAAA,IAED,mBAAmB,WAAW,UAAU;;AAEvC,YAAM,UAAU;AAAA,QACf,QAAQ;AAAA,UACP,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,UACT,SAAS;AAAA,QACT;AAAA,QACD,OAAO;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,QACP;AAAA,QACD,QAAQ;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,QACR;AAAA,MACD;AACA,eAAO,aAAQ,SAAS,MAAjB,mBAAqB,cAAa;AAAA,IACzC;AAAA,IAED,gBAAgB,OAAO;AACtB,UAAI,CAAC,SAAS,MAAM,OAAO;AAAM,eAAO;AAExC,YAAM,UAAU,CAAC;AACjB,YAAM,QAAQ,MAAM;AAGpB,UAAI,MAAM,OAAO,MAAM;AACtB,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,GAAG,CAAC,EAAE;AAC7C,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,EAAE,CAAC,EAAE;AAAA,iBAClC,MAAM,OAAO,MAAM;AAC7B,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,CAAC,CAAC,EAAE;AAC3C,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,EAAE,CAAC,EAAE;AAAA,iBAClC,MAAM,OAAO,MAAM;AAC7B,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,GAAG,CAAC,EAAE;AAC7C,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,GAAG,CAAC,EAAE;AAC7C,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,EAAE,CAAC,EAAE;AAAA,iBAClC,MAAM,OAAO,MAAM;AAC7B,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,CAAC,CAAC,EAAE;AAC3C,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,CAAC,CAAC,EAAE;AAC3C,gBAAQ,KAAK,OAAO,KAAK,MAAM,QAAQ,EAAE,CAAC,EAAE;AAAA,MAC7C;AAEA,aAAO,QAAQ,SAAS,IAAI,QAAQ,KAAK,IAAI,IAAI;AAAA,IACjD;AAAA,IAED,UAAU,KAAK;AACd,WAAK,YAAY;AAAA,IACjB;AAAA,IAED,cAAc,GAAG;AAChB,WAAK,aAAa,EAAE,OAAO;AAAA,IAC3B;AAAA,IAED,eAAe,GAAG;AACjB,WAAK,cAAc,EAAE,OAAO;AAAA,IAC5B;AAAA,IAED,mBAAmB;AAClB,UAAI,KAAK,cAAc;AAAO,eAAO;AACrC,aAAO,KAAK;AAAA,IACZ;AAAA,IAED,yBAAyB;AACxB,YAAM,eAAe;AAAA,QACpB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AACA,aAAO,aAAa,KAAK,SAAS,KAAK;AAAA,IACvC;AAAA,IAED,iBAAiB,OAAO;AACvB,UAAI,MAAM,SAAS;AAAG,eAAO;AAC7B,aAAO,KAAK,IAAK,MAAM,KAAK,MAAM,OAAQ,KAAK,GAAG;AAAA,IAClD;AAAA,IAED,cAAc,OAAO;AAEpB,aAAO,MAAM,OAAO,QAAQ,MAAM,MAAM,MAAM;AAAA,IAC9C;AAAA,IAED,MAAM,iBAAiB,OAAO;AAC7B,UAAI,CAAC,KAAK,UAAU;AACnBA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C;AAAA,MACD;AACA,UAAI,CAAC,MAAM,MAAM,CAAC,MAAM,UAAU;AACjCA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,QAAQ;AAChD;AAAA,MACD;AACA,UAAI;AACH,cAAM,SAAU,MAAM,MAAM,MAAM,WAAY,oBAAoB;AAClE,cAAM,YAAY,MAAM,MAAM,MAAM;AAEpC,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,EAAE,YAAY,UAAU;AAAA,SAC9B;AACD,YAAI,SAAS,SAAS,SAAS,YAAY;AAE1C,cAAI,SAAS,KAAK,QAAQ;AAEzB,gBAAI,SAAS,KAAK,OAAO,kBAAkB,CAAC,MAAM,QAAQ,SAAS,KAAK,OAAO,cAAc,GAAG;AAC/F,uBAAS,KAAK,OAAO,iBAAiB,OAAO,KAAK,SAAS,KAAK,OAAO,cAAc,EAAE,IAAI,UAAQ;AAClG,uBAAO,EAAE,MAAM,GAAG,SAAS,KAAK,OAAO,eAAe,IAAI,EAAE;AAAA,cAC7D,CAAC;AAAA,YACF;AAEA,iBAAK,SAAS,OAAO,OAAO,CAAE,GAAE,SAAS,KAAK,MAAM;AACpDF,4BAAAA,UAAU,SAAS,SAAS,KAAK;AACjC,iBAAK,WAAU;AAAA,iBACT;AAEN,kBAAM,KAAK;UACZ;AACA,gBAAM,KAAK;AACX,eAAK,aAAY;AACjBC,8BAAI,UAAU,EAAE,OAAO,SAAS,KAAK,SAAS,MAAM,WAAW;AAAA,eACzD;AACNA,8BAAI,UAAU,EAAE,OAAO,SAAS,KAAK,SAAS,MAAM,QAAQ;AAAA,QAC7D;AAAA,MACC,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9BA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,MAC9C;AAAA,IACA;AAAA,IAED,gBAAgB,OAAO;AACtB,WAAK,gBAAgB;AACrB,WAAK,aAAa;AAAA,IAClB;AAAA,IAED,cAAc;AACb,WAAK,aAAa;AAClB,WAAK,gBAAgB;AAAA,IACrB;AAAA,IAED,UAAU,OAAO;AAChB,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AAAA,IACtB;AAAA,IAED,kBAAkB;AACjB,WAAK,iBAAiB;AACtB,WAAK,gBAAgB;AAAA,IACrB;AAAA,IAED,MAAM,aAAa,OAAO;AACzB,UAAI,CAAC,KAAK,UAAU;AACnBA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C;AAAA,MACD;AACA,UAAI,CAAC,MAAM,MAAM,CAAC,MAAM,UAAU;AACjCA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,QAAQ;AAChD;AAAA,MACD;AACA,UAAI;AACH,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,EAAE,IAAI,MAAM,MAAM,MAAM,KAAK;AAAA,SACnC;AACD,YAAI,SAAS,SAAS,yBAAyB;AAC9C,eAAK,WAAW;AAChBD,8BAAI,UAAU,EAAE,OAAO,SAAS,KAAK,SAAS,MAAM,WAAW;AAAA,eACzD;AACNA,8BAAI,UAAU,EAAE,OAAO,SAAS,KAAK,SAAS,MAAM,QAAQ;AAAA,QAC7D;AAAA,MACC,SAAO,OAAO;AACfA,sBAAAA,MAAA,MAAA,SAAA,kCAAc,WAAW,KAAK;AAC9BA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAAA,MAC9C;AAAA,IACA;AAAA,IACD,YAAY,OAAO;AAElB,YAAM,YAAY,MAAM,MAAM,MAAM,QAAQ;AAC5C,aAAO,MAAM,OAAO,UAAU,MAAM,OAAO,SAAS,UAAU,SAAS,GAAG,KAAK,UAAU,SAAS,GAAG,KAAK,UAAU,SAAS,IAAI,KAAK,UAAU,SAAS,IAAI,KAAK,UAAU,SAAS,IAAI;AAAA,IACzL;AAAA,IACD,kBAAkB,MAAM;AACvB,WAAK,oBAAoB;AACzB,YAAM,QAAQ,KAAK,aAAa,KAAK,OAAK,EAAE,QAAQ,IAAI;AACxD,WAAK,yBAAyB,QAAQ,MAAM,QAAQ;AACpD,WAAK,oBAAoB;AAAA,IACzB;AAAA,IACD,qBAAqB;AACpB,WAAK,oBAAoB;AAAA,IACzB;AAAA,IACD,MAAM,cAAc,OAAO,MAAM;AAChCA,0BAAA,MAAA,OAAA,kCAAY,SAAS,MAAM,MAAM,OAAO,IAAI;AAC5C,WAAK,iBAAiB,IAAI,IAAI,MAAM;AACpC,WAAK,oBAAoB;AAGzB,UAAI,MAAM,YAAY,MAAM,IAAI;AAE/B,YAAI,KAAK,kBAAkB,KAAK,GAAG;AAClCA,wBAAAA,qDAAY,cAAc;AAC1B;AAAA,QACD;AAEA,YAAI;AACH,gBAAM,YAAY,MAAM,QAAQ,MAAM;AAGtCA,wBAAAA,MAAI,YAAY;AAAA,YACf,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAGD,gBAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,YAC5C,MAAM;AAAA,YACN,MAAM,EAAE,YAAY,UAAU;AAAA,UAC/B,CAAC;AAGDD,wBAAG,MAAC,YAAW;AAEf,cAAI,YAAY,SAAS,SAAS,uBAAuB;AACxDA,0BAAA,MAAA,MAAA,OAAA,kCAAY,SAAS,SAAS,IAAI;AAElC,gBAAI,SAAS,KAAK,QAAQ;AAEzB,kBAAI,SAAS,KAAK,OAAO,kBAAkB,CAAC,MAAM,QAAQ,SAAS,KAAK,OAAO,cAAc,GAAG;AAC/F,yBAAS,KAAK,OAAO,iBAAiB,OAAO,KAAK,SAAS,KAAK,OAAO,cAAc,EAAE,IAAI,UAAQ;AAClG,yBAAO,EAAE,MAAM,GAAG,SAAS,KAAK,OAAO,eAAe,IAAI;gBAC3D,CAAC;AAAA,cACF;AAEA,mBAAK,SAAS,OAAO,OAAO,CAAE,GAAE,SAAS,KAAK,MAAM;AACpDD,8BAAAA,UAAU,SAAS,SAAS,KAAK;AAGjC,oBAAM,eAAe,KAAK,OAAO,eAAe,KAAK,OAAK,EAAE,SAAS,aAAa,EAAE,OAAO,SAAS;AACpG,kBAAI,cAAc;AACjBC,8BAAA,MAAA,MAAA,OAAA,kCAAY,aAAa,SAAS;AAClC,6BAAa,WAAW;AACxB,oBAAI,aAAa,OAAO,QAAW;AAClC,+BAAa,KAAK;AAAA,gBACnB;AAAA,cACD;AAGA,mBAAK,WAAU;AACf,oBAAM,KAAK;AAGX,yBAAW,MAAM;AAChB,qBAAK,aAAY;AAAA,cACjB,GAAE,EAAE;AAGLA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO,SAAS,KAAK,WAAW,OAAO,SAAS;AAAA,gBAChD,MAAM;AAAA,cACP,CAAC;AAAA,YACF;AAAA,iBACM;AAENA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAQ,YAAY,SAAS,QAAQ,SAAS,KAAK,WAAY;AAAA,cAC/D,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACC,SAAO,OAAO;AACfA,wBAAG,MAAC,YAAW;AACfA,+EAAc,WAAW,KAAK;AAC9BA,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAAA,QAC9C;AAAA,aACM;AACNA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAAA,MACjD;AAAA,IACA;AAAA,IACD,MAAM,oBAAoB;AACzB,UAAI;AACH,cAAM,MAAM,MAAMC,eAAS,UAAC,YAAY;AAAA,UACvC,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,QACR,CAAC;AACD,YAAI,IAAI,SAAS,iBAAiB;AACjC,eAAK,eAAe,IAAI;AAAA,QACzB;AAAA,MACD,SAAS,GAAG;AACXD,4EAAa,YAAY,CAAC;AAAA,MAC3B;AAAA,IACA;AAAA,IACD,eAAe,OAAO;AAErB,YAAM,aAAa,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,QAAQ,OAAO;AAC3F,aAAO,WAAW,SAAS,MAAM,IAAI,KAAK,WAAW,SAAS,MAAM,EAAE;AAAA,IACvE;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACn+BA,GAAG,WAAW,eAAe;"}