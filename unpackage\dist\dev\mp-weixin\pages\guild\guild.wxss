
.container.data-v-44e2720a {
	padding: 20rpx;
	padding-bottom: 140rpx; /* 为tabBar留出空间 */
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}
.guild-info.data-v-44e2720a {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.guild-header.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.guild-name.data-v-44e2720a {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.guild-level.data-v-44e2720a {
	font-size: 28rpx;
	color: #667eea;
	background: #f0f4ff;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}
.guild-stats.data-v-44e2720a {
	display: flex;
	gap: 30rpx;
}
.stat-item.data-v-44e2720a {
	display: flex;
	align-items: center;
}
.stat-label.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
	margin-right: 10rpx;
}
.stat-value.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}
.guild-actions.data-v-44e2720a {
	margin-top: 20rpx;
	text-align: center;
}
.daily-reward-btn.data-v-44e2720a {
	background: linear-gradient(135deg, #f39c12, #e67e22);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 15rpx 30rpx;
	font-size: 26rpx;
}
.daily-reward-btn[disabled].data-v-44e2720a {
	background: #ccc;
	color: #666;
}
.no-guild.data-v-44e2720a {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 60rpx 30rpx;
	text-align: center;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.no-guild-title.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}
.no-guild-desc.data-v-44e2720a {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 40rpx;
}
.join-guild-btn.data-v-44e2720a {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}
.guild-functions.data-v-44e2720a {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
.function-grid.data-v-44e2720a {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}
.function-item.data-v-44e2720a {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 30rpx;
	text-align: center;
	transition: all 0.3s;
}
.function-item.data-v-44e2720a:active {
	background: #e9ecef;
	transform: scale(0.98);
}
.function-icon.data-v-44e2720a {
	font-size: 48rpx;
	display: block;
	margin-bottom: 15rpx;
}
.function-name.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}
.tasks-section.data-v-44e2720a,
.skills-section.data-v-44e2720a,
.members-section.data-v-44e2720a,
.shop-section.data-v-44e2720a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(255, 255, 255, 0.98);
	z-index: 1000;
	padding: 20rpx;
}
.section-header.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 2rpx solid #f0f0f0;
	margin-bottom: 20rpx;
}
.section-title.data-v-44e2720a {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.section-close.data-v-44e2720a {
	font-size: 40rpx;
	color: #999;
	line-height: 1;
}
.tasks-list.data-v-44e2720a,
.skills-list.data-v-44e2720a,
.members-list.data-v-44e2720a,
.shop-list.data-v-44e2720a {
	height: calc(100vh - 120rpx);
}
.task-item.data-v-44e2720a,
.skill-item.data-v-44e2720a,
.member-item.data-v-44e2720a,
.shop-item.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: white;
	border-radius: 15rpx;
	margin-bottom: 15rpx;
}
.task-info.data-v-44e2720a,
.skill-info.data-v-44e2720a,
.member-info.data-v-44e2720a,
.item-info.data-v-44e2720a {
	flex: 1;
}
.task-name.data-v-44e2720a,
.skill-name.data-v-44e2720a,
.member-name.data-v-44e2720a,
.item-name.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 8rpx;
}
.task-desc.data-v-44e2720a,
.skill-desc.data-v-44e2720a,
.item-desc.data-v-44e2720a {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 8rpx;
}
.task-reward.data-v-44e2720a {
	font-size: 24rpx;
	color: #f39c12;
	display: block;
}
.skill-type.data-v-44e2720a {
	font-size: 24rpx;
	color: #667eea;
	display: block;
	margin-bottom: 8rpx;
}
.member-position.data-v-44e2720a,
.member-level.data-v-44e2720a {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}
.task-status.data-v-44e2720a,
.skill-status.data-v-44e2720a,
.member-contribution.data-v-44e2720a {
	text-align: center;
}
.task-difficulty.data-v-44e2720a {
	font-size: 24rpx;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	display: block;
	margin-bottom: 10rpx;
}
.difficulty-easy.data-v-44e2720a {
	background: #d4edda;
	color: #155724;
}
.difficulty-medium.data-v-44e2720a {
	background: #fff3cd;
	color: #856404;
}
.difficulty-hard.data-v-44e2720a {
	background: #f8d7da;
	color: #721c24;
}
.skill-level.data-v-44e2720a {
	font-size: 24rpx;
	color: #667eea;
	display: block;
	margin-bottom: 10rpx;
}
.contribution-label.data-v-44e2720a {
	font-size: 24rpx;
	color: #666;
	margin-right: 10rpx;
}
.contribution-value.data-v-44e2720a {
	font-size: 28rpx;
	font-weight: bold;
	color: #f39c12;
}
.accept-task-btn.data-v-44e2720a,
.learn-skill-btn.data-v-44e2720a,
.buy-item-btn.data-v-44e2720a {
	background: #27ae60;
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 12rpx 24rpx;
	font-size: 26rpx;
}
.accept-task-btn[disabled].data-v-44e2720a,
.learn-skill-btn[disabled].data-v-44e2720a,
.buy-item-btn[disabled].data-v-44e2720a {
	opacity: 0.5;
	background: #ccc;
}
.empty-tasks.data-v-44e2720a {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}
.modal-overlay.data-v-44e2720a {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
}
.modal-content.data-v-44e2720a {
	background: white;
	border-radius: 20rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}
.modal-header.data-v-44e2720a {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.modal-title.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.modal-close.data-v-44e2720a {
	font-size: 40rpx;
	color: #999;
	line-height: 1;
}
.modal-body.data-v-44e2720a {
	padding: 30rpx;
	max-height: 400rpx;
	overflow-y: auto;
}
.guild-option.data-v-44e2720a {
	padding: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.guild-option.data-v-44e2720a:last-child {
	border-bottom: none;
}
.guild-option-name.data-v-44e2720a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.guild-option-desc.data-v-44e2720a {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 10rpx;
}
.guild-option-requirement.data-v-44e2720a {
	font-size: 24rpx;
	color: #e74c3c;
	display: block;
}
.guild-option-available.data-v-44e2720a {
	font-size: 24rpx;
	color: #27ae60;
	display: block;
}
.guild-option-disabled.data-v-44e2720a {
	opacity: 0.6;
	background: #f8f9fa;
}
.detail-name.data-v-44e2720a {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}
.detail-desc.data-v-44e2720a,
.detail-requirement.data-v-44e2720a,
.detail-reward.data-v-44e2720a {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}
.modal-footer.data-v-44e2720a {
	display: flex;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #f0f0f0;
	gap: 20rpx;
}
.modal-btn.data-v-44e2720a {
	flex: 1;
	padding: 20rpx;
	border: none;
	border-radius: 15rpx;
	font-size: 28rpx;
}
.cancel-btn.data-v-44e2720a {
	background: #f0f0f0;
	color: #666;
}
.confirm-btn.data-v-44e2720a {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}
.confirm-btn[disabled].data-v-44e2720a {
	opacity: 0.5;
	background: #ccc;
}
