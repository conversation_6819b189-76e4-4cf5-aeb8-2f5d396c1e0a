# 奖励与掉落系统
# 负责处理所有奖励结算、掉落分配、奖励推送等逻辑

from inventory_system import add_item_to_inventory
import logging

logger = logging.getLogger(__name__)

def add_reward_to_player(player, reward, user_id=None, server=None):
    """
    向玩家发放奖励（银两、经验、武学点、物品等），并自动推送奖励信息。
    :param player: 玩家对象
    :param reward: 奖励字典，如{"银两": 100, "物品": [{"id": "item001", "count": 1}]}
    :param user_id: 玩家ID（如需持久化或推送）
    :param server: GameServer对象（如需调用异步方法）
    """
    # 奖励银两
    if '银两' in reward:
        player['money'] = player.get('money', 0) + reward['银两']
    # 奖励经验
    if '历练值' in reward:
        player['experience'] = player.get('experience', 0) + reward['历练值']
    # 奖励武学点
    if '武学点' in reward:
        player['skill_points'] = player.get('skill_points', 0) + reward['武学点']
    # 奖励物品
    if '物品' in reward:
        items = reward['物品']
        if not isinstance(items, list):
            items = [items]
        for item in items:
            item_id = item['id'] if isinstance(item, dict) else item
            count = item.get('count', 1) if isinstance(item, dict) else 1
            # 支持异步server.add_item_to_inventory
            if server and user_id:
                import asyncio
                coro = server.add_item_to_inventory(user_id, player, {'id': item_id, 'quantity': count})
                try:
                    if asyncio.iscoroutine(coro):
                        asyncio.get_event_loop().run_until_complete(coro)
                except Exception as e:
                    logger.error(f"添加物品失败: {item_id}, {e}")
            else:
                add_item_to_inventory(player, item_id, count)
    # 其他奖励类型可扩展
    # TODO: 推送奖励信息到前端（如有server和websocket）
    return player


def handle_monster_drop(monster, player, user_id=None, server=None):
    """
    处理怪物掉落，返回奖励内容，并调用 add_reward_to_player。
    :param monster: 怪物对象
    :param player: 玩家对象
    :param user_id: 玩家ID
    :param server: GameServer对象
    """
    # 假设monster有rewards字段，结构如{"银两": 50, "物品": [{"id": "item001", "count": 1}]}
    rewards = monster.get('rewards', {})
    add_reward_to_player(player, rewards, user_id=user_id, server=server)
    return rewards

# 其他奖励相关函数可在此扩展 