# 生活技能系统说明

## 概述

新玩家创建时，默认只会4种生活技能，这些技能专门用于采集活动，不参与任何属性加成逻辑。

## 生活技能列表

### 默认生活技能（等级0，已解锁）
1. **采药** - 用于采集草药
2. **伐木** - 用于采集木材
3. **挖矿** - 用于采集矿石
4. **剥皮** - 用于采集兽皮

## 系统特点

### 1. 独立分组
- 生活技能在前端武学页面中单独显示为"生活技能"分组
- 与基础武学、初级武学等武学分组分开显示

### 2. 无属性加成
- 生活技能升级时不增加任何角色属性（攻击、防御、气血等）
- 生活技能不参与天赋点增加逻辑
- 纯粹用于提升采集效率和成功率

### 3. 学习机制
- 生活技能可以通过"读书学武"功能学习
- 消耗体力获得经验值
- 使用二次方公式计算升级所需经验：E = k × l²
- 基础系数 k = 50（生活技能系数较低，容易升级）

### 4. 体力消耗
- 每次学习消耗10点体力
- 基础经验值增长：10 + 当前等级 × 2
- 根据境界获得额外经验加成

## 与其他武学的关系

### 武学获取方式
- **生活技能**：创建角色时默认获得
- **基础武学**：需要通过找老师学习解锁
- **初级武学**：需要基础武学达到20级后解锁
- **高级武学**：需要初级武学高等级后解锁
- **江湖绝学**：稀有，特殊获取

### 属性加成对比
| 技能类型 | 属性加成 | 天赋点增加 |
|---------|---------|-----------|
| 生活技能 | ❌ 无 | ❌ 无 |
| 基础武学 | ✅ 有 | ✅ 有 |
| 初级武学 | ✅ 有 | ✅ 有 |
| 高级武学 | ✅ 有 | ✅ 有 |
| 江湖绝学 | ✅ 有 | ✅ 有 |

## 前端显示

### 武学页面分组
```
生活技能
├── 采药 (等级0/经验0)
├── 伐木 (等级0/经验0)
├── 挖矿 (等级0/经验0)
└── 剥皮 (等级0/经验0)

基础武学
└── (需要找老师学习)

初级武学
└── (需要基础武学20级解锁)

高级武学
└── (需要初级武学高等级解锁)

江湖绝学
└── (稀有获取)
```

## 测试验证

使用 `test_basic_life_skills.py` 脚本可以验证：
1. 新玩家只有4种生活技能
2. 生活技能等级都是0
3. 生活技能可以正常学习升级
4. 生活技能不参与属性加成
5. 生活技能单独分组显示

## 总结

生活技能系统为玩家提供了基础的采集能力，同时保持了武学系统的纯粹性。玩家需要通过找老师学习来获得真正的武学技能，这些武学技能才会提供属性加成和天赋点增加。 