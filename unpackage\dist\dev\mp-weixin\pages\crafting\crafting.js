"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../utils/websocket.js");
const utils_gameState = require("../../utils/gameState.js");
const utils_gameData = require("../../utils/gameData.js");
const _sfc_main = {
  data() {
    return {
      player: {},
      craftingLevel: 1,
      craftableItems: [],
      showDetail: false,
      selectedRecipe: null,
      isLoading: false
    };
  },
  computed: {
    // 移除材料统计 - 只显示合成配方中需要的材料
  },
  onLoad() {
    this.updateData();
    this.loadCraftableItems();
  },
  onShow() {
    this.updateData();
    this.loadCraftableItems();
  },
  onUnload() {
  },
  methods: {
    updateData() {
      this.player = { ...utils_gameState.gameState.player };
      this.isAuthed = utils_gameState.gameState.isAuthed || false;
      this.craftingLevel = 1;
    },
    async loadCraftableItems() {
      try {
        this.isLoading = true;
        let response = await utils_gameData.gameUtils.sendMessage({
          type: "crafting_action",
          data: { action: "get_craftable_items" }
        });
        if (response.type !== "get_craftable_success" && response.data && response.data.craftable_items) {
          response = { type: "get_craftable_success", data: response.data };
        }
        this.isLoading = false;
        if (response.type === "get_craftable_success") {
          this.craftableItems = response.data.craftable_items || [];
          this.player.energy = response.data.energy || 0;
        } else if (response.type === "get_craftable_failed") {
          common_vendor.index.showToast({
            title: response.data.message || "获取可合成物品失败",
            icon: "none"
          });
        } else if (response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message || "获取可合成物品失败",
            icon: "none"
          });
        }
      } catch (error) {
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "加载失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    },
    parseRecipe(recipe) {
      const materials = {};
      if (!recipe)
        return materials;
      if (typeof recipe === "object") {
        return recipe;
      }
      if (typeof recipe === "string") {
        if (recipe.trim().startsWith("{")) {
          try {
            return JSON.parse(recipe);
          } catch (e) {
            try {
              const fixed = recipe.replace(/'/g, '"');
              return JSON.parse(fixed);
            } catch (e2) {
            }
          }
        }
      }
      const parts = recipe.split(",");
      for (const part of parts) {
        if (part.includes(":")) {
          const [material, quantity] = part.split(":");
          materials[material.trim()] = parseInt(quantity.trim());
        }
      }
      return materials;
    },
    getMaterialCount(materialName) {
      let count = 0;
      utils_gameState.gameState.inventory.forEach((item) => {
        if (item.name === materialName) {
          count += item.quantity || 1;
        } else if (item.id && item.id.includes(materialName.replace("残页", "_canyie"))) {
          count += item.quantity || 1;
        }
      });
      return count;
    },
    getMissingMaterials(recipe) {
      if (!recipe || !recipe.craft_recipe)
        return [];
      const materials = this.parseRecipe(recipe.craft_recipe);
      const missing = [];
      for (const [material, quantity] of Object.entries(materials)) {
        if (this.getMaterialCount(material) < quantity) {
          missing.push(`${material}(需要${quantity}，现有${this.getMaterialCount(material)})`);
        }
      }
      return missing;
    },
    getQualityColor(quality) {
      return utils_gameData.gameUtils.getQualityColor(quality);
    },
    getQualityName(quality) {
      const qualities = {
        "common": "普通",
        "fine": "精良",
        "rare": "稀有",
        "epic": "传说",
        "legendary": "神品"
      };
      return qualities[quality] || "普通";
    },
    getTypeName(type) {
      const types = {
        "book": "秘籍",
        "consumable": "消耗品",
        "weapon": "武器",
        "armor": "防具",
        "necklace": "项链",
        "helmet": "头盔",
        "offhand": "副手",
        "accessory": "饰品",
        "medal": "勋章"
      };
      return types[type] || type;
    },
    canCraft(recipe) {
      if (!recipe || !recipe.craft_recipe)
        return false;
      if (recipe.can_craft !== void 0) {
        if (this.player.energy < 10) {
          return false;
        }
        return recipe.can_craft;
      }
      const materials = this.parseRecipe(recipe.craft_recipe);
      for (const [material, quantity] of Object.entries(materials)) {
        if (this.getMaterialCount(material) < quantity) {
          return false;
        }
      }
      if (this.player.energy < 10) {
        return false;
      }
      return true;
    },
    showRecipeDetail(recipe) {
      this.selectedRecipe = recipe;
      this.showDetail = true;
    },
    closeDetail() {
      this.showDetail = false;
      this.selectedRecipe = null;
    },
    craftItem(recipe) {
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (!this.canCraft(recipe)) {
        common_vendor.index.showToast({
          title: "材料或体力不足",
          icon: "none"
        });
        return;
      }
      common_vendor.index.showModal({
        title: "确认合成",
        content: `确定要合成 ${recipe.name} 吗？
消耗体力: 10点`,
        success: (res) => {
          if (res.confirm) {
            this.performCraft(recipe);
          }
        }
      });
    },
    async performCraft(recipe) {
      try {
        this.isLoading = true;
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "crafting_action",
          data: {
            action: "craft_item",
            item_id: recipe.id
          }
        });
        this.isLoading = false;
        if (response.type === "craft_success") {
          common_vendor.index.showToast({
            title: response.data.message || "合成成功",
            icon: "success"
          });
          if (response.data.inventory) {
            utils_gameState.gameState.inventory = response.data.inventory;
          }
          if (response.data.energy !== void 0) {
            this.player.energy = response.data.energy;
            utils_gameState.gameState.player.energy = response.data.energy;
          }
          this.closeDetail();
          this.loadCraftableItems();
        } else if (response.type === "craft_failed") {
          common_vendor.index.showToast({
            title: response.data.message || "合成失败",
            icon: "none"
          });
        } else if (response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message || "合成失败",
            icon: "none"
          });
        }
      } catch (error) {
        console.error("合成失败:", error);
        this.isLoading = false;
        common_vendor.index.showToast({
          title: "合成失败: " + (error.message || "未知错误"),
          icon: "none"
        });
      }
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($data.craftingLevel),
    b: common_vendor.t($data.player.energy || 0),
    c: common_vendor.t($data.player.max_energy || 100),
    d: common_vendor.t($data.craftableItems.length),
    e: common_vendor.t($data.isLoading ? "加载中..." : "刷新"),
    f: common_vendor.o((...args) => $options.loadCraftableItems && $options.loadCraftableItems(...args)),
    g: $data.isLoading,
    h: $data.isLoading
  }, $data.isLoading ? {} : $data.craftableItems.length === 0 ? {} : {
    j: common_vendor.f($data.craftableItems, (recipe, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(recipe.icon || "📦"),
        b: common_vendor.t(recipe.name),
        c: common_vendor.t($options.getQualityName(recipe.quality)),
        d: common_vendor.t($options.getTypeName(recipe.type)),
        e: recipe.can_craft
      }, recipe.can_craft ? {} : {}, {
        f: recipe.id,
        g: recipe.can_craft ? 1 : "",
        h: !recipe.can_craft ? 1 : "",
        i: common_vendor.o(($event) => $options.showRecipeDetail(recipe), recipe.id)
      });
    })
  }, {
    i: $data.craftableItems.length === 0,
    k: $data.showDetail
  }, $data.showDetail ? common_vendor.e({
    l: common_vendor.t($data.selectedRecipe.name),
    m: common_vendor.t($options.getQualityName($data.selectedRecipe.quality)),
    n: common_vendor.t($data.selectedRecipe.description),
    o: common_vendor.f($options.parseRecipe($data.selectedRecipe.craft_recipe), (quantity, material, i0) => {
      return {
        a: common_vendor.t(material),
        b: common_vendor.t($options.getMaterialCount(material)),
        c: common_vendor.t(quantity),
        d: material,
        e: $options.getMaterialCount(material) >= quantity ? 1 : "",
        f: $options.getMaterialCount(material) < quantity ? 1 : ""
      };
    }),
    p: $options.getMissingMaterials($data.selectedRecipe).length > 0
  }, $options.getMissingMaterials($data.selectedRecipe).length > 0 ? {
    q: common_vendor.f($options.getMissingMaterials($data.selectedRecipe), (material, k0, i0) => {
      return {
        a: common_vendor.t(material),
        b: material
      };
    })
  } : {}, {
    r: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args)),
    s: common_vendor.o(($event) => $options.craftItem($data.selectedRecipe)),
    t: !$options.canCraft($data.selectedRecipe),
    v: common_vendor.o(() => {
    }),
    w: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args))
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d6bc61f4"]]);
wx.createPage(MiniProgramPage);
