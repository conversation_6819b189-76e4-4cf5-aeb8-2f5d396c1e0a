"use strict";
const utils_websocket = require("./websocket.js");
const gameUtils = {
  // 计算伤害
  calculateDamage(attacker, defender, skill = null) {
    let baseDamage = attacker.attack;
    if (skill) {
      baseDamage *= skill.damageMultiplier || 1;
    }
    const isCrit = Math.random() < attacker.critRate;
    if (isCrit) {
      baseDamage *= attacker.critDamage;
    }
    const finalDamage = Math.max(1, baseDamage - defender.defense);
    return {
      damage: Math.floor(finalDamage),
      isCrit
    };
  },
  // 命中判定
  isHit(attacker, defender) {
    const hitChance = attacker.hitRate - defender.dodgeRate;
    return Math.random() < hitChance;
  },
  // 招架判定
  isBlocked(defender) {
    return Math.random() < defender.blockRate;
  },
  // 随机事件生成（前端不参与计算，仅用于显示）
  generateRandomEvent() {
    const events = [
      { type: 1, name: "好运事件", description: "你遇到了一个善良的商人，获得了一些奖励。" },
      { type: 2, name: "遭遇NPC", description: "你遇到了一个神秘的江湖人士。" },
      { type: 3, name: "采集事件", description: "你发现了一片资源丰富的区域。" },
      { type: 4, name: "普通事件", description: "你漫步在江湖中，感受着武侠世界的魅力。" },
      { type: 5, name: "奇遇事件", description: "你遇到了一个千载难逢的奇遇！" },
      { type: 6, name: "恩怨事件", description: "江湖恩怨，是非难辨。" },
      { type: 7, name: "组队事件", description: "你遇到了其他江湖人士，可以组队冒险。" }
    ];
    return events[3];
  },
  // 格式化数字
  formatNumber(num) {
    if (num >= 1e4) {
      return (num / 1e4).toFixed(1) + "万";
    }
    return num.toString();
  },
  // 获取品质颜色
  getQualityColor(quality) {
    const qualities = {
      "common": "#9e9e9e",
      "uncommon": "#4caf50",
      "rare": "#2196f3",
      "epic": "#9c27b0",
      "legendary": "#ff9800",
      "mythic": "#f44336"
    };
    return qualities[quality] || "#9e9e9e";
  },
  sendMessage(msg) {
    return new Promise((resolve, reject) => {
      let type = msg.type;
      let data = msg.data || {};
      if (type === "npc_function") {
        data = {
          npc_name: msg.npc_name,
          function: msg.function,
          data: msg.data || {}
        };
      }
      if (type === "get_equipment_data") {
        type = "get_player_data";
      }
      const timeoutDuration = type === "escape_battle" ? 15e3 : 1e4;
      let isResolved = false;
      const timeout = setTimeout(() => {
        if (!isResolved) {
          isResolved = true;
          cleanup();
          resolve({
            type: type + "_timeout",
            data: { message: "请求超时，请检查网络连接" }
          });
        }
      }, timeoutDuration);
      const cleanup = () => {
        clearTimeout(timeout);
        utils_websocket.wsManager.off(type + "_success", handler);
        utils_websocket.wsManager.off(type + "_failed", handler);
        utils_websocket.wsManager.off(type + "_error", handler);
        utils_websocket.wsManager.off("error", errorHandler);
        if (type === "market_action") {
          utils_websocket.wsManager.off("market_list", handler);
          utils_websocket.wsManager.off("success", handler);
          utils_websocket.wsManager.off("error", handler);
        }
        if (type === "get_map_npcs") {
          utils_websocket.wsManager.off("map_npcs", handler);
        }
        if (type === "npc_function") {
          utils_websocket.wsManager.off("npc_talk", handler);
          utils_websocket.wsManager.off("shop_items", handler);
          utils_websocket.wsManager.off("buy_success", handler);
          utils_websocket.wsManager.off("sell_success", handler);
          utils_websocket.wsManager.off("transport_destinations", handler);
          utils_websocket.wsManager.off("transport_success", handler);
          utils_websocket.wsManager.off("heal_success", handler);
          utils_websocket.wsManager.off("info_services", handler);
          utils_websocket.wsManager.off("info_success", handler);
        }
        if (type === "get_bonus_summary") {
          utils_websocket.wsManager.off("bonus_summary", handler);
        }
        if (type === "get_inventory_data") {
          utils_websocket.wsManager.off("inventory_data", handler);
        }
        if (type === "get_skills_data") {
          utils_websocket.wsManager.off("skills_data", handler);
        }
        if (type === "get_martial_configs") {
          utils_websocket.wsManager.off("martial_configs", handler);
        }
        if (type === "equip_item") {
          utils_websocket.wsManager.off("equip_success", handler);
          utils_websocket.wsManager.off("equip_failed", handler);
        }
        if (type === "get_player_data") {
          utils_websocket.wsManager.off("player_data", handler);
        }
        if (type === "get_player_data") {
          utils_websocket.wsManager.off("get_player_data_success", handler);
          utils_websocket.wsManager.off("get_player_data_failed", handler);
          utils_websocket.wsManager.off("get_player_data_error", handler);
        }
        if (type === "crafting_action") {
          utils_websocket.wsManager.off("get_craftable_success", handler);
          utils_websocket.wsManager.off("get_craftable_failed", handler);
          utils_websocket.wsManager.off("craft_success", handler);
          utils_websocket.wsManager.off("craft_failed", handler);
        }
        if (type === "start_battle_from_encounter") {
          utils_websocket.wsManager.off("start_battle_from_encounter_success", handler);
          utils_websocket.wsManager.off("start_battle_from_encounter_failed", handler);
        }
        if (type === "escape_battle") {
          utils_websocket.wsManager.off("escape_battle_result", handler);
        }
        if (type === "get_ranking") {
          utils_websocket.wsManager.off("success", handler);
          utils_websocket.wsManager.off("error", handler);
        }
        if (type === "gather_action") {
          utils_websocket.wsManager.off("gathering_result", handler);
        }
      };
      const handler = (resp) => {
        if (isResolved)
          return;
        isResolved = true;
        cleanup();
        if (type === "get_player_data" && msg.type === "get_equipment_data") {
          if (resp && resp.equipment) {
            resolve({
              type: "equipment_data",
              data: resp.equipment
            });
            return;
          }
        }
        let responseType = type + (resp.success !== false ? "_success" : "_failed");
        if (type === "get_inventory_data" && resp.inventory !== void 0) {
          responseType = "inventory_data";
        }
        if (type === "get_skills_data" && resp.skills !== void 0) {
          responseType = "skills_data";
        }
        if (type === "equip_item") {
          if (resp.success !== false) {
            responseType = "equip_success";
          } else {
            responseType = "equip_failed";
          }
        }
        if (type === "get_player_data" && resp.type === "player_data") {
          responseType = "player_data";
        }
        if (type === "market_action") {
          responseType = resp.type || responseType;
          resolve({
            type: responseType,
            data: resp.data || resp
          });
          return;
        }
        if (type === "crafting_action" && resp.type === "get_craftable_success") {
          responseType = "get_craftable_success";
        }
        if (type === "crafting_action" && resp.type === "craft_success") {
          responseType = "craft_success";
        }
        if (type === "crafting_action" && resp.type === "craft_failed") {
          responseType = "craft_failed";
        }
        if (type === "get_ranking") {
          if (resp.type === "success") {
            responseType = "get_ranking_success";
          } else if (resp.type === "error") {
            responseType = "get_ranking_failed";
          }
          resolve({
            type: responseType,
            data: resp.data || resp
          });
          return;
        }
        if (type === "redeem_code") {
          if (resp.type === "success") {
            responseType = "redeem_code_success";
          } else if (resp.type === "error") {
            responseType = "redeem_code_failed";
          }
          resolve({
            type: responseType,
            data: resp.data || resp
          });
          return;
        }
        if (type === "npc_function") {
          resolve({
            type: resp.type || responseType,
            data: resp.data || resp
          });
          return;
        }
        resolve({
          type: responseType,
          data: resp
        });
      };
      const errorHandler = (error) => {
        if (msg.type === "get_equipment_data") {
          resolve({
            type: "equipment_data",
            data: {}
          });
          return;
        }
        resolve({
          type: type + "_failed",
          data: { message: error.message || "请求失败" }
        });
      };
      utils_websocket.wsManager.on(type + "_success", handler);
      utils_websocket.wsManager.on(type + "_failed", handler);
      utils_websocket.wsManager.on(type + "_error", handler);
      utils_websocket.wsManager.on("error", errorHandler);
      if (type === "crafting_action") {
        utils_websocket.wsManager.on("get_craftable_success", handler);
        utils_websocket.wsManager.on("get_craftable_failed", handler);
        utils_websocket.wsManager.on("craft_success", handler);
        utils_websocket.wsManager.on("craft_failed", handler);
      }
      if (type === "start_battle_from_encounter") {
        utils_websocket.wsManager.on("start_battle_from_encounter_success", handler);
        utils_websocket.wsManager.on("start_battle_from_encounter_failed", handler);
      }
      if (type === "escape_battle") {
        utils_websocket.wsManager.on("escape_battle_result", handler);
      }
      if (type === "get_player_data") {
        utils_websocket.wsManager.on("player_data", handler);
      }
      if (type === "get_player_data") {
        utils_websocket.wsManager.on("get_player_data_success", handler);
        utils_websocket.wsManager.on("get_player_data_failed", handler);
        utils_websocket.wsManager.on("get_player_data_error", handler);
      }
      if (type === "market_action") {
        utils_websocket.wsManager.on("market_list", handler);
        utils_websocket.wsManager.on("success", handler);
        utils_websocket.wsManager.on("error", handler);
      }
      if (type === "get_map_npcs") {
        utils_websocket.wsManager.on("map_npcs", handler);
      }
      if (type === "npc_function") {
        utils_websocket.wsManager.on("npc_talk", handler);
        utils_websocket.wsManager.on("shop_items", handler);
        utils_websocket.wsManager.on("buy_success", handler);
        utils_websocket.wsManager.on("sell_success", handler);
        utils_websocket.wsManager.on("transport_destinations", handler);
        utils_websocket.wsManager.on("transport_success", handler);
        utils_websocket.wsManager.on("heal_success", handler);
        utils_websocket.wsManager.on("info_services", handler);
        utils_websocket.wsManager.on("info_success", handler);
        utils_websocket.wsManager.on("error", handler);
      }
      if (type === "get_bonus_summary") {
        utils_websocket.wsManager.on("bonus_summary", handler);
      }
      if (type === "get_inventory_data") {
        utils_websocket.wsManager.on("inventory_data", handler);
      }
      if (type === "gather_action") {
        utils_websocket.wsManager.on("gathering_result", handler);
      }
      if (type === "get_skills_data") {
        utils_websocket.wsManager.on("skills_data", handler);
      }
      if (type === "get_martial_configs") {
        utils_websocket.wsManager.on("martial_configs", handler);
      }
      if (type === "equip_item") {
        utils_websocket.wsManager.on("equip_success", handler);
        utils_websocket.wsManager.on("equip_failed", handler);
      }
      if (type === "get_ranking") {
        utils_websocket.wsManager.on("success", handler);
        utils_websocket.wsManager.on("error", handler);
      }
      if (type === "redeem_code") {
        utils_websocket.wsManager.on("success", handler);
        utils_websocket.wsManager.on("error", handler);
      }
      if (!utils_websocket.wsManager.isConnected) {
        utils_websocket.wsManager.connect().then(() => {
          utils_websocket.wsManager.sendMessage(type, data);
        }).catch((err) => {
          resolve({
            type: type + "_failed",
            data: { message: "连接服务器失败，请检查网络" }
          });
        });
      } else {
        utils_websocket.wsManager.sendMessage(type, data);
      }
    });
  }
};
exports.gameUtils = gameUtils;
