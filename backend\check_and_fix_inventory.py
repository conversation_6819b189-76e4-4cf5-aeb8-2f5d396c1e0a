import sqlite3
import json
import os

DB_PATH = os.path.join(os.path.dirname(__file__), 'game.db')


def check_and_fix_all_inventories_and_id():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT id, user_id, data FROM players")
    rows = cursor.fetchall()
    fixed_count = 0
    id_fixed_count = 0
    for player_id, user_id, player_data_json in rows:
        try:
            player_data = json.loads(player_data_json)
        except Exception as e:
            print(f"[ERROR] 玩家ID {player_id} 数据解析失败: {e}")
            continue
        # 修复 inventory
        inventory = player_data.get('inventory', None)
        if inventory is None or not isinstance(inventory, list):
            print(f"[修复] 玩家ID {player_id} inventory 字段异常，已初始化为空数组")
            player_data['inventory'] = []
            fixed_count += 1
        # 修复 id 字段
        if 'id' not in player_data or str(player_data['id']) != str(user_id):
            print(f"[修复] 玩家ID {player_id} 缺少或错误的'id'字段，已补全为 user_id={user_id}")
            player_data['id'] = user_id
            id_fixed_count += 1
        # 保存修复后的数据
        cursor.execute(
            "UPDATE players SET data = ? WHERE id = ?",
            (json.dumps(player_data, ensure_ascii=False), player_id)
        )
    conn.commit()
    conn.close()
    print(f"已完成检查，修复背包玩家数：{fixed_count}，修复id字段玩家数：{id_fixed_count}")

def clean_equipped_martial_field(db_path='game.db'):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT id, data FROM players')
    rows = cursor.fetchall()
    updated = 0
    for user_id, data in rows:
        try:
            player = json.loads(data)
            if 'equipped_martial' in player:
                del player['equipped_martial']
                new_data = json.dumps(player, ensure_ascii=False)
                cursor.execute('UPDATE players SET data=? WHERE id=?', (new_data, user_id))
                updated += 1
        except Exception as e:
            print(f'[ERROR] 处理玩家{user_id}数据失败: {e}')
    conn.commit()
    print(f'[INFO] 已清理 equipped_martial 字段的玩家数: {updated}')
    conn.close()

if __name__ == '__main__':
    check_and_fix_all_inventories_and_id()
    clean_equipped_martial_field() 