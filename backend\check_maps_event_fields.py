import json
from probability_config import EVENT_NAME_TO_TYPE

with open('backend/maps.json', encoding='utf-8') as f:
    maps = json.load(f)

print('支持的事件名:', list(EVENT_NAME_TO_TYPE.keys()))
print('-----------------------------')

all_ok = True
for m in maps:
    map_id = m.get('id')
    map_name = m.get('名称', m.get('name', map_id))
    event_prob = m.get('事件概率', {})
    illegal = []
    for k in event_prob.keys():
        if k not in EVENT_NAME_TO_TYPE and k != '空事件':
            illegal.append(k)
    if illegal:
        all_ok = False
        print(f'地图【{map_name}】(id={map_id}) 存在非法事件名: {illegal}')
        for wrong in illegal:
            # 提示最接近的合法事件名
            import difflib
            candidates = difflib.get_close_matches(wrong, EVENT_NAME_TO_TYPE.keys(), n=2)
            print(f'  建议替换为: {candidates or "请查阅文档"}')
if all_ok:
    print('所有地图事件概率字段均合法！') 