#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志清理工具
用于清理过大的日志文件，保留最近的日志记录
"""

import os
import shutil
from datetime import datetime

def clean_log_file(log_path, max_lines=1000):
    """
    清理日志文件，只保留最后的max_lines行
    
    Args:
        log_path: 日志文件路径
        max_lines: 保留的最大行数
    """
    if not os.path.exists(log_path):
        print(f"日志文件不存在: {log_path}")
        return
    
    # 获取文件大小
    file_size = os.path.getsize(log_path)
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"当前日志文件大小: {file_size_mb:.2f} MB")
    
    # 如果文件小于1MB，不需要清理
    if file_size_mb < 1:
        print("日志文件较小，无需清理")
        return
    
    # 备份原文件
    backup_path = f"{log_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(log_path, backup_path)
    print(f"已备份原日志文件到: {backup_path}")
    
    # 读取文件的最后max_lines行
    with open(log_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    total_lines = len(lines)
    print(f"原文件总行数: {total_lines}")
    
    if total_lines <= max_lines:
        print("文件行数未超过限制，无需清理")
        return
    
    # 保留最后的max_lines行
    lines_to_keep = lines[-max_lines:]
    
    # 写入清理后的内容
    with open(log_path, 'w', encoding='utf-8') as f:
        f.write(f"# 日志已于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 清理，保留最后 {max_lines} 行\n")
        f.writelines(lines_to_keep)
    
    # 获取清理后的文件大小
    new_file_size = os.path.getsize(log_path)
    new_file_size_mb = new_file_size / (1024 * 1024)
    
    print(f"清理完成!")
    print(f"删除行数: {total_lines - max_lines}")
    print(f"保留行数: {max_lines}")
    print(f"清理后文件大小: {new_file_size_mb:.2f} MB")
    print(f"节省空间: {file_size_mb - new_file_size_mb:.2f} MB")

def main():
    """主函数"""
    print("=" * 50)
    print("日志清理工具")
    print("=" * 50)
    
    # 清理游戏服务器日志
    log_file = "game_server.log"
    if os.path.exists(log_file):
        print(f"\n正在清理: {log_file}")
        clean_log_file(log_file, max_lines=1000)
    else:
        print(f"\n日志文件不存在: {log_file}")
    
    print("\n日志清理完成!")

if __name__ == "__main__":
    main()
