# TabBar图标说明

## 当前状态

✅ **已实现自定义TabBar**
- 使用自定义tabBar组件，无需依赖图标文件
- "闯"按钮已突出显示，使用红色渐变背景
- 所有按钮都有动画效果和选中状态

## 自定义TabBar特点

### 视觉效果
- **整体背景**：紫色渐变背景 (`#667eea` 到 `#764ba2`)
- **普通按钮**：白色半透明圆形背景
- **"闯"按钮**：红色渐变背景 (`#ff6b6b` 到 `#ee5a24`)，更大尺寸
- **选中状态**：按钮放大，背景更亮

### 布局设计
- **5个按钮**：背包、武功、闯、商店、门派
- **"闯"按钮居中**：突出显示，尺寸比其他按钮大20%
- **响应式设计**：适配不同屏幕尺寸
- **安全区域**：支持iPhone等设备的底部安全区域

### 交互效果
- **点击动画**：按钮点击时有缩放效果
- **选中状态**：当前页面对应的按钮高亮
- **平滑过渡**：所有状态变化都有0.3秒的过渡动画

## 技术实现

### 文件结构
```
custom-tab-bar/
└── index.vue          # 自定义tabBar组件
```

### 关键特性
- **Vue 3组件**：使用Composition API
- **响应式数据**：自动更新选中状态
- **事件处理**：支持页面切换
- **样式隔离**：使用scoped样式

## 使用说明

### 开发者
1. 自定义tabBar已自动启用
2. 每个页面都会自动设置对应的选中状态
3. 无需手动管理图标文件

### 用户
1. 底部导航栏自动显示
2. "闯"按钮明显突出，易于识别
3. 点击按钮即可切换页面

## 后续优化建议

### 可选的图标支持
如果需要添加图标，可以：
1. 准备PNG格式的图标文件（32x32px）
2. 在`custom-tab-bar/index.vue`中添加图标显示
3. 图标文件放在`static/tabbar/`目录下

### 图标文件命名建议
```
static/tabbar/
├── backpack.png
├── backpack-active.png
├── skills.png
├── skills-active.png
├── adventure.png
├── adventure-active.png
├── shop.png
├── shop-active.png
├── guild.png
└── guild-active.png
```

## 注意事项

1. **微信小程序限制**：只支持PNG/JPG/JPEG格式的图标
2. **图标尺寸**：建议32x32像素，确保清晰度
3. **文件大小**：单个图标文件不超过10KB
4. **兼容性**：自定义tabBar在所有平台都有效

---

**当前状态：✅ 自定义TabBar已完美实现，无需额外配置** 