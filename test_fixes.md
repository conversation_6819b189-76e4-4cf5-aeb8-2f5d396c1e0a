# 修复验证清单

## 问题1：购买物品页面未正确显示玩家持有的银两

### 修复内容：
1. **updateData方法优化**：
   - 修改了银两获取逻辑：`this.money = this.player.money || gameState.money || 0`
   - 优先从player对象获取银两，确保数据一致性

2. **后端价格一致性修复**：
   - 修复了npc_system中_buy_item方法的价格计算
   - 确保购买时使用的价格与显示的价格一致
   - 使用ItemSystem获取标准价格

3. **前端响应处理**：
   - 确认前端正确处理`buy_success`响应类型
   - 购买成功后调用`updateData()`刷新数据

### 验证步骤：
1. 打开购买弹窗，检查"当前银两"是否正确显示
2. 尝试购买物品，检查是否能正确扣除银两
3. 购买后检查银两是否正确更新

## 问题2：在线侠客未显示玩家自己

### 修复内容：
1. **条件判断修复**：
   - 将`mapPlayers.length <= 1`改为`mapPlayers.length === 0`
   - 修改提示文字为"当前地图暂无侠客"

2. **异常处理优化**：
   - 在fetchMapPlayers的catch块中，即使获取失败也显示自己
   - 添加了player数据存在性检查

3. **数据构建保护**：
   - 在构建selfPlayer对象前检查this.player是否存在
   - 确保在各种情况下都能正确显示自己

### 验证步骤：
1. 打开侠客列表，检查自己是否显示在第一位
2. 检查自己是否有特殊的渐变背景和"（我）"标签
3. 确认点击自己不会触发交互菜单
4. 在网络异常情况下，检查是否仍能显示自己

## 技术细节

### 修改的文件：
- `pages/index/index.vue`：前端主要逻辑
- `backend/npc_system.py`：后端NPC商店逻辑

### 关键修改点：
1. **银两显示**：优先从player.money获取
2. **侠客列表**：确保自己始终显示在第一位
3. **价格一致性**：统一使用ItemSystem的价格
4. **异常处理**：增强了错误情况下的用户体验

### 预期效果：
- 购买弹窗正确显示当前银两
- 购买功能正常工作
- 侠客列表始终显示自己在第一位
- 界面更加稳定和用户友好
