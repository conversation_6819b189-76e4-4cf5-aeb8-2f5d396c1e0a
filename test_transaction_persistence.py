#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易记录持久化功能
"""

import asyncio
import sqlite3
import sys
import json
from datetime import datetime

sys.path.append('backend')

from server import GameServer
from market_system import MarketSystem

class TransactionPersistenceTester:
    def __init__(self):
        self.server = None
        
    async def setup(self):
        """初始化测试环境"""
        print("=== 初始化测试环境 ===")
        self.server = GameServer()
        await self.server.init_db_task
        print("✓ 服务器初始化完成")
        
    async def test_transaction_logging(self):
        """测试交易记录功能"""
        print("\n=== 测试交易记录功能 ===")
        
        try:
            # 创建模拟websocket
            class MockWebSocket:
                def __init__(self, user_id):
                    self.user_id = user_id
                    
            # 创建买家和卖家
            buyer_id = 1001
            seller_id = 1002
            
            buyer_player = {
                'name': '买家测试',
                'money': 5000,
                'inventory': []
            }
            
            seller_player = {
                'name': '卖家测试',
                'money': 1000,
                'inventory': [
                    {
                        'id': 'chitie_jian',
                        'name': '赤铁剑',
                        'quality': 'common',
                        'quantity': 1,
                        'unique_id': 'test_transaction_001'
                    }
                ]
            }
            
            # 保存玩家数据
            self.server.player_data[str(buyer_id)] = buyer_player
            self.server.player_data[str(seller_id)] = seller_player
            
            # 卖家上架物品
            seller_ws = MockWebSocket(seller_id)
            list_result = await self.server.market_system.handle_market_action({
                'action': 'list',
                'item_id': 'chitie_jian',
                'price': 100,
                'quantity': 1
            }, seller_ws)
            
            if list_result['type'] == 'success':
                print("✓ 卖家成功上架物品")
                
                # 获取订单ID
                orders = self.server.market_system.market_orders
                if orders:
                    order_id = orders[-1]['id']  # 最新的订单
                    
                    # 买家购买物品
                    buyer_ws = MockWebSocket(buyer_id)
                    buy_result = await self.server.market_system.handle_market_action({
                        'action': 'buy',
                        'order_id': order_id
                    }, buyer_ws)
                    
                    if buy_result['type'] == 'success':
                        print("✓ 买家成功购买物品")
                        
                        # 检查交易记录
                        await self.check_transaction_records(buyer_id, seller_id)
                        
                    else:
                        print(f"✗ 购买失败: {buy_result['data']['message']}")
                else:
                    print("✗ 没有找到上架的订单")
            else:
                print(f"✗ 上架失败: {list_result['data']['message']}")
                
        except Exception as e:
            print(f"✗ 交易记录测试失败: {e}")
            
    async def check_transaction_records(self, buyer_id, seller_id):
        """检查数据库中的交易记录"""
        print("\n=== 检查交易记录 ===")
        
        try:
            conn = sqlite3.connect('backend/game.db')
            cursor = conn.cursor()
            
            # 查询所有交易记录
            cursor.execute("SELECT * FROM transactions ORDER BY timestamp DESC LIMIT 10")
            records = cursor.fetchall()
            
            print(f"✓ 数据库中有 {len(records)} 条交易记录")
            
            for record in records:
                print(f"  记录ID: {record[0]}")
                print(f"  用户ID: {record[1]}")
                print(f"  交易类型: {record[2]}")
                print(f"  物品ID: {record[3]}")
                print(f"  物品名称: {record[4]}")
                print(f"  数量: {record[5]}")
                print(f"  价格: {record[6]}")
                print(f"  时间: {record[7]}")
                print("  ---")
                
            conn.close()
            
        except Exception as e:
            print(f"✗ 检查交易记录失败: {e}")
            
    async def test_market_persistence_after_restart(self):
        """测试市场数据在重启后的持久化"""
        print("\n=== 测试市场持久化 ===")
        
        try:
            # 记录当前订单数量
            original_count = len(self.server.market_system.market_orders)
            print(f"✓ 当前市场订单数量: {original_count}")
            
            # 创建新的市场系统实例（模拟重启）
            new_market_system = MarketSystem(self.server)
            
            # 检查订单是否正确加载
            loaded_count = len(new_market_system.market_orders)
            print(f"✓ 重新加载后订单数量: {loaded_count}")
            
            if loaded_count == original_count:
                print("✓ 市场订单持久化正常")
            else:
                print("✗ 市场订单持久化异常")
                
        except Exception as e:
            print(f"✗ 市场持久化测试失败: {e}")
            
    async def test_inventory_consistency(self):
        """测试背包数据一致性"""
        print("\n=== 测试背包数据一致性 ===")
        
        try:
            # 检查所有玩家的背包数据
            for user_id, player in self.server.player_data.items():
                inventory = player.get('inventory', [])
                print(f"✓ 玩家 {player.get('name', '未知')} (ID: {user_id}) 背包物品数量: {len(inventory)}")
                
                # 检查物品数据完整性
                for item in inventory:
                    if 'id' not in item:
                        print(f"  ✗ 发现无ID物品: {item}")
                    elif 'quantity' not in item:
                        print(f"  ✗ 发现无数量物品: {item}")
                    else:
                        print(f"  ✓ 物品 {item['id']} 数据完整")
                        
        except Exception as e:
            print(f"✗ 背包一致性检查失败: {e}")
            
    async def run_all_tests(self):
        """运行所有测试"""
        print("开始交易持久化测试...")
        
        await self.setup()
        await self.test_transaction_logging()
        await self.test_market_persistence_after_restart()
        await self.test_inventory_consistency()
        
        print("\n=== 测试完成 ===")

async def main():
    tester = TransactionPersistenceTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
