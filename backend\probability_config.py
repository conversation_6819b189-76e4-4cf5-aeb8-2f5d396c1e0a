#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
概率配置文件
用于管理和调整事件概率
"""

import os
import json

# 全局默认概率
EMPTY_EVENT_PROBABILITY = 0.60  # 普通事件概率 60%
EVENT_PROBABILITIES = {
    1: 0.08,   # 好运事件: 8%
    2: 0.20,   # 遭遇NPC: 20%
    3: 0.20,   # 采集事件: 20%
    5: 0.002,  # 奇遇事件: 0.2%
    6: 0.08,   # 恩怨事件: 8%
    7: 0.08,   # 组队事件: 8%
    8: 0.08,   # 商队事件: 8%
    9: 0.08,   # 江湖传闻: 8%
    10: 0.12,  # 天气事件: 12%
    11: 0.000002,  # 神秘事件: 0.0002%
    12: 0.02   # 节日事件: 2%
}

# 事件名到编号的映射
EVENT_NAME_TO_TYPE = {
    '好运事件': 1,
    '好运': 1,
    '遭遇NPC': 2,
    'NPC': 2,
    '采集事件': 3,
    '采集': 3,
    '普通事件': 4,
    '空事件': 4,  # 兼容旧名称
    '空': 4,
    '奇遇事件': 5,
    '奇遇': 5,
    '恩怨事件': 6,
    '恩怨': 6,
    '组队事件': 7,
    '组队': 7,
    '商队事件': 8,
    '商队': 8,
    '江湖传闻': 9,
    '传闻': 9,
    '天气事件': 10,
    '天气': 10,
    '神秘事件': 11,
    '神秘': 11,
    '节日事件': 12,
    '节日': 12
}

def load_map_probability_config():
    """
    从maps.json动态加载每个地图的事件概率配置，返回{map_id: {empty: 概率, events: {...}}}
    """
    base_dir = os.path.dirname(__file__)
    maps_path = os.path.join(base_dir, 'maps.json')
    if not os.path.exists(maps_path):
        return {}
    with open(maps_path, 'r', encoding='utf-8') as f:
        maps_list = json.load(f)
    result = {}
    for m in maps_list:
        map_id = m.get('id')
        event_prob = m.get('事件概率', {})
        empty = 0.0
        events = {}
        for k, v in event_prob.items():
            if k == '空事件' or k == '普通事件':
                empty = float(v) / 100 if v > 1 else float(v)
            elif k in EVENT_NAME_TO_TYPE:
                events[EVENT_NAME_TO_TYPE[k]] = float(v) / 100 if v > 1 else float(v)
        if map_id:
            result[map_id] = {'empty': empty, 'events': events}
    return result

# 概率验证
def validate_probabilities():
    """验证概率配置的有效性"""
    errors = []
    # 全局
    if not 0.0 <= EMPTY_EVENT_PROBABILITY <= 1.0:
        errors.append(f"空事件概率 {EMPTY_EVENT_PROBABILITY} 必须在0.0-1.0之间")
    total_prob = sum(EVENT_PROBABILITIES.values())
    if total_prob > 1.0:
        errors.append(f"事件概率总和 {total_prob} 不能超过1.0")
    for event_type, prob in EVENT_PROBABILITIES.items():
        if not 0.0 <= prob <= 1.0:
            errors.append(f"事件{event_type}概率 {prob} 必须在0.0-1.0之间")
    # 地图专属
    for map_id, conf in load_map_probability_config().items():
        empty = conf.get('empty', 0.0)
        if not 0.0 <= empty <= 1.0:
            errors.append(f"地图{map_id}空事件概率 {empty} 必须在0.0-1.0之间")
        events = conf.get('events', {})
        total = sum(events.values())
        if total > 1.0:
            errors.append(f"地图{map_id}事件概率总和 {total} 不能超过1.0")
        for event_type, prob in events.items():
            if not 0.0 <= prob <= 1.0:
                errors.append(f"地图{map_id}事件{event_type}概率 {prob} 必须在0.0-1.0之间")
    if errors:
        print("概率配置错误:")
        for error in errors:
            print(f"  ❌ {error}")
        return False
    else:
        print("✅ 概率配置验证通过")
        print(f"  全局空事件概率: {EMPTY_EVENT_PROBABILITY:.1%}")
        print(f"  全局事件概率总和: {total_prob:.1%}")
        print(f"  地图专属概率配置: {list(load_map_probability_config().keys())}")
        return True

# 概率说明
PROBABILITY_DESCRIPTION = """
概率系统说明：

第一轮概率判定：
- 空事件概率: {empty_prob:.1%}
- 其他事件概率: {other_prob:.1%}

第二轮概率判定（当命中其他事件时）：
- 好运事件: {good_fortune:.1%}
- 遭遇NPC: {npc:.1%}
- 采集事件: {gathering:.1%}
- 奇遇事件: {adventure:.3%}
- 恩怨事件: {enmity:.1%}
- 组队事件: {team:.1%}
- 商队事件: {caravan:.1%}
- 江湖传闻: {rumor:.1%}
- 天气事件: {weather:.1%}
- 神秘事件: {mystery:.4%}
- 节日事件: {festival:.1%}

实际概率计算：
- 空事件实际概率 = 第一轮命中空事件 + 第二轮未命中任何事件
- 其他事件实际概率 = (1 - 空事件概率) × 该事件在第二轮的概率
""".format(
    empty_prob=EMPTY_EVENT_PROBABILITY,
    other_prob=1.0 - EMPTY_EVENT_PROBABILITY,
    good_fortune=EVENT_PROBABILITIES[1],
    npc=EVENT_PROBABILITIES[2],
    gathering=EVENT_PROBABILITIES[3],
    adventure=EVENT_PROBABILITIES[5],
    enmity=EVENT_PROBABILITIES[6],
    team=EVENT_PROBABILITIES[7],
    caravan=EVENT_PROBABILITIES[8],
    rumor=EVENT_PROBABILITIES[9],
    weather=EVENT_PROBABILITIES[10],
    mystery=EVENT_PROBABILITIES[11],
    festival=EVENT_PROBABILITIES[12]
)

if __name__ == '__main__':
    print("=== 概率配置验证 ===")
    validate_probabilities()
    print("\n" + PROBABILITY_DESCRIPTION) 