# 属性自动恢复系统
# 提供体力、气血、内力等定时自动恢复功能
import asyncio
import logging

logger = logging.getLogger(__name__)

async def auto_attribute_regeneration(server, interval=10):
    """
    定时为所有在线玩家自动恢复属性。
    :param server: GameServer 实例，需有 player_data 字典
    :param interval: 恢复间隔（秒）
    """
    logger.warning(f"自动属性恢复系统启动，间隔: {interval}秒")
    while True:
        try:
            recovered_count = 0
            for user_id in list(server.player_data.keys()):
                # 每次都从数据库加载最新数据，避免属性回档
                try:
                    latest_player = await server.load_player_data(user_id)
                    if latest_player:
                        player = latest_player
                        server.player_data[user_id] = player
                    else:
                        player = server.player_data[user_id]
                except Exception as e:
                    logger.error(f"自动恢复时加载玩家{user_id}数据失败: {e}")
                    player = server.player_data[user_id]
                # 新增：战斗/疗伤/打坐等状态不自动恢复属性
                player_status = player.get('status', 'normal')
                if player_status in ['battle', 'fighting', 'healing', 'meditating']:
                    continue
                old_hp = player.get('hp', 0)
                old_mp = player.get('mp', 0)
                old_energy = player.get('energy', 0)
                old_spirit = player.get('spirit', 0)

                # 恢复属性
                recover_player_attributes(player)

                # 检查是否有变化
                new_hp = player.get('hp', 0)
                new_mp = player.get('mp', 0)
                new_energy = player.get('energy', 0)
                new_spirit = player.get('spirit', 0)

                if new_hp != old_hp or new_mp != old_mp or new_energy != old_energy or new_spirit != old_spirit:
                    recovered_count += 1
                    # 只在有显著变化时记录日志
                    if (new_hp - old_hp) > 5 or (new_mp - old_mp) > 5 or (new_energy - old_energy) > 5 or (new_spirit - old_spirit) > 5:
                        logger.warning(f"玩家 {user_id} 属性大幅恢复: HP {old_hp}->{new_hp}, MP {old_mp}->{new_mp}, 体力 {old_energy}->{new_energy}, 精力 {old_spirit}->{new_spirit}")

                    # 推送更新到前端
                    await push_player_data_update(server, user_id, player)
                    # 新增：保存到数据库，防止下次又被覆盖
                    await server.save_player_data(user_id, player)

            # 只在有恢复时记录日志，且降低日志级别
            if recovered_count > 0:
                logger.warning(f"本轮自动恢复完成，共恢复 {recovered_count} 个玩家")

        except Exception as e:
            logger.error(f"属性自动恢复异常: {e}")
        await asyncio.sleep(interval)

async def push_player_data_update(server, user_id, player):
    """
    推送玩家数据更新到前端
    """
    try:
        # 查找玩家的websocket连接
        for websocket in server.clients.values():
            if hasattr(websocket, 'user_id') and str(websocket.user_id) == str(user_id):
                import json
                await websocket.send(json.dumps({
                    'type': 'player_data',
                    'data': player
                }, ensure_ascii=False))
                logger.debug(f"已推送玩家数据更新到用户 {user_id}")
                break
    except Exception as e:
        logger.error(f"推送玩家数据更新失败: {e}")

def recover_player_attributes(player, base_hp=2, base_mp=1, base_energy=1, base_spirit=1):
    """
    根据天赋根骨动态恢复单个玩家的气血、内力、体力、精力等属性。
    :param player: 玩家对象
    :param base_hp: 基础每次恢复气血
    :param base_mp: 基础每次恢复内力
    :param base_energy: 基础每次恢复体力
    :param base_spirit: 基础每次恢复精力
    """
    talent = player.get('talent', {})
    constitution = talent.get('根骨', 15)
    # 每多1点根骨，回复速度提升2%
    constitution_bonus = max(0, constitution - 15) * 0.02
    
    hp_amount = int(base_hp * (1 + constitution_bonus))
    mp_amount = int(base_mp * (1 + constitution_bonus))
    energy_amount = int(base_energy * (1 + constitution_bonus))
    spirit_amount = int(base_spirit * (1 + constitution_bonus))
    
    # 恢复气血
    if 'hp' in player and 'max_hp' in player:
        if player['hp'] < player['max_hp']:
            player['hp'] = min(player['hp'] + hp_amount, player['max_hp'])
    
    # 恢复内力
    if 'mp' in player and 'max_mp' in player:
        if player['mp'] < player['max_mp']:
            player['mp'] = min(player['mp'] + mp_amount, player['max_mp'])
    
    # 恢复体力
    if 'energy' in player and 'max_energy' in player:
        if player['energy'] < player['max_energy']:
            player['energy'] = min(player['energy'] + energy_amount, player['max_energy'])
    
    # 恢复精力
    if 'spirit' in player and 'max_spirit' in player:
        # 确保当前精力值不超过最大精力值
        if player['spirit'] > player['max_spirit']:
            player['spirit'] = player['max_spirit']
        elif player['spirit'] < player['max_spirit']:
            player['spirit'] = min(player['spirit'] + spirit_amount, player['max_spirit']) 