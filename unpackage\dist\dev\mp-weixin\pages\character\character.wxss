
.character-compact.data-v-3387d3fa { 
	padding: 16rpx; 
	background: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%); 
	min-height: 100vh; 
	font-size: 26rpx;
}

/* 头部区域 */
.header-section.data-v-3387d3fa { 
	display: flex; 
	align-items: center; 
	gap: 16rpx; 
	margin-bottom: 16rpx; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.avatar-section.data-v-3387d3fa { 
	position: relative;
}
.avatar.data-v-3387d3fa { 
	width: 80rpx; 
	height: 80rpx; 
	border-radius: 50%; 
	border: 3rpx solid #667eea; 
	box-shadow: 0 4rpx 8rpx rgba(102,126,234,0.3);
}
.info-section.data-v-3387d3fa { 
	flex: 1;
}
.name-row.data-v-3387d3fa { 
	display: flex; 
	align-items: center;
	gap: 12rpx; 
	margin-bottom: 8rpx; 
	flex-wrap: wrap;
}
.name.data-v-3387d3fa { 
	font-weight: bold; 
	font-size: 32rpx; 
	color: #333;
}
.gender.data-v-3387d3fa { 
	font-size: 22rpx; 
	color: #888; 
	background: linear-gradient(135deg, #f0f0f0, #e0e0e0); 
	padding: 4rpx 8rpx; 
	border-radius: 8rpx;
	border: 1rpx solid #ddd;
}
.experience.data-v-3387d3fa { 
	color: #4CAF50; 
	font-weight: bold;
	font-size: 24rpx;
	background: rgba(76,175,80,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}
.fortune.data-v-3387d3fa { 
	color: #9C27B0; 
	font-weight: bold;
	background: rgba(156,39,176,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}
.stats-row.data-v-3387d3fa { 
	display: flex; 
	gap: 12rpx; 
	font-size: 24rpx; 
	flex-wrap: wrap;
}
.money.data-v-3387d3fa { 
	color: #ff9800; 
	font-weight: bold;
	background: rgba(255,152,0,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}
.skill-points.data-v-3387d3fa { 
	color: #9C27B0; 
	font-weight: bold;
	background: rgba(156,39,176,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}
.realm.data-v-3387d3fa { 
	color: #2196F3; 
	font-weight: bold;
	background: rgba(33,150,243,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

/* 属性网格 */
.attributes-grid.data-v-3387d3fa { 
	display: grid; 
	grid-template-columns: repeat(4, 1fr); 
	gap: 12rpx; 
	margin: 16rpx 0; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.attributes-grid .section-title.data-v-3387d3fa {
	grid-column: 1 / -1;
	font-size: 32rpx; 
	font-weight: bold; 
	color: #333; 
	margin-bottom: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	padding: 8rpx 12rpx;
	border-radius: 8rpx;
	text-align: center;
	justify-content: center;
}
.attr-item.data-v-3387d3fa { 
	display: flex; 
	flex-direction: column; 
	align-items: center; 
	padding: 12rpx 8rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 12rpx;
	border: 1rpx solid #dee2e6;
	transition: all 0.3s ease;
}
.attr-item.data-v-3387d3fa:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}
.attr-label.data-v-3387d3fa { 
	font-size: 22rpx; 
	color: #666; 
	margin-bottom: 6rpx;
	font-weight: 500;
}
.attr-value.data-v-3387d3fa { 
	font-size: 26rpx; 
	font-weight: bold; 
	color: #333;
}

/* 天赋区域 */
.talent-section.data-v-3387d3fa { 
	margin: 16rpx 0; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.section-title.data-v-3387d3fa { 
	font-size: 32rpx; 
	font-weight: bold; 
	color: #333; 
	margin-bottom: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}
.section-title.data-v-3387d3fa::before {
	content: "⭐";
	font-size: 32rpx;
}
.talent-list.data-v-3387d3fa {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	gap: 16rpx;
	overflow-x: auto;
}
.talent-item.data-v-3387d3fa {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8rpx 20rpx;
	background: #fffbe6;
	border-radius: 16rpx;
	border: 1rpx solid #ffeaa7;
	transition: all 0.3s ease;
}
.talent-item.data-v-3387d3fa:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}
.talent-label.data-v-3387d3fa { 
	font-size: 22rpx; 
	color: #856404; 
	margin-bottom: 6rpx;
	font-weight: 500;
}
.talent-value.data-v-3387d3fa { 
	font-size: 26rpx; 
	font-weight: bold; 
	color: #333;
}

/* 境界区域 */
.realm-section.data-v-3387d3fa { 
	margin: 16rpx 0; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.realm-header.data-v-3387d3fa { 
	display: flex; 
	justify-content: space-between; 
	align-items: center; 
	margin-bottom: 12rpx;
}
.realm-title.data-v-3387d3fa { 
	font-size: 28rpx; 
	font-weight: bold; 
	color: #333;
}
.realm-progress.data-v-3387d3fa { 
	font-size: 24rpx; 
	color: #666;
	background: rgba(0,0,0,0.05);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}
.progress-bar.data-v-3387d3fa { 
	width: 100%; 
	height: 12rpx; 
	background: #e9ecef; 
	border-radius: 6rpx; 
	margin: 8rpx 0; 
	overflow: hidden;
	box-shadow: inset 0 2rpx 4rpx rgba(0,0,0,0.1);
}
.progress-fill.data-v-3387d3fa { 
	height: 100%; 
	background: linear-gradient(90deg, #FFD700, #FFA500); 
	border-radius: 6rpx; 
	transition: width 0.3s ease;
	box-shadow: 0 2rpx 4rpx rgba(255,215,0,0.3);
}
.realm-bonus.data-v-3387d3fa { 
	font-size: 24rpx; 
	color: #ff9800; 
	font-style: italic;
	background: rgba(255,152,0,0.1);
	padding: 8rpx;
	border-radius: 8rpx;
	border-left: 4rpx solid #ff9800;
}

/* 装备区域 */
.equipment-section.data-v-3387d3fa { 
	margin: 16rpx 0; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.section-title.data-v-3387d3fa { 
	font-size: 32rpx; 
	font-weight: bold; 
	color: #333; 
	margin-bottom: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}
.section-title.data-v-3387d3fa::before {
	content: "⚔️";
	font-size: 32rpx;
}
.equipment-grid.data-v-3387d3fa { 
	display: grid; 
	grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));
	gap: 12rpx;
}
.equip-slot.data-v-3387d3fa { 
	min-width: 0;
	box-sizing: border-box;
	display: flex; 
	flex-direction: column; 
	align-items: center; 
	padding: 12rpx 8rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 12rpx;
	border: 2rpx solid #dee2e6;
	transition: all 0.3s ease;
	min-height: 80rpx;
	justify-content: center;
}
.equip-slot.data-v-3387d3fa:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
	border-color: #667eea;
}
.equip-icon.data-v-3387d3fa { 
	font-size: 32rpx; 
	color: #333; 
	margin-bottom: 6rpx;
}
.equip-name.data-v-3387d3fa { 
	font-size: 22rpx; 
	color: #333; 
	margin-top: 6rpx; 
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 100%;
	font-weight: 500;
	word-break: break-all;
}
.empty-text.data-v-3387d3fa { 
	font-size: 22rpx; 
	color: #ccc; 
	margin-top: 6rpx;
	font-style: italic;
}

/* 操作按钮 */
.action-section.data-v-3387d3fa { 
	display: flex; 
	gap: 16rpx; 
	margin-top: 16rpx;
}
.action-btn.data-v-3387d3fa { 
	flex: 1; 
	height: 56rpx; 
	border: none; 
	border-radius: 28rpx; 
	color: white; 
	font-size: 28rpx; 
	font-weight: bold; 
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}
.healing-btn.data-v-3387d3fa { 
	background: linear-gradient(135deg, #FF5722, #F44336);
}
.crafting-btn.data-v-3387d3fa { 
	background: linear-gradient(135deg, #2196F3, #1976D2);
}
.breakthrough-btn.data-v-3387d3fa { 
	background: linear-gradient(135deg, #9C27B0, #673AB7);
}
.backpack-btn.data-v-3387d3fa { 
	background: linear-gradient(135deg, #4CAF50, #388E3C);
}
.action-btn.data-v-3387d3fa:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}
.action-btn.data-v-3387d3fa:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 12rpx rgba(0,0,0,0.3);
}

/* 天赋详情弹窗样式 */
.talent-modal-mask.data-v-3387d3fa {
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}
.talent-modal-content.data-v-3387d3fa {
	background: white;
	border-radius: 20rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}
.talent-modal-header.data-v-3387d3fa {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}
.talent-modal-title.data-v-3387d3fa {
	font-size: 32rpx;
	font-weight: bold;
}
.talent-modal-close.data-v-3387d3fa {
	font-size: 40rpx;
	cursor: pointer;
	padding: 8rpx;
	border-radius: 50%;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.2);
}
.talent-modal-body.data-v-3387d3fa {
	padding: 24rpx;
	max-height: 60vh;
	overflow-y: auto;
}
.talent-current.data-v-3387d3fa {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
	padding: 16rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 12rpx;
	border-left: 6rpx solid #667eea;
}
.talent-current-label.data-v-3387d3fa {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}
.talent-current-value.data-v-3387d3fa {
	font-size: 32rpx;
	color: #667eea;
	font-weight: bold;
}
.talent-effects.data-v-3387d3fa {
	margin-top: 16rpx;
}
.talent-effects-title.data-v-3387d3fa {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}
.talent-effect-item.data-v-3387d3fa {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
	padding: 12rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	border-left: 4rpx solid #28a745;
}
.effect-label.data-v-3387d3fa {
	font-size: 26rpx;
	color: #666;
}
.effect-value.data-v-3387d3fa {
	font-size: 26rpx;
	color: #28a745;
	font-weight: bold;
}
.talent-modal-footer.data-v-3387d3fa {
	padding: 24rpx;
	border-top: 2rpx solid #eee;
	display: flex;
	justify-content: center;
}
.talent-modal-btn.data-v-3387d3fa {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 16rpx 48rpx;
	font-size: 28rpx;
	font-weight: bold;
	box-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
}
.talent-modal-btn.data-v-3387d3fa:active {
	transform: scale(0.95);
}

/* 天赋属性点击效果 */
.attr-item.data-v-3387d3fa {
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 8rpx;
	padding: 8rpx;
}
.attr-item.data-v-3387d3fa:hover {
	background: rgba(102, 126, 234, 0.1);
	transform: translateY(-2rpx);
}
.attr-item.data-v-3387d3fa:active {
	transform: scale(0.95);
}

/* 装备详情弹窗样式 */
.equipment-modal-mask.data-v-3387d3fa {
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}
.equipment-modal-content.data-v-3387d3fa {
	background: white;
	border-radius: 20rpx;
	width: 85%;
	max-width: 650rpx;
	max-height: 85vh;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}
.equipment-modal-header.data-v-3387d3fa {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	background: linear-gradient(135deg, #4CAF50, #388E3C);
	color: white;
}
.equipment-modal-title.data-v-3387d3fa {
	font-size: 32rpx;
	font-weight: bold;
}
.equipment-modal-close.data-v-3387d3fa {
	font-size: 40rpx;
	cursor: pointer;
	padding: 8rpx;
	border-radius: 50%;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.2);
}
.equipment-modal-body.data-v-3387d3fa {
	padding: 24rpx;
	max-height: 60vh;
	overflow-y: auto;
}
.equipment-info.data-v-3387d3fa {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.equipment-basic.data-v-3387d3fa {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 16rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 12rpx;
	border-left: 6rpx solid #4CAF50;
}
.equipment-icon.data-v-3387d3fa {
	font-size: 48rpx;
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: white;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.equipment-details.data-v-3387d3fa {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}
.equipment-type.data-v-3387d3fa,
.equipment-quality.data-v-3387d3fa,
.equipment-slot.data-v-3387d3fa {
	font-size: 26rpx;
	color: #666;
}
.equipment-stats.data-v-3387d3fa {
	margin-top: 16rpx;
}
.stats-title.data-v-3387d3fa {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}
.stat-item.data-v-3387d3fa {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
	padding: 12rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	border-left: 4rpx solid #2196F3;
}
.stat-label.data-v-3387d3fa {
	font-size: 26rpx;
	color: #666;
}
.stat-value.data-v-3387d3fa {
	font-size: 26rpx;
	color: #2196F3;
	font-weight: bold;
}

/* 装备属性效果样式 */
.equipment-effects.data-v-3387d3fa {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}
.no-effects.data-v-3387d3fa {
	padding: 16rpx;
	text-align: center;
}
.no-effects-text.data-v-3387d3fa {
	font-size: 24rpx;
	color: #999;
	font-style: italic;
}
.equipment-description.data-v-3387d3fa {
	margin-top: 16rpx;
}
.description-title.data-v-3387d3fa {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}
.description-text.data-v-3387d3fa {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	padding: 12rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
}
.equipment-modal-actions.data-v-3387d3fa {
	padding: 24rpx;
	border-top: 2rpx solid #eee;
	display: flex;
	gap: 16rpx;
}
.equipment-action-btn.data-v-3387d3fa {
	flex: 1;
	height: 56rpx;
	border: none;
	border-radius: 28rpx;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
}
.unequip-btn.data-v-3387d3fa {
	background: linear-gradient(135deg, #FF5722, #F44336);
}
.close-btn.data-v-3387d3fa {
	background: linear-gradient(135deg, #9E9E9E, #757575);
}
.equipment-action-btn.data-v-3387d3fa:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 装备槽点击效果 */
.equip-slot.data-v-3387d3fa {
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 8rpx;
}
.equip-slot.data-v-3387d3fa:hover {
	background: rgba(76, 175, 80, 0.1);
	transform: translateY(-2rpx);
}
.equip-slot.data-v-3387d3fa:active {
	transform: scale(0.95);
}
