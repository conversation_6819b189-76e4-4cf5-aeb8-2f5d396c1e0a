
.container.data-v-1c7c41d0 {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	background: #f5f7fa;
}
.header.data-v-1c7c41d0 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}
.energy-info.data-v-1c7c41d0 {
	display: flex;
	align-items: center;
}
.energy-label.data-v-1c7c41d0 {
	color: #fff;
	font-size: 28rpx;
	margin-right: 10rpx;
}
.energy-value.data-v-1c7c41d0 {
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
}
.skill-count.data-v-1c7c41d0 {
	background: rgba(255,255,255,0.2);
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
}
.count-text.data-v-1c7c41d0 {
	color: #fff;
	font-size: 24rpx;
}
.category-tabs.data-v-1c7c41d0 {
	display: flex;
	background: #fff;
	border-radius: 20rpx;
	padding: 10rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.tab-item.data-v-1c7c41d0 {
	flex: 1;
	text-align: center;
	padding: 20rpx 10rpx;
	border-radius: 15rpx;
	transition: all 0.3s;
}
.tab-item.active.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.tab-text.data-v-1c7c41d0 {
	font-size: 28rpx;
	color: #333;
}
.tab-item.active .tab-text.data-v-1c7c41d0 {
	color: #fff;
}
.filter-section.data-v-1c7c41d0 {
	background: #fff;
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.filter-row.data-v-1c7c41d0 {
	display: flex;
	gap: 20rpx;
}
.filter-item.data-v-1c7c41d0 {
	flex: 1;
	display: flex;
	align-items: center;
}
.filter-label.data-v-1c7c41d0 {
	font-size: 28rpx;
	color: #333;
	margin-right: 10rpx;
}
.picker-text.data-v-1c7c41d0 {
	font-size: 28rpx;
	color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	padding: 10rpx 20rpx;
	border-radius: 10rpx;
}
.skills-list.data-v-1c7c41d0 {
	height: calc(100vh - 400rpx);
}
.skill-group.data-v-1c7c41d0 {
	margin-bottom: 24px;
}
.group-header.data-v-1c7c41d0 {
	background: #fff;
	padding: 30rpx;
	border-radius: 20rpx 20rpx 0 0;
	border-bottom: 2rpx solid #f0f0f0;
}
.group-title.data-v-1c7c41d0 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0;
	line-height: 60rpx;
}
.group-desc.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #666;
}
.skill-item.data-v-1c7c41d0 {
	background: #fff;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.skill-item.data-v-1c7c41d0:last-child {
	border-bottom: none;
	border-radius: 0 0 20rpx 20rpx;
}
.skill-info.data-v-1c7c41d0 {
	flex: 1;
	margin-right: 20rpx;
}
.skill-header.data-v-1c7c41d0 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}
.skill-name.data-v-1c7c41d0 {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}
.skill-status-group.data-v-1c7c41d0 {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}
.skill-level-text.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	margin-bottom: 5rpx;
}
.skill-school.data-v-1c7c41d0 {
	font-size: 20rpx;
	color: #1890ff;
	background: rgba(24, 144, 255, 0.1);
	padding: 3rpx 10rpx;
	border-radius: 10rpx;
	margin-bottom: 5rpx;
}
.skill-equipped.data-v-1c7c41d0 {
	font-size: 20rpx;
	color: #52c41a;
	background: rgba(82, 196, 26, 0.1);
	padding: 3rpx 10rpx;
	border-radius: 10rpx;
}
.skill-desc.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 15rpx;
	line-height: 1.4;
}
.skill-progress.data-v-1c7c41d0 {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}
.progress-bg.data-v-1c7c41d0 {
	flex: 1;
	height: 20rpx;
	background: #f0f0f0;
	border-radius: 10rpx;
	margin-right: 15rpx;
	overflow: hidden;
}
.progress-fill.data-v-1c7c41d0 {
	height: 100%;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	border-radius: 10rpx;
	transition: width 0.3s;
}
.progress-text.data-v-1c7c41d0 {
	font-size: 22rpx;
	color: #666;
	min-width: 80rpx;
}
.skill-status.data-v-1c7c41d0 {
	margin-top: 10rpx;
}
.skill-unlock.data-v-1c7c41d0 {
	font-size: 22rpx;
	color: #52c41a;
}
.skill-locked.data-v-1c7c41d0 {
	font-size: 22rpx;
	color: #ff4d4f;
}
.skill-actions.data-v-1c7c41d0 {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}
.action-btn.data-v-1c7c41d0 {
	padding: 15rpx 30rpx;
	border-radius: 15rpx;
	font-size: 24rpx;
	border: none;
}
.study-btn.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}
.study-btn.data-v-1c7c41d0:disabled {
	background: #ccc;
	color: #999;
}
.use-btn.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
	color: #fff;
}
.use-btn.unequip.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
}
.use-btn.data-v-1c7c41d0:disabled {
	background: #ccc;
	color: #999;
}
.moves-btn.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	color: #fff;
}
.moves-btn.data-v-1c7c41d0:disabled {
	background: #ccc;
	color: #999;
}
.empty-skills.data-v-1c7c41d0 {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

/* 弹窗样式 */
.modal-overlay.data-v-1c7c41d0 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}
.modal-content.data-v-1c7c41d0 {
	background: #fff;
	border-radius: 20rpx;
	width: 85%;
	max-width: 650rpx;
	max-height: 90vh;
	overflow: hidden;
	box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.15);
	display: flex;
	flex-direction: column;
}
.modal-header.data-v-1c7c41d0 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
	flex-shrink: 0;
	background: #fff;
}
.modal-title.data-v-1c7c41d0 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.modal-close.data-v-1c7c41d0 {
	font-size: 40rpx;
	color: #999;
	cursor: pointer;
}
.modal-body.data-v-1c7c41d0 {
	padding: 30rpx;
	flex: 1;
	overflow-y: auto;
	background: #fafafa;
	min-height: 0;
}
.detail-header.data-v-1c7c41d0 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}
.detail-name.data-v-1c7c41d0 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.detail-status.data-v-1c7c41d0 {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}
.detail-level.data-v-1c7c41d0 {
	font-size: 28rpx;
	color: #667eea;
	margin-bottom: 5rpx;
}
.detail-school.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #1890ff;
	background: rgba(24, 144, 255, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
	margin-bottom: 5rpx;
}
.detail-equipped.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #52c41a;
	background: rgba(82, 196, 26, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
}
.detail-desc.data-v-1c7c41d0 {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 30rpx;
}
.detail-progress.data-v-1c7c41d0 {
	margin-bottom: 30rpx;
}
.progress-title.data-v-1c7c41d0 {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}
.progress-bar.data-v-1c7c41d0 {
	display: flex;
	align-items: center;
}
.detail-effects.data-v-1c7c41d0 {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f6ffed;
	border-radius: 10rpx;
	border-left: 4rpx solid #52c41a;
}
.effects-title.data-v-1c7c41d0 {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}
.effects-text.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

/* 武功特效样式 */
.detail-special-effects.data-v-1c7c41d0 {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
	border-radius: 12rpx;
	border-left: 4rpx solid #1890ff;
	box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.1);
}
.special-effects-title.data-v-1c7c41d0 {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 12rpx;
	display: block;
}
.special-effects-text.data-v-1c7c41d0 {
	font-size: 26rpx;
	color: #1890ff;
	font-weight: bold;
	background: rgba(24, 144, 255, 0.1);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	display: inline-block;
}
.detail-category.data-v-1c7c41d0 {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f0f8ff;
	border-radius: 10rpx;
	border-left: 4rpx solid #1890ff;
}
.category-title.data-v-1c7c41d0 {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}
.category-text.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}
.detail-moves.data-v-1c7c41d0 {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #fff7e6;
	border-radius: 10rpx;
	border-left: 4rpx solid #faad14;
}
.moves-title.data-v-1c7c41d0 {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 15rpx;
	display: block;
}
.moves-list.data-v-1c7c41d0 {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}
.move-item.data-v-1c7c41d0 {
	background: #ffffff;
	border-radius: 12rpx;
	padding: 20rpx;
	border: 1rpx solid #e8e8e8;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
}
.move-item.move-unlocked.data-v-1c7c41d0 {
	border-left: 4rpx solid #52c41a;
	background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
}
.move-item.move-locked.data-v-1c7c41d0 {
	border-left: 4rpx solid #d9d9d9;
	background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
	opacity: 0.7;
}
.move-header.data-v-1c7c41d0 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}
.move-name.data-v-1c7c41d0 {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}
.move-status-badge.data-v-1c7c41d0 {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: bold;
}
.move-status-badge.status-unlocked.data-v-1c7c41d0 {
	background: #f6ffed;
	color: #52c41a;
	border: 1rpx solid #b7eb8f;
}
.move-status-badge.status-locked.data-v-1c7c41d0 {
	background: #fafafa;
	color: #8c8c8c;
	border: 1rpx solid #d9d9d9;
}
.move-details.data-v-1c7c41d0 {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}
.move-unlock-condition.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #666;
}
.move-attack.data-v-1c7c41d0, .move-defense.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #1890ff;
	font-weight: bold;
}
.moves-container.data-v-1c7c41d0 {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.moves-container .move-item.data-v-1c7c41d0 {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 10rpx;
	border-left: 4rpx solid #667eea;
}
.move-name.data-v-1c7c41d0 {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}
.move-desc.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}
.modal-footer.data-v-1c7c41d0 {
	display: flex;
	padding: 30rpx;
	border-top: 2rpx solid #f0f0f0;
	gap: 20rpx;
	background: #fff;
	border-radius: 0 0 20rpx 20rpx;
	flex-shrink: 0;
	min-height: 120rpx;
}
.modal-btn.data-v-1c7c41d0 {
	flex: 1;
	padding: 24rpx 20rpx;
	border-radius: 15rpx;
	font-size: 30rpx;
	border: none;
	font-weight: 500;
	min-height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.cancel-btn.data-v-1c7c41d0 {
	background: #f0f0f0;
	color: #666;
}
.confirm-btn.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}
.confirm-btn.data-v-1c7c41d0:disabled {
	background: #ccc;
}
.use-btn.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
	color: #fff;
}
.use-btn.unequip.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
}

/* 标题和选择器同行样式 */
.title-selector-row.data-v-1c7c41d0 {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 0;
}
.group-title.data-v-1c7c41d0 {
	font-size: 17px;
	font-weight: bold;
	color: #7a5c2e;
	min-width: 64px;   /* 固定宽度，保证对齐 */
	text-align: right;
	margin-right: 24px;
	flex-shrink: 0;
}
.equip-selector.data-v-1c7c41d0 {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 8px;
	flex: 1;
}
.title-selector-row .skill-actions.data-v-1c7c41d0 {
	flex: 0 0 auto;
}

/* 使用武功选择器样式 */
.equip-selector.data-v-1c7c41d0 {
	margin: 0;
	display: flex;
	align-items: center;
}
.picker-display.data-v-1c7c41d0 {
	display: flex;
	align-items: center;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 0 8px;
	font-size: 12px;
	background: #fafbfc;
	min-width: 60px;
	height: 28px;
	transition: border 0.2s, box-shadow 0.2s, background 0.2s;
}
.picker-display.data-v-1c7c41d0:active, .picker-display.data-v-1c7c41d0:focus {
	border: 1px solid #b6b6b6;
	background: #f5f7fa;
}
.picker-text.data-v-1c7c41d0 {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.picker-arrow.data-v-1c7c41d0 {
	margin-left: 2px;
	font-size: 11px;
	color: #aaa;
}
/* 装备按钮样式 */
.equip-btn.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	height: 60rpx;
	padding: 12rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 15rpx;
	font-size: 24rpx;
	font-weight: 600;
	border: none;
}
.equip-btn.equipped.data-v-1c7c41d0 {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}
.martial-info.data-v-1c7c41d0 {
	display: none;
}
/* 增益效果样式 */
.martial-bonus.data-v-1c7c41d0 {
	margin-top: 15rpx;
	display: flex;
	align-items: flex-start;
}
.martial-bonus-title.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #666;
	margin-right: 15rpx;
	white-space: nowrap;
	font-weight: 600;
}
.martial-bonus-content.data-v-1c7c41d0 {
	font-size: 22rpx;
	color: #52c41a;
	line-height: 1.4;
	flex: 1;
	word-break: keep-all;
	white-space: normal;
}
.martial-select-modal-mask.data-v-1c7c41d0 {
	position: fixed;
	left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.18);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}
.martial-select-modal.data-v-1c7c41d0 {
	background: #ffffff;
	border-radius: 20rpx;
	min-width: 600rpx;
	max-width: 90vw;
	max-height: 70vh;
	box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
	display: flex;
	flex-direction: column;
	align-items: stretch;
	padding: 40rpx 30rpx 30rpx 30rpx;
	border: 2rpx solid #e2e8f0;
}
.martial-select-title.data-v-1c7c41d0 {
	font-size: 32rpx;
	font-weight: 700;
	color: #1e293b;
	margin-bottom: 20rpx;
	text-align: center;
}
.martial-select-list.data-v-1c7c41d0 {
	flex: 1;
	min-height: 120px;
	max-height: 40vh;
	overflow-y: auto;
	margin-bottom: 10px;
}
.martial-select-item.data-v-1c7c41d0 {
	display: flex;
	flex-direction: column;
	padding: 20rpx;
	border-radius: 12rpx;
	margin-bottom: 12rpx;
	background: #f8fafc;
	cursor: pointer;
	transition: all 0.2s ease;
	border: 2rpx solid #e2e8f0;
}
.martial-select-item.selected.data-v-1c7c41d0, .martial-select-item.data-v-1c7c41d0:hover {
	background: #e6f7ff;
	border-color: #667eea;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.1);
}
.martial-select-header.data-v-1c7c41d0 {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 8rpx;
}
.martial-select-name.data-v-1c7c41d0 {
	font-size: 30rpx;
	font-weight: 600;
	color: #2d3748;
	flex: 1;
}
.martial-select-status.data-v-1c7c41d0 {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}
.martial-select-level.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #667eea;
	font-weight: 500;
	margin-bottom: 4rpx;
}
.martial-select-quality.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #d69e2e;
	font-weight: 500;
	margin-bottom: 8rpx;
}
.martial-select-desc.data-v-1c7c41d0 {
	font-size: 24rpx;
	color: #4a5568;
	line-height: 1.5;
	font-weight: 400;
}
.martial-select-equipped.data-v-1c7c41d0 {
	font-size: 22rpx;
	color: #52c41a;
	background: rgba(82, 196, 26, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
	margin-top: 8rpx;
}
.martial-select-empty.data-v-1c7c41d0 {
	color: #aaa;
	font-size: 13px;
	text-align: center;
	margin: 16px 0;
}
.martial-select-cancel.data-v-1c7c41d0 {
	margin-top: 20rpx;
	background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
	color: #4a5568;
	border-radius: 50rpx;
	border: 2rpx solid #e2e8f0;
	font-size: 28rpx;
	font-weight: 600;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}
.martial-select-cancel.data-v-1c7c41d0:active {
	background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
	transform: scale(0.98);
}
.martial-select-tag.data-v-1c7c41d0 {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 16px;
	background: #f8f5e6;
	border: 1.5px solid #c9a86b;
	font-family: 'FZSongKeBenXian-Z07S', '仿宋', serif;
	font-size: 15px;
	color: #7a5c2e;
	padding: 4px 18px;
	margin-right: 4px;
	cursor: pointer;
	transition: all 0.2s ease;
	margin: 0;
}
.martial-select-tag.data-v-1c7c41d0:active, .martial-select-tag.data-v-1c7c41d0:focus {
	background: #e2e8f0;
	border-color: #667eea;
	transform: scale(0.98);
}
.martial-select-tag.data-v-1c7c41d0:disabled {
	background: #f1f5f9;
	border-color: #cbd5e0;
	color: #94a3b8;
	cursor: not-allowed;
	transform: none;
}
.martial-select-tag.data-v-1c7c41d0:disabled:active, .martial-select-tag.data-v-1c7c41d0:disabled:focus {
	background: #f1f5f9;
	border-color: #cbd5e0;
	transform: none;
}
.martial-select-tag-text.data-v-1c7c41d0 {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: center;
	font-weight: 500;
}
.martial-select-tag-arrow.data-v-1c7c41d0 {
	margin-left: 6px;
	font-size: 13px;
	color: #aaa;
}
.martial-clear-btn.data-v-1c7c41d0 {
	width: 22px;
	height: 22px;
	line-height: 20px;
	border-radius: 50%;
	background: #fff0f0;
	color: #d9534f;
	font-size: 16px;
	font-weight: bold;
	padding: 0;
	margin-left: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #e0b4b4;
	flex-shrink: 0;
}
.martial-list.data-v-1c7c41d0 {
  margin: 24rpx 0 0 0;
}
.martial-row.data-v-1c7c41d0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f8fa;
  border-radius: 16rpx;
  margin-bottom: 18rpx;
  padding: 0 18rpx;
  height: 72rpx;
}
.martial-type.data-v-1c7c41d0 {
  font-size: 28rpx;
  color: #3e2c13;
  font-weight: bold;
  width: 100rpx;
  text-align: left;
}
.martial-btn.data-v-1c7c41d0 {
  flex: 1;
  margin-left: 18rpx;
  font-size: 26rpx;
  border-radius: 12rpx;
  border: none;
  outline: none;
  text-align: center;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'STKaiti', 'KaiTi', 'FZKai-Z03', '楷体', serif;
}
.martial-btn.equipped.data-v-1c7c41d0 {
  background: linear-gradient(90deg, #bfa76a 0%, #f8f5e6 100%);
  color: #3e2c13;
  font-weight: bold;
  border: 2rpx solid #bfa76a;
}
.martial-btn.unequipped.data-v-1c7c41d0 {
  background: #f0f0f0;
  color: #bbb;
  border: 2rpx dashed #e0cda2;
}
.train-log-scroll.data-v-1c7c41d0 {
  padding: 0 12px;
  box-sizing: border-box;
}
.train-loading-mask.data-v-1c7c41d0 {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.25);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.train-loading-content.data-v-1c7c41d0 {
  background: #fff;
  border-radius: 16px;
  padding: 32px 36px 24px 36px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.train-spinner.data-v-1c7c41d0 {
  width: 36px;
  height: 36px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #a084fa;
  border-radius: 50%;
  animation: train-spin-1c7c41d0 1s linear infinite;
  margin-bottom: 16px;
}
@keyframes train-spin-1c7c41d0 {
0% { transform: rotate(0deg);
}
100% { transform: rotate(360deg);
}
}
.train-loading-text.data-v-1c7c41d0 {
  font-size: 16px;
  color: #7a5c2e;
  margin-top: 2px;
  letter-spacing: 1px;
}

