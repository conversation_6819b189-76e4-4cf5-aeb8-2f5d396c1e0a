"use strict";
const common_vendor = require("../common/vendor.js");
const utils_websocket = require("./websocket.js");
class GameStateManager {
  constructor() {
    this.player = null;
    this.inventory = [];
    this.equipment = {};
    this.skills = [];
    this.eventLog = [];
    this.money = 0;
    this.gold = 0;
    this.status = "normal";
    this.itemsConfig = {};
    this.mapsConfig = {};
    this.updateCallbacks = [];
    this.isAuthed = false;
    this.initWebSocketHandlers();
  }
  /**
   * 初始化WebSocket事件处理器
   */
  initWebSocketHandlers() {
    utils_websocket.wsManager.on("player_data", (data) => {
      if (data && typeof data === "object") {
        if (data.data && typeof data.data === "object") {
          this.player = data.data;
        } else {
          this.player = data;
        }
        this.notifyUpdate("player");
      } else {
        common_vendor.index.showToast({
          title: "玩家数据格式错误",
          icon: "none"
        });
      }
    });
    utils_websocket.wsManager.on("inventory_data", (data) => {
      if (data && data.inventory && Array.isArray(data.inventory)) {
        this.inventory = data.inventory;
      } else if (Array.isArray(data)) {
        this.inventory = data;
      } else if (data && typeof data === "object") {
        if (data.data && Array.isArray(data.data)) {
          this.inventory = data.data;
        } else {
          common_vendor.index.showToast({
            title: "背包数据格式无法识别",
            icon: "none"
          });
          this.inventory = [];
        }
      } else {
        common_vendor.index.showToast({
          title: "背包数据格式错误",
          icon: "none"
        });
        this.inventory = [];
      }
      this.notifyUpdate("inventory");
    });
    utils_websocket.wsManager.on("equipment_data", (data) => {
      if (data && typeof data === "object") {
        if (data.data && typeof data.data === "object") {
          this.equipment = data.data;
        } else {
          this.equipment = data;
        }
      } else {
        common_vendor.index.showToast({
          title: "装备数据格式错误",
          icon: "none"
        });
        this.equipment = {};
      }
      this.notifyUpdate("equipment");
    });
    utils_websocket.wsManager.on("skills_data", (data) => {
      if (data && Array.isArray(data)) {
        this.skills = data;
      } else if (data && typeof data === "object" && data.data && Array.isArray(data.data)) {
        this.skills = data.data;
      } else {
        common_vendor.index.showToast({
          title: "武功数据格式错误",
          icon: "none"
        });
        this.skills = [];
      }
      this.notifyUpdate("skills");
    });
    utils_websocket.wsManager.on("event_log", (data) => {
      if (data && Array.isArray(data)) {
        this.eventLog = data;
      } else if (data && typeof data === "object" && data.data && Array.isArray(data.data)) {
        this.eventLog = data.data;
      } else {
        common_vendor.index.showToast({
          title: "事件日志格式错误",
          icon: "none"
        });
        this.eventLog = [];
      }
      this.notifyUpdate("eventLog");
    });
    utils_websocket.wsManager.on("currency_update", (data) => {
      if (data && typeof data === "object") {
        this.money = data.silver || data.money || this.money || 0;
        this.gold = data.gold || this.gold || 0;
        this.notifyUpdate("currency");
      } else {
        common_vendor.index.showToast({
          title: "货币数据格式错误",
          icon: "none"
        });
      }
    });
    utils_websocket.wsManager.on("status_update", (data) => {
      if (data && typeof data === "object") {
        this.status = data.status || this.status || "normal";
        this.notifyUpdate("status");
      } else {
        common_vendor.index.showToast({
          title: "状态数据格式错误",
          icon: "none"
        });
      }
    });
    utils_websocket.wsManager.on("energy_update", (data) => {
      if (data && typeof data === "object" && this.player) {
        this.player.energy = data.energy || this.player.energy || 0;
        this.player.max_energy = data.max_energy || this.player.max_energy || 0;
        this.player.energy_regen_rate = data.energy_regen_rate || this.player.energy_regen_rate || 0;
        this.player.energy_regen_details = data.energy_regen_details || null;
        this.notifyUpdate("player");
      } else {
        common_vendor.index.showToast({
          title: "体力数据格式错误或玩家数据不存在",
          icon: "none"
        });
      }
    });
    utils_websocket.wsManager.on("auth_success", (data) => {
      this.isAuthed = true;
      if (data && typeof data === "object") {
        if (data.player && typeof data.player === "object") {
          this.player = data.player;
        }
        if (data.userInfo && typeof data.userInfo === "object") {
          if (!this.player)
            this.player = {};
          const userInfo = data.userInfo;
          this.player.username = userInfo.username || this.player.username;
          this.player.characterName = userInfo.characterName || this.player.characterName;
          this.player.userId = userInfo.userId || this.player.userId;
          this.player.gender = userInfo.gender || this.player.gender;
          if (userInfo.talent && typeof userInfo.talent === "object") {
            this.player.talent = userInfo.talent;
          }
        }
        if (data.money !== void 0)
          this.money = data.money;
        if (data.silver !== void 0)
          this.money = data.silver;
        if (data.gold !== void 0)
          this.gold = data.gold;
      }
      this.notifyUpdate("player");
      this.notifyUpdate("currency");
      this.notifyUpdate("status");
      this.notifyUpdate("auth");
      setTimeout(() => {
        this.requestAllData();
      }, 500);
    });
    utils_websocket.wsManager.on("auth_failed", (data) => {
      this.isAuthed = false;
      this.notifyUpdate("auth");
    });
    utils_websocket.wsManager.on("login_success", (data) => {
      this.isAuthed = true;
      if (data && typeof data === "object") {
        if (data.player && typeof data.player === "object") {
          this.player = data.player;
        }
        if (data.userInfo && typeof data.userInfo === "object") {
          if (!this.player)
            this.player = {};
          const userInfo = data.userInfo;
          this.player.username = userInfo.username || this.player.username;
          this.player.characterName = userInfo.characterName || this.player.characterName;
          this.player.userId = userInfo.userId || this.player.userId;
          this.player.gender = userInfo.gender || this.player.gender;
          if (userInfo.talent && typeof userInfo.talent === "object") {
            this.player.talent = userInfo.talent;
          }
        }
        if (data.money !== void 0)
          this.money = data.money;
        if (data.silver !== void 0)
          this.money = data.silver;
        if (data.gold !== void 0)
          this.gold = data.gold;
      }
      this.notifyUpdate("player");
      this.notifyUpdate("currency");
      this.notifyUpdate("status");
      this.notifyUpdate("auth");
      setTimeout(() => {
        this.requestAllData();
      }, 500);
    });
    utils_websocket.wsManager.on("error", (data) => {
      common_vendor.index.showToast({
        title: data.message || "操作失败",
        icon: "none"
      });
    });
    utils_websocket.wsManager.on("success", (data) => {
      common_vendor.index.showToast({
        title: data.message || "操作成功",
        icon: "success"
      });
    });
    utils_websocket.wsManager.on("connected", () => {
    });
    utils_websocket.wsManager.on("disconnected", () => {
    });
  }
  /**
   * 处理游戏事件
   */
  handleGameEvent(data) {
    const eventType = data.type || data.eventType || 5;
    const content = data.content || data.description || "你遇到了一个江湖事件";
    data.rewards || {};
    const realm_breakthrough = data.realm_breakthrough || null;
    const logEntry = {
      timestamp: (/* @__PURE__ */ new Date()).toLocaleTimeString("zh-CN", { hour12: false }),
      name: this.getEventTypeName(eventType),
      description: content
    };
    this.eventLog.unshift(logEntry);
    if (this.eventLog.length > 50)
      this.eventLog = this.eventLog.slice(0, 50);
    this.notifyUpdate("eventLog");
    if (realm_breakthrough) {
      setTimeout(() => {
        common_vendor.index.showModal({
          title: "境界突破",
          content: realm_breakthrough.message,
          showCancel: false,
          confirmText: "确定"
        });
      }, 500);
    }
  }
  /**
   * 获取事件类型名称
   */
  getEventTypeName(eventType) {
    const eventTypeMap = {
      1: "好运事件",
      2: "遭遇NPC",
      3: "采集事件",
      4: "普通事件",
      5: "奇遇事件",
      6: "恩怨事件",
      7: "组队事件",
      8: "商队事件",
      9: "江湖传闻",
      10: "天气事件",
      11: "神秘事件",
      12: "节日事件"
    };
    return eventTypeMap[eventType] || "江湖事件";
  }
  /**
   * 注册状态更新回调
   */
  onUpdate(callback) {
    this.updateCallbacks.push(callback);
  }
  /**
   * 移除状态更新回调
   */
  offUpdate(callback) {
    const index = this.updateCallbacks.indexOf(callback);
    if (index > -1) {
      this.updateCallbacks.splice(index, 1);
    }
  }
  /**
   * 通知状态更新
   */
  notifyUpdate(type) {
    this.updateCallbacks.forEach((callback, index) => {
      try {
        callback(type, this);
      } catch (error) {
      }
    });
  }
  /**
   * 初始化游戏状态
   */
  async init() {
    try {
      await utils_websocket.wsManager.connect();
    } catch (error) {
      console.error("游戏初始化失败:", error);
      common_vendor.index.showToast({
        title: "连接服务器失败",
        icon: "none"
      });
    }
  }
  /**
   * 请求所有游戏数据
   */
  requestAllData() {
    if (!utils_websocket.wsManager.isConnected) {
      utils_websocket.wsManager.connect().then(() => {
        this.sendDataRequests();
      }).catch((error) => {
        console.error("[gameState] WebSocket连接失败:", error);
      });
    } else {
      this.sendDataRequests();
    }
  }
  /**
   * 发送所有数据请求
   */
  sendDataRequests() {
    utils_websocket.wsManager.sendMessage("get_player_data");
    setTimeout(() => {
      utils_websocket.wsManager.sendMessage("get_inventory_data");
    }, 200);
    setTimeout(() => {
      const { gameUtils } = require("./gameData.js");
      if (gameUtils && gameUtils.sendMessage) {
        gameUtils.sendMessage({
          type: "get_equipment_data",
          data: {}
        }).then((response) => {
          if (response.type === "equipment_data" && response.data) {
            this.equipment = response.data;
            this.notifyUpdate("equipment");
          }
        }).catch((error) => {
          console.error("[gameState] 获取装备数据失败:", error);
        });
      } else {
        utils_websocket.wsManager.sendMessage("get_player_data");
      }
    }, 400);
    setTimeout(() => {
      utils_websocket.wsManager.sendMessage("get_skills_data");
    }, 600);
    setTimeout(() => {
      this.requestMapsConfig();
    }, 800);
    setTimeout(() => {
      this.requestItemsConfig();
    }, 1e3);
  }
  /**
   * 闯江湖
   */
  triggerAdventure() {
    const isAuthenticated = utils_websocket.wsManager.isAuthed || this.isAuthed;
    if (!isAuthenticated) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    if (!utils_websocket.wsManager.isConnected) {
      common_vendor.index.showToast({ title: "网络连接失败", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendAdventureRequest();
  }
  /**
   * 打坐
   */
  meditate() {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendMeditationRequest();
  }
  /**
   * 疗伤
   */
  heal() {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendHealingRequest();
  }
  /**
   * 装备操作
   */
  equipItem(itemId) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendEquipmentAction("equip", itemId);
  }
  unequipItem(slot) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendEquipmentAction("unequip", slot);
  }
  /**
   * 武功操作
   */
  learnSkill(skillId) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendSkillAction("learn", skillId);
  }
  practiceSkill(skillId) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendSkillAction("practice", skillId);
  }
  /**
   * 商店操作
   */
  buyItem(itemId, quantity = 1, npcId = "") {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    const data = { item_id: itemId, quantity };
    if (npcId)
      data.npc_id = npcId;
    utils_websocket.wsManager.sendShopAction("buy", data);
  }
  sellItem(itemId, quantity = 1) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendShopAction("sell", { itemId, quantity });
  }
  /**
   * 市场操作
   */
  listItem(itemId, price, quantity = 1) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendMarketAction("list", { itemId, price, quantity });
  }
  buyMarketItem(itemId) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendMarketAction("buy", { itemId });
  }
  cancelListing(itemId) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendMarketAction("cancel", { itemId });
  }
  /**
   * 门派操作
   */
  joinGuild(guildId) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendGuildAction("join", { guildId });
  }
  acceptGuildTask(taskId) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendGuildAction("accept_task", { taskId });
  }
  learnGuildSkill(skillId) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendGuildAction("learn_skill", { skillId });
  }
  /**
   * 打造操作
   */
  craftItem(recipeId) {
    if (!this.isAuthed) {
      common_vendor.index.showToast({ title: "请先登录", icon: "none" });
      return;
    }
    utils_websocket.wsManager.sendCraftingAction("craft", { recipeId });
  }
  /**
   * 获取玩家数据
   */
  getPlayer() {
    return this.player;
  }
  /**
   * 获取背包数据
   */
  getInventory() {
    return this.inventory;
  }
  /**
   * 获取装备数据
   */
  getEquipment() {
    return this.equipment;
  }
  /**
   * 获取武功数据
   */
  getSkills() {
    return this.skills;
  }
  /**
   * 获取事件日志
   */
  getEventLog() {
    return this.eventLog;
  }
  /**
   * 获取货币数据
   */
  getCurrency() {
    return {
      silver: this.money,
      gold: this.gold
    };
  }
  /**
   * 获取状态
   */
  getStatus() {
    return this.status;
  }
  /**
   * 断开连接
   */
  disconnect() {
    utils_websocket.wsManager.disconnect();
  }
  getPlayerData() {
    return this.player;
  }
  /**
   * 设置玩家数据并通知更新
   */
  setPlayerData(data) {
    this.player = data;
    this.notifyUpdate("player");
  }
  updateMoney() {
    if (this.player && typeof this.player.money === "number") {
      this.money = this.player.money;
    } else if (this.money) {
      this.money = this.money;
    }
  }
  updateData() {
    this.myItems = [...this.inventory];
  }
  /**
   * 新增：请求物品配置（结构化JSON）
   */
  requestItemsConfig() {
    return new Promise((resolve, reject) => {
      utils_websocket.wsManager.sendMessage("get_items_config", {});
      utils_websocket.wsManager.on("items_config", (data) => {
        this.itemsConfig = data || {};
        this.notifyUpdate("itemsConfig");
        resolve(this.itemsConfig);
      });
    });
  }
  /**
   * 获取物品配置（如未加载则自动请求）
   */
  async getItemsConfig() {
    if (Object.keys(this.itemsConfig).length === 0) {
      await this.requestItemsConfig();
    }
    return this.itemsConfig;
  }
  /**
   * 新增：请求地图配置（结构化JSON）
   */
  requestMapsConfig() {
    return new Promise((resolve, reject) => {
      utils_websocket.wsManager.sendMessage("get_maps_config", {});
      utils_websocket.wsManager.on("maps_config", (response) => {
        this.mapsConfig = {};
        const data = response.data || response;
        if (Array.isArray(data)) {
          for (const map of data) {
            if (map.id) {
              this.mapsConfig[map.id] = map;
            }
          }
        } else if (typeof data === "object" && data !== null) {
          for (const [id, map] of Object.entries(data)) {
            if (map && map.id) {
              this.mapsConfig[map.id] = map;
            }
          }
        }
        this.notifyUpdate("mapsConfig");
        resolve(this.mapsConfig);
      });
    });
  }
  /**
   * 获取地图配置（如未加载则自动请求）
   */
  async getMapsConfig() {
    if (Object.keys(this.mapsConfig).length === 0) {
      await this.requestMapsConfig();
    }
    return this.mapsConfig;
  }
}
const gameState = new GameStateManager();
exports.gameState = gameState;
