# 🎁 兑换码管理工具使用说明

## 功能概述

兑换码管理工具提供了完整的兑换码生命周期管理功能，包括创建、查看、启用/禁用兑换码等。

## 启动方式

### Windows
```bash
# 方式1：直接运行
python redeem_code_manager.py

# 方式2：使用批处理文件
start_redeem_manager.bat
```

### Linux/Mac
```bash
python3 redeem_code_manager.py
```

## 主要功能

### 1. 创建兑换码 🆕

支持创建包含多种奖励的兑换码：

**奖励类型：**
- `money` - 银两奖励
- `experience` - 历练值奖励  
- `item` - 物品奖励

**配置选项：**
- 兑换码名称和描述
- 最大使用次数
- 有效期（天数）
- 创建者信息

**示例配置：**
```json
{
  "name": "新手礼包",
  "description": "新玩家专属礼包",
  "rewards": [
    {"type": "money", "name": "银两", "quantity": 1000},
    {"type": "experience", "name": "历练值", "quantity": 500},
    {"type": "item", "name": "初级丹药", "item_id": "pill_basic", "quantity": 5}
  ],
  "max_uses": 100,
  "expires_days": 30
}
```

### 2. 查看兑换码列表 📋

- 查看所有兑换码
- 按状态筛选（活跃/已禁用）
- 显示使用情况和过期状态

### 3. 查看兑换码详情 🔍

- 完整的兑换码信息
- 奖励配置详情
- 使用记录和统计
- 最近使用者列表

### 4. 管理兑换码状态 ⚙️

- 启用兑换码
- 禁用兑换码
- 状态实时更新

## 数据库结构

### redeem_codes 表
```sql
CREATE TABLE redeem_codes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    code TEXT UNIQUE NOT NULL,           -- 兑换码
    name TEXT NOT NULL,                  -- 名称
    description TEXT,                    -- 描述
    rewards TEXT NOT NULL,               -- 奖励配置(JSON)
    max_uses INTEGER DEFAULT 1,         -- 最大使用次数
    current_uses INTEGER DEFAULT 0,     -- 当前使用次数
    expires_at TEXT,                     -- 过期时间
    created_at TEXT NOT NULL,           -- 创建时间
    created_by TEXT,                    -- 创建者
    status TEXT DEFAULT 'active'        -- 状态
);
```

### redeem_code_usage 表
```sql
CREATE TABLE redeem_code_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,           -- 用户ID
    code_id INTEGER NOT NULL,           -- 兑换码ID
    code TEXT NOT NULL,                 -- 兑换码
    rewards TEXT NOT NULL,              -- 实际获得奖励(JSON)
    redeemed_at TEXT NOT NULL,          -- 兑换时间
    FOREIGN KEY(user_id) REFERENCES users(id),
    FOREIGN KEY(code_id) REFERENCES redeem_codes(id),
    UNIQUE(user_id, code_id)           -- 每人每码只能用一次
);
```

## API 接口

### 1. 获取排行榜
```json
{
  "type": "get_ranking",
  "data": {
    "type": "wealth|experience|adventure",
    "limit": 10
  }
}
```

**响应：**
```json
{
  "type": "success",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "玩家名",
        "level": 10,
        "money": 50000,
        "experience": 10000,
        "adventureCount": 500
      }
    ],
    "myRanking": {
      "rank": 15,
      "money": 30000,
      "experience": 8000,
      "adventureCount": 300
    }
  }
}
```

### 2. 兑换码兑换
```json
{
  "type": "redeem_code",
  "data": {
    "code": "ABCD1234"
  }
}
```

**响应：**
```json
{
  "type": "success",
  "data": {
    "message": "兑换成功！",
    "rewards": [
      {"name": "银两", "quantity": 1000},
      {"name": "历练值", "quantity": 500}
    ]
  }
}
```

### 3. 获取兑换历史
```json
{
  "type": "get_redeem_history",
  "data": {}
}
```

**响应：**
```json
{
  "type": "success",
  "data": {
    "history": [
      {
        "code": "ABCD1234",
        "rewards": [
          {"name": "银两", "quantity": 1000}
        ],
        "redeemTime": "2024-01-01T12:00:00"
      }
    ]
  }
}
```

## 常见问题

### Q: 兑换码生成规则是什么？
A: 默认8位长度，使用大写字母和数字，排除容易混淆的字符（0、O、I、1）。

### Q: 如何设置永久有效的兑换码？
A: 创建时有效期天数留空即可。

### Q: 兑换码可以重复使用吗？
A: 每个兑换码每个玩家只能使用一次，但可以设置最大使用次数供多个玩家使用。

### Q: 如何批量创建兑换码？
A: 可以修改管理工具脚本，添加批量创建功能，或直接操作数据库。

### Q: 兑换码过期后还能使用吗？
A: 不能，系统会自动检查过期时间并拒绝过期兑换码的使用。

## 安全注意事项

1. **兑换码保密**：生成的兑换码应妥善保管，避免泄露
2. **权限控制**：管理工具应只允许授权人员使用
3. **数据备份**：定期备份兑换码数据，防止数据丢失
4. **监控使用**：定期检查兑换码使用情况，发现异常及时处理

## 扩展功能建议

1. **批量操作**：支持批量创建、启用、禁用兑换码
2. **模板系统**：预设常用的兑换码模板
3. **统计报表**：兑换码使用统计和分析
4. **Web界面**：提供Web管理界面，方便操作
5. **API接口**：提供HTTP API，支持外部系统集成
