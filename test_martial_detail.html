<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>武功详情功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .martial-card {
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .martial-card:hover {
            border-color: #1890ff;
            box-shadow: 0 2px 8px rgba(24,144,255,0.2);
        }
        
        .martial-name {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .martial-info {
            color: #666;
            font-size: 14px;
        }
        
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border-radius: 12px;
            padding: 0;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 12px 12px 0 0;
        }
        
        .modal-title {
            font-size: 20px;
            font-weight: bold;
            margin: 0;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .detail-special-effects {
            margin-bottom: 20px;
            padding: 16px;
            background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        
        .special-effects-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .special-effects-text {
            color: #1890ff;
            font-weight: bold;
            background: rgba(24, 144, 255, 0.1);
            padding: 4px 12px;
            border-radius: 16px;
            display: inline-block;
        }
        
        .moves-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
        }
        
        .moves-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .move-item {
            background: #ffffff;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e8e8e8;
            transition: all 0.3s ease;
        }
        
        .move-item.move-unlocked {
            border-left: 4px solid #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
        }
        
        .move-item.move-locked {
            border-left: 4px solid #d9d9d9;
            background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
            opacity: 0.7;
        }
        
        .move-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .move-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }
        
        .move-status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .move-status-badge.status-unlocked {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .move-status-badge.status-locked {
            background: #fafafa;
            color: #8c8c8c;
            border: 1px solid #d9d9d9;
        }
        
        .move-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .move-unlock-condition, .move-attack {
            font-size: 14px;
            color: #666;
        }
        
        .move-attack {
            color: #1890ff;
            font-weight: bold;
        }
        
        .close-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>武功详情功能测试</h1>
        <p>点击下方武功卡片查看详情，测试特效和招式列表显示</p>
        
        <div id="martialList"></div>
        
        <!-- 武功详情弹窗 -->
        <div id="martialModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="modalTitle">武功详情</h3>
                </div>
                <div class="modal-body" id="modalBody">
                    <!-- 内容将通过JavaScript动态生成 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟武功数据
        const martialData = [
            {
                name: "基本剑法",
                level: 15,
                type: "剑法",
                quality: "普通",
                specialEffect: "无",
                moves: [
                    { name: "直刺", unlockLevel: 0, attack: 10, unlocked: true },
                    { name: "横扫", unlockLevel: 10, attack: 20, unlocked: true },
                    { name: "上挑", unlockLevel: 20, attack: 30, unlocked: false },
                    { name: "下劈", unlockLevel: 30, attack: 40, unlocked: false }
                ]
            },
            {
                name: "无名剑法",
                level: 25,
                type: "剑法",
                quality: "稀有",
                specialEffect: "穿刺",
                moves: [
                    { name: "无名初现", unlockLevel: 0, attack: 10, unlocked: true },
                    { name: "剑气如虹", unlockLevel: 10, attack: 20, unlocked: true },
                    { name: "剑影重重", unlockLevel: 20, attack: 30, unlocked: true },
                    { name: "剑断长空", unlockLevel: 30, attack: 40, unlocked: false }
                ]
            },
            {
                name: "无名轻功",
                level: 18,
                type: "轻功",
                quality: "稀有",
                specialEffect: "闪避",
                moves: [
                    { name: "雪中行", unlockLevel: 0, defense: 10, unlocked: true },
                    { name: "影随雪动", unlockLevel: 10, defense: 20, unlocked: true },
                    { name: "雪落无声", unlockLevel: 20, defense: 30, unlocked: false },
                    { name: "雪舞长空", unlockLevel: 30, defense: 40, unlocked: false }
                ]
            }
        ];

        function renderMartialList() {
            const listContainer = document.getElementById('martialList');
            listContainer.innerHTML = '';
            
            martialData.forEach((martial, index) => {
                const card = document.createElement('div');
                card.className = 'martial-card';
                card.onclick = () => showMartialDetail(martial);
                
                card.innerHTML = `
                    <div class="martial-name">${martial.name}</div>
                    <div class="martial-info">
                        ${martial.level}级 | ${martial.type} | ${martial.quality}品质
                    </div>
                `;
                
                listContainer.appendChild(card);
            });
        }

        function showMartialDetail(martial) {
            const modal = document.getElementById('martialModal');
            const title = document.getElementById('modalTitle');
            const body = document.getElementById('modalBody');
            
            title.textContent = `${martial.name} - ${martial.level}级`;
            
            let bodyHTML = '';
            
            // 武功特效
            if (martial.specialEffect && martial.specialEffect !== '无') {
                bodyHTML += `
                    <div class="detail-special-effects">
                        <div class="special-effects-title">武功特效:</div>
                        <span class="special-effects-text">${martial.specialEffect}</span>
                    </div>
                `;
            }
            
            // 招式列表
            if (martial.moves && martial.moves.length > 0) {
                bodyHTML += `
                    <div class="moves-title">招式列表:</div>
                    <div class="moves-list">
                `;
                
                martial.moves.forEach(move => {
                    const unlockedClass = move.unlocked ? 'move-unlocked' : 'move-locked';
                    const statusClass = move.unlocked ? 'status-unlocked' : 'status-locked';
                    const statusText = move.unlocked ? '已解锁' : '未解锁';
                    
                    bodyHTML += `
                        <div class="move-item ${unlockedClass}">
                            <div class="move-header">
                                <span class="move-name">${move.name}</span>
                                <span class="move-status-badge ${statusClass}">${statusText}</span>
                            </div>
                            <div class="move-details">
                                <span class="move-unlock-condition">解锁条件: ${move.unlockLevel}级</span>
                                ${move.attack ? `<span class="move-attack">攻击力: +${move.attack}</span>` : ''}
                                ${move.defense ? `<span class="move-attack">防御力: +${move.defense}</span>` : ''}
                            </div>
                        </div>
                    `;
                });
                
                bodyHTML += '</div>';
            }
            
            bodyHTML += '<button class="close-btn" onclick="closeMartialDetail()">关闭</button>';
            
            body.innerHTML = bodyHTML;
            modal.style.display = 'block';
        }

        function closeMartialDetail() {
            document.getElementById('martialModal').style.display = 'none';
        }

        // 点击模态框背景关闭
        document.getElementById('martialModal').onclick = function(e) {
            if (e.target === this) {
                closeMartialDetail();
            }
        };

        // 初始化页面
        renderMartialList();
    </script>
</body>
</html>
