import sqlite3
import json
import os
from enhanced_martial_system import get_martial_info

DB_PATH = os.path.join(os.path.dirname(__file__), 'game.db')

def fix_martial_type_to_empty_hand(db_path=DB_PATH):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 获取所有玩家数据
    cursor.execute("SELECT id, data FROM players")
    rows = cursor.fetchall()
    fixed_count = 0

    for player_id, data_json in rows:
        player_data = json.loads(data_json)
        changed = False

        martial_skills = player_data.get('martial_skills')
        # 兼容 dict 和 list
        if isinstance(martial_skills, dict):
            for name, skill in martial_skills.items():
                martial_info = get_martial_info(name)
                if not martial_info:
                    continue
                correct_type = martial_info.get('类型') or martial_info.get('type')
                if correct_type == '空手' and (skill.get('type') == '拳法' or skill.get('类型') == '拳法' or skill.get('类别') == '拳法'):
                    skill['type'] = '空手'
                    skill['类型'] = '空手'
                    skill['类别'] = '空手'
                    changed = True
        elif isinstance(martial_skills, list):
            for skill in martial_skills:
                name = skill.get('name') or skill.get('名称')
                martial_info = get_martial_info(name)
                if not martial_info:
                    continue
                correct_type = martial_info.get('类型') or martial_info.get('type')
                if correct_type == '空手' and (skill.get('type') == '拳法' or skill.get('类型') == '拳法' or skill.get('类别') == '拳法'):
                    skill['type'] = '空手'
                    skill['类型'] = '空手'
                    skill['类别'] = '空手'
                    changed = True

        if changed:
            cursor.execute("UPDATE players SET data=? WHERE id=?", (json.dumps(player_data, ensure_ascii=False), player_id))
            fixed_count += 1

    conn.commit()
    conn.close()
    print(f"已修复 {fixed_count} 条玩家 martial_skills 中的拳法为空手。")

if __name__ == '__main__':
    fix_martial_type_to_empty_hand()