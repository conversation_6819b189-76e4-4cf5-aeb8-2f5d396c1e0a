#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查物品配置
"""

import sys
sys.path.append('backend')

from item_system import load_items_config

def main():
    print("=== 检查物品配置 ===")
    
    items = load_items_config()
    print(f"总共加载了 {len(items)} 个物品")
    
    # 检查商店中的物品是否存在
    shop_items = [
        'wooden_sword', 'wooden_knife', 'wooden_spear', 'wooden_staff',
        'wooden_armor', 'wooden_shield', 'pickaxe_1', 'axe_1', 'sickle_1', 'knife_1'
    ]
    
    print("\n=== 检查商店物品 ===")
    for item_id in shop_items:
        if item_id in items:
            item = items[item_id]
            print(f"✓ {item_id}: {item.get('name', '未知名称')}")
        else:
            print(f"✗ {item_id}: 不存在")
    
    # 显示前10个物品
    print("\n=== 前10个物品 ===")
    for i, (item_id, item) in enumerate(items.items()):
        if i >= 10:
            break
        print(f"{item_id}: {item.get('name', '未知名称')} ({item.get('type', '未知类型')})")

if __name__ == "__main__":
    main()
