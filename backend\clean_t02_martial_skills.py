import sqlite3
import json
import os

# 1. 加载所有合法技能名
with open(os.path.join(os.path.dirname(__file__), 'wugong_enhanced.json'), encoding='utf-8') as f:
    wugong_names = {item['武功名'] for item in json.load(f)}
with open(os.path.join(os.path.dirname(__file__), 'life_skills.json'), encoding='utf-8') as f:
    life_names = {item['技能名'] for item in json.load(f)}
all_valid_names = wugong_names | life_names

# 2. 连接数据库，查找t02
DB_PATH = os.path.join(os.path.dirname(__file__), 'game.db')
conn = sqlite3.connect(DB_PATH)
cursor = conn.cursor()
cursor.execute("SELECT id FROM users WHERE username=?", ('t02',))
row = cursor.fetchone()
if not row:
    print("未找到账号 t02")
    exit()
user_id = row[0]
cursor.execute("SELECT id, data FROM players WHERE user_id=?", (user_id,))
row = cursor.fetchone()
if not row:
    print("未找到玩家数据 t02")
    exit()
player_id, data_json = row
player_data = json.loads(data_json)

# 3. 清理 martial_skills
martial_skills = player_data.get('martial_skills', {})
if isinstance(martial_skills, list):
    martial_skills = [s for s in martial_skills if s.get('name') in all_valid_names]
else:
    martial_skills = {k: v for k, v in martial_skills.items() if k in all_valid_names}
player_data['martial_skills'] = martial_skills

# 4. 保存
cursor.execute("UPDATE players SET data=? WHERE id=?", (json.dumps(player_data, ensure_ascii=False), player_id))
conn.commit()
conn.close()
print("清理完成。") 