{"version": 3, "file": "guild.js", "sources": ["pages/guild/guild.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZ3VpbGQvZ3VpbGQudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 门派信息 -->\r\n\t\t<view class=\"guild-info\" v-if=\"playerGuild\">\r\n\t\t\t<view class=\"guild-header\">\r\n\t\t\t\t<text class=\"guild-name\">{{ playerGuild.name }}</text>\r\n\t\t\t\t<text class=\"guild-level\">等级 {{ playerGuild.level }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"guild-stats\">\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-label\">声望:</text>\r\n\t\t\t\t\t<text class=\"stat-value\">{{ player.reputation }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-label\">贡献:</text>\r\n\t\t\t\t\t<text class=\"stat-value\">{{ playerGuild.contribution }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"stat-item\">\r\n\t\t\t\t\t<text class=\"stat-label\">职位:</text>\r\n\t\t\t\t\t<text class=\"stat-value\">{{ getPositionName(playerGuild.position) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 未加入门派 -->\r\n\t\t<view class=\"no-guild\" v-else>\r\n\t\t\t<text class=\"no-guild-title\">尚未加入门派</text>\r\n\t\t\t<text class=\"no-guild-desc\">加入门派可以获得专属武功和任务</text>\r\n\t\t\t<button class=\"join-guild-btn\" @click=\"showGuildList\">加入门派</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 门派功能 -->\r\n\t\t<view class=\"guild-functions\" v-if=\"playerGuild\">\r\n\t\t\t<view class=\"function-grid\">\r\n\t\t\t\t<view class=\"function-item\" @click=\"showTasks\">\r\n\t\t\t\t\t<text class=\"function-icon\">📋</text>\r\n\t\t\t\t\t<text class=\"function-name\">门派任务</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-item\" @click=\"showSkills\">\r\n\t\t\t\t\t<text class=\"function-icon\">⚔️</text>\r\n\t\t\t\t\t<text class=\"function-name\">门派武功</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-item\" @click=\"showMembers\">\r\n\t\t\t\t\t<text class=\"function-icon\">👥</text>\r\n\t\t\t\t\t<text class=\"function-name\">门派成员</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"function-item\" @click=\"showShop\">\r\n\t\t\t\t\t<text class=\"function-icon\">🏪</text>\r\n\t\t\t\t\t<text class=\"function-name\">门派商店</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 门派任务 -->\r\n\t\t<view class=\"tasks-section\" v-if=\"showTasksSection\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">门派任务</text>\r\n\t\t\t\t<text class=\"section-close\" @click=\"hideTasks\">×</text>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view class=\"tasks-list\" scroll-y=\"true\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tclass=\"task-item\" \r\n\t\t\t\t\tv-for=\"(task, index) in availableTasks\" \r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t@click=\"showTaskDetail(task)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"task-info\">\r\n\t\t\t\t\t\t<text class=\"task-name\">{{ task.name }}</text>\r\n\t\t\t\t\t\t<text class=\"task-desc\">{{ task.description }}</text>\r\n\t\t\t\t\t\t<text class=\"task-reward\">奖励: {{ task.reward }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"task-status\">\r\n\t\t\t\t\t\t<text class=\"task-difficulty\" :class=\"getDifficultyClass(task.difficulty)\">\r\n\t\t\t\t\t\t\t{{ getDifficultyName(task.difficulty) }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<button \r\n\t\t\t\t\t\t\tclass=\"accept-task-btn\" \r\n\t\t\t\t\t\t\************=\"acceptTask(task)\"\r\n\t\t\t\t\t\t\t:disabled=\"!canAcceptTask(task)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t接受\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"empty-tasks\" v-if=\"availableTasks.length === 0\">\r\n\t\t\t\t\t<text>暂无可接任务</text>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 门派武功 -->\r\n\t\t<view class=\"skills-section\" v-if=\"showSkillsSection\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">门派武功</text>\r\n\t\t\t\t<text class=\"section-close\" @click=\"hideSkills\">×</text>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view class=\"skills-list\" scroll-y=\"true\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tclass=\"skill-item\" \r\n\t\t\t\t\tv-for=\"(skill, index) in guildSkills\" \r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t@click=\"showSkillDetail(skill)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"skill-info\">\r\n\t\t\t\t\t\t<text class=\"skill-name\">{{ skill.name }}</text>\r\n\t\t\t\t\t\t<text class=\"skill-type\">{{ getSkillTypeName(skill.type) }}</text>\r\n\t\t\t\t\t\t<text class=\"skill-desc\">{{ skill.description }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"skill-status\">\r\n\t\t\t\t\t\t<text class=\"skill-level\" v-if=\"skill.level\">等级 {{ skill.level }}</text>\r\n\t\t\t\t\t\t<button \r\n\t\t\t\t\t\t\tclass=\"learn-skill-btn\" \r\n\t\t\t\t\t\t\************=\"learnSkill(skill)\"\r\n\t\t\t\t\t\t\t:disabled=\"!canLearnSkill(skill)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{{ skill.learned ? '修炼' : '学习' }}\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 门派成员 -->\r\n\t\t<view class=\"members-section\" v-if=\"showMembersSection\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">门派成员</text>\r\n\t\t\t\t<text class=\"section-close\" @click=\"hideMembers\">×</text>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view class=\"members-list\" scroll-y=\"true\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tclass=\"member-item\" \r\n\t\t\t\t\tv-for=\"(member, index) in guildMembers\" \r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"member-info\">\r\n\t\t\t\t\t\t<text class=\"member-name\">{{ member.name }}</text>\r\n\t\t\t\t\t\t<text class=\"member-position\">{{ getPositionName(member.position) }}</text>\r\n\t\t\t\t\t\t<text class=\"member-level\">等级 {{ member.level }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"member-contribution\">\r\n\t\t\t\t\t\t<text class=\"contribution-label\">贡献:</text>\r\n\t\t\t\t\t\t<text class=\"contribution-value\">{{ member.contribution }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 门派商店 -->\r\n\t\t<view class=\"shop-section\" v-if=\"showShopSection\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">门派商店</text>\r\n\t\t\t\t<text class=\"section-close\" @click=\"hideShop\">×</text>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view class=\"shop-list\" scroll-y=\"true\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tclass=\"shop-item\" \r\n\t\t\t\t\tv-for=\"(item, index) in guildShopItems\" \r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t@click=\"showShopItemDetail(item)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"item-info\">\r\n\t\t\t\t\t\t<text class=\"item-name\">{{ item.name }}</text>\r\n\t\t\t\t\t\t<text class=\"item-desc\">{{ item.description }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"item-price\">\r\n\t\t\t\t\t\t<text class=\"price-value\">{{ item.price }}</text>\r\n\t\t\t\t\t\t<text class=\"price-unit\">贡献</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<button \r\n\t\t\t\t\t\tclass=\"buy-item-btn\" \r\n\t\t\t\t\t\************=\"buyShopItem(item)\"\r\n\t\t\t\t\t\t:disabled=\"!canBuyItem(item)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t购买\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 门派列表弹窗 -->\r\n\t\t<view class=\"modal-overlay\" v-if=\"showGuildListModal\" @click=\"closeGuildList\">\r\n\t\t\t<view class=\"modal-content\" @click.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">选择门派</text>\r\n\t\t\t\t\t<text class=\"modal-close\" @click=\"closeGuildList\">×</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t\t<view \r\n\t\t\t\t\t\tclass=\"guild-option\" \r\n\t\t\t\t\t\tv-for=\"(guild, index) in availableGuilds\" \r\n\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t@click=\"selectGuild(guild)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text class=\"guild-option-name\">{{ guild.name }}</text>\r\n\t\t\t\t\t\t<text class=\"guild-option-desc\">{{ guild.description }}</text>\r\n\t\t\t\t\t\t<text class=\"guild-option-requirement\">要求: 等级 {{ guild.requirement }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 任务详情弹窗 -->\r\n\t\t<view class=\"modal-overlay\" v-if=\"showTaskDetail\" @click=\"closeTaskDetail\">\r\n\t\t\t<view class=\"modal-content\" @click.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">任务详情</text>\r\n\t\t\t\t\t<text class=\"modal-close\" @click=\"closeTaskDetail\">×</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\" v-if=\"selectedTask\">\r\n\t\t\t\t\t<text class=\"detail-name\">{{ selectedTask.name }}</text>\r\n\t\t\t\t\t<text class=\"detail-desc\">{{ selectedTask.description }}</text>\r\n\t\t\t\t\t<text class=\"detail-requirement\">要求: {{ selectedTask.requirement }}</text>\r\n\t\t\t\t\t<text class=\"detail-reward\">奖励: {{ selectedTask.reward }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-footer\">\r\n\t\t\t\t\t<button class=\"modal-btn cancel-btn\" @click=\"closeTaskDetail\">关闭</button>\r\n\t\t\t\t\t<button \r\n\t\t\t\t\t\tclass=\"modal-btn confirm-btn\" \r\n\t\t\t\t\t\t@click=\"acceptTask(selectedTask)\"\r\n\t\t\t\t\t\t:disabled=\"!canAcceptTask(selectedTask)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t接受任务\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport gameState from '../../utils/gameState.js'\r\nimport { gameUtils } from '../../utils/gameData.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tplayer: {},\r\n\t\t\tplayerGuild: null,\r\n\t\t\tshowTasksSection: false,\r\n\t\t\tshowSkillsSection: false,\r\n\t\t\tshowMembersSection: false,\r\n\t\t\tshowShopSection: false,\r\n\t\t\tshowGuildListModal: false,\r\n\t\t\tshowTaskDetail: false,\r\n\t\t\tselectedTask: null,\r\n\t\t\tavailableGuilds: [\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'wudang',\r\n\t\t\t\t\tname: '武当派',\r\n\t\t\t\t\tdescription: '以太极拳和剑法闻名，注重内功修为',\r\n\t\t\t\t\trequirement: 5\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'shaolin',\r\n\t\t\t\t\tname: '少林寺',\r\n\t\t\t\t\tdescription: '佛门武功，以刚猛著称，擅长拳法和棍法',\r\n\t\t\t\t\trequirement: 5\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'emei',\r\n\t\t\t\t\tname: '峨眉派',\r\n\t\t\t\t\tdescription: '女子门派，剑法精妙，医术高超',\r\n\t\t\t\t\trequirement: 5\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tavailableTasks: [\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'task_1',\r\n\t\t\t\t\tname: '收集药材',\r\n\t\t\t\t\tdescription: '收集10株金银花',\r\n\t\t\t\t\trequirement: '等级 5',\r\n\t\t\t\t\treward: '贡献 50, 银两 100',\r\n\t\t\t\t\tdifficulty: 'easy'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'task_2',\r\n\t\t\t\t\tname: '击败山贼',\r\n\t\t\t\t\tdescription: '击败5名山贼',\r\n\t\t\t\t\trequirement: '等级 8',\r\n\t\t\t\t\treward: '贡献 100, 银两 200',\r\n\t\t\t\t\tdifficulty: 'medium'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'task_3',\r\n\t\t\t\t\tname: '护送商队',\r\n\t\t\t\t\tdescription: '护送商队安全到达目的地',\r\n\t\t\t\t\trequirement: '等级 10',\r\n\t\t\t\t\treward: '贡献 200, 银两 500',\r\n\t\t\t\t\tdifficulty: 'hard'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tguildSkills: [\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'taiji_quan',\r\n\t\t\t\t\tname: '太极拳',\r\n\t\t\t\t\ttype: 'external',\r\n\t\t\t\t\tdescription: '武当派绝学，以柔克刚',\r\n\t\t\t\t\tlevel: 1,\r\n\t\t\t\t\tlearned: false,\r\n\t\t\t\t\trequirement: '贡献 100'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'wudang_sword',\r\n\t\t\t\t\tname: '武当剑法',\r\n\t\t\t\t\ttype: 'external',\r\n\t\t\t\t\tdescription: '武当派剑法，剑走轻灵',\r\n\t\t\t\t\tlevel: 1,\r\n\t\t\t\t\tlearned: false,\r\n\t\t\t\t\trequirement: '贡献 150'\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tguildMembers: [\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '张三丰',\r\n\t\t\t\t\tposition: 'master',\r\n\t\t\t\t\tlevel: 99,\r\n\t\t\t\t\tcontribution: 10000\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '宋远桥',\r\n\t\t\t\t\tposition: 'elder',\r\n\t\t\t\t\tlevel: 80,\r\n\t\t\t\t\tcontribution: 5000\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tname: '俞莲舟',\r\n\t\t\t\t\tposition: 'elder',\r\n\t\t\t\t\tlevel: 75,\r\n\t\t\t\t\tcontribution: 4500\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tguildShopItems: [\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'guild_sword',\r\n\t\t\t\t\tname: '门派长剑',\r\n\t\t\t\t\tdescription: '武当派制式长剑',\r\n\t\t\t\t\tprice: 200,\r\n\t\t\t\t\ttype: 'weapon',\r\n\t\t\t\t\tquality: 'rare'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'guild_armor',\r\n\t\t\t\t\tname: '门派道袍',\r\n\t\t\t\t\tdescription: '武当派制式道袍',\r\n\t\t\t\t\tprice: 150,\r\n\t\t\t\t\ttype: 'armor',\r\n\t\t\t\t\tquality: 'fine'\r\n\t\t\t\t},\r\n\t\t\t\t{\r\n\t\t\t\t\tid: 'guild_potion',\r\n\t\t\t\t\tname: '门派丹药',\r\n\t\t\t\t\tdescription: '武当派秘制丹药',\r\n\t\t\t\t\tprice: 50,\r\n\t\t\t\t\ttype: 'potion',\r\n\t\t\t\t\tquality: 'fine'\r\n\t\t\t\t}\r\n\t\t\t]\r\n\t\t}\r\n\t},\r\n\t\r\n\tonLoad() {\r\n\t\tthis.updateData()\r\n\t},\r\n\t\r\n\tonShow() {\r\n\t\tthis.updateData()\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\tupdateData() {\r\n\t\t\tthis.player = { ...gameState.player }\r\n\t\t\t// 模拟玩家已加入武当派\r\n\t\t\tif (!this.playerGuild) {\r\n\t\t\t\tthis.playerGuild = {\r\n\t\t\t\t\tid: 'wudang',\r\n\t\t\t\t\tname: '武当派',\r\n\t\t\t\t\tlevel: 1,\r\n\t\t\t\t\tcontribution: 0,\r\n\t\t\t\t\tposition: 'disciple'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tgetPositionName(position) {\r\n\t\t\tconst positions = {\r\n\t\t\t\t'master': '掌门',\r\n\t\t\t\t'elder': '长老',\r\n\t\t\t\t'disciple': '弟子',\r\n\t\t\t\t'outer': '外门弟子'\r\n\t\t\t}\r\n\t\t\treturn positions[position] || '弟子'\r\n\t\t},\r\n\t\t\r\n\t\tshowGuildList() {\r\n\t\t\tthis.showGuildListModal = true\r\n\t\t},\r\n\t\t\r\n\t\tcloseGuildList() {\r\n\t\t\tthis.showGuildListModal = false\r\n\t\t},\r\n\t\t\r\n\t\tselectGuild(guild) {\r\n\t\t\tif (this.player.level < guild.requirement) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: `等级不足，需要等级 ${guild.requirement}`,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '确认加入',\r\n\t\t\t\tcontent: `确定要加入 ${guild.name} 吗？`,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.playerGuild = {\r\n\t\t\t\t\t\t\tid: guild.id,\r\n\t\t\t\t\t\t\tname: guild.name,\r\n\t\t\t\t\t\t\tlevel: 1,\r\n\t\t\t\t\t\t\tcontribution: 0,\r\n\t\t\t\t\t\t\tposition: 'disciple'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.closeGuildList()\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '加入门派成功！',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\tshowTasks() {\r\n\t\t\tthis.showTasksSection = true\r\n\t\t\tthis.hideOtherSections()\r\n\t\t},\r\n\t\t\r\n\t\thideTasks() {\r\n\t\t\tthis.showTasksSection = false\r\n\t\t},\r\n\t\t\r\n\t\tshowSkills() {\r\n\t\t\tthis.showSkillsSection = true\r\n\t\t\tthis.hideOtherSections()\r\n\t\t},\r\n\t\t\r\n\t\thideSkills() {\r\n\t\t\tthis.showSkillsSection = false\r\n\t\t},\r\n\t\t\r\n\t\tshowMembers() {\r\n\t\t\tthis.showMembersSection = true\r\n\t\t\tthis.hideOtherSections()\r\n\t\t},\r\n\t\t\r\n\t\thideMembers() {\r\n\t\t\tthis.showMembersSection = false\r\n\t\t},\r\n\t\t\r\n\t\tshowShop() {\r\n\t\t\tthis.showShopSection = true\r\n\t\t\tthis.hideOtherSections()\r\n\t\t},\r\n\t\t\r\n\t\thideShop() {\r\n\t\t\tthis.showShopSection = false\r\n\t\t},\r\n\t\t\r\n\t\thideOtherSections() {\r\n\t\t\tthis.showTasksSection = false\r\n\t\t\tthis.showSkillsSection = false\r\n\t\t\tthis.showMembersSection = false\r\n\t\t\tthis.showShopSection = false\r\n\t\t},\r\n\t\t\r\n\t\tgetDifficultyClass(difficulty) {\r\n\t\t\tconst classes = {\r\n\t\t\t\t'easy': 'difficulty-easy',\r\n\t\t\t\t'medium': 'difficulty-medium',\r\n\t\t\t\t'hard': 'difficulty-hard'\r\n\t\t\t}\r\n\t\t\treturn classes[difficulty] || 'difficulty-easy'\r\n\t\t},\r\n\t\t\r\n\t\tgetDifficultyName(difficulty) {\r\n\t\t\tconst names = {\r\n\t\t\t\t'easy': '简单',\r\n\t\t\t\t'medium': '中等',\r\n\t\t\t\t'hard': '困难'\r\n\t\t\t}\r\n\t\t\treturn names[difficulty] || '简单'\r\n\t\t},\r\n\t\t\r\n\t\tgetSkillTypeName(type) {\r\n\t\t\tconst types = {\r\n\t\t\t\t'external': '外功',\r\n\t\t\t\t'internal': '内功',\r\n\t\t\t\t'light': '轻功',\r\n\t\t\t\t'heart': '心法',\r\n\t\t\t\t'special': '特技'\r\n\t\t\t}\r\n\t\t\treturn types[type] || '武功'\r\n\t\t},\r\n\t\t\r\n\t\tcanAcceptTask(task) {\r\n\t\t\t// 检查其他要求（如果有的话）\r\n\t\t\treturn true\r\n\t\t},\r\n\t\t\r\n\t\tcanLearnSkill(skill) {\r\n\t\t\tif (skill.learned) {\r\n\t\t\t\treturn true // 可以修炼\r\n\t\t\t}\r\n\t\t\t// 检查贡献要求\r\n\t\t\tconst contributionMatch = skill.requirement.match(/贡献 (\\d+)/)\r\n\t\t\tif (contributionMatch && this.playerGuild.contribution < parseInt(contributionMatch[1])) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t\treturn true\r\n\t\t},\r\n\t\t\r\n\t\tcanBuyItem(item) {\r\n\t\t\treturn this.playerGuild.contribution >= item.price\r\n\t\t},\r\n\t\t\r\n\t\tshowTaskDetail(task) {\r\n\t\t\tthis.selectedTask = task\r\n\t\t\tthis.showTaskDetail = true\r\n\t\t},\r\n\t\t\r\n\t\tcloseTaskDetail() {\r\n\t\t\tthis.showTaskDetail = false\r\n\t\t\tthis.selectedTask = null\r\n\t\t},\r\n\t\t\r\n\t\tacceptTask(task) {\r\n\t\t\tif (!gameState.isAuthed) {\r\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (!this.canAcceptTask(task)) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '不满足任务要求',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '接受任务',\r\n\t\t\t\tcontent: `确定要接受任务 \"${task.name}\" 吗？`,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t// 这里应该将任务添加到玩家的任务列表中\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '任务已接受',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.closeTaskDetail()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\tshowSkillDetail(skill) {\r\n\t\t\t// 显示武功详情\r\n\t\t},\r\n\t\t\r\n\t\tlearnSkill(skill) {\r\n\t\t\tif (!gameState.isAuthed) {\r\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (!this.canLearnSkill(skill)) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '贡献不足',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif (skill.learned) {\r\n\t\t\t\t// 修炼武功\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '开始修炼',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t})\r\n\t\t\t} else {\r\n\t\t\t\t// 学习武功\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '学习武功',\r\n\t\t\t\t\tcontent: `确定要学习 ${skill.name} 吗？\\n消耗贡献: ${skill.requirement}`,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\tskill.learned = true\r\n\t\t\t\t\t\t\t// 扣除贡献\r\n\t\t\t\t\t\t\tconst contributionMatch = skill.requirement.match(/贡献 (\\d+)/)\r\n\t\t\t\t\t\t\tif (contributionMatch) {\r\n\t\t\t\t\t\t\t\tthis.playerGuild.contribution -= parseInt(contributionMatch[1])\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '学习成功！',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tshowShopItemDetail(item) {\r\n\t\t\t// 显示商店物品详情\r\n\t\t},\r\n\t\t\r\n\t\tbuyShopItem(item) {\r\n\t\t\tif (!gameState.isAuthed) {\r\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (!this.canBuyItem(item)) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '贡献不足',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '购买物品',\r\n\t\t\t\tcontent: `确定要购买 ${item.name} 吗？\\n消耗贡献: ${item.price}`,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.playerGuild.contribution -= item.price\r\n\t\t\t\t\t\t// 适配字段\r\n\t\t\t\t\t\tconst type = item.type || '';\r\n\t\t\t\t\t\tconst sellable = (typeof item.sellable !== 'undefined' ? item.sellable : true) ? true : false;\r\n\t\t\t\t\t\tconst unique_id = item.unique_id || `${item.id}_${Date.now()}_${Math.floor(Math.random()*10000)}`;\r\n\t\t\t\t\t\tgameState.addItem({ ...item, type, sellable, unique_id })\r\n\t\t\t\t\t\tgameState.save()\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '购买成功！',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n\tpadding: 20rpx;\r\n\tpadding-bottom: 140rpx; /* 为tabBar留出空间 */\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.guild-info {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.guild-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.guild-name {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.guild-level {\r\n\tfont-size: 28rpx;\r\n\tcolor: #667eea;\r\n\tbackground: #f0f4ff;\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.guild-stats {\r\n\tdisplay: flex;\r\n\tgap: 30rpx;\r\n}\r\n\r\n.stat-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.stat-label {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.stat-value {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.no-guild {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 60rpx 30rpx;\r\n\ttext-align: center;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.no-guild-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.no-guild-desc {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.join-guild-btn {\r\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\tcolor: white;\r\n\tborder: none;\r\n\tborder-radius: 25rpx;\r\n\tpadding: 20rpx 40rpx;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.guild-functions {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.function-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(2, 1fr);\r\n\tgap: 20rpx;\r\n}\r\n\r\n.function-item {\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 15rpx;\r\n\tpadding: 30rpx;\r\n\ttext-align: center;\r\n\ttransition: all 0.3s;\r\n}\r\n\r\n.function-item:active {\r\n\tbackground: #e9ecef;\r\n\ttransform: scale(0.98);\r\n}\r\n\r\n.function-icon {\r\n\tfont-size: 48rpx;\r\n\tdisplay: block;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.function-name {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.tasks-section,\r\n.skills-section,\r\n.members-section,\r\n.shop-section {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(255, 255, 255, 0.98);\r\n\tz-index: 1000;\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.section-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20rpx 0;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.section-close {\r\n\tfont-size: 40rpx;\r\n\tcolor: #999;\r\n\tline-height: 1;\r\n}\r\n\r\n.tasks-list,\r\n.skills-list,\r\n.members-list,\r\n.shop-list {\r\n\theight: calc(100vh - 120rpx);\r\n}\r\n\r\n.task-item,\r\n.skill-item,\r\n.member-item,\r\n.shop-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 20rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n\tbackground: white;\r\n\tborder-radius: 15rpx;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.task-info,\r\n.skill-info,\r\n.member-info,\r\n.item-info {\r\n\tflex: 1;\r\n}\r\n\r\n.task-name,\r\n.skill-name,\r\n.member-name,\r\n.item-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.task-desc,\r\n.skill-desc,\r\n.item-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.task-reward {\r\n\tfont-size: 24rpx;\r\n\tcolor: #f39c12;\r\n\tdisplay: block;\r\n}\r\n\r\n.skill-type {\r\n\tfont-size: 24rpx;\r\n\tcolor: #667eea;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.member-position,\r\n.member-level {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.task-status,\r\n.skill-status,\r\n.member-contribution {\r\n\ttext-align: center;\r\n}\r\n\r\n.task-difficulty {\r\n\tfont-size: 24rpx;\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.difficulty-easy {\r\n\tbackground: #d4edda;\r\n\tcolor: #155724;\r\n}\r\n\r\n.difficulty-medium {\r\n\tbackground: #fff3cd;\r\n\tcolor: #856404;\r\n}\r\n\r\n.difficulty-hard {\r\n\tbackground: #f8d7da;\r\n\tcolor: #721c24;\r\n}\r\n\r\n.skill-level {\r\n\tfont-size: 24rpx;\r\n\tcolor: #667eea;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.contribution-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.contribution-value {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #f39c12;\r\n}\r\n\r\n.accept-task-btn,\r\n.learn-skill-btn,\r\n.buy-item-btn {\r\n\tbackground: #27ae60;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 12rpx 24rpx;\r\n\tfont-size: 26rpx;\r\n}\r\n\r\n.accept-task-btn[disabled],\r\n.learn-skill-btn[disabled],\r\n.buy-item-btn[disabled] {\r\n\topacity: 0.5;\r\n\tbackground: #ccc;\r\n}\r\n\r\n.empty-tasks {\r\n\ttext-align: center;\r\n\tpadding: 100rpx 0;\r\n\tcolor: #999;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.modal-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 2000;\r\n}\r\n\r\n.modal-content {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\twidth: 80%;\r\n\tmax-width: 600rpx;\r\n\tmax-height: 80vh;\r\n\toverflow: hidden;\r\n}\r\n\r\n.modal-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.modal-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.modal-close {\r\n\tfont-size: 40rpx;\r\n\tcolor: #999;\r\n\tline-height: 1;\r\n}\r\n\r\n.modal-body {\r\n\tpadding: 30rpx;\r\n\tmax-height: 400rpx;\r\n\toverflow-y: auto;\r\n}\r\n\r\n.guild-option {\r\n\tpadding: 20rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.guild-option:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.guild-option-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.guild-option-desc {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.guild-option-requirement {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tdisplay: block;\r\n}\r\n\r\n.detail-name {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.detail-desc,\r\n.detail-requirement,\r\n.detail-reward {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.modal-footer {\r\n\tdisplay: flex;\r\n\tpadding: 20rpx 30rpx;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.modal-btn {\r\n\tflex: 1;\r\n\tpadding: 20rpx;\r\n\tborder: none;\r\n\tborder-radius: 15rpx;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.cancel-btn {\r\n\tbackground: #f0f0f0;\r\n\tcolor: #666;\r\n}\r\n\r\n.confirm-btn {\r\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\tcolor: white;\r\n}\r\n\r\n.confirm-btn[disabled] {\r\n\topacity: 0.5;\r\n\tbackground: #ccc;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/zjjhx/仗剑江湖行/pages/guild/guild.vue'\nwx.createPage(MiniProgramPage)"], "names": ["gameState", "uni"], "mappings": ";;;;AAyOA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,QAAQ,CAAE;AAAA,MACV,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,mBAAmB;AAAA,MACnB,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,MACjB,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,iBAAiB;AAAA,QAChB;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,aAAa;AAAA,QACb;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,aAAa;AAAA,QACb;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,aAAa;AAAA,QACd;AAAA,MACA;AAAA,MACD,gBAAgB;AAAA,QACf;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,YAAY;AAAA,QACZ;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,YAAY;AAAA,QACZ;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,aAAa;AAAA,UACb,QAAQ;AAAA,UACR,YAAY;AAAA,QACb;AAAA,MACA;AAAA,MACD,aAAa;AAAA,QACZ;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,QACb;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,QACd;AAAA,MACA;AAAA,MACD,cAAc;AAAA,QACb;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,UACP,cAAc;AAAA,QACd;AAAA,QACD;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,UACP,cAAc;AAAA,QACd;AAAA,QACD;AAAA,UACC,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAO;AAAA,UACP,cAAc;AAAA,QACf;AAAA,MACA;AAAA,MACD,gBAAgB;AAAA,QACf;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACT;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACT;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,aAAa;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,SAAS;AAAA,QACV;AAAA,MACD;AAAA,IACD;AAAA,EACA;AAAA,EAED,SAAS;AACR,SAAK,WAAW;AAAA,EAChB;AAAA,EAED,SAAS;AACR,SAAK,WAAW;AAAA,EAChB;AAAA,EAED,SAAS;AAAA,IACR,aAAa;AACZ,WAAK,SAAS,EAAE,GAAGA,gBAAAA,UAAU,OAAO;AAEpC,UAAI,CAAC,KAAK,aAAa;AACtB,aAAK,cAAc;AAAA,UAClB,IAAI;AAAA,UACJ,MAAM;AAAA,UACN,OAAO;AAAA,UACP,cAAc;AAAA,UACd,UAAU;AAAA,QACX;AAAA,MACD;AAAA,IACA;AAAA,IAED,gBAAgB,UAAU;AACzB,YAAM,YAAY;AAAA,QACjB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,SAAS;AAAA,MACV;AACA,aAAO,UAAU,QAAQ,KAAK;AAAA,IAC9B;AAAA,IAED,gBAAgB;AACf,WAAK,qBAAqB;AAAA,IAC1B;AAAA,IAED,iBAAiB;AAChB,WAAK,qBAAqB;AAAA,IAC1B;AAAA,IAED,YAAY,OAAO;AAClB,UAAI,KAAK,OAAO,QAAQ,MAAM,aAAa;AAC1CC,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,aAAa,MAAM,WAAW;AAAA,UACrC,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,SAAS,MAAM,IAAI;AAAA,QAC5B,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,cAAc;AAAA,cAClB,IAAI,MAAM;AAAA,cACV,MAAM,MAAM;AAAA,cACZ,OAAO;AAAA,cACP,cAAc;AAAA,cACd,UAAU;AAAA,YACX;AACA,iBAAK,eAAe;AAEpBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,aACN;AAAA,UACF;AAAA,QACD;AAAA,OACA;AAAA,IACD;AAAA,IAED,YAAY;AACX,WAAK,mBAAmB;AACxB,WAAK,kBAAkB;AAAA,IACvB;AAAA,IAED,YAAY;AACX,WAAK,mBAAmB;AAAA,IACxB;AAAA,IAED,aAAa;AACZ,WAAK,oBAAoB;AACzB,WAAK,kBAAkB;AAAA,IACvB;AAAA,IAED,aAAa;AACZ,WAAK,oBAAoB;AAAA,IACzB;AAAA,IAED,cAAc;AACb,WAAK,qBAAqB;AAC1B,WAAK,kBAAkB;AAAA,IACvB;AAAA,IAED,cAAc;AACb,WAAK,qBAAqB;AAAA,IAC1B;AAAA,IAED,WAAW;AACV,WAAK,kBAAkB;AACvB,WAAK,kBAAkB;AAAA,IACvB;AAAA,IAED,WAAW;AACV,WAAK,kBAAkB;AAAA,IACvB;AAAA,IAED,oBAAoB;AACnB,WAAK,mBAAmB;AACxB,WAAK,oBAAoB;AACzB,WAAK,qBAAqB;AAC1B,WAAK,kBAAkB;AAAA,IACvB;AAAA,IAED,mBAAmB,YAAY;AAC9B,YAAM,UAAU;AAAA,QACf,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AACA,aAAO,QAAQ,UAAU,KAAK;AAAA,IAC9B;AAAA,IAED,kBAAkB,YAAY;AAC7B,YAAM,QAAQ;AAAA,QACb,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,QAAQ;AAAA,MACT;AACA,aAAO,MAAM,UAAU,KAAK;AAAA,IAC5B;AAAA,IAED,iBAAiB,MAAM;AACtB,YAAM,QAAQ;AAAA,QACb,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA,QACT,WAAW;AAAA,MACZ;AACA,aAAO,MAAM,IAAI,KAAK;AAAA,IACtB;AAAA,IAED,cAAc,MAAM;AAEnB,aAAO;AAAA,IACP;AAAA,IAED,cAAc,OAAO;AACpB,UAAI,MAAM,SAAS;AAClB,eAAO;AAAA,MACR;AAEA,YAAM,oBAAoB,MAAM,YAAY,MAAM,UAAU;AAC5D,UAAI,qBAAqB,KAAK,YAAY,eAAe,SAAS,kBAAkB,CAAC,CAAC,GAAG;AACxF,eAAO;AAAA,MACR;AACA,aAAO;AAAA,IACP;AAAA,IAED,WAAW,MAAM;AAChB,aAAO,KAAK,YAAY,gBAAgB,KAAK;AAAA,IAC7C;AAAA,IAED,eAAe,MAAM;AACpB,WAAK,eAAe;AACpB,WAAK,iBAAiB;AAAA,IACtB;AAAA,IAED,kBAAkB;AACjB,WAAK,iBAAiB;AACtB,WAAK,eAAe;AAAA,IACpB;AAAA,IAED,WAAW,MAAM;AAChB,UAAI,CAACD,gBAAS,UAAC,UAAU;AACxBC,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAC7C;AAAA,MACD;AACA,UAAI,CAAC,KAAK,cAAc,IAAI,GAAG;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,YAAY,KAAK,IAAI;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhBA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,aACN;AACD,iBAAK,gBAAgB;AAAA,UACtB;AAAA,QACD;AAAA,OACA;AAAA,IACD;AAAA,IAED,gBAAgB,OAAO;AAAA,IAEtB;AAAA,IAED,WAAW,OAAO;AACjB,UAAI,CAACD,gBAAS,UAAC,UAAU;AACxBC,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAC7C;AAAA,MACD;AACA,UAAI,CAAC,KAAK,cAAc,KAAK,GAAG;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAEA,UAAI,MAAM,SAAS;AAElBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AAAA,aACK;AAENA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS,SAAS,MAAM,IAAI;AAAA,QAAc,MAAM,WAAW;AAAA,UAC3D,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAChB,oBAAM,UAAU;AAEhB,oBAAM,oBAAoB,MAAM,YAAY,MAAM,UAAU;AAC5D,kBAAI,mBAAmB;AACtB,qBAAK,YAAY,gBAAgB,SAAS,kBAAkB,CAAC,CAAC;AAAA,cAC/D;AAEAA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,eACN;AAAA,YACF;AAAA,UACD;AAAA,SACA;AAAA,MACF;AAAA,IACA;AAAA,IAED,mBAAmB,MAAM;AAAA,IAExB;AAAA,IAED,YAAY,MAAM;AACjB,UAAI,CAACD,gBAAS,UAAC,UAAU;AACxBC,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAC7C;AAAA,MACD;AACA,UAAI,CAAC,KAAK,WAAW,IAAI,GAAG;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AACAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,IAAI;AAAA,QAAc,KAAK,KAAK;AAAA,QACnD,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,YAAY,gBAAgB,KAAK;AAEtC,kBAAM,OAAO,KAAK,QAAQ;AAC1B,kBAAM,YAAY,OAAO,KAAK,aAAa,cAAc,KAAK,WAAW,QAAQ,OAAO;AACxF,kBAAM,YAAY,KAAK,aAAa,GAAG,KAAK,EAAE,IAAI,KAAK,IAAG,CAAE,IAAI,KAAK,MAAM,KAAK,WAAS,GAAK,CAAC;AAC/FD,4BAAS,UAAC,QAAQ,EAAE,GAAG,MAAM,MAAM,UAAU,WAAW;AACxDA,4BAAAA,UAAU,KAAK;AACfC,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,aACN;AAAA,UACF;AAAA,QACD;AAAA,OACA;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxoBA,GAAG,WAAW,eAAe;"}