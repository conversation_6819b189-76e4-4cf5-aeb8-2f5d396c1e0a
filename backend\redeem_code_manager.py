#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兑换码管理工具
用于生成、管理和查看兑换码
"""

import aiosqlite
import asyncio
import json
import secrets
import string
from datetime import datetime, timedelta
import os
import sys

# 数据库路径
DB_PATH = os.path.join(os.path.dirname(__file__), 'game.db')

class RedeemCodeManager:
    def __init__(self):
        self.db_path = DB_PATH
    
    async def init_db(self):
        """初始化数据库连接"""
        async with aiosqlite.connect(self.db_path) as db:
            # 确保兑换码表存在
            await db.execute("""
            CREATE TABLE IF NOT EXISTS redeem_codes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                rewards TEXT NOT NULL,
                max_uses INTEGER DEFAULT 1,
                current_uses INTEGER DEFAULT 0,
                expires_at TEXT,
                created_at TEXT NOT NULL,
                created_by TEXT,
                status TEXT DEFAULT 'active'
            )
            """)
            
            await db.execute("""
            CREATE TABLE IF NOT EXISTS redeem_code_usage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                code_id INTEGER NOT NULL,
                code TEXT NOT NULL,
                rewards TEXT NOT NULL,
                redeemed_at TEXT NOT NULL,
                FOREIGN KEY(code_id) REFERENCES redeem_codes(id),
                UNIQUE(user_id, code_id)
            )
            """)
            
            await db.commit()
    
    def generate_code(self, length=8):
        """生成随机兑换码"""
        # 使用大写字母和数字，避免容易混淆的字符
        chars = string.ascii_uppercase + string.digits
        chars = chars.replace('0', '').replace('O', '').replace('I', '').replace('1', '')
        return ''.join(secrets.choice(chars) for _ in range(length))
    
    async def create_code(self, name, description, rewards, max_uses=1, expires_days=None, created_by="admin"):
        """创建兑换码"""
        try:
            code = self.generate_code()
            current_time = datetime.now().isoformat()
            expires_at = None
            
            if expires_days:
                expires_at = (datetime.now() + timedelta(days=expires_days)).isoformat()
            
            # 验证奖励格式
            if isinstance(rewards, str):
                rewards = json.loads(rewards)
            
            rewards_str = json.dumps(rewards, ensure_ascii=False)
            
            async with aiosqlite.connect(self.db_path) as db:
                await db.execute("""
                    INSERT INTO redeem_codes 
                    (code, name, description, rewards, max_uses, expires_at, created_at, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (code, name, description, rewards_str, max_uses, expires_at, current_time, created_by))
                
                await db.commit()
            
            print(f"✅ 兑换码创建成功！")
            print(f"📝 兑换码: {code}")
            print(f"🏷️  名称: {name}")
            print(f"📄 描述: {description}")
            print(f"🎁 奖励: {rewards}")
            print(f"🔢 最大使用次数: {max_uses}")
            if expires_at:
                print(f"⏰ 过期时间: {expires_at}")
            print("-" * 50)
            
            return code
            
        except Exception as e:
            print(f"❌ 创建兑换码失败: {e}")
            return None
    
    async def list_codes(self, status=None, limit=20):
        """列出兑换码"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                if status:
                    query = """
                        SELECT code, name, description, max_uses, current_uses, 
                               expires_at, created_at, status
                        FROM redeem_codes 
                        WHERE status = ?
                        ORDER BY created_at DESC 
                        LIMIT ?
                    """
                    params = (status, limit)
                else:
                    query = """
                        SELECT code, name, description, max_uses, current_uses, 
                               expires_at, created_at, status
                        FROM redeem_codes 
                        ORDER BY created_at DESC 
                        LIMIT ?
                    """
                    params = (limit,)
                
                async with db.execute(query, params) as cursor:
                    rows = await cursor.fetchall()
                
                if not rows:
                    print("📭 暂无兑换码")
                    return
                
                print(f"📋 兑换码列表 (共 {len(rows)} 个):")
                print("-" * 100)
                print(f"{'兑换码':<12} {'名称':<15} {'使用情况':<10} {'状态':<8} {'过期时间':<20} {'创建时间':<20}")
                print("-" * 100)
                
                for row in rows:
                    code, name, description, max_uses, current_uses, expires_at, created_at, status = row
                    
                    # 检查是否过期
                    expired = False
                    if expires_at:
                        try:
                            expire_time = datetime.fromisoformat(expires_at)
                            if datetime.now() > expire_time:
                                expired = True
                        except:
                            pass
                    
                    usage = f"{current_uses}/{max_uses}"
                    status_display = "过期" if expired else status
                    expires_display = expires_at[:19] if expires_at else "永久"
                    created_display = created_at[:19]
                    
                    print(f"{code:<12} {name:<15} {usage:<10} {status_display:<8} {expires_display:<20} {created_display:<20}")
                
                print("-" * 100)
                
        except Exception as e:
            print(f"❌ 获取兑换码列表失败: {e}")
    
    async def get_code_info(self, code):
        """获取兑换码详细信息"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute("""
                    SELECT id, code, name, description, rewards, max_uses, current_uses, 
                           expires_at, created_at, created_by, status
                    FROM redeem_codes 
                    WHERE code = ?
                """, (code,)) as cursor:
                    row = await cursor.fetchone()
                
                if not row:
                    print(f"❌ 兑换码 {code} 不存在")
                    return
                
                code_id, code, name, description, rewards_str, max_uses, current_uses, expires_at, created_at, created_by, status = row
                
                try:
                    rewards = json.loads(rewards_str)
                except:
                    rewards = rewards_str
                
                print(f"📝 兑换码详细信息:")
                print(f"🔑 兑换码: {code}")
                print(f"🏷️  名称: {name}")
                print(f"📄 描述: {description}")
                print(f"🎁 奖励: {json.dumps(rewards, ensure_ascii=False, indent=2)}")
                print(f"🔢 使用情况: {current_uses}/{max_uses}")
                print(f"📅 创建时间: {created_at}")
                print(f"👤 创建者: {created_by}")
                print(f"⏰ 过期时间: {expires_at or '永久'}")
                print(f"📊 状态: {status}")
                
                # 获取使用记录
                async with db.execute("""
                    SELECT u.character_name, rcu.redeemed_at
                    FROM redeem_code_usage rcu
                    JOIN users u ON rcu.user_id = u.id
                    WHERE rcu.code_id = ?
                    ORDER BY rcu.redeemed_at DESC
                    LIMIT 10
                """, (code_id,)) as cursor:
                    usage_rows = await cursor.fetchall()
                
                if usage_rows:
                    print(f"\n📈 最近使用记录:")
                    for usage_row in usage_rows:
                        character_name, redeemed_at = usage_row
                        print(f"  • {character_name} - {redeemed_at[:19]}")
                
                print("-" * 50)
                
        except Exception as e:
            print(f"❌ 获取兑换码信息失败: {e}")
    
    async def disable_code(self, code):
        """禁用兑换码"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                result = await db.execute("""
                    UPDATE redeem_codes 
                    SET status = 'disabled' 
                    WHERE code = ?
                """, (code,))
                
                await db.commit()
                
                if result.rowcount > 0:
                    print(f"✅ 兑换码 {code} 已禁用")
                else:
                    print(f"❌ 兑换码 {code} 不存在")
                
        except Exception as e:
            print(f"❌ 禁用兑换码失败: {e}")
    
    async def enable_code(self, code):
        """启用兑换码"""
        try:
            async with aiosqlite.connect(self.db_path) as db:
                result = await db.execute("""
                    UPDATE redeem_codes 
                    SET status = 'active' 
                    WHERE code = ?
                """, (code,))
                
                await db.commit()
                
                if result.rowcount > 0:
                    print(f"✅ 兑换码 {code} 已启用")
                else:
                    print(f"❌ 兑换码 {code} 不存在")
                
        except Exception as e:
            print(f"❌ 启用兑换码失败: {e}")

async def main():
    """主函数"""
    manager = RedeemCodeManager()
    await manager.init_db()
    
    print("🎁 兑换码管理工具")
    print("=" * 50)
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 创建兑换码")
        print("2. 查看兑换码列表")
        print("3. 查看兑换码详情")
        print("4. 禁用兑换码")
        print("5. 启用兑换码")
        print("6. 退出")
        
        choice = input("\n请输入选项 (1-6): ").strip()
        
        if choice == '1':
            await create_code_interactive(manager)
        elif choice == '2':
            await list_codes_interactive(manager)
        elif choice == '3':
            code = input("请输入兑换码: ").strip()
            if code:
                await manager.get_code_info(code)
        elif choice == '4':
            code = input("请输入要禁用的兑换码: ").strip()
            if code:
                await manager.disable_code(code)
        elif choice == '5':
            code = input("请输入要启用的兑换码: ").strip()
            if code:
                await manager.enable_code(code)
        elif choice == '6':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选项，请重新选择")

async def create_code_interactive(manager):
    """交互式创建兑换码"""
    print("\n🎁 创建新兑换码")
    print("-" * 30)
    
    name = input("兑换码名称: ").strip()
    if not name:
        print("❌ 名称不能为空")
        return
    
    description = input("兑换码描述 (可选): ").strip()
    
    print("\n🎁 配置奖励 (支持多个奖励):")
    print("奖励类型: money(银两), experience(经验), item(物品)")
    
    rewards = []
    while True:
        reward_type = input("奖励类型 (输入 'done' 完成): ").strip().lower()
        if reward_type == 'done':
            break
        
        if reward_type not in ['money', 'experience', 'item']:
            print("❌ 无效的奖励类型")
            continue
        
        if reward_type == 'money':
            try:
                amount = int(input("银两数量: "))
                rewards.append({
                    'type': 'money',
                    'name': '银两',
                    'quantity': amount
                })
            except ValueError:
                print("❌ 请输入有效数字")
                continue
        
        elif reward_type == 'experience':
            try:
                amount = int(input("经验数量: "))
                rewards.append({
                    'type': 'experience',
                    'name': '历练值',
                    'quantity': amount
                })
            except ValueError:
                print("❌ 请输入有效数字")
                continue
        
        elif reward_type == 'item':
            item_id = input("物品ID: ").strip()
            item_name = input("物品名称: ").strip()
            try:
                quantity = int(input("物品数量: "))
                rewards.append({
                    'type': 'item',
                    'name': item_name,
                    'item_id': item_id,
                    'quantity': quantity
                })
            except ValueError:
                print("❌ 请输入有效数字")
                continue
        
        print(f"✅ 已添加奖励: {rewards[-1]}")
    
    if not rewards:
        print("❌ 至少需要一个奖励")
        return
    
    try:
        max_uses = int(input("最大使用次数 (默认1): ") or "1")
    except ValueError:
        max_uses = 1
    
    expires_days = input("有效期天数 (留空为永久): ").strip()
    expires_days = int(expires_days) if expires_days.isdigit() else None
    
    created_by = input("创建者 (默认admin): ").strip() or "admin"
    
    # 创建兑换码
    await manager.create_code(name, description, rewards, max_uses, expires_days, created_by)

async def list_codes_interactive(manager):
    """交互式查看兑换码列表"""
    print("\n📋 兑换码列表选项:")
    print("1. 查看所有兑换码")
    print("2. 查看活跃兑换码")
    print("3. 查看已禁用兑换码")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == '1':
        await manager.list_codes()
    elif choice == '2':
        await manager.list_codes(status='active')
    elif choice == '3':
        await manager.list_codes(status='disabled')
    else:
        print("❌ 无效选项")

if __name__ == "__main__":
    asyncio.run(main())
