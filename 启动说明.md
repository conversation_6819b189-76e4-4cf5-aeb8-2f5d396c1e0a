# 🚀 仗剑江湖行 - 启动说明

## 📋 项目状态

✅ **已完成功能：**
- 完整的游戏框架搭建
- 主页面（江湖）界面和功能
- 背包系统（装备、物品管理）
- 武功系统（修炼、使用）
- 商店系统（购买、出售）
- 游戏数据模型和状态管理
- 本地数据存储
✅ 项目结构已修复  
✅ CSS语法错误已修复  
✅ 配置文件已优化  

## 🎮 游戏特色

1. **闯江湖系统**
   - 点击按钮触发随机事件
   - 7种不同类型的事件（好运、NPC遭遇、采集、奇遇等）
   - 实时事件日志记录

2. **角色属性系统**
   - 气血、内力、体力、精力
   - 攻击、防御、声望、因果
   - 等级和经验值系统

3. **装备系统**
   - 武器、护甲、项链、手镯、坐骑
   - 5种品质等级（普通到神品）
   - 装备属性加成

4. **武功系统**
   - 5种武功类型（外功、内功、轻功、心法、特技）
   - 5种品级（黄级到绝世）
   - 修炼提升等级

5. **商店系统**
   - 装备商、药材铺、材料商
   - 购买和出售功能
   - 物品详情查看

## 🛠️ 如何运行

### 方法一：使用HBuilderX（推荐）

1. **下载HBuilderX**
   - 访问：https://www.dcloud.io/hbuilderx.html
   - 下载HBuilderX标准版或App开发版

2. **打开项目**
   - 打开HBuilderX
   - 文件 -> 打开目录 -> 选择项目文件夹 `D:\zjjhx\仗剑江湖行`

3. **运行项目**
   - 点击工具栏的"运行"按钮
   - 选择"运行到小程序模拟器" -> "微信开发者工具"

### 方法二：使用微信开发者工具

1. **打开微信开发者工具**
2. **选择"导入项目"**
3. **项目目录选择**：`D:\zjjhx\仗剑江湖行\unpackage\dist\dev\mp-weixin\`
4. **AppID**：填写你的小程序AppID（如果没有可以选择"测试号"）

### 方法三：使用命令行（需要Node.js环境）

1. **安装Node.js**
   - 访问：https://nodejs.org/
   - 下载并安装LTS版本

2. **安装依赖**
   ```bash
   npm install
   ```

3. **运行项目**
   ```bash
   npm run dev:mp-weixin
   ```

## 📱 测试建议

1. **基础功能测试**
   - 点击"闯江湖"按钮，查看事件触发
   - 检查属性条的变化
   - 查看事件日志记录

2. **背包系统测试**
   - 获得物品后查看背包
   - 尝试装备和卸下装备
   - 测试物品分类功能

3. **武功系统测试**
   - 修炼武功（需要精力）
   - 使用武功（需要内力）
   - 查看武功升级效果

4. **商店系统测试**
   - 购买商品（需要银两）
   - 出售物品获得银两
   - 切换不同商店类型

## 🎯 游戏玩法提示

1. **新手入门**
   - 先点击"闯江湖"获得初始资源
   - 在商店购买基础装备
   - 修炼武功提升实力

2. **进阶策略**
   - 合理分配精力修炼武功
   - 收集稀有装备提升属性
   - 管理好银两和物品

3. **注意事项**
   - 重伤状态无法闯江湖，需要疗伤
   - 精力不足无法修炼武功
   - 内力不足无法使用武功

## 🔧 技术特点

- **响应式设计**：适配不同屏幕尺寸
- **本地存储**：游戏数据自动保存
- **模块化架构**：易于扩展和维护
- **武侠风格UI**：渐变背景和卡片设计

## 📈 后续开发计划

- [ ] 门派系统
- [ ] 打造系统
- [ ] 市场交易
- [ ] 疗伤系统
- [ ] 更多武功和装备
- [ ] 成就系统
- [ ] 排行榜

## 🐛 已知问题

- TabBar图标需要自行添加（目前使用默认图标）
- 部分高级功能待开发
- 数据平衡性需要进一步调整

## ❗ 常见错误解决

### 错误：未找到app.json
**原因**：直接使用微信开发者工具打开uni-app项目根目录
**解决**：
1. 使用HBuilderX编译项目
2. 在微信开发者工具中打开编译后的目录：`unpackage/dist/dev/mp-weixin/`

### 错误：npm命令不存在
**原因**：系统未安装Node.js
**解决**：
1. 安装Node.js：https://nodejs.org/
2. 或直接使用HBuilderX（推荐）

### 错误：编译失败
**原因**：CSS语法错误或配置文件问题
**解决**：
1. 检查Vue文件语法
2. 清理编译缓存
3. 重新编译项目

## 💡 开发建议

1. **数据平衡**：根据测试反馈调整各种数值
2. **内容扩展**：添加更多武功、装备、事件
3. **用户体验**：优化界面交互和动画效果
4. **性能优化**：减少不必要的计算和渲染

## 🎯 项目结构

```
仗剑江湖行/
├── App.vue              # 应用入口
├── main.js              # 主入口
├── pages.json           # 页面配置
├── manifest.json        # 应用配置
├── uni.scss             # 全局样式
├── package.json         # 项目配置
├── pages/               # 页面目录
│   ├── login/           # 登录页面
│   ├── index/           # 首页
│   ├── backpack/        # 背包页面
│   ├── skills/          # 武功页面
│   ├── shop/            # 商店页面
│   └── guild/           # 门派页面
├── components/          # 组件目录
├── utils/               # 工具目录
├── static/              # 静态资源
└── backend/             # 后端代码
```

## 🎯 功能说明

### 底部导航栏
- **背包** - 物品管理和装备
- **武功** - 武功修炼、疗伤、打造
- **闯** - 闯江湖（主要功能，突出显示）
- **商店** - 购买物品和玩家交易
- **门派** - 门派相关功能

### 自定义TabBar特色
- **"闯"按钮突出显示**：使用红色渐变背景，更大的圆形按钮
- **渐变背景**：整个tabBar使用紫色渐变背景
- **动画效果**：点击时有缩放动画效果
- **选中状态**：当前页面对应的按钮会高亮显示

### 主要功能
- 用户登录和认证
- 角色属性和状态管理
- 武功修炼系统
- 物品和装备系统
- 商店和市场系统
- 地图切换功能
- 江湖事件系统

## 🎯 注意事项

1. **首次运行**：需要先启动后端服务器
2. **网络连接**：确保WebSocket连接正常
3. **数据同步**：登录后会自动同步游戏数据
4. **图标问题**：目前tabBar只显示文字，图标需要后续添加

## 🎯 常见问题

### Q: 编译失败怎么办？
A: 检查是否有语法错误，确保所有Vue文件格式正确

### Q: 无法连接服务器？
A: 检查后端服务是否启动，网络连接是否正常

### Q: 页面显示异常？
A: 清理编译缓存后重新编译

## 🎯 技术支持

如有问题，请检查：
1. 项目文件是否完整
2. 依赖是否正确安装
3. 编译环境是否正常
4. 微信开发者工具版本是否最新

---

**祝您游戏愉快！** 🎮 