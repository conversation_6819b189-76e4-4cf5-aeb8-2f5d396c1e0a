{"version": 3, "file": "gameState.js", "sources": ["utils/gameState.js"], "sourcesContent": ["/**\n * 游戏状态管理器\n * 负责管理前端游戏状态，与WebSocket通信配合\n */\n\nimport wsManager from './websocket.js'\n\nclass GameStateManager {\n\tconstructor() {\n\t\t// 游戏数据（从服务器获取）\n\t\tthis.player = null\n\t\tthis.inventory = []\n\t\tthis.equipment = {}\n\t\tthis.skills = []\n\t\tthis.eventLog = []\n\t\tthis.money = 0\n\t\tthis.gold = 0\n\t\tthis.status = 'normal'\n\t\t// 新增：物品配置缓存\n\t\tthis.itemsConfig = {} \n\t\t// 新增：地图配置缓存\n\t\tthis.mapsConfig = {} \n\t\t// 状态更新回调\n\t\tthis.updateCallbacks = []\n\t\t// 新增认证标志\n\t\tthis.isAuthed = false\n\t\t// 初始化WebSocket事件监听\n\t\tthis.initWebSocketHandlers()\n\t}\n\n\t/**\n\t * 初始化WebSocket事件处理器\n\t */\n\tinitWebSocketHandlers() {\n\t\t\n\t\t// 玩家数据更新\n\t\twsManager.on('player_data', (data) => {\n\t\t\tconsole.log('[玩家数据] 收到玩家数据更新:', data);\n\t\t\t// 确保正确处理各种数据格式\n\t\t\tif (data && typeof data === 'object') {\n\t\t\t\tif (data.data && typeof data.data === 'object') {\n\t\t\t\t\t// 处理 {data: {...}} 格式\n\t\t\t\t\tthis.player = data.data;\n\t\t\t\t} else {\n\t\t\t\t\t// 处理直接返回对象的格式\n\t\t\t\t\tthis.player = data;\n\t\t\t\t}\n\t\t\t\tconsole.log('[玩家数据] 更新后的玩家数据:', this.player);\n\t\t\t\tthis.notifyUpdate('player');\n\t\t\t} else {\n\t\t\t\tconsole.error('[玩家数据] 收到无效的玩家数据:', data);\n\t\t\t}\n\t\t});\n\n\t\t// 背包数据更新\n\t\twsManager.on('inventory_data', (data) => {\n\t\t\tconsole.log('[背包数据] 收到背包数据更新:', data);\n\t\t\t// 后端返回的数据格式是 {inventory: [...], capacity: 50}\n\t\t\t// 我们需要提取inventory数组\n\t\t\tif (data && data.inventory && Array.isArray(data.inventory)) {\n\t\t\t\tthis.inventory = data.inventory;\n\t\t\t} else if (Array.isArray(data)) {\n\t\t\t\t// 兼容直接返回数组的情况\n\t\t\t\tthis.inventory = data;\n\t\t\t} else if (data && typeof data === 'object') {\n\t\t\t\t// 尝试提取data字段\n\t\t\t\tif (data.data && Array.isArray(data.data)) {\n\t\t\t\t\tthis.inventory = data.data;\n\t\t\t\t} else {\n\t\t\t\t\tconsole.warn('[背包数据] 背包数据格式无法识别:', data);\n\t\t\t\t\tthis.inventory = [];\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconsole.warn('[背包数据] 背包数据格式错误:', data);\n\t\t\t\tthis.inventory = [];\n\t\t\t}\n\t\t\tconsole.log('[背包数据] 更新后的背包数据:', this.inventory);\n\t\t\tthis.notifyUpdate('inventory');\n\t\t});\n\n\t\t// 装备数据更新\n\t\twsManager.on('equipment_data', (data) => {\n\t\t\tconsole.log('[装备数据] 收到装备数据更新:', data);\n\t\t\tif (data && typeof data === 'object') {\n\t\t\t\tif (data.data && typeof data.data === 'object') {\n\t\t\t\t\t// 处理 {data: {...}} 格式\n\t\t\t\t\tthis.equipment = data.data;\n\t\t\t\t} else {\n\t\t\t\t\t// 处理直接返回对象的格式\n\t\t\t\t\tthis.equipment = data;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconsole.warn('[装备数据] 装备数据格式错误:', data);\n\t\t\t\tthis.equipment = {};\n\t\t\t}\n\t\t\tconsole.log('[装备数据] 更新后的装备数据:', this.equipment);\n\t\t\tthis.notifyUpdate('equipment');\n\t\t});\n\n\t\t// 武功数据更新\n\t\twsManager.on('skills_data', (data) => {\n\t\t\tconsole.log('[武功数据] 收到武功数据更新:', data);\n\t\t\tif (data && Array.isArray(data)) {\n\t\t\t\tthis.skills = data;\n\t\t\t} else if (data && typeof data === 'object' && data.data && Array.isArray(data.data)) {\n\t\t\t\tthis.skills = data.data;\n\t\t\t} else {\n\t\t\t\tconsole.warn('[武功数据] 武功数据格式错误:', data);\n\t\t\t\tthis.skills = [];\n\t\t\t}\n\t\t\tconsole.log('[武功数据] 更新后的武功数据:', this.skills);\n\t\t\tthis.notifyUpdate('skills');\n\t\t});\n\n\t\t// 事件日志更新\n\t\twsManager.on('event_log', (data) => {\n\t\t\tconsole.log('[事件日志] 收到事件日志更新:', data);\n\t\t\tif (data && Array.isArray(data)) {\n\t\t\t\tthis.eventLog = data;\n\t\t\t} else if (data && typeof data === 'object' && data.data && Array.isArray(data.data)) {\n\t\t\t\tthis.eventLog = data.data;\n\t\t\t} else {\n\t\t\t\tconsole.warn('[事件日志] 事件日志格式错误:', data);\n\t\t\t\tthis.eventLog = [];\n\t\t\t}\n\t\t\tconsole.log('[事件日志] 更新后的事件日志:', this.eventLog);\n\t\t\tthis.notifyUpdate('eventLog');\n\t\t});\n\n\t\t// 货币更新\n\t\twsManager.on('currency_update', (data) => {\n\t\t\tconsole.log('[货币更新] 收到货币更新:', data);\n\t\t\tif (data && typeof data === 'object') {\n\t\t\t\tthis.money = data.silver || data.money || this.money || 0;\n\t\t\t\tthis.gold = data.gold || this.gold || 0;\n\t\t\t\tconsole.log('[货币更新] 更新后的货币:', { money: this.money, gold: this.gold });\n\t\t\t\tthis.notifyUpdate('currency');\n\t\t\t} else {\n\t\t\t\tconsole.warn('[货币更新] 货币数据格式错误:', data);\n\t\t\t}\n\t\t});\n\n\t\t// 状态更新\n\t\twsManager.on('status_update', (data) => {\n\t\t\tconsole.log('[状态更新] 收到状态更新:', data);\n\t\t\tif (data && typeof data === 'object') {\n\t\t\t\tthis.status = data.status || this.status || 'normal';\n\t\t\t\tconsole.log('[状态更新] 更新后的状态:', this.status);\n\t\t\t\tthis.notifyUpdate('status');\n\t\t\t} else {\n\t\t\t\tconsole.warn('[状态更新] 状态数据格式错误:', data);\n\t\t\t}\n\t\t});\n\n\t\t// 体力值更新\n\t\twsManager.on('energy_update', (data) => {\n\t\t\tconsole.log('[体力更新] 收到体力更新:', data);\n\t\t\tif (data && typeof data === 'object' && this.player) {\n\t\t\t\tthis.player.energy = data.energy || this.player.energy || 0;\n\t\t\t\tthis.player.max_energy = data.max_energy || this.player.max_energy || 0;\n\t\t\t\tthis.player.energy_regen_rate = data.energy_regen_rate || this.player.energy_regen_rate || 0;\n\t\t\t\t// 接收后端计算的体力恢复详情\n\t\t\t\tthis.player.energy_regen_details = data.energy_regen_details || null;\n\t\t\t\tconsole.log('[体力更新] 更新后的体力:', { \n\t\t\t\t\tenergy: this.player.energy, \n\t\t\t\t\tmax_energy: this.player.max_energy,\n\t\t\t\t\tregen_rate: this.player.energy_regen_rate\n\t\t\t\t});\n\t\t\t\tthis.notifyUpdate('player');\n\t\t\t} else {\n\t\t\t\tconsole.warn('[体力更新] 体力数据格式错误或玩家数据不存在:', data);\n\t\t\t}\n\t\t});\n\n\t\t// 认证成功\n\t\twsManager.on('auth_success', (data) => {\n\t\t\tconsole.log('[认证成功] 收到认证成功:', data);\n\t\t\tthis.isAuthed = true;\n\t\t\t\n\t\t\t// 处理认证成功时返回的玩家数据\n\t\t\tif (data && typeof data === 'object') {\n\t\t\t\tif (data.player && typeof data.player === 'object') {\n\t\t\t\t\tthis.player = data.player;\n\t\t\t\t\tconsole.log('[认证成功] 更新玩家数据:', this.player);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (data.userInfo && typeof data.userInfo === 'object') {\n\t\t\t\t\t// 如果认证响应中包含userInfo，也尝试更新玩家数据\n\t\t\t\t\tif (!this.player) this.player = {};\n\t\t\t\t\t\n\t\t\t\t\t// 合并userInfo中的关键字段到player\n\t\t\t\t\tconst userInfo = data.userInfo;\n\t\t\t\t\tthis.player.username = userInfo.username || this.player.username;\n\t\t\t\t\tthis.player.characterName = userInfo.characterName || this.player.characterName;\n\t\t\t\t\tthis.player.userId = userInfo.userId || this.player.userId;\n\t\t\t\t\tthis.player.gender = userInfo.gender || this.player.gender;\n\t\t\t\t\t\n\t\t\t\t\t// 如果有talent数据，也更新\n\t\t\t\t\tif (userInfo.talent && typeof userInfo.talent === 'object') {\n\t\t\t\t\t\tthis.player.talent = userInfo.talent;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('[认证成功] 从userInfo更新玩家数据:', this.player);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理货币数据\n\t\t\t\tif (data.money !== undefined) this.money = data.money;\n\t\t\t\tif (data.silver !== undefined) this.money = data.silver;\n\t\t\t\tif (data.gold !== undefined) this.gold = data.gold;\n\t\t\t}\n\t\t\t\n\t\t\t// 通知所有关键状态更新\n\t\t\tthis.notifyUpdate('player');\n\t\t\tthis.notifyUpdate('currency');\n\t\t\tthis.notifyUpdate('status');\n\t\t\tthis.notifyUpdate('auth');\n\t\t\t\n\t\t\t// 认证成功后，主动请求完整的玩家数据\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.requestAllData();\n\t\t\t}, 500);\n\t\t});\n\n\t\t// 认证失败\n\t\twsManager.on('auth_failed', (data) => {\n\t\t\tconsole.log('[认证失败] 认证失败:', data);\n\t\t\tthis.isAuthed = false;\n\t\t\tthis.notifyUpdate('auth');\n\t\t});\n\n\t\t// 登录成功\n\t\twsManager.on('login_success', (data) => {\n\t\t\tconsole.log('[登录成功] 收到登录成功:', data);\n\t\t\tthis.isAuthed = true;\n\t\t\t\n\t\t\t// 处理登录成功时返回的玩家数据\n\t\t\tif (data && typeof data === 'object') {\n\t\t\t\tif (data.player && typeof data.player === 'object') {\n\t\t\t\t\tthis.player = data.player;\n\t\t\t\t\tconsole.log('[登录成功] 更新玩家数据:', this.player);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (data.userInfo && typeof data.userInfo === 'object') {\n\t\t\t\t\t// 如果登录响应中包含userInfo，也尝试更新玩家数据\n\t\t\t\t\tif (!this.player) this.player = {};\n\t\t\t\t\t\n\t\t\t\t\t// 合并userInfo中的关键字段到player\n\t\t\t\t\tconst userInfo = data.userInfo;\n\t\t\t\t\tthis.player.username = userInfo.username || this.player.username;\n\t\t\t\t\tthis.player.characterName = userInfo.characterName || this.player.characterName;\n\t\t\t\t\tthis.player.userId = userInfo.userId || this.player.userId;\n\t\t\t\t\tthis.player.gender = userInfo.gender || this.player.gender;\n\t\t\t\t\t\n\t\t\t\t\t// 如果有talent数据，也更新\n\t\t\t\t\tif (userInfo.talent && typeof userInfo.talent === 'object') {\n\t\t\t\t\t\tthis.player.talent = userInfo.talent;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('[登录成功] 从userInfo更新玩家数据:', this.player);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理货币数据\n\t\t\t\tif (data.money !== undefined) this.money = data.money;\n\t\t\t\tif (data.silver !== undefined) this.money = data.silver;\n\t\t\t\tif (data.gold !== undefined) this.gold = data.gold;\n\t\t\t}\n\t\t\t\n\t\t\t// 通知所有关键状态更新\n\t\t\tthis.notifyUpdate('player');\n\t\t\tthis.notifyUpdate('currency');\n\t\t\tthis.notifyUpdate('status');\n\t\t\tthis.notifyUpdate('auth');\n\t\t\t\n\t\t\t// 登录成功后，主动请求完整的玩家数据\n\t\t\tsetTimeout(() => {\n\t\t\t\tthis.requestAllData();\n\t\t\t}, 500);\n\t\t});\n\n\t\t// 错误消息\n\t\twsManager.on('error', (data) => {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '操作失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t})\n\n\t\t// 成功消息\n\t\twsManager.on('success', (data) => {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: data.message || '操作成功',\n\t\t\t\ticon: 'success'\n\t\t\t})\n\t\t})\n\t\t\n\t\t// 监听连接状态变化\n\t\twsManager.on('connected', () => {\n\t\t\t// WebSocket连接已建立\n\t\t})\n\t\t\n\t\twsManager.on('disconnected', () => {\n\t\t\t// WebSocket连接已断开\n\t\t})\n\t}\n\n\t/**\n\t * 处理游戏事件\n\t */\n\thandleGameEvent(data) {\n\t\tconsole.log('[GameState] 处理游戏事件:', data);\n\t\t\n\t\t// 兼容不同的数据格式\n\t\tconst eventType = data.type || data.eventType || 5;\n\t\tconst content = data.content || data.description || '你遇到了一个江湖事件';\n\t\tconst rewards = data.rewards || {};\n\t\tconst realm_breakthrough = data.realm_breakthrough || null;\n\n\t\t// 追加到事件日志\n\t\tconst logEntry = {\n\t\t\ttimestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),\n\t\t\tname: this.getEventTypeName(eventType),\n\t\t\tdescription: content\n\t\t};\n\t\t\n\t\tconsole.log('[GameState] 创建日志条目:', logEntry);\n\t\tthis.eventLog.unshift(logEntry);\n\t\t\n\t\t// 最多保留50条\n\t\tif (this.eventLog.length > 50) this.eventLog = this.eventLog.slice(0, 50);\n\t\t\n\t\t// 通知更新\n\t\tconsole.log('[GameState] 通知eventLog更新, 当前日志条目数:', this.eventLog.length);\n\t\tthis.notifyUpdate('eventLog');\n\n\t\t// 处理境界突破\n\t\tif (realm_breakthrough) {\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '境界突破',\n\t\t\t\t\tcontent: realm_breakthrough.message,\n\t\t\t\t\tshowCancel: false,\n\t\t\t\t\tconfirmText: '确定'\n\t\t\t\t});\n\t\t\t}, 500);\n\t\t}\n\n\t\t// 彻底去除奖励弹窗逻辑\n\t\t// if (rewards && Object.keys(rewards).length > 0) {\n\t\t// \tsetTimeout(() => {\n\t\t// \t\tlet rewardText = '获得奖励：\\n'\n\t\t// \t\tfor (const [key, value] of Object.entries(rewards)) {\n\t\t// \t\t\trewardText += `${key}: ${value}\\n`\n\t\t// \t\t}\n\t\t// \tuni.showModal({\n\t\t// \t\ttitle: '获得奖励',\n\t\t// \t\tcontent: rewardText,\n\t\t// \t\tshowCancel: false,\n\t\t// \t\tconfirmText: '确定'\n\t\t// \t})\n\t\t// }, 500)\n\t\t// }\n\t}\n\t\n\t/**\n\t * 获取事件类型名称\n\t */\n\tgetEventTypeName(eventType) {\n\t\tconst eventTypeMap = {\n\t\t\t1: '好运事件',\n\t\t\t2: '遭遇NPC',\n\t\t\t3: '采集事件',\n\t\t\t4: '空事件',\n\t\t\t5: '奇遇事件',\n\t\t\t6: '恩怨事件',\n\t\t\t7: '组队事件',\n\t\t\t8: '商队事件',\n\t\t\t9: '江湖传闻',\n\t\t\t10: '天气事件',\n\t\t\t11: '神秘事件',\n\t\t\t12: '节日事件'\n\t\t};\n\t\t\n\t\treturn eventTypeMap[eventType] || '江湖事件';\n\t}\n\n\t/**\n\t * 注册状态更新回调\n\t */\n\tonUpdate(callback) {\n\t\tthis.updateCallbacks.push(callback)\n\t}\n\n\t/**\n\t * 移除状态更新回调\n\t */\n\toffUpdate(callback) {\n\t\tconst index = this.updateCallbacks.indexOf(callback)\n\t\tif (index > -1) {\n\t\t\tthis.updateCallbacks.splice(index, 1)\n\t\t}\n\t}\n\n\t/**\n\t * 通知状态更新\n\t */\n\tnotifyUpdate(type) {\n\t\tthis.updateCallbacks.forEach((callback, index) => {\n\t\t\ttry {\n\t\t\t\tcallback(type, this)\n\t\t\t} catch (error) {\n\t\t\t\t// 状态更新回调执行失败\n\t\t\t}\n\t\t})\n\t}\n\n\t/**\n\t * 初始化游戏状态\n\t */\n\tasync init() {\n\t\ttry {\n\t\t\tconsole.log('开始初始化游戏状态...');\n\t\t\t\n\t\t\t// 连接WebSocket\n\t\t\tconsole.log('连接WebSocket...');\n\t\t\tawait wsManager.connect();\n\t\t\tconsole.log('WebSocket连接成功，等待自动认证...');\n\t\t\t\n\t\t\t// 认证消息会在WebSocket连接成功后自动发送\n\t\t\t// 不需要在这里重复发送\n\t\t\t\n\t\t} catch (error) {\n\t\t\tconsole.error('游戏初始化失败:', error)\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '连接服务器失败',\n\t\t\t\ticon: 'none'\n\t\t\t})\n\t\t}\n\t}\n\n\t/**\n\t * 请求所有游戏数据\n\t */\n\trequestAllData() {\n\t\tconsole.log('[gameState] 请求所有游戏数据');\n\t\t\n\t\t// 确保WebSocket连接已建立\n\t\tif (!wsManager.isConnected) {\n\t\t\tconsole.log('[gameState] WebSocket未连接，尝试连接...');\n\t\t\twsManager.connect().then(() => {\n\t\t\t\tconsole.log('[gameState] WebSocket连接成功，发送数据请求');\n\t\t\t\tthis.sendDataRequests();\n\t\t\t}).catch(error => {\n\t\t\t\tconsole.error('[gameState] WebSocket连接失败:', error);\n\t\t\t});\n\t\t} else {\n\t\t\tconsole.log('[gameState] WebSocket已连接，直接发送数据请求');\n\t\t\tthis.sendDataRequests();\n\t\t}\n\t}\n\t\n\t/**\n\t * 发送所有数据请求\n\t */\n\tsendDataRequests() {\n\t\tconsole.log('[gameState] 发送所有数据请求');\n\t\t\n\t\t// 请求玩家数据\n\t\twsManager.sendMessage('get_player_data');\n\t\tconsole.log('[gameState] 已发送玩家数据请求');\n\t\t\n\t\t// 请求背包数据\n\t\tsetTimeout(() => {\n\t\t\twsManager.sendMessage('get_inventory_data');\n\t\t\tconsole.log('[gameState] 已发送背包数据请求');\n\t\t}, 200);\n\t\t\n\t\t// 请求装备数据 - 使用特殊处理方式\n\t\tsetTimeout(() => {\n\t\t\t// 使用gameUtils.sendMessage而不是直接使用wsManager.sendMessage\n\t\t\t// 这样可以利用gameUtils.sendMessage中对get_equipment_data的特殊处理\n\t\t\tconst { gameUtils } = require('./gameData.js');\n\t\t\tif (gameUtils && gameUtils.sendMessage) {\n\t\t\t\tgameUtils.sendMessage({\n\t\t\t\t\ttype: 'get_equipment_data',\n\t\t\t\t\tdata: {}\n\t\t\t\t}).then(response => {\n\t\t\t\t\tconsole.log('[gameState] 装备数据响应:', response);\n\t\t\t\t\t// 手动触发装备数据处理\n\t\t\t\t\tif (response.type === 'equipment_data' && response.data) {\n\t\t\t\t\t\tthis.equipment = response.data;\n\t\t\t\t\t\tthis.notifyUpdate('equipment');\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('[gameState] 获取装备数据失败:', error);\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tconsole.log('[gameState] gameUtils不可用，使用备用方式请求装备数据');\n\t\t\t\twsManager.sendMessage('get_player_data');\n\t\t\t}\n\t\t\tconsole.log('[gameState] 已发送装备数据请求');\n\t\t}, 400);\n\t\t\n\t\t// 请求武功数据\n\t\tsetTimeout(() => {\n\t\t\twsManager.sendMessage('get_skills_data');\n\t\t\tconsole.log('[gameState] 已发送武功数据请求');\n\t\t}, 600);\n\t\t\n\t\t// 请求地图配置\n\t\tsetTimeout(() => {\n\t\t\tthis.requestMapsConfig();\n\t\t\tconsole.log('[gameState] 已发送地图配置请求');\n\t\t}, 800);\n\t\t\n\t\t// 请求物品配置\n\t\tsetTimeout(() => {\n\t\t\tthis.requestItemsConfig();\n\t\t\tconsole.log('[gameState] 已发送物品配置请求');\n\t\t}, 1000);\n\t}\n\n\t/**\n\t * 闯江湖\n\t */\n\ttriggerAdventure() {\n\t\tconsole.log('=== GameState.triggerAdventure() 被调用 ===');\n\t\tconsole.log('当前时间:', new Date().toLocaleString());\n\t\tconsole.log('当前认证状态:', this.isAuthed);\n\t\tconsole.log('WebSocket认证状态:', wsManager.isAuthed);\n\t\tconsole.log('WebSocket连接状态:', wsManager.isConnected);\n\t\tconsole.log('WebSocket服务器地址:', wsManager.serverUrl);\n\t\t\n\t\t// 优先使用WebSocket管理器的认证状态\n\t\tconst isAuthenticated = wsManager.isAuthed || this.isAuthed;\n\t\t\n\t\tif (!isAuthenticated) {\n\t\t\tconsole.log('❌ GameState: 未认证，显示提示');\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' })\n\t\t\treturn\n\t\t}\n\t\t\n\t\tif (!wsManager.isConnected) {\n\t\t\tconsole.log('❌ GameState: WebSocket未连接，显示提示');\n\t\t\tuni.showToast({ title: '网络连接失败', icon: 'none' })\n\t\t\treturn\n\t\t}\n\t\t\n\t\tconsole.log('✅ GameState: 发送闯江湖请求');\n\t\twsManager.sendAdventureRequest()\n\t\tconsole.log('✅ GameState: 闯江湖请求已发送');\n\t}\n\n\t/**\n\t * 打坐\n\t */\n\tmeditate() {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendMeditationRequest();\n\t}\n\n\t/**\n\t * 疗伤\n\t */\n\theal() {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendHealingRequest();\n\t}\n\n\t/**\n\t * 装备操作\n\t */\n\tequipItem(itemId) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendEquipmentAction('equip', itemId);\n\t}\n\n\tunequipItem(slot) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendEquipmentAction('unequip', slot);\n\t}\n\n\t/**\n\t * 武功操作\n\t */\n\tlearnSkill(skillId) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendSkillAction('learn', skillId);\n\t}\n\n\tpracticeSkill(skillId) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendSkillAction('practice', skillId);\n\t}\n\n\t/**\n\t * 商店操作\n\t */\n\tbuyItem(itemId, quantity = 1, npcId = '') {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\tconst data = { item_id: itemId, quantity };\n\t\tif (npcId) data.npc_id = npcId;\n\t\twsManager.sendShopAction('buy', data);\n\t}\n\n\tsellItem(itemId, quantity = 1) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendShopAction('sell', { itemId, quantity });\n\t}\n\n\t/**\n\t * 市场操作\n\t */\n\tlistItem(itemId, price, quantity = 1) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendMarketAction('list', { itemId, price, quantity });\n\t}\n\n\tbuyMarketItem(itemId) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendMarketAction('buy', { itemId });\n\t}\n\n\tcancelListing(itemId) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendMarketAction('cancel', { itemId });\n\t}\n\n\t/**\n\t * 门派操作\n\t */\n\tjoinGuild(guildId) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendGuildAction('join', { guildId });\n\t}\n\n\tacceptGuildTask(taskId) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendGuildAction('accept_task', { taskId });\n\t}\n\n\tlearnGuildSkill(skillId) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendGuildAction('learn_skill', { skillId });\n\t}\n\n\t/**\n\t * 打造操作\n\t */\n\tcraftItem(recipeId) {\n\t\tif (!this.isAuthed) {\n\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\treturn;\n\t\t}\n\t\twsManager.sendCraftingAction('craft', { recipeId });\n\t}\n\n\t/**\n\t * 获取玩家数据\n\t */\n\tgetPlayer() {\n\t\treturn this.player\n\t}\n\n\t/**\n\t * 获取背包数据\n\t */\n\tgetInventory() {\n\t\treturn this.inventory\n\t}\n\n\t/**\n\t * 获取装备数据\n\t */\n\tgetEquipment() {\n\t\treturn this.equipment\n\t}\n\n\t/**\n\t * 获取武功数据\n\t */\n\tgetSkills() {\n\t\treturn this.skills\n\t}\n\n\t/**\n\t * 获取事件日志\n\t */\n\tgetEventLog() {\n\t\treturn this.eventLog\n\t}\n\n\t/**\n\t * 获取货币数据\n\t */\n\tgetCurrency() {\n\t\treturn {\n\t\t\tsilver: this.money,\n\t\t\tgold: this.gold\n\t\t}\n\t}\n\n\t/**\n\t * 获取状态\n\t */\n\tgetStatus() {\n\t\treturn this.status\n\t}\n\n\t/**\n\t * 断开连接\n\t */\n\tdisconnect() {\n\t\twsManager.disconnect()\n\t}\n\n\tgetPlayerData() {\n\t\treturn this.player;\n\t}\n\n\t/**\n\t * 设置玩家数据并通知更新\n\t */\n\tsetPlayerData(data) {\n\t\tthis.player = data;\n\t\tthis.notifyUpdate('player');\n\t}\n\n\tupdateMoney() {\n\t\t// 优先从player对象获取最新银两\n\t\tif (this.player && (typeof this.player.money === 'number')) {\n\t\t\tthis.money = this.player.money\n\t\t} else if (this.money) {\n\t\t\tthis.money = this.money\n\t\t}\n\t}\n\n\tupdateData() {\n\t\tthis.myItems = [...this.inventory]\n\t}\n\n\t/**\n\t * 新增：请求物品配置（结构化JSON）\n\t */\n\trequestItemsConfig() {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\twsManager.sendMessage('get_items_config', {})\n\t\t\twsManager.on('items_config', (data) => {\n\t\t\t\tthis.itemsConfig = data || {}\n\t\t\t\tthis.notifyUpdate('itemsConfig')\n\t\t\t\tresolve(this.itemsConfig)\n\t\t\t})\n\t\t\t// 可加超时处理\n\t\t})\n\t}\n\n\t/**\n\t * 获取物品配置（如未加载则自动请求）\n\t */\n\tasync getItemsConfig() {\n\t\tif (Object.keys(this.itemsConfig).length === 0) {\n\t\t\tawait this.requestItemsConfig()\n\t\t}\n\t\treturn this.itemsConfig\n\t}\n\n\t/**\n\t * 新增：请求地图配置（结构化JSON）\n\t */\n\trequestMapsConfig() {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tconsole.log('[地图配置] 开始请求地图配置...');\n\t\t\twsManager.sendMessage('get_maps_config', {})\n\t\t\twsManager.on('maps_config', (response) => {\n\t\t\t\tconsole.log('[地图配置] 收到地图配置响应:', response);\n\t\t\t\tthis.mapsConfig = {};\n\t\t\t\t// 后端返回格式：{'type': 'maps_config', 'data': ...}\n\t\t\t\tconst data = response.data || response;\n\t\t\t\tconsole.log('[地图配置] 解析后的数据:', data);\n\t\t\t\tif (Array.isArray(data)) {\n\t\t\t\t\tfor (const map of data) {\n\t\t\t\t\t\tif (map.id) {\n\t\t\t\t\t\t\tthis.mapsConfig[map.id] = map;\n\t\t\t\t\t\t\tconsole.log(`[地图配置] 添加地图: ${map.id} -> ${map.名称 || map.name}`);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else if (typeof data === 'object' && data !== null) {\n\t\t\t\t\t// 兼容对象格式\n\t\t\t\t\tfor (const [id, map] of Object.entries(data)) {\n\t\t\t\t\t\tif (map && map.id) {\n\t\t\t\t\t\t\tthis.mapsConfig[map.id] = map;\n\t\t\t\t\t\t\tconsole.log(`[地图配置] 添加地图: ${map.id} -> ${map.名称 || map.name}`);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tconsole.log('[地图配置] 最终地图配置:', this.mapsConfig);\n\t\t\t\tthis.notifyUpdate('mapsConfig')\n\t\t\t\tresolve(this.mapsConfig)\n\t\t\t})\n\t\t\t// 可加超时处理\n\t\t})\n\t}\n\n\t/**\n\t * 获取地图配置（如未加载则自动请求）\n\t */\n\tasync getMapsConfig() {\n\t\tif (Object.keys(this.mapsConfig).length === 0) {\n\t\t\tawait this.requestMapsConfig()\n\t\t}\n\t\treturn this.mapsConfig\n\t}\n}\n\n// 创建单例实例\nconst gameState = new GameStateManager()\n\nexport default gameState "], "names": ["wsManager", "uni"], "mappings": ";;;AAOA,MAAM,iBAAiB;AAAA,EACtB,cAAc;AAEb,SAAK,SAAS;AACd,SAAK,YAAY,CAAE;AACnB,SAAK,YAAY,CAAE;AACnB,SAAK,SAAS,CAAE;AAChB,SAAK,WAAW,CAAE;AAClB,SAAK,QAAQ;AACb,SAAK,OAAO;AACZ,SAAK,SAAS;AAEd,SAAK,cAAc,CAAE;AAErB,SAAK,aAAa,CAAE;AAEpB,SAAK,kBAAkB,CAAE;AAEzB,SAAK,WAAW;AAEhB,SAAK,sBAAuB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKD,wBAAwB;AAGvBA,oBAAAA,UAAU,GAAG,eAAe,CAAC,SAAS;AACrCC,oBAAY,MAAA,MAAA,OAAA,4BAAA,oBAAoB,IAAI;AAEpC,UAAI,QAAQ,OAAO,SAAS,UAAU;AACrC,YAAI,KAAK,QAAQ,OAAO,KAAK,SAAS,UAAU;AAE/C,eAAK,SAAS,KAAK;AAAA,QACxB,OAAW;AAEN,eAAK,SAAS;AAAA,QACd;AACDA,sBAAY,MAAA,MAAA,OAAA,4BAAA,oBAAoB,KAAK,MAAM;AAC3C,aAAK,aAAa,QAAQ;AAAA,MAC9B,OAAU;AACNA,sBAAA,MAAA,MAAA,SAAA,4BAAc,qBAAqB,IAAI;AAAA,MACvC;AAAA,IACJ,CAAG;AAGDD,oBAAAA,UAAU,GAAG,kBAAkB,CAAC,SAAS;AACxCC,oBAAY,MAAA,MAAA,OAAA,4BAAA,oBAAoB,IAAI;AAGpC,UAAI,QAAQ,KAAK,aAAa,MAAM,QAAQ,KAAK,SAAS,GAAG;AAC5D,aAAK,YAAY,KAAK;AAAA,MACtB,WAAU,MAAM,QAAQ,IAAI,GAAG;AAE/B,aAAK,YAAY;AAAA,MACjB,WAAU,QAAQ,OAAO,SAAS,UAAU;AAE5C,YAAI,KAAK,QAAQ,MAAM,QAAQ,KAAK,IAAI,GAAG;AAC1C,eAAK,YAAY,KAAK;AAAA,QAC3B,OAAW;AACNA,wEAAa,sBAAsB,IAAI;AACvC,eAAK,YAAY;QACjB;AAAA,MACL,OAAU;AACNA,sBAAA,MAAA,MAAA,QAAA,4BAAa,oBAAoB,IAAI;AACrC,aAAK,YAAY;MACjB;AACDA,oBAAA,MAAA,MAAA,OAAA,4BAAY,oBAAoB,KAAK,SAAS;AAC9C,WAAK,aAAa,WAAW;AAAA,IAChC,CAAG;AAGDD,oBAAAA,UAAU,GAAG,kBAAkB,CAAC,SAAS;AACxCC,oBAAY,MAAA,MAAA,OAAA,4BAAA,oBAAoB,IAAI;AACpC,UAAI,QAAQ,OAAO,SAAS,UAAU;AACrC,YAAI,KAAK,QAAQ,OAAO,KAAK,SAAS,UAAU;AAE/C,eAAK,YAAY,KAAK;AAAA,QAC3B,OAAW;AAEN,eAAK,YAAY;AAAA,QACjB;AAAA,MACL,OAAU;AACNA,sBAAA,MAAA,MAAA,QAAA,4BAAa,oBAAoB,IAAI;AACrC,aAAK,YAAY;MACjB;AACDA,oBAAA,MAAA,MAAA,OAAA,4BAAY,oBAAoB,KAAK,SAAS;AAC9C,WAAK,aAAa,WAAW;AAAA,IAChC,CAAG;AAGDD,oBAAAA,UAAU,GAAG,eAAe,CAAC,SAAS;AACrCC,oBAAY,MAAA,MAAA,OAAA,6BAAA,oBAAoB,IAAI;AACpC,UAAI,QAAQ,MAAM,QAAQ,IAAI,GAAG;AAChC,aAAK,SAAS;AAAA,MACd,WAAU,QAAQ,OAAO,SAAS,YAAY,KAAK,QAAQ,MAAM,QAAQ,KAAK,IAAI,GAAG;AACrF,aAAK,SAAS,KAAK;AAAA,MACvB,OAAU;AACNA,sBAAA,MAAA,MAAA,QAAA,6BAAa,oBAAoB,IAAI;AACrC,aAAK,SAAS;MACd;AACDA,oBAAY,MAAA,MAAA,OAAA,6BAAA,oBAAoB,KAAK,MAAM;AAC3C,WAAK,aAAa,QAAQ;AAAA,IAC7B,CAAG;AAGDD,oBAAAA,UAAU,GAAG,aAAa,CAAC,SAAS;AACnCC,oBAAY,MAAA,MAAA,OAAA,6BAAA,oBAAoB,IAAI;AACpC,UAAI,QAAQ,MAAM,QAAQ,IAAI,GAAG;AAChC,aAAK,WAAW;AAAA,MAChB,WAAU,QAAQ,OAAO,SAAS,YAAY,KAAK,QAAQ,MAAM,QAAQ,KAAK,IAAI,GAAG;AACrF,aAAK,WAAW,KAAK;AAAA,MACzB,OAAU;AACNA,sBAAA,MAAA,MAAA,QAAA,6BAAa,oBAAoB,IAAI;AACrC,aAAK,WAAW;MAChB;AACDA,oBAAA,MAAA,MAAA,OAAA,6BAAY,oBAAoB,KAAK,QAAQ;AAC7C,WAAK,aAAa,UAAU;AAAA,IAC/B,CAAG;AAGDD,oBAAAA,UAAU,GAAG,mBAAmB,CAAC,SAAS;AACzCC,oBAAA,MAAA,MAAA,OAAA,6BAAY,kBAAkB,IAAI;AAClC,UAAI,QAAQ,OAAO,SAAS,UAAU;AACrC,aAAK,QAAQ,KAAK,UAAU,KAAK,SAAS,KAAK,SAAS;AACxD,aAAK,OAAO,KAAK,QAAQ,KAAK,QAAQ;AACtCA,sBAAAA,MAAA,MAAA,OAAA,6BAAY,kBAAkB,EAAE,OAAO,KAAK,OAAO,MAAM,KAAK,KAAM,CAAA;AACpE,aAAK,aAAa,UAAU;AAAA,MAChC,OAAU;AACNA,sBAAA,MAAA,MAAA,QAAA,6BAAa,oBAAoB,IAAI;AAAA,MACrC;AAAA,IACJ,CAAG;AAGDD,oBAAAA,UAAU,GAAG,iBAAiB,CAAC,SAAS;AACvCC,oBAAA,MAAA,MAAA,OAAA,6BAAY,kBAAkB,IAAI;AAClC,UAAI,QAAQ,OAAO,SAAS,UAAU;AACrC,aAAK,SAAS,KAAK,UAAU,KAAK,UAAU;AAC5CA,sBAAA,MAAA,MAAA,OAAA,6BAAY,kBAAkB,KAAK,MAAM;AACzC,aAAK,aAAa,QAAQ;AAAA,MAC9B,OAAU;AACNA,sBAAA,MAAA,MAAA,QAAA,6BAAa,oBAAoB,IAAI;AAAA,MACrC;AAAA,IACJ,CAAG;AAGDD,oBAAAA,UAAU,GAAG,iBAAiB,CAAC,SAAS;AACvCC,oBAAA,MAAA,MAAA,OAAA,6BAAY,kBAAkB,IAAI;AAClC,UAAI,QAAQ,OAAO,SAAS,YAAY,KAAK,QAAQ;AACpD,aAAK,OAAO,SAAS,KAAK,UAAU,KAAK,OAAO,UAAU;AAC1D,aAAK,OAAO,aAAa,KAAK,cAAc,KAAK,OAAO,cAAc;AACtE,aAAK,OAAO,oBAAoB,KAAK,qBAAqB,KAAK,OAAO,qBAAqB;AAE3F,aAAK,OAAO,uBAAuB,KAAK,wBAAwB;AAChEA,sBAAAA,MAAA,MAAA,OAAA,6BAAY,kBAAkB;AAAA,UAC7B,QAAQ,KAAK,OAAO;AAAA,UACpB,YAAY,KAAK,OAAO;AAAA,UACxB,YAAY,KAAK,OAAO;AAAA,QAC7B,CAAK;AACD,aAAK,aAAa,QAAQ;AAAA,MAC9B,OAAU;AACNA,sBAAa,MAAA,MAAA,QAAA,6BAAA,4BAA4B,IAAI;AAAA,MAC7C;AAAA,IACJ,CAAG;AAGDD,oBAAAA,UAAU,GAAG,gBAAgB,CAAC,SAAS;AACtCC,oBAAA,MAAA,MAAA,OAAA,6BAAY,kBAAkB,IAAI;AAClC,WAAK,WAAW;AAGhB,UAAI,QAAQ,OAAO,SAAS,UAAU;AACrC,YAAI,KAAK,UAAU,OAAO,KAAK,WAAW,UAAU;AACnD,eAAK,SAAS,KAAK;AACnBA,wEAAY,kBAAkB,KAAK,MAAM;AAAA,QACzC;AAED,YAAI,KAAK,YAAY,OAAO,KAAK,aAAa,UAAU;AAEvD,cAAI,CAAC,KAAK;AAAQ,iBAAK,SAAS,CAAA;AAGhC,gBAAM,WAAW,KAAK;AACtB,eAAK,OAAO,WAAW,SAAS,YAAY,KAAK,OAAO;AACxD,eAAK,OAAO,gBAAgB,SAAS,iBAAiB,KAAK,OAAO;AAClE,eAAK,OAAO,SAAS,SAAS,UAAU,KAAK,OAAO;AACpD,eAAK,OAAO,SAAS,SAAS,UAAU,KAAK,OAAO;AAGpD,cAAI,SAAS,UAAU,OAAO,SAAS,WAAW,UAAU;AAC3D,iBAAK,OAAO,SAAS,SAAS;AAAA,UAC9B;AAEDA,wEAAY,2BAA2B,KAAK,MAAM;AAAA,QAClD;AAGD,YAAI,KAAK,UAAU;AAAW,eAAK,QAAQ,KAAK;AAChD,YAAI,KAAK,WAAW;AAAW,eAAK,QAAQ,KAAK;AACjD,YAAI,KAAK,SAAS;AAAW,eAAK,OAAO,KAAK;AAAA,MAC9C;AAGD,WAAK,aAAa,QAAQ;AAC1B,WAAK,aAAa,UAAU;AAC5B,WAAK,aAAa,QAAQ;AAC1B,WAAK,aAAa,MAAM;AAGxB,iBAAW,MAAM;AAChB,aAAK,eAAc;AAAA,MACnB,GAAE,GAAG;AAAA,IACT,CAAG;AAGDD,oBAAAA,UAAU,GAAG,eAAe,CAAC,SAAS;AACrCC,oBAAA,MAAA,MAAA,OAAA,6BAAY,gBAAgB,IAAI;AAChC,WAAK,WAAW;AAChB,WAAK,aAAa,MAAM;AAAA,IAC3B,CAAG;AAGDD,oBAAAA,UAAU,GAAG,iBAAiB,CAAC,SAAS;AACvCC,oBAAA,MAAA,MAAA,OAAA,6BAAY,kBAAkB,IAAI;AAClC,WAAK,WAAW;AAGhB,UAAI,QAAQ,OAAO,SAAS,UAAU;AACrC,YAAI,KAAK,UAAU,OAAO,KAAK,WAAW,UAAU;AACnD,eAAK,SAAS,KAAK;AACnBA,wEAAY,kBAAkB,KAAK,MAAM;AAAA,QACzC;AAED,YAAI,KAAK,YAAY,OAAO,KAAK,aAAa,UAAU;AAEvD,cAAI,CAAC,KAAK;AAAQ,iBAAK,SAAS,CAAA;AAGhC,gBAAM,WAAW,KAAK;AACtB,eAAK,OAAO,WAAW,SAAS,YAAY,KAAK,OAAO;AACxD,eAAK,OAAO,gBAAgB,SAAS,iBAAiB,KAAK,OAAO;AAClE,eAAK,OAAO,SAAS,SAAS,UAAU,KAAK,OAAO;AACpD,eAAK,OAAO,SAAS,SAAS,UAAU,KAAK,OAAO;AAGpD,cAAI,SAAS,UAAU,OAAO,SAAS,WAAW,UAAU;AAC3D,iBAAK,OAAO,SAAS,SAAS;AAAA,UAC9B;AAEDA,wEAAY,2BAA2B,KAAK,MAAM;AAAA,QAClD;AAGD,YAAI,KAAK,UAAU;AAAW,eAAK,QAAQ,KAAK;AAChD,YAAI,KAAK,WAAW;AAAW,eAAK,QAAQ,KAAK;AACjD,YAAI,KAAK,SAAS;AAAW,eAAK,OAAO,KAAK;AAAA,MAC9C;AAGD,WAAK,aAAa,QAAQ;AAC1B,WAAK,aAAa,UAAU;AAC5B,WAAK,aAAa,QAAQ;AAC1B,WAAK,aAAa,MAAM;AAGxB,iBAAW,MAAM;AAChB,aAAK,eAAc;AAAA,MACnB,GAAE,GAAG;AAAA,IACT,CAAG;AAGDD,oBAAAA,UAAU,GAAG,SAAS,CAAC,SAAS;AAC/BC,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACV,CAAI;AAAA,IACJ,CAAG;AAGDD,oBAAAA,UAAU,GAAG,WAAW,CAAC,SAAS;AACjCC,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACV,CAAI;AAAA,IACJ,CAAG;AAGDD,8BAAU,GAAG,aAAa,MAAM;AAAA,IAElC,CAAG;AAEDA,8BAAU,GAAG,gBAAgB,MAAM;AAAA,IAErC,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB,MAAM;AACrBC,kBAAA,MAAA,MAAA,OAAA,6BAAY,uBAAuB,IAAI;AAGvC,UAAM,YAAY,KAAK,QAAQ,KAAK,aAAa;AACjD,UAAM,UAAU,KAAK,WAAW,KAAK,eAAe;AACpC,SAAK,WAAW,CAAG;AACnC,UAAM,qBAAqB,KAAK,sBAAsB;AAGtD,UAAM,WAAW;AAAA,MAChB,YAAW,oBAAI,QAAO,mBAAmB,SAAS,EAAE,QAAQ,OAAO;AAAA,MACnE,MAAM,KAAK,iBAAiB,SAAS;AAAA,MACrC,aAAa;AAAA,IAChB;AAEEA,kBAAA,MAAA,MAAA,OAAA,6BAAY,uBAAuB,QAAQ;AAC3C,SAAK,SAAS,QAAQ,QAAQ;AAG9B,QAAI,KAAK,SAAS,SAAS;AAAI,WAAK,WAAW,KAAK,SAAS,MAAM,GAAG,EAAE;AAGxEA,wBAAA,MAAA,OAAA,6BAAY,sCAAsC,KAAK,SAAS,MAAM;AACtE,SAAK,aAAa,UAAU;AAG5B,QAAI,oBAAoB;AACvB,iBAAW,MAAM;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS,mBAAmB;AAAA,UAC5B,YAAY;AAAA,UACZ,aAAa;AAAA,QAClB,CAAK;AAAA,MACD,GAAE,GAAG;AAAA,IACN;AAAA,EAiBD;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB,WAAW;AAC3B,UAAM,eAAe;AAAA,MACpB,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,GAAG;AAAA,MACH,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACP;AAEE,WAAO,aAAa,SAAS,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKD,SAAS,UAAU;AAClB,SAAK,gBAAgB,KAAK,QAAQ;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,UAAU;AACnB,UAAM,QAAQ,KAAK,gBAAgB,QAAQ,QAAQ;AACnD,QAAI,QAAQ,IAAI;AACf,WAAK,gBAAgB,OAAO,OAAO,CAAC;AAAA,IACpC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa,MAAM;AAClB,SAAK,gBAAgB,QAAQ,CAAC,UAAU,UAAU;AACjD,UAAI;AACH,iBAAS,MAAM,IAAI;AAAA,MACnB,SAAQ,OAAO;AAAA,MAEf;AAAA,IACJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,OAAO;AACZ,QAAI;AACHA,oBAAAA,MAAA,MAAA,OAAA,6BAAY,cAAc;AAG1BA,oBAAAA,MAAY,MAAA,OAAA,6BAAA,gBAAgB;AAC5B,YAAMD,gBAAAA,UAAU;AAChBC,oBAAAA,MAAY,MAAA,OAAA,6BAAA,yBAAyB;AAAA,IAKrC,SAAQ,OAAO;AACfA,oBAAAA,MAAc,MAAA,SAAA,6BAAA,YAAY,KAAK;AAC/BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAI;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AAChBA,kBAAAA,MAAY,MAAA,OAAA,6BAAA,sBAAsB;AAGlC,QAAI,CAACD,gBAAS,UAAC,aAAa;AAC3BC,oBAAAA,MAAA,MAAA,OAAA,6BAAY,kCAAkC;AAC9CD,gCAAU,UAAU,KAAK,MAAM;AAC9BC,sBAAAA,MAAY,MAAA,OAAA,6BAAA,kCAAkC;AAC9C,aAAK,iBAAgB;AAAA,MACzB,CAAI,EAAE,MAAM,WAAS;AACjBA,wEAAc,8BAA8B,KAAK;AAAA,MACrD,CAAI;AAAA,IACJ,OAAS;AACNA,oBAAAA,MAAA,MAAA,OAAA,6BAAY,mCAAmC;AAC/C,WAAK,iBAAgB;AAAA,IACrB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB;AAClBA,kBAAAA,MAAY,MAAA,OAAA,6BAAA,sBAAsB;AAGlCD,8BAAU,YAAY,iBAAiB;AACvCC,kBAAAA,MAAY,MAAA,OAAA,6BAAA,uBAAuB;AAGnC,eAAW,MAAM;AAChBD,gCAAU,YAAY,oBAAoB;AAC1CC,oBAAAA,MAAA,MAAA,OAAA,6BAAY,uBAAuB;AAAA,IACnC,GAAE,GAAG;AAGN,eAAW,MAAM;AAGhB,YAAM,EAAE,UAAS,IAAK,QAAQ,eAAe;AAC7C,UAAI,aAAa,UAAU,aAAa;AACvC,kBAAU,YAAY;AAAA,UACrB,MAAM;AAAA,UACN,MAAM,CAAE;AAAA,QACb,CAAK,EAAE,KAAK,cAAY;AACnBA,wBAAA,MAAA,MAAA,OAAA,6BAAY,uBAAuB,QAAQ;AAE3C,cAAI,SAAS,SAAS,oBAAoB,SAAS,MAAM;AACxD,iBAAK,YAAY,SAAS;AAC1B,iBAAK,aAAa,WAAW;AAAA,UAC7B;AAAA,QACN,CAAK,EAAE,MAAM,WAAS;AACjBA,wBAAA,MAAA,MAAA,SAAA,6BAAc,yBAAyB,KAAK;AAAA,QACjD,CAAK;AAAA,MACL,OAAU;AACNA,sBAAAA,MAAA,MAAA,OAAA,6BAAY,uCAAuC;AACnDD,kCAAU,YAAY,iBAAiB;AAAA,MACvC;AACDC,oBAAAA,MAAA,MAAA,OAAA,6BAAY,uBAAuB;AAAA,IACnC,GAAE,GAAG;AAGN,eAAW,MAAM;AAChBD,gCAAU,YAAY,iBAAiB;AACvCC,oBAAAA,MAAA,MAAA,OAAA,6BAAY,uBAAuB;AAAA,IACnC,GAAE,GAAG;AAGN,eAAW,MAAM;AAChB,WAAK,kBAAiB;AACtBA,oBAAAA,MAAA,MAAA,OAAA,6BAAY,uBAAuB;AAAA,IACnC,GAAE,GAAG;AAGN,eAAW,MAAM;AAChB,WAAK,mBAAkB;AACvBA,oBAAAA,MAAA,MAAA,OAAA,6BAAY,uBAAuB;AAAA,IACnC,GAAE,GAAI;AAAA,EACP;AAAA;AAAA;AAAA;AAAA,EAKD,mBAAmB;AAClBA,kBAAAA,gDAAY,0CAA0C;AACtDA,wBAAY,MAAA,OAAA,6BAAA,UAAS,oBAAI,KAAI,GAAG,eAAc,CAAE;AAChDA,kBAAY,MAAA,MAAA,OAAA,6BAAA,WAAW,KAAK,QAAQ;AACpCA,kBAAY,MAAA,MAAA,OAAA,6BAAA,kBAAkBD,gBAAAA,UAAU,QAAQ;AAChDC,kBAAY,MAAA,MAAA,OAAA,6BAAA,kBAAkBD,gBAAAA,UAAU,WAAW;AACnDC,kBAAY,MAAA,MAAA,OAAA,6BAAA,mBAAmBD,gBAAAA,UAAU,SAAS;AAGlD,UAAM,kBAAkBA,gBAAS,UAAC,YAAY,KAAK;AAEnD,QAAI,CAAC,iBAAiB;AACrBC,oBAAAA,MAAA,MAAA,OAAA,6BAAY,uBAAuB;AACnCA,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C;AAAA,IACA;AAED,QAAI,CAACD,gBAAS,UAAC,aAAa;AAC3BC,oBAAAA,MAAA,MAAA,OAAA,6BAAY,gCAAgC;AAC5CA,oBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,QAAQ;AAC/C;AAAA,IACA;AAEDA,kBAAAA,MAAY,MAAA,OAAA,6BAAA,sBAAsB;AAClCD,oBAAAA,UAAU,qBAAsB;AAChCC,kBAAAA,MAAY,MAAA,OAAA,6BAAA,uBAAuB;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAKD,WAAW;AACV,QAAI,CAAC,KAAK,UAAU;AACnBA,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAS,UAAC,sBAAqB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO;AACN,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAS,UAAC,mBAAkB;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,QAAQ;AACjB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,oBAAoB,SAAS,MAAM;AAAA,EAC7C;AAAA,EAED,YAAY,MAAM;AACjB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,oBAAoB,WAAW,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAKD,WAAW,SAAS;AACnB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,gBAAgB,SAAS,OAAO;AAAA,EAC1C;AAAA,EAED,cAAc,SAAS;AACtB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,gBAAgB,YAAY,OAAO;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAKD,QAAQ,QAAQ,WAAW,GAAG,QAAQ,IAAI;AACzC,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACD,UAAM,OAAO,EAAE,SAAS,QAAQ,SAAQ;AACxC,QAAI;AAAO,WAAK,SAAS;AACzBD,oBAAAA,UAAU,eAAe,OAAO,IAAI;AAAA,EACpC;AAAA,EAED,SAAS,QAAQ,WAAW,GAAG;AAC9B,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAS,UAAC,eAAe,QAAQ,EAAE,QAAQ,SAAU,CAAA;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAKD,SAAS,QAAQ,OAAO,WAAW,GAAG;AACrC,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAS,UAAC,iBAAiB,QAAQ,EAAE,QAAQ,OAAO,SAAQ,CAAE;AAAA,EAC9D;AAAA,EAED,cAAc,QAAQ;AACrB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,iBAAiB,OAAO,EAAE,OAAQ,CAAA;AAAA,EAC5C;AAAA,EAED,cAAc,QAAQ;AACrB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,iBAAiB,UAAU,EAAE,OAAQ,CAAA;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,SAAS;AAClB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,gBAAgB,QAAQ,EAAE,QAAS,CAAA;AAAA,EAC7C;AAAA,EAED,gBAAgB,QAAQ;AACvB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,gBAAgB,eAAe,EAAE,OAAQ,CAAA;AAAA,EACnD;AAAA,EAED,gBAAgB,SAAS;AACxB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,gBAAgB,eAAe,EAAE,QAAS,CAAA;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU,UAAU;AACnB,QAAI,CAAC,KAAK,UAAU;AACnBC,oBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAM,CAAE;AAC7C;AAAA,IACA;AACDD,oBAAAA,UAAU,mBAAmB,SAAS,EAAE,SAAU,CAAA;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY;AACX,WAAO,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACd,WAAO,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACd,WAAO,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY;AACX,WAAO,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc;AACb,WAAO,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc;AACb,WAAO;AAAA,MACN,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,IACX;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,YAAY;AACX,WAAO,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa;AACZA,oBAAAA,UAAU,WAAY;AAAA,EACtB;AAAA,EAED,gBAAgB;AACf,WAAO,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc,MAAM;AACnB,SAAK,SAAS;AACd,SAAK,aAAa,QAAQ;AAAA,EAC1B;AAAA,EAED,cAAc;AAEb,QAAI,KAAK,UAAW,OAAO,KAAK,OAAO,UAAU,UAAW;AAC3D,WAAK,QAAQ,KAAK,OAAO;AAAA,IAC5B,WAAa,KAAK,OAAO;AACtB,WAAK,QAAQ,KAAK;AAAA,IAClB;AAAA,EACD;AAAA,EAED,aAAa;AACZ,SAAK,UAAU,CAAC,GAAG,KAAK,SAAS;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAKD,qBAAqB;AACpB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,gCAAU,YAAY,oBAAoB,EAAE;AAC5CA,sBAAAA,UAAU,GAAG,gBAAgB,CAAC,SAAS;AACtC,aAAK,cAAc,QAAQ,CAAE;AAC7B,aAAK,aAAa,aAAa;AAC/B,gBAAQ,KAAK,WAAW;AAAA,MAC5B,CAAI;AAAA,IAEJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,iBAAiB;AACtB,QAAI,OAAO,KAAK,KAAK,WAAW,EAAE,WAAW,GAAG;AAC/C,YAAM,KAAK,mBAAoB;AAAA,IAC/B;AACD,WAAO,KAAK;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AACnB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCC,oBAAAA,MAAA,MAAA,OAAA,6BAAY,oBAAoB;AAChCD,gCAAU,YAAY,mBAAmB,EAAE;AAC3CA,sBAAAA,UAAU,GAAG,eAAe,CAAC,aAAa;AACzCC,sBAAA,MAAA,MAAA,OAAA,6BAAY,oBAAoB,QAAQ;AACxC,aAAK,aAAa;AAElB,cAAM,OAAO,SAAS,QAAQ;AAC9BA,sBAAY,MAAA,MAAA,OAAA,6BAAA,kBAAkB,IAAI;AAClC,YAAI,MAAM,QAAQ,IAAI,GAAG;AACxB,qBAAW,OAAO,MAAM;AACvB,gBAAI,IAAI,IAAI;AACX,mBAAK,WAAW,IAAI,EAAE,IAAI;AAC1BA,4BAAAA,MAAA,MAAA,OAAA,6BAAY,gBAAgB,IAAI,EAAE,OAAO,IAAI,MAAM,IAAI,IAAI,EAAE;AAAA,YAC7D;AAAA,UACD;AAAA,QACD,WAAU,OAAO,SAAS,YAAY,SAAS,MAAM;AAErD,qBAAW,CAAC,IAAI,GAAG,KAAK,OAAO,QAAQ,IAAI,GAAG;AAC7C,gBAAI,OAAO,IAAI,IAAI;AAClB,mBAAK,WAAW,IAAI,EAAE,IAAI;AAC1BA,4BAAAA,MAAA,MAAA,OAAA,6BAAY,gBAAgB,IAAI,EAAE,OAAO,IAAI,MAAM,IAAI,IAAI,EAAE;AAAA,YAC7D;AAAA,UACD;AAAA,QACD;AACDA,sBAAY,MAAA,MAAA,OAAA,6BAAA,kBAAkB,KAAK,UAAU;AAC7C,aAAK,aAAa,YAAY;AAC9B,gBAAQ,KAAK,UAAU;AAAA,MAC3B,CAAI;AAAA,IAEJ,CAAG;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,gBAAgB;AACrB,QAAI,OAAO,KAAK,KAAK,UAAU,EAAE,WAAW,GAAG;AAC9C,YAAM,KAAK,kBAAmB;AAAA,IAC9B;AACD,WAAO,KAAK;AAAA,EACZ;AACF;AAGK,MAAC,YAAY,IAAI,iBAAgB;;"}