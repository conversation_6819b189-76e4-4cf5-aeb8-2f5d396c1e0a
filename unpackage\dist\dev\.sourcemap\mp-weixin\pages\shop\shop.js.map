{"version": 3, "file": "shop.js", "sources": ["pages/shop/shop.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc2hvcC9zaG9wLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view>\r\n\t\t<text>【调试】showListModal: {{ showListModal }}</text>\r\n\t</view>\r\n\t<view class=\"container\">\r\n\t\t<!-- 顶部信息 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"money-info\">\r\n\t\t\t\t<text class=\"money-label\">银两:</text>\r\n\t\t\t\t<text class=\"money-value\">{{ formatNumber(money) }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"shop-type\">\r\n\t\t\t\t<text class=\"shop-name\">{{ currentShop.name }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 商店切换 -->\r\n\t\t<!-- <view class=\"shop-tabs\"> ... </view> -->\r\n\r\n\t\t<!-- 市场功能 -->\r\n\t\t<view v-if=\"currentShopType === 'market'\" class=\"market-section\">\r\n\t\t\t<view class=\"market-actions\">\r\n\t\t\t\t<button class=\"market-btn\" @click=\"listItem\">\r\n\t\t\t\t\t<text class=\"btn-text\">上架物品</text>\r\n\t\t\t\t</button>\r\n\t\t\t\t<button class=\"market-btn\" @click=\"showMyOrders\">\r\n\t\t\t\t\t<text class=\"btn-text\">订单列表</text>\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t<scroll-view class=\"order-list-scroll\" scroll-y=\"true\">\r\n\t\t\t\t<view class=\"order-list\">\r\n\t\t\t\t\t<view v-if=\"marketList.length === 0\" class=\"market-empty\">暂无玩家上架物品</view>\r\n\t\t\t\t\t<view v-for=\"order in marketList\" :key=\"order.id\" class=\"order-card\">\r\n\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\tclass=\"item-img\"\r\n\t\t\t\t\t\t\t:src=\"order.item && order.item.img ? order.item.img : '/static/logo.png'\"\r\n\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t<view class=\"order-info\">\r\n\t\t\t\t\t\t\t<view class=\"item-name\">{{ order.item && order.item.name ? order.item.name : '未知物品' }}</view>\r\n\t\t\t\t\t\t\t<view class=\"item-detail\">\r\n\t\t\t\t\t\t\t\t<text>单价：</text><text class=\"price\">{{ order.price }}</text>\r\n\t\t\t\t\t\t\t\t<text> 数量：</text><text>{{ order.quantity }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"seller\">卖家：{{ order.seller }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<button class=\"buy-btn\" @click=\"buyMarketItem(order)\">购买</button>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 商品列表 -->\r\n\t\t<scroll-view v-else class=\"goods-list\" scroll-y=\"true\">\r\n\t\t\t<view \r\n\t\t\t\tclass=\"goods-item simple-row\" \r\n\t\t\t\tv-for=\"(item, index) in currentShop.goods\" \r\n\t\t\t\t:key=\"index\"\r\n\t\t\t\t@click=\"showItemDetail(item)\"\r\n\t\t\t>\r\n\t\t\t\t<text class=\"item-name\">{{ item.name }}</text>\r\n\t\t\t\t<text class=\"item-quality\" :style=\"{ color: getQualityColor(item.quality) }\">\r\n\t\t\t\t\t{{ getQualityName(item.quality) }}\r\n\t\t\t\t</text>\r\n\t\t\t\t<!-- 类型显示直接用 item.type，不再用 getTypeName(item.类型 || item.type) -->\r\n\t\t\t\t<text class=\"item-type\">{{ item.type }}</text>\r\n\t\t\t\t<text class=\"item-price main-price\">{{ item.price }} 银两</text>\r\n\t\t\t\t<button class=\"buy-btn\" @click.stop=\"buyItem(item)\">购买</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"empty-goods\" v-if=\"currentShop.goods.length === 0\">\r\n\t\t\t\t<text>暂无商品</text>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<!-- 物品详情弹窗 -->\r\n\t\t<view class=\"modal-overlay\" v-if=\"showDetail\" @click=\"closeDetail\">\r\n\t\t\t<view class=\"modal-content\" @click.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">商品详情</text>\r\n\t\t\t\t\t<text class=\"modal-close\" @click=\"closeDetail\">×</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\" v-if=\"selectedItem\">\r\n\t\t\t\t\t<text class=\"detail-name\">{{ selectedItem.name }}</text>\r\n\t\t\t\t\t<text class=\"detail-quality\" :style=\"{ color: getQualityColor(selectedItem.quality) }\">\r\n\t\t\t\t\t\t品质: {{ getQualityName(selectedItem.quality) }}\r\n\t\t\t\t\t</text>\r\n\t\t\t\t\t<text class=\"detail-type\">类型: {{ selectedItem.type }}</text>\r\n\t\t\t\t\t<text class=\"detail-desc\" v-if=\"selectedItem.description\">{{ selectedItem.description }}</text>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 装备属性 -->\r\n\t\t\t\t\t<view class=\"detail-stats\" v-if=\"selectedItem.attack || selectedItem.defense || selectedItem.hp || selectedItem.mp\">\r\n\t\t\t\t\t\t<text class=\"stats-title\">属性加成:</text>\r\n\t\t\t\t\t\t<text v-if=\"selectedItem.attack\">攻击: {{ selectedItem.attack }}</text>\r\n\t\t\t\t\t\t<text v-if=\"selectedItem.defense\">防御: {{ selectedItem.defense }}</text>\r\n\t\t\t\t\t\t<text v-if=\"selectedItem.hp\">气血: {{ selectedItem.hp }}</text>\r\n\t\t\t\t\t\t<text v-if=\"selectedItem.mp\">内力: {{ selectedItem.mp }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"detail-price\">\r\n\t\t\t\t\t\t<text class=\"price-title\">价格:</text>\r\n\t\t\t\t\t\t<text class=\"price-value\">{{ selectedItem.price }} 银两</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-footer\">\r\n\t\t\t\t\t<button class=\"modal-btn cancel-btn\" @click=\"closeDetail\">关闭</button>\r\n\t\t\t\t\t<button class=\"modal-btn confirm-btn\" @click=\"buyItem(selectedItem)\">\r\n\t\t\t\t\t\t购买\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 上架弹窗 -->\r\n\t\t<view v-if=\"showListModal\" class=\"modal-overlay\" @click=\"showListModal=false\">\r\n\t\t\t<view class=\"modal-content\" @click.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">上架物品</text>\r\n\t\t\t\t\t<text class=\"modal-close\" @click=\"showListModal=false\">×</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t\t<view v-if=\"!listItemData\">\r\n\t\t\t\t\t\t<text>【调试】filteredMyItems: {{ filteredMyItems.length }}</text>\r\n\t\t\t\t\t\t<text>【调试】myItems: {{ myItems && myItems.length }}</text>\r\n\t\t\t\t\t\t<text>【调试】gameState.inventory: {{ (gameState && gameState.inventory && gameState.inventory.length) || 0 }}</text>\r\n\t\t\t\t\t\t<view v-if=\"filteredMyItems.length===0\" class=\"empty-items\">暂无可上架物品</view>\r\n\t\t\t\t\t\t<scroll-view class=\"list-select-list\" scroll-y=\"true\">\r\n\t\t\t\t\t\t\t<view v-for=\"item in filteredMyItems\" :key=\"item.id\" class=\"list-select-item\" @click=\"selectListItem(item)\">\r\n\t\t\t\t\t\t\t\t<text class=\"item-name\">{{ item.name }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"item-quality\" :style=\"{ color: getQualityColor(item.quality) }\">{{ getQualityName(item.quality) }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"item-quantity\" v-if=\"item.quantity>1\">x{{ item.quantity }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t<text>物品：{{ listItemData.name }}</text>\r\n\t\t\t\t\t\t<input v-model=\"listPrice\" type=\"number\" placeholder=\"请输入价格\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-footer\">\r\n\t\t\t\t\t<button @click=\"showListModal=false\">取消</button>\r\n\t\t\t\t\t<button v-if=\"listItemData\" @click=\"confirmListItem\">确认上架</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 订单列表弹窗 -->\r\n\t\t<view v-if=\"showMyOrdersModal\" class=\"modal-overlay\" @click=\"showMyOrdersModal=false\">\r\n\t\t\t<view class=\"modal-content\" @click.stop>\r\n\t\t\t\t<view class=\"modal-header\">\r\n\t\t\t\t\t<text class=\"modal-title\">我的订单</text>\r\n\t\t\t\t\t<text class=\"modal-close\" @click=\"showMyOrdersModal=false\">×</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"modal-body\">\r\n\t\t\t\t\t<scroll-view class=\"order-list-scroll\" scroll-y=\"true\">\r\n\t\t\t\t\t\t<view class=\"order-list\">\r\n\t\t\t\t\t\t\t<view v-if=\"myOrderList.length === 0\" class=\"market-empty\">暂无订单</view>\r\n\t\t\t\t\t\t\t<view v-for=\"order in myOrderList\" :key=\"order.id\" class=\"order-card\">\r\n\t\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\t\tclass=\"item-img\"\r\n\t\t\t\t\t\t\t\t\t:src=\"order.item && order.item.img ? order.item.img : '/static/logo.png'\"\r\n\t\t\t\t\t\t\t\t\tmode=\"aspectFill\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<view class=\"order-info\">\r\n\t\t\t\t\t\t\t\t\t<view class=\"item-name\">{{ order.item && order.item.name ? order.item.name : '未知物品' }}</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"item-detail\">\r\n\t\t\t\t\t\t\t\t\t\t<text>单价：</text><text class=\"price\">{{ order.price }}</text>\r\n\t\t\t\t\t\t\t\t\t\t<text> 数量：</text><text>{{ order.quantity }}</text>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t\t<view class=\"seller\">卖家：{{ order.seller }}</view>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t<!-- 可加“下架”按钮等操作 -->\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport gameState from '../../utils/gameState.js'\r\nimport { gameUtils } from '../../utils/gameData.js'\r\nimport wsManager from '../../utils/websocket.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tmoney: 0,\r\n\t\t\tmyItems: [],\r\n\t\t\tcurrentShopType: 'market',\r\n\t\t\tshowDetail: false,\r\n\t\t\tselectedItem: null,\r\n\t\t\tnpcId: 'changan_coalboss',\r\n\t\t\tshops: [\r\n\t\t\t\t{\r\n\t\t\t\t\ttype: 'market',\r\n\t\t\t\t\tname: '市场',\r\n\t\t\t\t\tgoods: []\r\n\t\t\t\t}\r\n\t\t\t],\r\n\t\t\tloading: false,\r\n\t\t\t// 新增：物品配置\r\n\t\t\titemsConfig: {},\r\n\t\t\tmarketList: [], // 玩家市场物品\r\n\t\t\tshowListModal: false, // 上架弹窗\r\n\t\t\tlistItemData: null,   // 当前要上架的物品\r\n\t\t\tlistPrice: '',        // 上架价格\r\n\t\t\tlistQuantity: 1,     // 上架数量\r\n\t\t\tlistTab: 'item', // 上架弹窗的tab，'item'或'martial'\r\n\t\t\tmyMartials: [], // 玩家武功列表\r\n\t\t\tlistMartialData: null, // 当前要上架的武功\r\n\t\t\tshowMyOrdersModal: false, // 控制订单弹窗显示\r\n\t\t\tmyOrderList: [], // 当前玩家订单\r\n\t\t}\r\n\t},\r\n\t\r\n\tcomputed: {\r\n\t\tcurrentShop() {\r\n\t\t\treturn this.shops[0];\r\n\t\t},\r\n\t\tfilteredMyItems() {\r\n\t\t\t// 合并物品配置后再过滤\r\n\t\t\treturn this.myItems\r\n\t\t\t\t.map(item => {\r\n\t\t\t\t\tconst config = this.itemsConfig[item.id] || {};\r\n\t\t\t\t\treturn { ...item, ...config };\r\n\t\t\t\t})\r\n\t\t\t\t.filter(item =>\r\n\t\t\t\t\t(item.is_sellable === undefined && item.sellable === undefined) ||\r\n\t\t\t\t\titem.is_sellable === true ||\r\n\t\t\t\t\titem.is_sellable === 1 ||\r\n\t\t\t\t\titem.sellable === true ||\r\n\t\t\t\t\titem.sellable === 1\r\n\t\t\t\t);\r\n\t\t}\r\n\t},\r\n\t\r\n\tonLoad() {\r\n\t\tconsole.log('【调试】shop.vue onLoad被调用');\r\n\t\tthis.requestPlayerData()\r\n\t\tthis.updateMoney()\r\n\t\tthis.updateData()\r\n\t\tthis.fetchShopGoods('changan_coalboss')\r\n\t\tthis.loadItemsConfig() // 新增\r\n\t\tthis.refreshMarket()\r\n\t},\r\n\t\r\n\tonShow() {\r\n\t\tconsole.log('【调试】shop.vue onShow被调用');\r\n\t\tthis.requestPlayerData()\r\n\t\tthis.updateMoney()\r\n\t\tthis.updateData()\r\n\t\tthis.refreshMarket() // 新增，确保每次页面显示都刷新订单\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\trequestPlayerData() {\r\n\t\t\tgameUtils.sendMessage({\r\n\t\t\t\ttype: 'get_player_data',\r\n\t\t\t\tdata: {}\r\n\t\t\t}).then(response => {\r\n\t\t\t\tif (response && response.type === 'player_data' && response.data) {\r\n\t\t\t\t\tif (typeof response.data.money === 'number') {\r\n\t\t\t\t\t\tthis.money = response.data.money;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tupdateData() {\r\n\t\t\tthis.money = gameState.money\r\n\t\t\tthis.myItems = [...gameState.inventory]\r\n\t\t\tconsole.log('【调试】updateData赋值myItems:', this.myItems);\r\n\t\t},\r\n\t\t\r\n\t\tformatNumber(num) {\r\n\t\t\treturn gameUtils.formatNumber(num)\r\n\t\t},\r\n\t\t\r\n\t\tgetQualityColor(quality) {\r\n\t\t\treturn gameUtils.getQualityColor(quality)\r\n\t\t},\r\n\t\t\r\n\t\tgetQualityName(quality) {\r\n\t\t\tconst qualities = {\r\n\t\t\t\t'common': '普通',\r\n\t\t\t\t'fine': '精良',\r\n\t\t\t\t'rare': '稀有',\r\n\t\t\t\t'epic': '传说',\r\n\t\t\t\t'legendary': '神品'\r\n\t\t\t}\r\n\t\t\treturn qualities[quality] || '普通'\r\n\t\t},\r\n\t\t\r\n\t\tgetTypeName(type) {\r\n\t\t\tconst types = {\r\n\t\t\t\t'weapon': '武器',\r\n\t\t\t\t'armor': '护甲',\r\n\t\t\t\t'necklace': '项链',\r\n\t\t\t\t'bracelet': '手镯',\r\n\t\t\t\t'mount': '坐骑',\r\n\t\t\t\t'material': '材料',\r\n\t\t\t\t'herb': '草药'\r\n\t\t\t}\r\n\t\t\treturn types[type] || '未知'\r\n\t\t},\r\n\t\t\r\n\t\tgetSellPrice(item) {\r\n\t\t\tconst prices = {\r\n\t\t\t\t'common': 10,\r\n\t\t\t\t'fine': 50,\r\n\t\t\t\t'rare': 200,\r\n\t\t\t\t'epic': 1000,\r\n\t\t\t\t'legendary': 5000\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tconst basePrice = prices[item.quality] || 10\r\n\t\t\treturn basePrice * (item.quantity || 1)\r\n\t\t},\r\n\t\t\r\n\t\tswitchShop(shopType) {\r\n\t\t\tconsole.log('【调试】切换shopType到：', shopType);\r\n\t\t\tthis.currentShopType = shopType\r\n\t\t\tif (shopType === 'equipment') this.npcId = 'changan_coalboss'\r\n\t\t\telse if (shopType === 'herb') this.npcId = 'changan_herbboss'\r\n\t\t\telse if (shopType === 'material') this.npcId = 'changan_materialboss'\r\n\t\t\telse this.npcId = ''\r\n\t\t\t// 自动请求对应商店数据\r\n\t\t\tif (this.npcId) {\r\n\t\t\t\tthis.fetchShopGoods(this.npcId)\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tshowItemDetail(item) {\r\n\t\t\tthis.selectedItem = item\r\n\t\t\tthis.showDetail = true\r\n\t\t},\r\n\t\t\r\n\t\tcloseDetail() {\r\n\t\t\tthis.showDetail = false\r\n\t\t\tthis.selectedItem = null\r\n\t\t},\r\n\t\t\r\n\t\tasync buyItem(item) {\r\n\t\t\tif (!gameState.isAuthed) {\r\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif (this.money < item.price) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '银两不足',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\t// 购买物品时，强制带 type（英文）、sellable 字段\r\n\t\t\t\tconst type = item.type || '';\r\n\t\t\t\tconst sellable = (typeof item.sellable !== 'undefined' ? item.sellable : true) ? true : false;\r\n\t\t\t\tconst response = await this.$request({\r\n\t\t\t\t\ttype: 'shop_action',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\taction: 'buy',\r\n\t\t\t\t\t\titem_id: item.id,\r\n\t\t\t\t\t\tquantity: 1, // 购买数量\r\n\t\t\t\t\t\ttype,\r\n\t\t\t\t\t\tsellable\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 检查响应是否包含错误\r\n\t\t\t\tif (response && response.type === 'error') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '购买失败',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 等待后端返回player_data和inventory_data自动刷新\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '购买成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t\tthis.closeDetail();\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('购买失败:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '购买失败，请重试',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tsellMyItem(item) {\r\n\t\t\tif (!gameState.isAuthed) {\r\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tconst sellPrice = this.getSellPrice(item)\r\n\t\t\t\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '确认出售',\r\n\t\t\t\tcontent: `确定要出售 ${item.name} 吗？\\n获得: ${sellPrice} 银两`,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tgameState.money += sellPrice\r\n\t\t\t\t\t\tgameState.removeItem(item.id, item.quantity || 1)\r\n\t\t\t\t\t\tthis.updateData()\r\n\t\t\t\t\t\tgameState.save()\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '出售成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\tasync listItem() {\r\n\t\t\tconsole.log('【调试】listItem被调用');\r\n\t\t\ttry {\r\n\t\t\t\tawait this.fetchPlayerInventory();\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('【调试】fetchPlayerInventory异常', e);\r\n\t\t\t}\r\n\t\t\tthis.updateData();\r\n\t\t\tthis.listItemData = null;\r\n\t\t\tthis.listPrice = '';\r\n\t\t\tthis.showListModal = true;\r\n\t\t\tconsole.log('【调试】showListModal已设为', this.showListModal);\r\n\t\t},\r\n\r\n\t\tasync fetchPlayerInventory() {\r\n\t\t\tconst resp = await gameUtils.sendMessage({\r\n\t\t\t\ttype: 'get_inventory_data'\r\n\t\t\t});\r\n\t\t\tconsole.log('【调试】get_inventory_data resp:', resp);\r\n\t\t\tlet inventory;\r\n\t\t\tif (resp && resp.data && Array.isArray(resp.data.inventory)) {\r\n\t\t\t\tinventory = resp.data.inventory;\r\n\t\t\t} else if (resp && Array.isArray(resp.inventory)) {\r\n\t\t\t\tinventory = resp.inventory;\r\n\t\t\t} else if (resp && resp.data && Array.isArray(resp.data)) {\r\n\t\t\t\tinventory = resp.data;\r\n\t\t\t}\r\n\t\t\tif (Array.isArray(inventory)) {\r\n\t\t\t\tgameState.inventory = inventory;\r\n\t\t\t}\r\n\t\t\tconsole.log('【调试】fetchPlayerInventory最终赋值:', gameState.inventory);\r\n\t\t},\r\n\r\n\t\tasync confirmListItem() {\r\n\t\t\tif (!this.listItemData || !this.listPrice) {\r\n\t\t\t\tuni.showToast({ title: '请选择物品并输入价格', icon: 'none' });\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tconst resp = await gameUtils.sendMessage({\r\n\t\t\t\ttype: 'market_action',\r\n\t\t\t\tdata: {\r\n\t\t\t\t\taction: 'list',\r\n\t\t\t\t\titem_id: this.listItemData.id,\r\n\t\t\t\t\tprice: Number(this.listPrice),\r\n\t\t\t\t\tquantity: 1\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tuni.showToast({ title: (resp.data && resp.data.message) || '上架成功', icon: 'none' });\r\n\t\t\tthis.showListModal = false;\r\n\t\t\tthis.refreshMarket();\r\n\t\t},\r\n\t\t\r\n\t\tasync refreshMarket() {\r\n\t\t\tconsole.log('【调试】refreshMarket被调用');\r\n\t\t\tconst resp = await gameUtils.sendMessage({\r\n\t\t\t\ttype: 'market_action',\r\n\t\t\t\tdata: { action: 'get_market_list' }\r\n\t\t\t});\r\n\t\t\tconsole.log('【调试】resp完整：', resp);\r\n\t\t\tconst list = (resp && resp.list) || [];\r\n\t\t\tconsole.log('【调试】最终订单list：', list);\r\n\t\t\tthis.marketList = list.map(order => ({\r\n\t\t\t\t...order,\r\n\t\t\t\titem: order.item || { name: '未知物品', quality: 'common' }\r\n\t\t\t}));\r\n\t\t\tconsole.log('【调试】赋值后的marketList：', this.marketList);\r\n\t\t},\r\n\r\n\t\tasync buyMarketItem(order) {\r\n\t\t\tconst resp = await gameUtils.sendMessage({\r\n\t\t\t\ttype: 'market_action',\r\n\t\t\t\tdata: { action: 'buy', order_id: Number(order.id) }\r\n\t\t\t});\r\n\t\t\tuni.showToast({ title: (resp.data && resp.data.message) || '购买成功', icon: 'none' });\r\n\t\t\tthis.refreshMarket();\r\n\t\t},\r\n\t\t\r\n\t\tfetchShopGoods(npcId) {\r\n\t\t\tthis.loading = true;\r\n\t\t\tgameUtils.sendMessage({\r\n\t\t\t\ttype: 'shop_action',\r\n\t\t\t\tdata: { npc_id: npcId }\r\n\t\t\t}).then(response => {\r\n\t\t\t\tif (response && response.type === 'shop_items' && response.data && response.data.items) {\r\n\t\t\t\t\tthis.shops[0].goods = response.data.items.map(item => ({\r\n\t\t\t\t\t\tid: item.id,\r\n\t\t\t\t\t\tname: item.name,\r\n\t\t\t\t\t\tdescription: item.desc,\r\n\t\t\t\t\t\ticon: item.icon,\r\n\t\t\t\t\t\tprice: item.price,\r\n\t\t\t\t\t\tstock: item.stock,\r\n\t\t\t\t\t\tattack: item.attack,\r\n\t\t\t\t\t\tdefense: item.defense,\r\n\t\t\t\t\t\thp: item.hp,\r\n\t\t\t\t\t\tmp: item.mp,\r\n\t\t\t\t\t\ttype: item.type,\r\n\t\t\t\t\t\tquality: item.quality\r\n\t\t\t\t\t}));\r\n\t\t\t\t}\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}).catch(error => {\r\n\t\t\t\tconsole.error('获取商店物品失败:', error);\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tupdateMoney() {\r\n\t\t\t// 优先从player对象获取最新银两\r\n\t\t\tif (gameState.player && (typeof gameState.player.money === 'number')) {\r\n\t\t\t\tthis.money = gameState.player.money\r\n\t\t\t} else if (gameState.money) {\r\n\t\t\t\tthis.money = gameState.money\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tdestroyed() {\r\n\t\t\t// 不再需要移除监听器，因为我们使用gameUtils.sendMessage\r\n\t\t},\r\n\r\n\t\tasync loadItemsConfig() {\r\n\t\t\tthis.itemsConfig = await gameState.getItemsConfig();\r\n\t\t},\r\n\t\t// 可在需要时通过 this.itemsConfig[itemId] 获取物品详情\r\n\r\n\t\tselectListItem(item) {\r\n\t\t\tthis.listItemData = item;\r\n\t\t\tthis.listPrice = '';\r\n\t\t},\r\n\r\n\t\tselectListMartial(martial) {\r\n\t\t\tthis.listMartialData = martial;\r\n\t\t\tthis.listPrice = '';\r\n\t\t},\r\n\r\n\t\tasync fetchMyMartials() {\r\n\t\t\tconst resp = await gameUtils.sendMessage({\r\n\t\t\t\ttype: 'get_player_martials'\r\n\t\t\t});\r\n\t\t\tthis.myMartials = resp.martials || [];\r\n\t\t},\r\n\r\n\t\tasync loadMyMartials() {\r\n\t\t\tawait this.fetchMyMartials();\r\n\t\t},\r\n\r\n\t\tshowMyOrders() {\r\n\t\t\tconst player = gameState.player || {};\r\n\t\t\tconst myName = player.name || player.username || '';\r\n\t\t\t// 只展示当前玩家上架的前10个订单\r\n\t\t\tthis.myOrderList = this.marketList.filter(order => order.seller === myName).slice(0, 10);\r\n\t\t\tthis.showMyOrdersModal = true;\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n\tpadding: 20rpx;\r\n\tpadding-bottom: 140rpx; /* 为tabBar留出空间 */\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tbackground: rgba(255, 255, 255, 0.9);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.money-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.money-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.money-value {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.shop-name {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.shop-tabs {\r\n\tdisplay: flex;\r\n\tbackground: rgba(255, 255, 255, 0.9);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 10rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.tab-item {\r\n\tflex: 1;\r\n\ttext-align: center;\r\n\tpadding: 15rpx;\r\n\tborder-radius: 15rpx;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.tab-item.active {\r\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n}\r\n\r\n.tab-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.tab-item.active .tab-text {\r\n\tcolor: white;\r\n}\r\n\r\n.market-section {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n\tflex: 1;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.market-info {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.market-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.market-desc {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n}\r\n\r\n.market-actions {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.market-btn {\r\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\tcolor: white;\r\n\tborder: none;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 8rpx 16rpx;\r\n\tfont-size: 24rpx;\r\n}\r\n\r\n.btn-text {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: white;\r\n}\r\n\r\n.market-list {\r\n\theight: 300rpx;\r\n}\r\n\r\n.market-empty {\r\n\ttext-align: center;\r\n\tpadding: 100rpx 0;\r\n\tcolor: #999;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.goods-list {\r\n\theight: 400rpx;\r\n\tpadding: 20rpx;\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.goods-item.simple-row {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: 180rpx 70rpx 30rpx 110rpx minmax(120rpx, 1fr);\r\n\talign-items: center;\r\n\tpadding: 16rpx 0;\r\n\tborder-bottom: 1px solid #eee;\r\n\tbackground: rgba(255,255,255,0.92);\r\n\tborder-radius: 8rpx;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.item-name {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tmargin-right: 0;\r\n\tmax-width: 180rpx;\r\n\tflex-shrink: 0;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n}\r\n\r\n.item-quality {\r\n\tfont-size: 26rpx;\r\n\tmargin-right: 0;\r\n\twidth: 70rpx;\r\n\tflex-shrink: 0;\r\n\ttext-align: center;\r\n}\r\n\r\n.item-type {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 0;\r\n\twidth: 30rpx;\r\n\tflex-shrink: 0;\r\n\ttext-align: center;\r\n}\r\n\r\n.item-sep {\r\n\tcolor: #bbb;\r\n\tmargin: 0 10rpx;\r\n\tfont-size: 26rpx;\r\n}\r\n\r\n.item-price, .main-price {\r\n\tdisplay: inline-block;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\tvertical-align: middle;\r\n\tfont-size: 26rpx;\r\n}\r\n\r\n.buy-btn {\r\n\tfont-size: 26rpx;\r\n\tpadding: 8rpx 24rpx;\r\n\tbackground: linear-gradient(90deg, #5a8dee 60%, #42e9f6 100%);\r\n\tcolor: #fff;\r\n\tborder-radius: 8rpx;\r\n\tjustify-self: end;\r\n\tmin-width: 120rpx;\r\n\tbox-shadow: 0 2rpx 8rpx rgba(90,141,238,0.12);\r\n\ttransition: background 0.2s;\r\n}\r\n\r\n.buy-btn:active {\r\n\tbackground: #3a6fd6;\r\n}\r\n\r\n.empty-goods {\r\n\ttext-align: center;\r\n\tpadding: 100rpx 0;\r\n\tcolor: #999;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.my-items-section {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n\tflex: 1;\r\n}\r\n\r\n.section-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.section-subtitle {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.my-items-list {\r\n\theight: 300rpx;\r\n}\r\n\r\n.my-item {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 15rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n\tbackground: white;\r\n\tborder-radius: 15rpx;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.my-item:last-child {\r\n\tborder-bottom: none;\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.item-quantity {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-left: 10rpx;\r\n}\r\n\r\n.sell-price {\r\n\ttext-align: right;\r\n}\r\n\r\n.empty-items {\r\n\ttext-align: center;\r\n\tpadding: 50rpx 0;\r\n\tcolor: #999;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.modal-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 1000;\r\n}\r\n\r\n.modal-content {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\twidth: 80%;\r\n\tmax-width: 600rpx;\r\n\tmax-height: 80vh;\r\n\toverflow: hidden;\r\n}\r\n\r\n.modal-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n}\r\n\r\n.modal-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.modal-close {\r\n\tfont-size: 40rpx;\r\n\tcolor: #999;\r\n\tline-height: 1;\r\n}\r\n\r\n.modal-body {\r\n\tpadding: 30rpx;\r\n\tmax-height: 400rpx;\r\n\toverflow-y: auto;\r\n}\r\n\r\n.detail-name {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.detail-quality,\r\n.detail-type {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 15rpx;\r\n}\r\n\r\n.detail-desc {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n\tdisplay: block;\r\n\tmargin-bottom: 20rpx;\r\n\tline-height: 1.5;\r\n}\r\n\r\n.detail-stats {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.stats-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.detail-stats text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.detail-price {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.price-title {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.price-value {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #e74c3c;\r\n}\r\n\r\n.modal-footer {\r\n\tdisplay: flex;\r\n\tpadding: 20rpx 30rpx;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.modal-btn {\r\n\tflex: 1;\r\n\tpadding: 20rpx;\r\n\tborder: none;\r\n\tborder-radius: 15rpx;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.cancel-btn {\r\n\tbackground: #f0f0f0;\r\n\tcolor: #666;\r\n}\r\n\r\n.confirm-btn {\r\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\r\n\tcolor: white;\r\n}\r\n\r\n.order-list {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 20rpx;\r\n\tpadding: 20rpx;\r\n}\r\n.order-card {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tbackground: #fff;\r\n\tborder-radius: 16rpx;\r\n\tbox-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);\r\n\tpadding: 20rpx;\r\n\tgap: 20rpx;\r\n}\r\n.item-img {\r\n\twidth: 100rpx;\r\n\theight: 100rpx;\r\n\tborder-radius: 12rpx;\r\n\tbackground: #f5f5f5;\r\n\tobject-fit: cover;\r\n}\r\n.order-info {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 8rpx;\r\n}\r\n.item-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n.item-detail {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n.price {\r\n\tcolor: #e43d33;\r\n\tfont-weight: bold;\r\n}\r\n.seller {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n.buy-btn {\r\n\tbackground: linear-gradient(90deg, #ffb347, #ffcc33);\r\n\tcolor: #fff;\r\n\tborder: none;\r\n\tborder-radius: 24rpx;\r\n\tpadding: 0 32rpx;\r\n\tfont-size: 28rpx;\r\n\theight: 60rpx;\r\n\tline-height: 60rpx;\r\n}\r\n\r\n.list-tabs {\r\n\tdisplay: flex;\r\n\tjustify-content: space-around;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 10rpx 0;\r\n\tborder-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.list-tab {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tpadding: 10rpx 20rpx;\r\n\tborder-bottom: 2rpx solid transparent;\r\n}\r\n\r\n.list-tab.active {\r\n\tcolor: #333;\r\n\tborder-bottom-color: #667eea;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.list-select-list {\r\n\tmax-height: 200rpx; /* 控制列表高度 */\r\n\toverflow-y: auto;\r\n}\r\n\r\n.list-select-item {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 15rpx 0;\r\n\tborder-bottom: 1rpx solid #eee;\r\n}\r\n\r\n.list-select-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.item-name {\r\n\tflex: 1;\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\twhite-space: nowrap;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.item-quality {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.item-quantity {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.item-level {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tflex-shrink: 0;\r\n}\r\n\r\n.order-list-scroll {\r\n\tmax-height: calc(100vh - 260rpx);\r\n\tmin-height: 200rpx;\r\n\toverflow-y: auto;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/zjjhx/仗剑江湖行/pages/shop/shop.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "gameUtils", "gameState"], "mappings": ";;;;;AAyLA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,OAAO;AAAA,MACP,SAAS,CAAE;AAAA,MACX,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,OAAO;AAAA,MACP,OAAO;AAAA,QACN;AAAA,UACC,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO,CAAC;AAAA,QACT;AAAA,MACA;AAAA,MACD,SAAS;AAAA;AAAA,MAET,aAAa,CAAE;AAAA,MACf,YAAY,CAAE;AAAA;AAAA,MACd,eAAe;AAAA;AAAA,MACf,cAAc;AAAA;AAAA,MACd,WAAW;AAAA;AAAA,MACX,cAAc;AAAA;AAAA,MACd,SAAS;AAAA;AAAA,MACT,YAAY,CAAE;AAAA;AAAA,MACd,iBAAiB;AAAA;AAAA,MACjB,mBAAmB;AAAA;AAAA,MACnB,aAAa,CAAE;AAAA;AAAA,IAChB;AAAA,EACA;AAAA,EAED,UAAU;AAAA,IACT,cAAc;AACb,aAAO,KAAK,MAAM,CAAC;AAAA,IACnB;AAAA,IACD,kBAAkB;AAEjB,aAAO,KAAK,QACV,IAAI,UAAQ;AACZ,cAAM,SAAS,KAAK,YAAY,KAAK,EAAE,KAAK;AAC5C,eAAO,EAAE,GAAG,MAAM,GAAG;OACrB,EACA;AAAA,QAAO,UACN,KAAK,gBAAgB,UAAa,KAAK,aAAa,UACrD,KAAK,gBAAgB,QACrB,KAAK,gBAAgB,KACrB,KAAK,aAAa,QAClB,KAAK,aAAa;AAAA;IAErB;AAAA,EACA;AAAA,EAED,SAAS;AACRA,kBAAAA,MAAA,MAAA,OAAA,8BAAY,wBAAwB;AACpC,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,eAAe,kBAAkB;AACtC,SAAK,gBAAgB;AACrB,SAAK,cAAc;AAAA,EACnB;AAAA,EAED,SAAS;AACRA,kBAAAA,MAAA,MAAA,OAAA,8BAAY,wBAAwB;AACpC,SAAK,kBAAkB;AACvB,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,cAAc;AAAA,EACnB;AAAA,EAED,SAAS;AAAA,IACR,oBAAoB;AACnBC,qBAAAA,UAAU,YAAY;AAAA,QACrB,MAAM;AAAA,QACN,MAAM,CAAC;AAAA,MACR,CAAC,EAAE,KAAK,cAAY;AACnB,YAAI,YAAY,SAAS,SAAS,iBAAiB,SAAS,MAAM;AACjE,cAAI,OAAO,SAAS,KAAK,UAAU,UAAU;AAC5C,iBAAK,QAAQ,SAAS,KAAK;AAAA,UAC5B;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAED,aAAa;AACZ,WAAK,QAAQC,gBAAAA,UAAU;AACvB,WAAK,UAAU,CAAC,GAAGA,gBAAAA,UAAU,SAAS;AACtCF,oBAAY,MAAA,MAAA,OAAA,8BAAA,4BAA4B,KAAK,OAAO;AAAA,IACpD;AAAA,IAED,aAAa,KAAK;AACjB,aAAOC,eAAS,UAAC,aAAa,GAAG;AAAA,IACjC;AAAA,IAED,gBAAgB,SAAS;AACxB,aAAOA,eAAS,UAAC,gBAAgB,OAAO;AAAA,IACxC;AAAA,IAED,eAAe,SAAS;AACvB,YAAM,YAAY;AAAA,QACjB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,MACd;AACA,aAAO,UAAU,OAAO,KAAK;AAAA,IAC7B;AAAA,IAED,YAAY,MAAM;AACjB,YAAM,QAAQ;AAAA,QACb,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,MACT;AACA,aAAO,MAAM,IAAI,KAAK;AAAA,IACtB;AAAA,IAED,aAAa,MAAM;AAClB,YAAM,SAAS;AAAA,QACd,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,MACd;AAEA,YAAM,YAAY,OAAO,KAAK,OAAO,KAAK;AAC1C,aAAO,aAAa,KAAK,YAAY;AAAA,IACrC;AAAA,IAED,WAAW,UAAU;AACpBD,oBAAY,MAAA,MAAA,OAAA,8BAAA,oBAAoB,QAAQ;AACxC,WAAK,kBAAkB;AACvB,UAAI,aAAa;AAAa,aAAK,QAAQ;AAAA,eAClC,aAAa;AAAQ,aAAK,QAAQ;AAAA,eAClC,aAAa;AAAY,aAAK,QAAQ;AAAA;AAC1C,aAAK,QAAQ;AAElB,UAAI,KAAK,OAAO;AACf,aAAK,eAAe,KAAK,KAAK;AAAA,MAC/B;AAAA,IACA;AAAA,IAED,eAAe,MAAM;AACpB,WAAK,eAAe;AACpB,WAAK,aAAa;AAAA,IAClB;AAAA,IAED,cAAc;AACb,WAAK,aAAa;AAClB,WAAK,eAAe;AAAA,IACpB;AAAA,IAED,MAAM,QAAQ,MAAM;AACnB,UAAI,CAACE,gBAAS,UAAC,UAAU;AACxBF,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAC7C;AAAA,MACD;AACA,UAAI,KAAK,QAAQ,KAAK,OAAO;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAEA,UAAI;AAEH,cAAM,OAAO,KAAK,QAAQ;AAC1B,cAAM,YAAY,OAAO,KAAK,aAAa,cAAc,KAAK,WAAW,QAAQ,OAAO;AACxF,cAAM,WAAW,MAAM,KAAK,SAAS;AAAA,UACpC,MAAM;AAAA,UACN,MAAM;AAAA,YACL,QAAQ;AAAA,YACR,SAAS,KAAK;AAAA,YACd,UAAU;AAAA;AAAA,YACV;AAAA,YACA;AAAA,UACD;AAAA,QACD,CAAC;AAGD,YAAI,YAAY,SAAS,SAAS,SAAS;AAC1CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AACD;AAAA,QACD;AAGAA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD,aAAK,YAAW;AAAA,MACf,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,8BAAc,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,WAAW,MAAM;AAChB,UAAI,CAACE,gBAAS,UAAC,UAAU;AACxBF,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAC7C;AAAA,MACD;AACA,YAAM,YAAY,KAAK,aAAa,IAAI;AAExCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,IAAI;AAAA,MAAY,SAAS;AAAA,QAChD,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChBE,4BAAS,UAAC,SAAS;AACnBA,4BAAS,UAAC,WAAW,KAAK,IAAI,KAAK,YAAY,CAAC;AAChD,iBAAK,WAAW;AAChBA,4BAAAA,UAAU,KAAK;AAEfF,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,aACN;AAAA,UACF;AAAA,QACD;AAAA,OACA;AAAA,IACD;AAAA,IAED,MAAM,WAAW;AAChBA,oBAAAA,MAAY,MAAA,OAAA,8BAAA,iBAAiB;AAC7B,UAAI;AACH,cAAM,KAAK;MACZ,SAAS,GAAG;AACXA,sBAAc,MAAA,MAAA,SAAA,8BAAA,8BAA8B,CAAC;AAAA,MAC9C;AACA,WAAK,WAAU;AACf,WAAK,eAAe;AACpB,WAAK,YAAY;AACjB,WAAK,gBAAgB;AACrBA,oBAAY,MAAA,MAAA,OAAA,8BAAA,wBAAwB,KAAK,aAAa;AAAA,IACtD;AAAA,IAED,MAAM,uBAAuB;AAC5B,YAAM,OAAO,MAAMC,eAAS,UAAC,YAAY;AAAA,QACxC,MAAM;AAAA,MACP,CAAC;AACDD,oBAAA,MAAA,MAAA,OAAA,8BAAY,gCAAgC,IAAI;AAChD,UAAI;AACJ,UAAI,QAAQ,KAAK,QAAQ,MAAM,QAAQ,KAAK,KAAK,SAAS,GAAG;AAC5D,oBAAY,KAAK,KAAK;AAAA,MACvB,WAAW,QAAQ,MAAM,QAAQ,KAAK,SAAS,GAAG;AACjD,oBAAY,KAAK;AAAA,iBACP,QAAQ,KAAK,QAAQ,MAAM,QAAQ,KAAK,IAAI,GAAG;AACzD,oBAAY,KAAK;AAAA,MAClB;AACA,UAAI,MAAM,QAAQ,SAAS,GAAG;AAC7BE,wBAAS,UAAC,YAAY;AAAA,MACvB;AACAF,oBAAA,MAAA,MAAA,OAAA,8BAAY,iCAAiCE,gBAAAA,UAAU,SAAS;AAAA,IAChE;AAAA,IAED,MAAM,kBAAkB;AACvB,UAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,WAAW;AAC1CF,sBAAG,MAAC,UAAU,EAAE,OAAO,cAAc,MAAM,OAAO,CAAC;AACnD;AAAA,MACD;AACA,YAAM,OAAO,MAAMC,eAAS,UAAC,YAAY;AAAA,QACxC,MAAM;AAAA,QACN,MAAM;AAAA,UACL,QAAQ;AAAA,UACR,SAAS,KAAK,aAAa;AAAA,UAC3B,OAAO,OAAO,KAAK,SAAS;AAAA,UAC5B,UAAU;AAAA,QACX;AAAA,MACD,CAAC;AACDD,oBAAAA,MAAI,UAAU,EAAE,OAAQ,KAAK,QAAQ,KAAK,KAAK,WAAY,QAAQ,MAAM,OAAQ,CAAA;AACjF,WAAK,gBAAgB;AACrB,WAAK,cAAa;AAAA,IAClB;AAAA,IAED,MAAM,gBAAgB;AACrBA,oBAAAA,MAAA,MAAA,OAAA,8BAAY,sBAAsB;AAClC,YAAM,OAAO,MAAMC,eAAS,UAAC,YAAY;AAAA,QACxC,MAAM;AAAA,QACN,MAAM,EAAE,QAAQ,kBAAkB;AAAA,MACnC,CAAC;AACDD,oBAAA,MAAA,MAAA,OAAA,8BAAY,eAAe,IAAI;AAC/B,YAAM,OAAQ,QAAQ,KAAK,QAAS,CAAA;AACpCA,oBAAA,MAAA,MAAA,OAAA,8BAAY,iBAAiB,IAAI;AACjC,WAAK,aAAa,KAAK,IAAI,YAAU;AAAA,QACpC,GAAG;AAAA,QACH,MAAM,MAAM,QAAQ,EAAE,MAAM,QAAQ,SAAS,SAAS;AAAA,MACtD,EAAC;AACFA,oBAAA,MAAA,MAAA,OAAA,8BAAY,uBAAuB,KAAK,UAAU;AAAA,IAClD;AAAA,IAED,MAAM,cAAc,OAAO;AAC1B,YAAM,OAAO,MAAMC,eAAS,UAAC,YAAY;AAAA,QACxC,MAAM;AAAA,QACN,MAAM,EAAE,QAAQ,OAAO,UAAU,OAAO,MAAM,EAAE,EAAE;AAAA,MACnD,CAAC;AACDD,oBAAAA,MAAI,UAAU,EAAE,OAAQ,KAAK,QAAQ,KAAK,KAAK,WAAY,QAAQ,MAAM,OAAQ,CAAA;AACjF,WAAK,cAAa;AAAA,IAClB;AAAA,IAED,eAAe,OAAO;AACrB,WAAK,UAAU;AACfC,qBAAAA,UAAU,YAAY;AAAA,QACrB,MAAM;AAAA,QACN,MAAM,EAAE,QAAQ,MAAM;AAAA,MACvB,CAAC,EAAE,KAAK,cAAY;AACnB,YAAI,YAAY,SAAS,SAAS,gBAAgB,SAAS,QAAQ,SAAS,KAAK,OAAO;AACvF,eAAK,MAAM,CAAC,EAAE,QAAQ,SAAS,KAAK,MAAM,IAAI,WAAS;AAAA,YACtD,IAAI,KAAK;AAAA,YACT,MAAM,KAAK;AAAA,YACX,aAAa,KAAK;AAAA,YAClB,MAAM,KAAK;AAAA,YACX,OAAO,KAAK;AAAA,YACZ,OAAO,KAAK;AAAA,YACZ,QAAQ,KAAK;AAAA,YACb,SAAS,KAAK;AAAA,YACd,IAAI,KAAK;AAAA,YACT,IAAI,KAAK;AAAA,YACT,MAAM,KAAK;AAAA,YACX,SAAS,KAAK;AAAA,UACd,EAAC;AAAA,QACH;AACA,aAAK,UAAU;AAAA,MAChB,CAAC,EAAE,MAAM,WAAS;AACjBD,sBAAc,MAAA,MAAA,SAAA,8BAAA,aAAa,KAAK;AAChC,aAAK,UAAU;AAAA,MAChB,CAAC;AAAA,IACD;AAAA,IAED,cAAc;AAEb,UAAIE,gBAAAA,UAAU,UAAW,OAAOA,gBAAAA,UAAU,OAAO,UAAU,UAAW;AACrE,aAAK,QAAQA,0BAAU,OAAO;AAAA,iBACpBA,gBAAS,UAAC,OAAO;AAC3B,aAAK,QAAQA,gBAAAA,UAAU;AAAA,MACxB;AAAA,IACA;AAAA,IAED,YAAY;AAAA,IAEX;AAAA,IAED,MAAM,kBAAkB;AACvB,WAAK,cAAc,MAAMA,gBAAS,UAAC,eAAc;AAAA,IACjD;AAAA;AAAA,IAGD,eAAe,MAAM;AACpB,WAAK,eAAe;AACpB,WAAK,YAAY;AAAA,IACjB;AAAA,IAED,kBAAkB,SAAS;AAC1B,WAAK,kBAAkB;AACvB,WAAK,YAAY;AAAA,IACjB;AAAA,IAED,MAAM,kBAAkB;AACvB,YAAM,OAAO,MAAMD,eAAS,UAAC,YAAY;AAAA,QACxC,MAAM;AAAA,MACP,CAAC;AACD,WAAK,aAAa,KAAK,YAAY,CAAA;AAAA,IACnC;AAAA,IAED,MAAM,iBAAiB;AACtB,YAAM,KAAK;IACX;AAAA,IAED,eAAe;AACd,YAAM,SAASC,gBAAAA,UAAU,UAAU;AACnC,YAAM,SAAS,OAAO,QAAQ,OAAO,YAAY;AAEjD,WAAK,cAAc,KAAK,WAAW,OAAO,WAAS,MAAM,WAAW,MAAM,EAAE,MAAM,GAAG,EAAE;AACvF,WAAK,oBAAoB;AAAA,IAC1B;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC/jBA,GAAG,WAAW,eAAe;"}