import sqlite3
import json
import os

def add_skill_points_to_user(username='t02', points=1000000, db_path=os.path.join(os.path.dirname(__file__), 'game.db')):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 获取用户id
    cursor.execute("SELECT id FROM users WHERE username=?", (username,))
    row = cursor.fetchone()
    if not row:
        print(f"未找到账号: {username}")
        return
    user_id = row[0]

    # 获取玩家数据
    cursor.execute("SELECT id, data FROM players WHERE user_id=?", (user_id,))
    row = cursor.fetchone()
    if not row:
        print(f"未找到玩家数据: {username}")
        return
    player_id, data_json = row
    player_data = json.loads(data_json)

    # 增加武学点
    old_points = player_data.get('skill_points', 0)
    player_data['skill_points'] = old_points + points

    # 更新数据库
    cursor.execute("UPDATE players SET data=? WHERE id=?", (json.dumps(player_data, ensure_ascii=False), player_id))
    conn.commit()
    conn.close()
    print(f"账号 {username} 已增加 {points} 武学点，当前武学点: {player_data['skill_points']}")

if __name__ == "__main__":
    add_skill_points_to_user() 