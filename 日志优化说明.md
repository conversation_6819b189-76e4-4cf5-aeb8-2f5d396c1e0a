# 日志膨胀问题解决方案

## 问题分析

服务端运行几个小时后，日志文件急速膨胀，主要原因：

1. **日志级别过低** - 原来设置为 INFO 级别，记录了大量不必要的信息
2. **频繁请求记录** - 每个 WebSocket 消息都记录日志，包括：
   - `get_player_data` - 获取玩家数据（非常频繁）
   - `get_inventory_data` - 获取背包数据（频繁）
   - `get_bonus_summary` - 获取增益摘要（非常频繁）
   - `get_map_npcs` - 获取地图NPC（非常频繁）
   - `get_map_players` - 获取地图玩家（非常频繁）
   - `ping` - 心跳请求（频繁）
3. **自动恢复系统** - 每10秒记录详细的属性恢复信息
4. **聊天系统** - 每条聊天消息都记录日志
5. **详细调试信息** - 包含完整的玩家数据等大量信息

## 解决方案

### 1. 日志级别优化
- 将日志级别从 `INFO` 改为 `WARNING`
- 只记录重要的操作和错误信息

### 2. 日志轮转
- 使用 `RotatingFileHandler` 实现日志轮转
- 单个日志文件最大 10MB
- 保留 5 个备份文件
- 自动压缩和清理旧日志

### 3. 选择性日志记录
定义重要消息类型，只记录这些操作的日志：
```python
IMPORTANT_MESSAGE_TYPES = {
    'register', 'login', 'auth', 'adventure', 'meditation', 'healing',
    'equip_item', 'unequip_item', 'shop_action', 'market_action', 
    'guild_action', 'crafting_action', 'realm_breakthrough', 'use_item',
    'study_martial', 'train_martial', 'redeem_code', 'send_chat_message',
    'sect_action', 'quest_action'
}
```

### 4. 频繁请求不记录日志
以下请求类型不再记录日志：
- `get_player_data` - 获取玩家数据
- `get_inventory_data` - 获取背包数据
- `get_bonus_summary` - 获取增益摘要
- `get_map_npcs` - 获取地图NPC
- `get_map_players` - 获取地图玩家
- `ping` - 心跳请求
- `get_martial_data` - 获取武学数据
- `get_ranking` - 获取排行榜
- 等其他频繁的查询请求

### 5. 自动恢复系统优化
- 移除每次恢复的详细日志
- 只在有显著变化时记录警告日志
- 降低日志级别为 WARNING

### 6. 聊天系统优化
- 移除聊天消息内容的日志记录
- 移除聊天扣费的详细日志

### 7. 详细信息优化
- 移除玩家数据的完整输出
- 移除装备数据的详细日志
- 只保留错误和重要状态变化的日志

## 使用工具

### 日志清理工具
运行 `backend/clean_logs.bat` 可以手动清理日志文件：
- 备份原日志文件
- 只保留最后1000行日志
- 显示清理前后的文件大小对比

### 自动轮转
服务器会自动进行日志轮转：
- 当日志文件超过10MB时自动轮转
- 保留最近5个日志文件
- 旧文件自动删除

## 预期效果

优化后的日志系统：
1. **大幅减少日志量** - 预计减少90%以上的日志输出
2. **提高性能** - 减少磁盘I/O操作
3. **保留重要信息** - 仍然记录所有重要操作和错误
4. **自动管理** - 无需手动清理，自动轮转和删除旧日志
5. **便于调试** - 重要操作仍有日志记录，便于问题排查

## 监控建议

1. **定期检查日志大小** - 确保优化效果
2. **关注错误日志** - 重点关注 WARNING 和 ERROR 级别的日志
3. **性能监控** - 观察服务器性能是否有改善
4. **功能验证** - 确保所有功能正常工作

## 回滚方案

如果需要更详细的日志进行调试，可以临时修改：
```python
# 在 server.py 中修改日志级别
logging.basicConfig(level=logging.INFO)  # 改回 INFO 级别
```

但建议只在调试时使用，调试完成后改回 WARNING 级别。
