{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"login-container\">\r\n\t\t<!-- 游戏标题 -->\r\n\t\t<view class=\"game-title\">\r\n\t\t\t<text class=\"title-text\">仗剑江湖行</text>\r\n\t\t\t<text class=\"subtitle-text\">武侠点击式游戏</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 登录表单 -->\r\n\t\t<view class=\"login-form\" v-if=\"!isRegistering\">\r\n\t\t\t<view class=\"form-title\">登录江湖</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-group\">\r\n\t\t\t\t<text class=\"input-label\">账号</text>\r\n\t\t\t\t<view class=\"input-wrapper\">\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tclass=\"input-field\" \r\n\t\t\t\t\t\tv-model=\"loginForm.username\" \r\n\t\t\t\t\t\tplaceholder=\"请输入账号\" \r\n\t\t\t\t\t\tplaceholder-style=\"color:#999;\"\r\n\t\t\t\t\t\tconfirm-type=\"next\" \r\n\t\t\t\t\t\tmaxlength=\"20\"\r\n\t\t\t\t\t\tcursor-spacing=\"10\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-group\">\r\n\t\t\t\t<text class=\"input-label\">密码</text>\r\n\t\t\t\t<view class=\"input-wrapper\">\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\ttype=\"password\" \r\n\t\t\t\t\t\tclass=\"input-field\" \r\n\t\t\t\t\t\tv-model=\"loginForm.password\" \r\n\t\t\t\t\t\tplaceholder=\"请输入密码\" \r\n\t\t\t\t\t\tplaceholder-style=\"color:#999;\"\r\n\t\t\t\t\t\tconfirm-type=\"done\" \r\n\t\t\t\t\t\tmaxlength=\"20\"\r\n\t\t\t\t\t\tcursor-spacing=\"10\"\r\n\t\t\t\t\t\t@confirm=\"handleLogin\" \r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<button class=\"login-btn primary-btn\" @click=\"handleLogin\">\r\n\t\t\t\t<text class=\"btn-text\">登录</text>\r\n\t\t\t</button>\r\n\t\t\t\r\n\t\t\t<view class=\"form-footer\">\r\n\t\t\t\t<text class=\"footer-text\">还没有账号？</text>\r\n\t\t\t\t<text class=\"footer-link\" @click=\"switchToRegister\">立即注册</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 注册表单 -->\r\n\t\t<view class=\"register-form\" v-if=\"isRegistering\">\r\n\t\t\t<view class=\"form-title\">创建角色</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-group\">\r\n\t\t\t\t<text class=\"input-label\">账号</text>\r\n\t\t\t\t<view class=\"input-wrapper\">\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tclass=\"input-field\" \r\n\t\t\t\t\t\tv-model=\"registerForm.username\" \r\n\t\t\t\t\t\tplaceholder=\"请输入账号（3-20位字符）\" \r\n\t\t\t\t\t\tplaceholder-style=\"color:#999;\"\r\n\t\t\t\t\t\tconfirm-type=\"next\" \r\n\t\t\t\t\t\tmaxlength=\"20\"\r\n\t\t\t\t\t\tcursor-spacing=\"10\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-group\">\r\n\t\t\t\t<text class=\"input-label\">密码</text>\r\n\t\t\t\t<view class=\"input-wrapper\">\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\ttype=\"password\" \r\n\t\t\t\t\t\tclass=\"input-field\" \r\n\t\t\t\t\t\tv-model=\"registerForm.password\" \r\n\t\t\t\t\t\tplaceholder=\"请输入密码（6-20位字符）\" \r\n\t\t\t\t\t\tplaceholder-style=\"color:#999;\"\r\n\t\t\t\t\t\tconfirm-type=\"next\" \r\n\t\t\t\t\t\tmaxlength=\"20\"\r\n\t\t\t\t\t\tcursor-spacing=\"10\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-group\">\r\n\t\t\t\t<text class=\"input-label\">确认密码</text>\r\n\t\t\t\t<view class=\"input-wrapper\">\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\ttype=\"password\" \r\n\t\t\t\t\t\tclass=\"input-field\" \r\n\t\t\t\t\t\tv-model=\"registerForm.confirmPassword\" \r\n\t\t\t\t\t\tplaceholder=\"请再次输入密码\" \r\n\t\t\t\t\t\tplaceholder-style=\"color:#999;\"\r\n\t\t\t\t\t\tconfirm-type=\"next\" \r\n\t\t\t\t\t\tmaxlength=\"20\"\r\n\t\t\t\t\t\tcursor-spacing=\"10\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-group\">\r\n\t\t\t\t<text class=\"input-label\">角色名称</text>\r\n\t\t\t\t<view class=\"input-wrapper\">\r\n\t\t\t\t\t<input \r\n\t\t\t\t\t\ttype=\"text\" \r\n\t\t\t\t\t\tclass=\"input-field\" \r\n\t\t\t\t\t\tv-model=\"registerForm.characterName\" \r\n\t\t\t\t\t\tplaceholder=\"请输入角色名称（2-10位字符）\" \r\n\t\t\t\t\t\tplaceholder-style=\"color:#999;\"\r\n\t\t\t\t\t\tconfirm-type=\"done\" \r\n\t\t\t\t\t\tmaxlength=\"10\"\r\n\t\t\t\t\t\tcursor-spacing=\"10\"\r\n\t\t\t\t\t\t@confirm=\"handleRegister\" \r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"input-group\">\r\n\t\t\t\t<text class=\"input-label\">性别</text>\r\n\t\t\t\t<view class=\"gender-options\">\r\n\t\t\t\t\t<view class=\"gender-option\" :class=\"{ active: registerForm.gender === '男' }\" @click=\"selectGender('男')\">\r\n\t\t\t\t\t\t<text class=\"gender-text\">男</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gender-option\" :class=\"{ active: registerForm.gender === '女' }\" @click=\"selectGender('女')\">\r\n\t\t\t\t\t\t<text class=\"gender-text\">女</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<view class=\"character-preview\" v-if=\"registerForm.characterName\">\r\n\t\t\t\t<text class=\"preview-label\">角色预览</text>\r\n\t\t\t\t<view class=\"character-card\">\r\n\t\t\t\t\t<text class=\"character-name\">{{ registerForm.characterName }}</text>\r\n\t\t\t\t\t<text class=\"character-level\">Lv.1 新手侠客</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<button class=\"register-btn primary-btn\" @click=\"handleRegister\" :disabled=\"!isFormValid\">\r\n\t\t\t\t<text class=\"btn-text\">创建角色</text>\r\n\t\t\t</button>\r\n\t\t\t\r\n\t\t\t<view class=\"form-footer\">\r\n\t\t\t\t<text class=\"footer-text\">已有账号？</text>\r\n\t\t\t\t<text class=\"footer-link\" @click=\"switchToLogin\">立即登录</text>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 天赋说明 -->\r\n\t\t\t<view class=\"talent-desc\" style=\"margin-top: 30rpx; color: #888; font-size: 24rpx; line-height: 1.7;\">\r\n\t\t\t\t角色天赋说明：<br/>\r\n\t\t\t\t- 力量、悟性、身法、根骨四项天赋，初始总和100，每项最低15，注册时系统自动随机分配。<br/>\r\n\t\t\t\t- 富源属性：初始为1，影响角色的机遇和资源获取。<br/>\r\n\t\t\t\t- 天赋数值由服务器随机生成，确保公平性。\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 加载状态 -->\r\n\t\t<view class=\"loading-overlay\" v-if=\"isLoading\">\r\n\t\t\t<view class=\"loading-content\">\r\n\t\t\t\t<text class=\"loading-text\">{{ loadingText }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport wsManager from '../../utils/websocket.js'\r\nimport { gameUtils } from '../../utils/gameData.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tisRegistering: false,\r\n\t\t\tisLoading: false,\r\n\t\t\tloadingText: '正在处理...',\r\n\t\t\tloginForm: {\r\n\t\t\t\tusername: '',\r\n\t\t\t\tpassword: ''\r\n\t\t\t},\r\n\t\t\tregisterForm: {\r\n\t\t\t\tusername: '',\r\n\t\t\t\tpassword: '',\r\n\t\t\t\tconfirmPassword: '',\r\n\t\t\t\tcharacterName: '',\r\n\t\t\t\tgender: '男'\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\t\r\n\tcomputed: {\r\n\t\tisFormValid() {\r\n\t\t\tconst form = this.registerForm;\r\n\t\t\treturn form.username.length >= 3 && \r\n\t\t\t\t   form.username.length <= 20 &&\r\n\t\t\t\t   form.password.length >= 6 && \r\n\t\t\t\t   form.password.length <= 20 &&\r\n\t\t\t\t   form.password === form.confirmPassword &&\r\n\t\t\t\t   form.characterName.length >= 2 && \r\n\t\t\t\t   form.characterName.length <= 10;\r\n\t\t}\r\n\t},\r\n\t\r\n\tonLoad() {\r\n\t\tconsole.log('登录页面加载');\r\n\t\t// 通知WebSocket管理器当前在登录页面\r\n\t\twsManager.setLoginPageStatus(true);\r\n\t\t\r\n\t\t// 检查是否已登录，但不立即执行自动登录\r\n\t\t// 延迟执行，避免阻塞UI渲染\r\n\t\tsetTimeout(() => {\r\n\t\t\tthis.checkLoginStatus();\r\n\t\t}, 1000);\r\n\t},\r\n\t\r\n\tonUnload() {\r\n\t\t// 离开登录页面时通知WebSocket管理器\r\n\t\twsManager.setLoginPageStatus(false);\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\tcheckLoginStatus() {\r\n\t\t\tconst token = uni.getStorageSync('token');\r\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\r\n\t\t\t\r\n\t\t\tif (token && userInfo) {\r\n\t\t\t\t// 已登录，但不立即跳转，先显示界面\r\n\t\t\t\tconsole.log('发现本地存储的登录信息，准备自动登录');\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '发现已保存的登录信息',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 1500\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 延迟执行自动登录，确保UI已渲染完成\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.autoLogin();\r\n\t\t\t\t}, 1500);\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tasync autoLogin() {\r\n\t\t\ttry {\r\n\t\t\t\t// 获取本地存储的token和用户信息\r\n\t\t\t\tconst token = uni.getStorageSync('token');\r\n\t\t\t\tconst userInfo = uni.getStorageSync('userInfo');\r\n\t\t\t\t\r\n\t\t\t\tif (!token) {\r\n\t\t\t\t\tconsole.log('未找到token，无法自动登录');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tthis.loadingText = '正在自动登录...';\r\n\t\t\t\t\r\n\t\t\t\t// 连接WebSocket\r\n\t\t\t\tawait wsManager.connect();\r\n\t\t\t\t\r\n\t\t\t\t// 发送认证消息并处理响应\r\n\t\t\t\tconst response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'auth',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\ttoken: token,\r\n\t\t\t\t\t\tuserInfo: userInfo\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tif (response && response.type === 'auth_success') {\r\n\t\t\t\t\tthis.handleAutoLoginSuccess(response.data);\r\n\t\t\t\t} else if (response && response.type === 'auth_failed') {\r\n\t\t\t\t\tthis.handleAutoLoginFailed(response.data);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '自动登录失败，请手动登录',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('自动登录失败:', error);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '自动登录失败，请手动登录',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\thandleAutoLoginSuccess(data) {\r\n\t\t\tthis.isLoading = false;\r\n\t\t\tconsole.log('自动登录认证成功:', data);\r\n\t\t\t\r\n\t\t\t// 更新本地存储的用户信息\r\n\t\t\tif (data.userInfo) {\r\n\t\t\t\tuni.setStorageSync('userInfo', data.userInfo);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '自动登录成功',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\t// 跳转到游戏页面\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tuni.switchTab({\r\n\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t});\r\n\t\t\t}, 1000);\r\n\t\t},\r\n\t\t\r\n\t\thandleAutoLoginFailed(data) {\r\n\t\t\tthis.isLoading = false;\r\n\t\t\tconsole.error('自动登录认证失败:', data);\r\n\t\t\t// 自动登录失败时清除本地token和userInfo\r\n\t\t\tuni.removeStorageSync('token');\r\n\t\t\tuni.removeStorageSync('userInfo');\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: data.message || '自动登录失败，请重新登录',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 3000\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tswitchToRegister() {\r\n\t\t\tthis.isRegistering = true;\r\n\t\t},\r\n\t\t\r\n\t\tswitchToLogin() {\r\n\t\t\tthis.isRegistering = false;\r\n\t\t},\r\n\t\t\r\n\t\tselectGender(gender) {\r\n\t\t\tthis.registerForm.gender = gender;\r\n\t\t},\r\n\t\t\r\n\t\tasync handleLogin() {\r\n\t\t\tconsole.log('尝试登录，用户名:', this.loginForm.username);\r\n\t\t\t\r\n\t\t\tif (!this.loginForm.username || !this.loginForm.password) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请输入账号和密码',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tthis.loadingText = '正在登录...';\r\n\t\t\t\t\r\n\t\t\t\t// 连接WebSocket\r\n\t\t\t\tawait wsManager.connect();\r\n\t\t\t\t\r\n\t\t\t\t// 发送登录消息并处理响应\r\n\t\t\t\tconst response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'login',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tusername: this.loginForm.username,\r\n\t\t\t\t\t\tpassword: this.loginForm.password\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tif (response && response.type === 'login_success') {\r\n\t\t\t\t\tthis.handleLoginSuccess(response.data);\r\n\t\t\t\t} else if (response && response.type === 'login_failed') {\r\n\t\t\t\t\tthis.handleLoginFailed(response.data);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '登录失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('登录失败:', error);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '登录失败: ' + error.message,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tasync handleRegister() {\r\n\t\t\tif (!this.isFormValid) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请检查输入信息',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tthis.loadingText = '正在创建角色...';\r\n\t\t\t\t\r\n\t\t\t\t// 连接WebSocket\r\n\t\t\t\tawait wsManager.connect();\r\n\t\t\t\t\r\n\t\t\t\t// 发送注册消息并处理响应\r\n\t\t\t\tconst response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'register',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tusername: this.registerForm.username,\r\n\t\t\t\t\t\tpassword: this.registerForm.password,\r\n\t\t\t\t\t\tcharacterName: this.registerForm.characterName,\r\n\t\t\t\t\t\tgender: this.registerForm.gender\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tif (response && response.type === 'register_success') {\r\n\t\t\t\t\tthis.handleRegisterSuccess(response.data);\r\n\t\t\t\t} else if (response && response.type === 'register_failed') {\r\n\t\t\t\t\tthis.handleRegisterFailed(response.data);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '注册失败，请重试',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('注册失败:', error);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '注册失败: ' + error.message,\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tasync handleLoginSuccess(data) {\r\n\t\t\tthis.isLoading = false;\r\n\t\t\tconsole.log('登录成功，收到数据:', data);\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\t// 保存登录信息\r\n\t\t\t\tuni.setStorageSync('token', data.token);\r\n\t\t\t\tuni.setStorageSync('userInfo', data.userInfo);\r\n\t\t\t\tconsole.log('已保存token:', data.token);\r\n\t\t\t\tconsole.log('已保存userInfo:', data.userInfo);\r\n\t\t\t\t\r\n\t\t\t\t// 验证保存是否成功\r\n\t\t\t\tconst savedToken = uni.getStorageSync('token');\r\n\t\t\t\tconst savedUserInfo = uni.getStorageSync('userInfo');\r\n\t\t\t\tconsole.log('验证保存 - token:', savedToken);\r\n\t\t\t\tconsole.log('验证保存 - userInfo:', savedUserInfo);\r\n\t\t\t\t\r\n\t\t\t\t// 通知WebSocket管理器已不在登录页面\r\n\t\t\t\twsManager.setLoginPageStatus(false);\r\n\t\t\t\t\r\n\t\t\t\t// 显示登录成功提示\r\n\t\t\t\tuni.showToast({ \r\n\t\t\t\t\ttitle: '登录成功', \r\n\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\tduration: 1500\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 确保WebSocket连接已建立\r\n\t\t\t\tif (!wsManager.isConnected) {\r\n\t\t\t\t\tconsole.log('WebSocket未连接，尝试连接...');\r\n\t\t\t\t\tawait wsManager.connect();\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 更新认证状态\r\n\t\t\t\twsManager.isAuthed = true;\r\n\t\t\t\t\r\n\t\t\t\t// 直接跳转到首页，无需再次认证\r\n\t\t\t\t// 延迟跳转，确保提示显示完成\r\n\t\t\t\tconsole.log('准备跳转到首页...');\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tconsole.log('执行跳转操作');\r\n\t\t\t\t\tuni.switchTab({ \r\n\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\tconsole.log('跳转成功');\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\tconsole.error('跳转失败:', err);\r\n\t\t\t\t\t\t\t// 尝试使用redirectTo作为备选方案\r\n\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 1500);\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('处理登录成功时出错:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '登录过程中出现错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\thandleLoginFailed(data) {\r\n\t\t\tthis.isLoading = false;\r\n\t\t\tconsole.error('登录失败:', data);\r\n\t\t\t// 登录失败时清除本地token和userInfo\r\n\t\t\tuni.removeStorageSync('token');\r\n\t\t\tuni.removeStorageSync('userInfo');\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: data.message || '登录失败',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 3000\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\thandleRegisterSuccess(data) {\r\n\t\t\tthis.isLoading = false;\r\n\t\t\tconsole.log('注册成功，收到数据:', data);\r\n\t\t\t\r\n\t\t\ttry {\r\n\t\t\t\t// 保存登录信息\r\n\t\t\t\tuni.setStorageSync('token', data.token);\r\n\t\t\t\tuni.setStorageSync('userInfo', data.userInfo);\r\n\t\t\t\tconsole.log('已保存token:', data.token);\r\n\t\t\t\tconsole.log('已保存userInfo:', data.userInfo);\r\n\t\t\t\t\r\n\t\t\t\t// 验证保存是否成功\r\n\t\t\t\tconst savedToken = uni.getStorageSync('token');\r\n\t\t\t\tconst savedUserInfo = uni.getStorageSync('userInfo');\r\n\t\t\t\tconsole.log('验证保存 - token:', savedToken);\r\n\t\t\t\tconsole.log('验证保存 - userInfo:', savedUserInfo);\r\n\t\t\t\t\r\n\t\t\t\t// 通知WebSocket管理器已不在登录页面\r\n\t\t\t\twsManager.setLoginPageStatus(false);\r\n\t\t\t\t\r\n\t\t\t\t// 准备天赋和富源信息字符串\r\n\t\t\t\tlet talentStr = '';\r\n\t\t\t\tif (data.userInfo && data.userInfo.talent) {\r\n\t\t\t\t\ttalentStr = `\\n力量：${data.userInfo.talent['力量']}  悟性：${data.userInfo.talent['悟性']}  身法：${data.userInfo.talent['身法']}  根骨：${data.userInfo.talent['根骨']}`;\r\n\t\t\t\t}\r\n\t\t\t\tlet fortuneStr = data.userInfo && data.userInfo.fortune ? `\\n富源：${data.userInfo.fortune}` : '';\r\n\t\t\t\t\r\n\t\t\t\t// 显示注册成功对话框\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\ttitle: '角色创建成功',\r\n\t\t\t\t\tcontent: `恭喜你，角色创建成功！${talentStr}${fortuneStr}`,\r\n\t\t\t\t\tshowCancel: false,\r\n\t\t\t\t\tsuccess: async () => {\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\t// 确保WebSocket连接已建立\r\n\t\t\t\t\t\t\tif (!wsManager.isConnected) {\r\n\t\t\t\t\t\t\t\tconsole.log('WebSocket未连接，尝试连接...');\r\n\t\t\t\t\t\t\t\tawait wsManager.connect();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 更新认证状态\r\n\t\t\t\t\t\t\twsManager.isAuthed = true;\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 显示跳转提示\r\n\t\t\t\t\t\t\tuni.showToast({ \r\n\t\t\t\t\t\t\t\ttitle: '正在进入游戏', \r\n\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t// 直接跳转到首页\r\n\t\t\t\t\t\t\tconsole.log('准备跳转到首页...');\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\tconsole.log('执行跳转操作');\r\n\t\t\t\t\t\t\t\tuni.switchTab({ \r\n\t\t\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\t\t\tconsole.log('跳转成功');\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\tfail: function(err) {\r\n\t\t\t\t\t\t\t\t\t\tconsole.error('跳转失败:', err);\r\n\t\t\t\t\t\t\t\t\t\t// 尝试使用redirectTo作为备选方案\r\n\t\t\t\t\t\t\t\t\t\tuni.redirectTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/index/index'\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\t\tconsole.error('处理注册成功后跳转时出错:', error);\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '进入游戏时出现错误',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('处理注册成功时出错:', error);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '注册过程中出现错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\thandleRegisterFailed(data) {\r\n\t\t\tthis.isLoading = false;\r\n\t\t\t// 注册失败时清除本地token和userInfo\r\n\t\t\tuni.removeStorageSync('token');\r\n\t\t\tuni.removeStorageSync('userInfo');\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: data.message || '注册失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.login-container {\r\n\tmin-height: 100vh;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tpadding: 40rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.game-title {\r\n\ttext-align: center;\r\n\tmargin-bottom: 80rpx;\r\n}\r\n\r\n.title-text {\r\n\tfont-size: 60rpx;\r\n\tfont-weight: bold;\r\n\tcolor: white;\r\n\ttext-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);\r\n\tdisplay: block;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.subtitle-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n\tdisplay: block;\r\n}\r\n\r\n.login-form,\r\n.register-form {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 30rpx;\r\n\tpadding: 60rpx 40rpx;\r\n\twidth: 100%;\r\n\tmax-width: 600rpx;\r\n\tbox-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);\r\n\tposition: relative;\r\n\tz-index: 10;\r\n}\r\n\r\n.form-title {\r\n\tfont-size: 40rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\ttext-align: center;\r\n\tmargin-bottom: 60rpx;\r\n}\r\n\r\n.input-group {\r\n\tmargin-bottom: 40rpx;\r\n\tposition: relative;\r\n\tz-index: 20;\r\n}\r\n\r\n.input-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n/* 确保输入框可点击 */\r\n.input-wrapper {\r\n\tposition: relative;\r\n\tz-index: 999;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.input-field {\r\n\twidth: 100%;\r\n\theight: 80rpx;\r\n\tborder: 2rpx solid #e0e0e0;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 0 30rpx;\r\n\tfont-size: 28rpx;\r\n\tbackground: white;\r\n\tbox-sizing: border-box;\r\n\tcolor: #333;\r\n\tposition: relative;\r\n\tz-index: 1000;\r\n}\r\n\r\n.input-field:focus {\r\n\tborder-color: #667eea;\r\n\tz-index: 1001;\r\n}\r\n\r\n.gender-options {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\tposition: relative;\r\n\tz-index: 20;\r\n}\r\n\r\n.gender-option {\r\n\tflex: 1;\r\n\theight: 80rpx;\r\n\tborder: 2rpx solid #e0e0e0;\r\n\tborder-radius: 20rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tbackground: white;\r\n\ttransition: all 0.3s ease;\r\n\tposition: relative;\r\n\tz-index: 20;\r\n}\r\n\r\n.gender-option.active {\r\n\tborder-color: #667eea;\r\n\tbackground: #667eea;\r\n}\r\n\r\n.gender-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.gender-option.active .gender-text {\r\n\tcolor: white;\r\n}\r\n\r\n.character-preview {\r\n\tmargin: 40rpx 0;\r\n\tpadding: 30rpx;\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.preview-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.character-card {\r\n\ttext-align: center;\r\n}\r\n\r\n.character-name {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.character-level {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tdisplay: block;\r\n}\r\n\r\n.primary-btn {\r\n\twidth: 100%;\r\n\theight: 90rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tborder: none;\r\n\tborder-radius: 45rpx;\r\n\tcolor: white;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 40rpx;\r\n\tbox-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);\r\n\tposition: relative;\r\n\tz-index: 20;\r\n}\r\n\r\n.primary-btn:active {\r\n\ttransform: translateY(2rpx);\r\n\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);\r\n}\r\n\r\n.primary-btn:disabled {\r\n\tbackground: #ccc;\r\n\tbox-shadow: none;\r\n}\r\n\r\n.form-footer {\r\n\ttext-align: center;\r\n}\r\n\r\n.footer-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.footer-link {\r\n\tfont-size: 28rpx;\r\n\tcolor: #667eea;\r\n\tmargin-left: 10rpx;\r\n}\r\n\r\n.loading-overlay {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 9999;\r\n}\r\n\r\n.loading-content {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\tpadding: 60rpx 40rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.loading-text {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.talent-desc {\r\n\ttext-align: center;\r\n\tmargin-top: 30rpx;\r\n\tcolor: #888;\r\n\tfont-size: 24rpx;\r\n\tline-height: 1.7;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/zjjhx/仗剑江湖行/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "wsManager", "gameUtils"], "mappings": ";;;;AA+KA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,eAAe;AAAA,MACf,WAAW;AAAA,MACX,aAAa;AAAA,MACb,WAAW;AAAA,QACV,UAAU;AAAA,QACV,UAAU;AAAA,MACV;AAAA,MACD,cAAc;AAAA,QACb,UAAU;AAAA,QACV,UAAU;AAAA,QACV,iBAAiB;AAAA,QACjB,eAAe;AAAA,QACf,QAAQ;AAAA,MACT;AAAA,IACD;AAAA,EACA;AAAA,EAED,UAAU;AAAA,IACT,cAAc;AACb,YAAM,OAAO,KAAK;AAClB,aAAO,KAAK,SAAS,UAAU,KAC3B,KAAK,SAAS,UAAU,MACxB,KAAK,SAAS,UAAU,KACxB,KAAK,SAAS,UAAU,MACxB,KAAK,aAAa,KAAK,mBACvB,KAAK,cAAc,UAAU,KAC7B,KAAK,cAAc,UAAU;AAAA,IAClC;AAAA,EACA;AAAA,EAED,SAAS;AACRA,kBAAAA,MAAA,MAAA,OAAA,gCAAY,QAAQ;AAEpBC,8BAAU,mBAAmB,IAAI;AAIjC,eAAW,MAAM;AAChB,WAAK,iBAAgB;AAAA,IACrB,GAAE,GAAI;AAAA,EACP;AAAA,EAED,WAAW;AAEVA,8BAAU,mBAAmB,KAAK;AAAA,EAClC;AAAA,EAED,SAAS;AAAA,IACR,mBAAmB;AAClB,YAAM,QAAQD,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9C,UAAI,SAAS,UAAU;AAEtBA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,oBAAoB;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAGD,mBAAW,MAAM;AAChB,eAAK,UAAS;AAAA,QACd,GAAE,IAAI;AAAA,MACR;AAAA,IACA;AAAA,IAED,MAAM,YAAY;AACjB,UAAI;AAEH,cAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO;AACxC,cAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9C,YAAI,CAAC,OAAO;AACXA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,iBAAiB;AAC7B;AAAA,QACD;AAEA,aAAK,YAAY;AACjB,aAAK,cAAc;AAGnB,cAAMC,gBAAAA,UAAU;AAGhB,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM;AAAA,YACL;AAAA,YACA;AAAA,UACD;AAAA,QACD,CAAC;AAED,YAAI,YAAY,SAAS,SAAS,gBAAgB;AACjD,eAAK,uBAAuB,SAAS,IAAI;AAAA,QACxC,WAAS,YAAY,SAAS,SAAS,eAAe;AACvD,eAAK,sBAAsB,SAAS,IAAI;AAAA,eAClC;AACN,eAAK,YAAY;AACjBF,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MAEC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,WAAW,KAAK;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,uBAAuB,MAAM;AAC5B,WAAK,YAAY;AACjBA,oBAAY,MAAA,MAAA,OAAA,gCAAA,aAAa,IAAI;AAG7B,UAAI,KAAK,UAAU;AAClBA,sBAAAA,MAAI,eAAe,YAAY,KAAK,QAAQ;AAAA,MAC7C;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAGD,iBAAW,MAAM;AAChBA,sBAAAA,MAAI,UAAU;AAAA,UACb,KAAK;AAAA,QACN,CAAC;AAAA,MACD,GAAE,GAAI;AAAA,IACP;AAAA,IAED,sBAAsB,MAAM;AAC3B,WAAK,YAAY;AACjBA,oBAAc,MAAA,MAAA,SAAA,gCAAA,aAAa,IAAI;AAE/BA,0BAAI,kBAAkB,OAAO;AAC7BA,0BAAI,kBAAkB,UAAU;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACD;AAAA,IAED,mBAAmB;AAClB,WAAK,gBAAgB;AAAA,IACrB;AAAA,IAED,gBAAgB;AACf,WAAK,gBAAgB;AAAA,IACrB;AAAA,IAED,aAAa,QAAQ;AACpB,WAAK,aAAa,SAAS;AAAA,IAC3B;AAAA,IAED,MAAM,cAAc;AACnBA,0BAAA,MAAA,OAAA,gCAAY,aAAa,KAAK,UAAU,QAAQ;AAEhD,UAAI,CAAC,KAAK,UAAU,YAAY,CAAC,KAAK,UAAU,UAAU;AACzDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI;AACH,aAAK,YAAY;AACjB,aAAK,cAAc;AAGnB,cAAMC,gBAAAA,UAAU;AAGhB,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM;AAAA,YACL,UAAU,KAAK,UAAU;AAAA,YACzB,UAAU,KAAK,UAAU;AAAA,UAC1B;AAAA,QACD,CAAC;AAED,YAAI,YAAY,SAAS,SAAS,iBAAiB;AAClD,eAAK,mBAAmB,SAAS,IAAI;AAAA,QACpC,WAAS,YAAY,SAAS,SAAS,gBAAgB;AACxD,eAAK,kBAAkB,SAAS,IAAI;AAAA,eAC9B;AACN,eAAK,YAAY;AACjBF,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MAEC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,KAAK;AAC5B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,WAAW,MAAM;AAAA,UACxB,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,MAAM,iBAAiB;AACtB,UAAI,CAAC,KAAK,aAAa;AACtBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AACD;AAAA,MACD;AAEA,UAAI;AACH,aAAK,YAAY;AACjB,aAAK,cAAc;AAGnB,cAAMC,gBAAAA,UAAU;AAGhB,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM;AAAA,YACL,UAAU,KAAK,aAAa;AAAA,YAC5B,UAAU,KAAK,aAAa;AAAA,YAC5B,eAAe,KAAK,aAAa;AAAA,YACjC,QAAQ,KAAK,aAAa;AAAA,UAC3B;AAAA,QACD,CAAC;AAED,YAAI,YAAY,SAAS,SAAS,oBAAoB;AACrD,eAAK,sBAAsB,SAAS,IAAI;AAAA,QACvC,WAAS,YAAY,SAAS,SAAS,mBAAmB;AAC3D,eAAK,qBAAqB,SAAS,IAAI;AAAA,eACjC;AACN,eAAK,YAAY;AACjBF,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MAEC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,KAAK;AAC5B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,WAAW,MAAM;AAAA,UACxB,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,MAAM,mBAAmB,MAAM;AAC9B,WAAK,YAAY;AACjBA,oBAAY,MAAA,MAAA,OAAA,gCAAA,cAAc,IAAI;AAE9B,UAAI;AAEHA,sBAAAA,MAAI,eAAe,SAAS,KAAK,KAAK;AACtCA,sBAAAA,MAAI,eAAe,YAAY,KAAK,QAAQ;AAC5CA,sBAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,KAAK,KAAK;AACnCA,sBAAA,MAAA,MAAA,OAAA,gCAAY,gBAAgB,KAAK,QAAQ;AAGzC,cAAM,aAAaA,cAAAA,MAAI,eAAe,OAAO;AAC7C,cAAM,gBAAgBA,cAAAA,MAAI,eAAe,UAAU;AACnDA,sBAAA,MAAA,MAAA,OAAA,gCAAY,iBAAiB,UAAU;AACvCA,sBAAY,MAAA,MAAA,OAAA,gCAAA,oBAAoB,aAAa;AAG7CC,kCAAU,mBAAmB,KAAK;AAGlCD,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAGD,YAAI,CAACC,gBAAS,UAAC,aAAa;AAC3BD,wBAAAA,MAAY,MAAA,OAAA,gCAAA,sBAAsB;AAClC,gBAAMC,gBAAAA,UAAU;QACjB;AAGAA,wBAAS,UAAC,WAAW;AAIrBD,sBAAAA,mDAAY,YAAY;AACxB,mBAAW,MAAM;AAChBA,wBAAAA,mDAAY,QAAQ;AACpBA,wBAAAA,MAAI,UAAU;AAAA,YACb,KAAK;AAAA,YACL,SAAS,WAAW;AACnBA,4BAAAA,MAAA,MAAA,OAAA,gCAAY,MAAM;AAAA,YAClB;AAAA,YACD,MAAM,SAAS,KAAK;AACnBA,4BAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,GAAG;AAE1BA,4BAAAA,MAAI,WAAW;AAAA,gBACd,KAAK;AAAA,cACN,CAAC;AAAA,YACF;AAAA,UACD,CAAC;AAAA,QACD,GAAE,IAAI;AAAA,MACN,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,gCAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,kBAAkB,MAAM;AACvB,WAAK,YAAY;AACjBA,oBAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,IAAI;AAE3BA,0BAAI,kBAAkB,OAAO;AAC7BA,0BAAI,kBAAkB,UAAU;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,UAAU;AAAA,MACX,CAAC;AAAA,IACD;AAAA,IAED,sBAAsB,MAAM;AAC3B,WAAK,YAAY;AACjBA,oBAAY,MAAA,MAAA,OAAA,gCAAA,cAAc,IAAI;AAE9B,UAAI;AAEHA,sBAAAA,MAAI,eAAe,SAAS,KAAK,KAAK;AACtCA,sBAAAA,MAAI,eAAe,YAAY,KAAK,QAAQ;AAC5CA,sBAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,KAAK,KAAK;AACnCA,sBAAA,MAAA,MAAA,OAAA,gCAAY,gBAAgB,KAAK,QAAQ;AAGzC,cAAM,aAAaA,cAAAA,MAAI,eAAe,OAAO;AAC7C,cAAM,gBAAgBA,cAAAA,MAAI,eAAe,UAAU;AACnDA,sBAAA,MAAA,MAAA,OAAA,gCAAY,iBAAiB,UAAU;AACvCA,sBAAY,MAAA,MAAA,OAAA,gCAAA,oBAAoB,aAAa;AAG7CC,kCAAU,mBAAmB,KAAK;AAGlC,YAAI,YAAY;AAChB,YAAI,KAAK,YAAY,KAAK,SAAS,QAAQ;AAC1C,sBAAY;AAAA,KAAQ,KAAK,SAAS,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,OAAO,IAAI,CAAC,QAAQ,KAAK,SAAS,OAAO,IAAI,CAAC;AAAA,QACrJ;AACA,YAAI,aAAa,KAAK,YAAY,KAAK,SAAS,UAAU;AAAA,KAAQ,KAAK,SAAS,OAAO,KAAK;AAG5FD,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS,cAAc,SAAS,GAAG,UAAU;AAAA,UAC7C,YAAY;AAAA,UACZ,SAAS,YAAY;AACpB,gBAAI;AAEH,kBAAI,CAACC,gBAAS,UAAC,aAAa;AAC3BD,8BAAAA,MAAY,MAAA,OAAA,gCAAA,sBAAsB;AAClC,sBAAMC,gBAAAA,UAAU;cACjB;AAGAA,8BAAS,UAAC,WAAW;AAGrBD,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,gBACN,UAAU;AAAA,cACX,CAAC;AAGDA,4BAAAA,MAAA,MAAA,OAAA,gCAAY,YAAY;AACxB,yBAAW,MAAM;AAChBA,8BAAAA,MAAY,MAAA,OAAA,gCAAA,QAAQ;AACpBA,8BAAAA,MAAI,UAAU;AAAA,kBACb,KAAK;AAAA,kBACL,SAAS,WAAW;AACnBA,kCAAAA,MAAY,MAAA,OAAA,gCAAA,MAAM;AAAA,kBAClB;AAAA,kBACD,MAAM,SAAS,KAAK;AACnBA,kCAAA,MAAA,MAAA,SAAA,gCAAc,SAAS,GAAG;AAE1BA,kCAAAA,MAAI,WAAW;AAAA,sBACd,KAAK;AAAA,oBACN,CAAC;AAAA,kBACF;AAAA,gBACD,CAAC;AAAA,cACD,GAAE,GAAI;AAAA,YACN,SAAO,OAAO;AACfA,4BAAc,MAAA,MAAA,SAAA,gCAAA,iBAAiB,KAAK;AACpCA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACP,CAAC;AAAA,YACF;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACA,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,gCAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,qBAAqB,MAAM;AAC1B,WAAK,YAAY;AAEjBA,0BAAI,kBAAkB,OAAO;AAC7BA,0BAAI,kBAAkB,UAAU;AAChCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,MACP,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpmBA,GAAG,WAAW,eAAe;"}