{"version": 3, "file": "crafting.js", "sources": ["pages/crafting/crafting.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY3JhZnRpbmcvY3JhZnRpbmcudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"container\">\r\n\t\t<!-- 头部信息 -->\r\n\t\t<view class=\"header\">\r\n\t\t\t<view class=\"crafting-level\">\r\n\t\t\t\t<text class=\"level-label\">打造等级:</text>\r\n\t\t\t\t<text class=\"level-value\">{{ craftingLevel }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"stamina-info\">\r\n\t\t\t\t<text class=\"stamina-label\">体力:</text>\r\n\t\t\t\t<text class=\"stamina-value\">{{ player.energy || 0 }}/{{ player.max_energy || 100 }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 材料统计 已移除 -->\r\n\r\n\t\t<!-- 可合成物品列表 -->\r\n\t\t<view class=\"recipes-section\">\r\n\t\t\t<view class=\"section-header\">\r\n\t\t\t\t<text class=\"section-title\">可合成物品 ({{ craftableItems.length }})</text>\r\n\t\t\t\t<button class=\"refresh-btn\" @click=\"loadCraftableItems\" :disabled=\"isLoading\">\r\n\t\t\t\t\t{{ isLoading ? '加载中...' : '刷新' }}\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 加载状态 -->\r\n\t\t\t<view v-if=\"isLoading\" class=\"loading-container\">\r\n\t\t\t\t<text class=\"loading-text\">正在加载可合成物品...</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 空状态 -->\r\n\t\t\t<view v-else-if=\"craftableItems.length === 0\" class=\"empty-container\">\r\n\t\t\t\t<text class=\"empty-icon\">🔧</text>\r\n\t\t\t\t<text class=\"empty-text\">暂无可合成物品</text>\r\n\t\t\t\t<text class=\"empty-desc\">请收集更多材料来解锁合成配方</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 物品列表 -->\r\n\t\t\t<view v-else class=\"recipes-grid\">\r\n\t\t\t\t<view \r\n\t\t\t\t\tv-for=\"recipe in craftableItems\" \r\n\t\t\t\t\t:key=\"recipe.id\" \r\n\t\t\t\t\tclass=\"recipe-item\"\r\n\t\t\t\t\t:class=\"{ 'can-craft': recipe.can_craft, 'cannot-craft': !recipe.can_craft }\"\r\n\t\t\t\t\t@click=\"showRecipeDetail(recipe)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<text class=\"recipe-icon\">{{ recipe.icon || '📦' }}</text>\r\n\t\t\t\t\t<text class=\"recipe-name\">{{ recipe.name }}</text>\r\n\t\t\t\t\t<text class=\"recipe-quality\">{{ getQualityName(recipe.quality) }}</text>\r\n\t\t\t\t\t<view class=\"recipe-type\">{{ getTypeName(recipe.type) }}</view>\r\n\t\t\t\t\t<view class=\"craft-status\">\r\n\t\t\t\t\t\t<text v-if=\"recipe.can_craft\" class=\"can-craft-text\">可合成</text>\r\n\t\t\t\t\t\t<text v-else class=\"cannot-craft-text\">材料不足</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 合成详情弹窗 -->\r\n\t\t<view v-if=\"showDetail\" class=\"detail-modal\" @click=\"closeDetail\">\r\n\t\t\t<view class=\"detail-content\" @click.stop>\r\n\t\t\t\t<view class=\"detail-header\">\r\n\t\t\t\t\t<text class=\"detail-title\">{{ selectedRecipe.name }}</text>\r\n\t\t\t\t\t<text class=\"detail-quality\">{{ getQualityName(selectedRecipe.quality) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"detail-info\">\r\n\t\t\t\t\t<text class=\"detail-desc\">{{ selectedRecipe.description }}</text>\r\n\t\t\t\t\t<text class=\"detail-stamina\">消耗体力: 10点</text>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<view class=\"materials-section\">\r\n\t\t\t\t\t\t<text class=\"materials-title\">所需材料:</text>\r\n\t\t\t\t\t\t<view class=\"materials-list\">\r\n\t\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\t\tv-for=\"(quantity, material) in parseRecipe(selectedRecipe.craft_recipe)\" \r\n\t\t\t\t\t\t\t\t:key=\"material\"\r\n\t\t\t\t\t\t\t\tclass=\"material-item\"\r\n\t\t\t\t\t\t\t\t:class=\"{ 'sufficient': getMaterialCount(material) >= quantity, 'insufficient': getMaterialCount(material) < quantity }\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<text class=\"material-name\">{{ material }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"material-quantity\">{{ getMaterialCount(material) }}/{{ quantity }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t<!-- 缺少材料提示 -->\r\n\t\t\t\t\t\t<view v-if=\"getMissingMaterials(selectedRecipe).length > 0\" class=\"missing-materials\">\r\n\t\t\t\t\t\t\t<text class=\"missing-title\">缺少材料:</text>\r\n\t\t\t\t\t\t\t<view class=\"missing-list\">\r\n\t\t\t\t\t\t\t\t<text v-for=\"material in getMissingMaterials(selectedRecipe)\" :key=\"material\" class=\"missing-item\">\r\n\t\t\t\t\t\t\t\t\t{{ material }}\r\n\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"modal-footer\">\r\n\t\t\t\t\t<button class=\"modal-btn cancel-btn\" @click=\"closeDetail\">关闭</button>\r\n\t\t\t\t\t<button \r\n\t\t\t\t\t\tclass=\"modal-btn confirm-btn\" \r\n\t\t\t\t\t\t@click=\"craftItem(selectedRecipe)\"\r\n\t\t\t\t\t\t:disabled=\"!canCraft(selectedRecipe)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t合成\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport wsManager from '../../utils/websocket.js'\r\nimport gameState from '../../utils/gameState.js'\r\nimport { gameUtils } from '../../utils/gameData.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tplayer: {},\r\n\t\t\tcraftingLevel: 1,\r\n\t\t\tcraftableItems: [],\r\n\t\t\tshowDetail: false,\r\n\t\t\tselectedRecipe: null,\r\n\t\t\tisLoading: false\r\n\t\t}\r\n\t},\r\n\t\r\n\tcomputed: {\r\n\t\t// 移除材料统计 - 只显示合成配方中需要的材料\r\n\t},\r\n\t\r\n\tonLoad() {\r\n\t\tthis.updateData()\r\n\t\tthis.loadCraftableItems()\r\n\t},\r\n\t\r\n\tonShow() {\r\n\t\tthis.updateData()\r\n\t\tthis.loadCraftableItems()\r\n\t},\r\n\t\r\n\tonUnload() {\r\n\t\t// No specific cleanup needed here as gameUtils handles its own listeners\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\tupdateData() {\r\n\t\t\tthis.player = { ...gameState.player }\r\n\t\t\tthis.isAuthed = gameState.isAuthed || false\r\n\t\t\t// 固定打造等级为1\r\n\t\t\tthis.craftingLevel = 1\r\n\t\t},\r\n\t\t\r\n\t\tasync loadCraftableItems() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.isLoading = true\r\n\t\t\t\t// 优先尝试 crafting_action\r\n\t\t\t\tlet response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'crafting_action',\r\n\t\t\t\t\tdata: { action: 'get_craftable_items' }\r\n\t\t\t\t});\r\n\t\t\t\t// 兼容后端直接推送 get_craftable_success\r\n\t\t\t\tif (response.type !== 'get_craftable_success' && response.data && response.data.craftable_items) {\r\n\t\t\t\t\tresponse = { type: 'get_craftable_success', data: response.data };\r\n\t\t\t\t}\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tif (response.type === 'get_craftable_success') {\r\n\t\t\t\t\tthis.craftableItems = response.data.craftable_items || [];\r\n\t\t\t\t\tthis.player.energy = response.data.energy || 0;\r\n\t\t\t\t} else if (response.type === 'get_craftable_failed') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '获取可合成物品失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (response.type === 'error') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '获取可合成物品失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tthis.isLoading = false\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '加载失败: ' + (error.message || '未知错误'),\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tparseRecipe(recipe) {\r\n\t\t\tconst materials = {};\r\n\t\t\tif (!recipe) return materials;\r\n\r\n\t\t\t// 如果是对象，直接返回\r\n\t\t\tif (typeof recipe === 'object') {\r\n\t\t\t\treturn recipe;\r\n\t\t\t}\r\n\r\n\t\t\t// 如果是字符串，尝试解析JSON或单引号对象字符串\r\n\t\t\tif (typeof recipe === 'string') {\r\n\t\t\t\tif (recipe.trim().startsWith('{')) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\t// 尝试标准JSON\r\n\t\t\t\t\t\treturn JSON.parse(recipe);\r\n\t\t\t\t\t} catch (e) {\r\n\t\t\t\t\t\t// 尝试单引号对象字符串\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst fixed = recipe.replace(/'/g, '\"');\r\n\t\t\t\t\t\t\treturn JSON.parse(fixed);\r\n\t\t\t\t\t\t} catch (e2) {}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// 否则按字符串分割\r\n\t\t\tconst parts = recipe.split(',');\r\n\t\t\tfor (const part of parts) {\r\n\t\t\t\tif (part.includes(':')) {\r\n\t\t\t\t\tconst [material, quantity] = part.split(':');\r\n\t\t\t\t\tmaterials[material.trim()] = parseInt(quantity.trim());\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\treturn materials;\r\n\t\t},\r\n\t\t\r\n\t\tgetMaterialCount(materialName) {\r\n\t\t\t// 从gameState的背包中统计材料数量，支持name和id双重匹配\r\n\t\t\tlet count = 0\r\n\t\t\tgameState.inventory.forEach(item => {\r\n\t\t\t\t// 直接匹配name\r\n\t\t\t\tif (item.name === materialName) {\r\n\t\t\t\t\tcount += item.quantity || 1\r\n\t\t\t\t}\r\n\t\t\t\t// 如果name没匹配到，尝试通过id匹配\r\n\t\t\t\telse if (item.id && item.id.includes(materialName.replace('残页', '_canyie'))) {\r\n\t\t\t\t\tcount += item.quantity || 1\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\treturn count\r\n\t\t},\r\n\t\t\r\n\t\tgetMissingMaterials(recipe) {\r\n\t\t\tif (!recipe || !recipe.craft_recipe) return []\r\n\t\t\t\r\n\t\t\tconst materials = this.parseRecipe(recipe.craft_recipe)\r\n\t\t\tconst missing = []\r\n\t\t\t\r\n\t\t\tfor (const [material, quantity] of Object.entries(materials)) {\r\n\t\t\t\tif (this.getMaterialCount(material) < quantity) {\r\n\t\t\t\t\tmissing.push(`${material}(需要${quantity}，现有${this.getMaterialCount(material)})`)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn missing\r\n\t\t},\r\n\t\t\r\n\t\tgetQualityColor(quality) {\r\n\t\t\treturn gameUtils.getQualityColor(quality)\r\n\t\t},\r\n\t\t\r\n\t\tgetQualityName(quality) {\r\n\t\t\tconst qualities = {\r\n\t\t\t\t'common': '普通',\r\n\t\t\t\t'fine': '精良',\r\n\t\t\t\t'rare': '稀有',\r\n\t\t\t\t'epic': '传说',\r\n\t\t\t\t'legendary': '神品'\r\n\t\t\t}\r\n\t\t\treturn qualities[quality] || '普通'\r\n\t\t},\r\n\t\t\r\n\t\tgetTypeName(type) {\r\n\t\t\tconst types = {\r\n\t\t\t\t'book': '秘籍',\r\n\t\t\t\t'consumable': '消耗品',\r\n\t\t\t\t'weapon': '武器',\r\n\t\t\t\t'armor': '防具',\r\n\t\t\t\t'necklace': '项链',\r\n\t\t\t\t'helmet': '头盔',\r\n\t\t\t\t'offhand': '副手',\r\n\t\t\t\t'accessory': '饰品',\r\n\t\t\t\t'medal': '勋章'\r\n\t\t\t}\r\n\t\t\treturn types[type] || type\r\n\t\t},\r\n\t\t\r\n\t\tcanCraft(recipe) {\r\n\t\t\tif (!recipe || !recipe.craft_recipe) return false\r\n\t\t\t\r\n\t\t\t// 优先使用后端返回的can_craft状态\r\n\t\t\tif (recipe.can_craft !== undefined) {\r\n\t\t\t\t// 但还要检查体力是否足够\r\n\t\t\t\tif (this.player.energy < 10) {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t\treturn recipe.can_craft\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 如果后端没有返回can_craft状态，则前端自己计算\r\n\t\t\tconst materials = this.parseRecipe(recipe.craft_recipe)\r\n\t\t\tfor (const [material, quantity] of Object.entries(materials)) {\r\n\t\t\t\tif (this.getMaterialCount(material) < quantity) {\r\n\t\t\t\t\treturn false\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 检查体力是否足够\r\n\t\t\tif (this.player.energy < 10) {\r\n\t\t\t\treturn false\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn true\r\n\t\t},\r\n\t\t\r\n\t\tshowRecipeDetail(recipe) {\r\n\t\t\tthis.selectedRecipe = recipe\r\n\t\t\tthis.showDetail = true\r\n\t\t},\r\n\t\t\r\n\t\tcloseDetail() {\r\n\t\t\tthis.showDetail = false\r\n\t\t\tthis.selectedRecipe = null\r\n\t\t},\r\n\t\t\r\n\t\tcraftItem(recipe) {\r\n\t\t\tif (!gameState.isAuthed) {\r\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' })\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\tif (!this.canCraft(recipe)) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '材料或体力不足',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\treturn\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '确认合成',\r\n\t\t\t\tcontent: `确定要合成 ${recipe.name} 吗？\\n消耗体力: 10点`,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.performCraft(recipe)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\t\r\n\t\tasync performCraft(recipe) {\r\n\t\t\ttry {\r\n\t\t\t\tthis.isLoading = true\r\n\t\t\t\t\r\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\r\n\t\t\t\tconst response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'crafting_action',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\taction: 'craft_item',\r\n\t\t\t\t\t\titem_id: recipe.id\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (response.type === 'craft_success') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '合成成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 更新本地数据\r\n\t\t\t\t\tif (response.data.inventory) {\r\n\t\t\t\t\t\tgameState.inventory = response.data.inventory;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (response.data.energy !== undefined) {\r\n\t\t\t\t\t\tthis.player.energy = response.data.energy;\r\n\t\t\t\t\t\tgameState.player.energy = response.data.energy;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.closeDetail();\r\n\t\t\t\t\tthis.loadCraftableItems(); // 重新加载可合成物品列表\r\n\t\t\t\t} else if (response.type === 'craft_failed') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '合成失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (response.type === 'error') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '合成失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('合成失败:', error)\r\n\t\t\t\tthis.isLoading = false\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '合成失败: ' + (error.message || '未知错误'),\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n\tpadding: 20rpx;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tbackground: rgba(255, 255, 255, 0.9);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.crafting-level,\r\n.stamina-info {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.level-label,\r\n.stamina-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.level-value,\r\n.stamina-value {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n/* 材料统计 */\r\n.materials-summary {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 20rpx;\r\n\tmargin-bottom: 20rpx;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.summary-title {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 15rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.no-materials {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 30rpx;\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 10rpx;\r\n\tborder: 1rpx dashed #ccc;\r\n}\r\n\r\n.no-materials-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.materials-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(auto-fill, minmax(150rpx, 1fr));\r\n\tgap: 10rpx;\r\n}\r\n\r\n.material-summary-item {\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 10rpx;\r\n\ttext-align: center;\r\n\tborder: 1rpx solid #e0e0e0;\r\n}\r\n\r\n.material-summary-name {\r\n\tfont-size: 24rpx;\r\n\tcolor: #333;\r\n\tdisplay: block;\r\n\tmargin-bottom: 5rpx;\r\n}\r\n\r\n.material-summary-count {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #667eea;\r\n}\r\n\r\n.recipes-section {\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tborder-radius: 20rpx;\r\n\tpadding: 20rpx;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.section-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.refresh-btn {\r\n\tbackground: #667eea;\r\n\tcolor: white;\r\n\tborder: none;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 10rpx 20rpx;\r\n\tfont-size: 24rpx;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.refresh-btn:hover {\r\n\tbackground: #5a6fd8;\r\n}\r\n\r\n.refresh-btn[disabled] {\r\n\tbackground: #bdc3c7;\r\n\tcursor: not-allowed;\r\n}\r\n\r\n/* 加载状态 */\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\tpadding: 60rpx;\r\n}\r\n\r\n.loading-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 空状态 */\r\n.empty-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tpadding: 80rpx 20rpx;\r\n\ttext-align: center;\r\n}\r\n\r\n.empty-icon {\r\n\tfont-size: 80rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.empty-desc {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.5;\r\n}\r\n\r\n.recipes-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));\r\n\tgap: 15rpx;\r\n}\r\n\r\n.recipe-item {\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 15rpx;\r\n\tpadding: 20rpx;\r\n\ttext-align: center;\r\n\tborder: 2rpx solid #e0e0e0;\r\n\ttransition: all 0.3s ease;\r\n\tcursor: pointer;\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n}\r\n\r\n.recipe-item:hover {\r\n\ttransform: translateY(-5rpx);\r\n\tbox-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.recipe-item.can-craft {\r\n\tborder-color: #27ae60;\r\n\tbackground: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);\r\n}\r\n\r\n.recipe-item.cannot-craft {\r\n\tborder-color: #e74c3c;\r\n\tbackground: linear-gradient(135deg, #fdecea 0%, #ffcdd2 100%);\r\n}\r\n\r\n.recipe-icon {\r\n\tfont-size: 48rpx;\r\n\tmargin-bottom: 15rpx;\r\n\tcolor: #667eea;\r\n\tdisplay: block;\r\n}\r\n\r\n.recipe-name {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 8rpx;\r\n\tdisplay: block;\r\n\tline-height: 1.2;\r\n}\r\n\r\n.recipe-quality {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 8rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.recipe-type {\r\n\tfont-size: 22rpx;\r\n\tcolor: #999;\r\n\tmargin-bottom: 15rpx;\r\n\tdisplay: block;\r\n\tbackground: rgba(102, 126, 234, 0.1);\r\n\tpadding: 4rpx 8rpx;\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.craft-status {\r\n\tmargin-top: 10rpx;\r\n}\r\n\r\n.can-craft-text {\r\n\tcolor: #27ae60;\r\n\tfont-size: 26rpx;\r\n\tfont-weight: bold;\r\n\tbackground: rgba(39, 174, 96, 0.1);\r\n\tpadding: 6rpx 12rpx;\r\n\tborder-radius: 20rpx;\r\n\tdisplay: inline-block;\r\n}\r\n\r\n.cannot-craft-text {\r\n\tcolor: #e74c3c;\r\n\tfont-size: 26rpx;\r\n\tfont-weight: bold;\r\n\tbackground: rgba(231, 76, 60, 0.1);\r\n\tpadding: 6rpx 12rpx;\r\n\tborder-radius: 20rpx;\r\n\tdisplay: inline-block;\r\n}\r\n\r\n.detail-modal {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0, 0, 0, 0.5);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 1000;\r\n\tanimation: fadeIn 0.3s ease;\r\n}\r\n\r\n.detail-content {\r\n\tbackground: white;\r\n\tborder-radius: 20rpx;\r\n\twidth: 90%;\r\n\tmax-width: 600rpx;\r\n\tmax-height: 80vh;\r\n\toverflow: hidden;\r\n\tanimation: slideUp 0.3s ease;\r\n}\r\n\r\n.detail-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #f0f0f0;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tcolor: white;\r\n}\r\n\r\n.detail-title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: bold;\r\n\tcolor: white;\r\n}\r\n\r\n.detail-quality {\r\n\tfont-size: 28rpx;\r\n\tcolor: rgba(255, 255, 255, 0.8);\r\n\tbackground: rgba(255, 255, 255, 0.2);\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.detail-info {\r\n\tpadding: 30rpx;\r\n\tmax-height: 400rpx;\r\n\toverflow-y: auto;\r\n}\r\n\r\n.detail-desc {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.5;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.detail-stamina {\r\n\tfont-size: 28rpx;\r\n\tcolor: #e74c3c;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 20rpx;\r\n\tdisplay: block;\r\n\tbackground: rgba(231, 76, 60, 0.1);\r\n\tpadding: 10rpx 15rpx;\r\n\tborder-radius: 10rpx;\r\n}\r\n\r\n.materials-section {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.materials-title {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 15rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.materials-list {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 10rpx;\r\n}\r\n\r\n.material-item {\r\n\tbackground: #f8f9fa;\r\n\tborder-radius: 10rpx;\r\n\tpadding: 15rpx;\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tfont-size: 26rpx;\r\n\tcolor: #333;\r\n\tborder: 1rpx solid #e0e0e0;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.material-item.sufficient {\r\n\tcolor: #27ae60;\r\n\tfont-weight: bold;\r\n\tbackground: #e8f5e9;\r\n\tborder-color: #27ae60;\r\n}\r\n\r\n.material-item.insufficient {\r\n\tcolor: #e74c3c;\r\n\tfont-weight: bold;\r\n\tbackground: #fdecea;\r\n\tborder-color: #e74c3c;\r\n}\r\n\r\n.material-name {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.material-quantity {\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n/* 缺少材料提示 */\r\n.missing-materials {\r\n\tmargin-top: 20rpx;\r\n\tpadding: 15rpx;\r\n\tbackground: #fdecea;\r\n\tborder-radius: 10rpx;\r\n\tborder: 1rpx solid #e74c3c;\r\n}\r\n\r\n.missing-title {\r\n\tfont-size: 26rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #e74c3c;\r\n\tmargin-bottom: 10rpx;\r\n\tdisplay: block;\r\n}\r\n\r\n.missing-list {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 8rpx;\r\n}\r\n\r\n.missing-item {\r\n\tfont-size: 24rpx;\r\n\tcolor: #e74c3c;\r\n\tbackground: rgba(231, 76, 60, 0.1);\r\n\tpadding: 4rpx 8rpx;\r\n\tborder-radius: 6rpx;\r\n}\r\n\r\n.modal-footer {\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\tpadding: 30rpx;\r\n\tborder-top: 1rpx solid #f0f0f0;\r\n\tbackground: #f8f9fa;\r\n}\r\n\r\n.modal-btn {\r\n\tflex: 1;\r\n\tpadding: 20rpx;\r\n\tborder-radius: 15rpx;\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tborder: none;\r\n\tcursor: pointer;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.cancel-btn {\r\n\tbackground: #95a5a6;\r\n\tcolor: white;\r\n}\r\n\r\n.cancel-btn:hover {\r\n\tbackground: #7f8c8d;\r\n}\r\n\r\n.confirm-btn {\r\n\tbackground: #27ae60;\r\n\tcolor: white;\r\n}\r\n\r\n.confirm-btn:hover {\r\n\tbackground: #229954;\r\n}\r\n\r\n.confirm-btn[disabled] {\r\n\tbackground: #bdc3c7;\r\n\tcolor: #7f8c8d;\r\n\tcursor: not-allowed;\r\n}\r\n\r\n@keyframes fadeIn {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n@keyframes slideUp {\r\n\tfrom {\r\n\t\ttransform: translateY(50rpx);\r\n\t\topacity: 0;\r\n\t}\r\n\tto {\r\n\t\ttransform: translateY(0);\r\n\t\topacity: 1;\r\n\t}\r\n}\r\n\r\n/* 响应式设计 */\r\n@media screen and (max-width: 375px) {\r\n\t.recipes-grid {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t\tgap: 10rpx;\r\n\t}\r\n\t\r\n\t.materials-grid {\r\n\t\tgrid-template-columns: repeat(2, 1fr);\r\n\t}\r\n\t\r\n\t.recipe-item {\r\n\t\tpadding: 15rpx;\r\n\t}\r\n\t\r\n\t.recipe-icon {\r\n\t\tfont-size: 36rpx;\r\n\t}\r\n\t\r\n\t.recipe-name {\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n}\r\n\r\n@media screen and (min-width: 376px) and (max-width: 750px) {\r\n\t.recipes-grid {\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t}\r\n\t\r\n\t.materials-grid {\r\n\t\tgrid-template-columns: repeat(3, 1fr);\r\n\t}\r\n}\r\n\r\n@media screen and (min-width: 751px) {\r\n\t.recipes-grid {\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t}\r\n\t\r\n\t.materials-grid {\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/zjjhx/仗剑江湖行/pages/crafting/crafting.vue'\nwx.createPage(MiniProgramPage)"], "names": ["gameState", "gameUtils", "uni"], "mappings": ";;;;;AAoHA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,QAAQ,CAAE;AAAA,MACV,eAAe;AAAA,MACf,gBAAgB,CAAE;AAAA,MAClB,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EAED,UAAU;AAAA;AAAA,EAET;AAAA,EAED,SAAS;AACR,SAAK,WAAW;AAChB,SAAK,mBAAmB;AAAA,EACxB;AAAA,EAED,SAAS;AACR,SAAK,WAAW;AAChB,SAAK,mBAAmB;AAAA,EACxB;AAAA,EAED,WAAW;AAAA,EAEV;AAAA,EAED,SAAS;AAAA,IACR,aAAa;AACZ,WAAK,SAAS,EAAE,GAAGA,gBAAAA,UAAU,OAAO;AACpC,WAAK,WAAWA,0BAAU,YAAY;AAEtC,WAAK,gBAAgB;AAAA,IACrB;AAAA,IAED,MAAM,qBAAqB;AAC1B,UAAI;AACH,aAAK,YAAY;AAEjB,YAAI,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC1C,MAAM;AAAA,UACN,MAAM,EAAE,QAAQ,sBAAsB;AAAA,QACvC,CAAC;AAED,YAAI,SAAS,SAAS,2BAA2B,SAAS,QAAQ,SAAS,KAAK,iBAAiB;AAChG,qBAAW,EAAE,MAAM,yBAAyB,MAAM,SAAS;QAC5D;AACA,aAAK,YAAY;AACjB,YAAI,SAAS,SAAS,yBAAyB;AAC9C,eAAK,iBAAiB,SAAS,KAAK,mBAAmB,CAAA;AACvD,eAAK,OAAO,SAAS,SAAS,KAAK,UAAU;AAAA,mBACnC,SAAS,SAAS,wBAAwB;AACpDC,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAAA,QACF,WAAW,SAAS,SAAS,SAAS;AACrCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACf,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM,WAAW;AAAA,UACpC,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACA;AAAA,IAED,YAAY,QAAQ;AACnB,YAAM,YAAY,CAAA;AAClB,UAAI,CAAC;AAAQ,eAAO;AAGpB,UAAI,OAAO,WAAW,UAAU;AAC/B,eAAO;AAAA,MACR;AAGA,UAAI,OAAO,WAAW,UAAU;AAC/B,YAAI,OAAO,KAAI,EAAG,WAAW,GAAG,GAAG;AAClC,cAAI;AAEH,mBAAO,KAAK,MAAM,MAAM;AAAA,UACzB,SAAS,GAAG;AAEX,gBAAI;AACH,oBAAM,QAAQ,OAAO,QAAQ,MAAM,GAAG;AACtC,qBAAO,KAAK,MAAM,KAAK;AAAA,qBACf,IAAI;AAAA,YAAC;AAAA,UACf;AAAA,QACD;AAAA,MACD;AAGA,YAAM,QAAQ,OAAO,MAAM,GAAG;AAC9B,iBAAW,QAAQ,OAAO;AACzB,YAAI,KAAK,SAAS,GAAG,GAAG;AACvB,gBAAM,CAAC,UAAU,QAAQ,IAAI,KAAK,MAAM,GAAG;AAC3C,oBAAU,SAAS,KAAM,CAAA,IAAI,SAAS,SAAS,KAAI,CAAE;AAAA,QACtD;AAAA,MACD;AACA,aAAO;AAAA,IACP;AAAA,IAED,iBAAiB,cAAc;AAE9B,UAAI,QAAQ;AACZF,gCAAU,UAAU,QAAQ,UAAQ;AAEnC,YAAI,KAAK,SAAS,cAAc;AAC/B,mBAAS,KAAK,YAAY;AAAA,QAC3B,WAES,KAAK,MAAM,KAAK,GAAG,SAAS,aAAa,QAAQ,MAAM,SAAS,CAAC,GAAG;AAC5E,mBAAS,KAAK,YAAY;AAAA,QAC3B;AAAA,OACA;AACD,aAAO;AAAA,IACP;AAAA,IAED,oBAAoB,QAAQ;AAC3B,UAAI,CAAC,UAAU,CAAC,OAAO;AAAc,eAAO,CAAC;AAE7C,YAAM,YAAY,KAAK,YAAY,OAAO,YAAY;AACtD,YAAM,UAAU,CAAC;AAEjB,iBAAW,CAAC,UAAU,QAAQ,KAAK,OAAO,QAAQ,SAAS,GAAG;AAC7D,YAAI,KAAK,iBAAiB,QAAQ,IAAI,UAAU;AAC/C,kBAAQ,KAAK,GAAG,QAAQ,MAAM,QAAQ,MAAM,KAAK,iBAAiB,QAAQ,CAAC,GAAG;AAAA,QAC/E;AAAA,MACD;AAEA,aAAO;AAAA,IACP;AAAA,IAED,gBAAgB,SAAS;AACxB,aAAOC,eAAS,UAAC,gBAAgB,OAAO;AAAA,IACxC;AAAA,IAED,eAAe,SAAS;AACvB,YAAM,YAAY;AAAA,QACjB,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,MACd;AACA,aAAO,UAAU,OAAO,KAAK;AAAA,IAC7B;AAAA,IAED,YAAY,MAAM;AACjB,YAAM,QAAQ;AAAA,QACb,QAAQ;AAAA,QACR,cAAc;AAAA,QACd,UAAU;AAAA,QACV,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,UAAU;AAAA,QACV,WAAW;AAAA,QACX,aAAa;AAAA,QACb,SAAS;AAAA,MACV;AACA,aAAO,MAAM,IAAI,KAAK;AAAA,IACtB;AAAA,IAED,SAAS,QAAQ;AAChB,UAAI,CAAC,UAAU,CAAC,OAAO;AAAc,eAAO;AAG5C,UAAI,OAAO,cAAc,QAAW;AAEnC,YAAI,KAAK,OAAO,SAAS,IAAI;AAC5B,iBAAO;AAAA,QACR;AACA,eAAO,OAAO;AAAA,MACf;AAGA,YAAM,YAAY,KAAK,YAAY,OAAO,YAAY;AACtD,iBAAW,CAAC,UAAU,QAAQ,KAAK,OAAO,QAAQ,SAAS,GAAG;AAC7D,YAAI,KAAK,iBAAiB,QAAQ,IAAI,UAAU;AAC/C,iBAAO;AAAA,QACR;AAAA,MACD;AAGA,UAAI,KAAK,OAAO,SAAS,IAAI;AAC5B,eAAO;AAAA,MACR;AAEA,aAAO;AAAA,IACP;AAAA,IAED,iBAAiB,QAAQ;AACxB,WAAK,iBAAiB;AACtB,WAAK,aAAa;AAAA,IAClB;AAAA,IAED,cAAc;AACb,WAAK,aAAa;AAClB,WAAK,iBAAiB;AAAA,IACtB;AAAA,IAED,UAAU,QAAQ;AACjB,UAAI,CAACD,gBAAS,UAAC,UAAU;AACxBE,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,QAAQ;AAC7C;AAAA,MACD;AACA,UAAI,CAAC,KAAK,SAAS,MAAM,GAAG;AAC3BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,SACN;AACD;AAAA,MACD;AAEAA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,SAAS,OAAO,IAAI;AAAA;AAAA,QAC7B,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,aAAa,MAAM;AAAA,UACzB;AAAA,QACD;AAAA,OACA;AAAA,IACD;AAAA,IAED,MAAM,aAAa,QAAQ;AAC1B,UAAI;AACH,aAAK,YAAY;AAGjB,cAAM,WAAW,MAAMD,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM;AAAA,YACL,QAAQ;AAAA,YACR,SAAS,OAAO;AAAA,UACjB;AAAA,QACD,CAAC;AAED,aAAK,YAAY;AAEjB,YAAI,SAAS,SAAS,iBAAiB;AACtCC,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAGD,cAAI,SAAS,KAAK,WAAW;AAC5BF,4BAAAA,UAAU,YAAY,SAAS,KAAK;AAAA,UACrC;AACA,cAAI,SAAS,KAAK,WAAW,QAAW;AACvC,iBAAK,OAAO,SAAS,SAAS,KAAK;AACnCA,4BAAAA,UAAU,OAAO,SAAS,SAAS,KAAK;AAAA,UACzC;AAEA,eAAK,YAAW;AAChB,eAAK,mBAAkB;AAAA,QACxB,WAAW,SAAS,SAAS,gBAAgB;AAC5CE,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAAA,QACF,WAAW,SAAS,SAAS,SAAS;AACrCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAAA,2DAAc,SAAS,KAAK;AAC5B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM,WAAW;AAAA,UACpC,MAAM;AAAA,SACN;AAAA,MACF;AAAA,IACD;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjZA,GAAG,WAAW,eAAe;"}