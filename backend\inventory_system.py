# 物品与背包管理系统
# 负责处理所有物品获取、消耗、背包数据推送等逻辑
import uuid
import logging
from item_system import item_system

logger = logging.getLogger(__name__)

async def add_item_to_inventory(player, item, count=1, user_id=None, save_func=None):
    """
    向玩家背包添加物品，支持异步保存。
    :param player: 玩家对象
    :param item: 物品对象或物品ID
    :param count: 数量
    :param user_id: 玩家ID
    :param save_func: 保存函数（如有）
    """
    # 兼容传入dict或id
    item_id = item['id'] if isinstance(item, dict) else item
    quantity = item.get('quantity', count) if isinstance(item, dict) else count
    item_obj = item_system.get_item(item_id)
    if not item_obj:
        logger.warning(f"尝试添加不存在的物品: {item_id}")
        return False
    # 检查是否已有相同物品且可堆叠
    if item_obj.stackable:
        existing_item = None
        for inv_item in player['inventory']:
            if inv_item['id'] == item_id:
                existing_item = inv_item
                break
        if existing_item:
            max_add = min(quantity, item_obj.max_stack - existing_item.get('quantity', 0))
            if max_add > 0:
                existing_item['quantity'] = existing_item.get('quantity', 0) + max_add
                logger.info(f"[ADD_ITEM] 堆叠物品: {item_id}, 数量: {existing_item['quantity']}")
                if save_func and user_id:
                    await save_func(user_id, player)
                return True
            else:
                logger.warning(f"物品堆叠已达上限: {item_id}")
                return False
    # 背包容量校验：只统计不同物品种类数
    inventory_capacity = player.get('inventory_capacity', 50)
    unique_item_ids = set([i['id'] for i in player['inventory']])
    if item_id not in unique_item_ids and len(unique_item_ids) >= inventory_capacity:
        logger.warning(f"背包已满，无法添加新物品: {item_id}")
        return False
    # 添加新物品
    inventory_item = item_system.create_inventory_item(item_id, quantity)
    inventory_item['type'] = item_obj.type.value
    inventory_item['quality'] = item_obj.quality.value
    inventory_item['sellable'] = item_obj.is_sellable
    inventory_item['unique_id'] = str(uuid.uuid4())
    for k in ['slot_type', 'desc', 'icon']:
        if hasattr(item_obj, k):
            inventory_item[k] = getattr(item_obj, k)
    player['inventory'].append(inventory_item)
    logger.info(f"[ADD_ITEM] 添加新物品: {item_id}, 背包种类数: {len(unique_item_ids) + 1 if item_id not in unique_item_ids else len(unique_item_ids)}")
    if save_func and user_id:
        await save_func(user_id, player)
    return True

async def handle_get_inventory_data(player):
    """
    获取背包数据，标准化物品unique_id。
    :param player: 玩家对象
    :return: 背包数据dict
    """
    if 'inventory' not in player:
        player['inventory'] = []
    for item in player['inventory']:
        if 'unique_id' not in item or not item['unique_id']:
            item['unique_id'] = str(uuid.uuid4())
    return {
        'inventory': player['inventory'],
        'capacity': player.get('inventory_capacity', 50)
    }

async def handle_equip_item(player, unique_id, slot_type, user_id=None, save_func=None, update_bonuses_func=None):
    """
    装备物品到指定槽位。
    :param player: 玩家对象
    :param unique_id: 背包物品唯一ID
    :param slot_type: 装备槽类型
    :param user_id: 玩家ID
    :param save_func: 保存函数
    :param update_bonuses_func: 属性更新函数
    """
    import copy
    import uuid
    item_index = next((i for i, item in enumerate(player['inventory']) if item.get('unique_id') == unique_id), -1)
    if item_index == -1:
        return {'type': 'error', 'data': {'message': '物品不存在'}}
    item = player['inventory'][item_index]
    # 检查类型
    if not can_equip_item(item, slot_type):
        return {'type': 'error', 'data': {'message': '物品类型不匹配此装备槽'}}
    # 可堆叠装备特殊处理
    if item.get('stackable', False) and item.get('quantity', 1) > 1:
        player['inventory'][item_index]['quantity'] -= 1
        equip_item = copy.deepcopy(item)
        equip_item['quantity'] = 1
        equip_item['unique_id'] = str(uuid.uuid4())
    else:
        equip_item = item
        player['inventory'].pop(item_index)
    old_equipment = player['equipment'][slot_type]
    if old_equipment:
        player['inventory'].append(old_equipment)
    player['equipment'][slot_type] = equip_item
    # 更新属性并保存
    if update_bonuses_func and user_id:
        player = await update_bonuses_func(user_id, player)
    if save_func and user_id:
        await save_func(user_id, player)
    return {
        'type': 'equip_success',
        'data': {
            'message': f'成功装备 {equip_item["name"]}',
            'equipment': player['equipment'],
            'inventory': player['inventory']
        }
    }

async def handle_unequip_item(player, slot_type, user_id=None, save_func=None, update_bonuses_func=None):
    """
    卸下装备到背包。
    :param player: 玩家对象
    :param slot_type: 装备槽类型
    :param user_id: 玩家ID
    :param save_func: 保存函数
    :param update_bonuses_func: 属性更新函数
    """
    from item_utils import standardize_item
    if not player['equipment'][slot_type]:
        return {'type': 'error', 'data': {'message': '该槽位没有装备'}}
    if len(player['inventory']) >= player.get('inventory_capacity', 50):
        return {'type': 'error', 'data': {'message': '背包已满，无法卸下装备'}}
    item = player['equipment'][slot_type]
    player['equipment'][slot_type] = None
    item = standardize_item(item)
    player['inventory'].append(item)
    if update_bonuses_func and user_id:
        player = await update_bonuses_func(user_id, player)
    if save_func and user_id:
        await save_func(user_id, player)
    return {
        'type': 'unequip_success',
        'data': {
            'message': f'成功卸下 {item["name"]}',
            'equipment': player['equipment'],
            'player': player
        }
    }

async def handle_destroy_item(player, unique_id, user_id=None, save_func=None):
    """
    销毁背包物品。
    :param player: 玩家对象
    :param unique_id: 背包物品唯一ID
    :param user_id: 玩家ID
    :param save_func: 保存函数
    """
    item_index = next((i for i, item in enumerate(player['inventory']) if item.get('unique_id') == unique_id), -1)
    if item_index == -1:
        return {'type': 'error', 'data': {'message': '物品不存在'}}
    item = player['inventory'].pop(item_index)
    if save_func and user_id:
        await save_func(user_id, player)
    return {
        'type': 'destroy_item_success',
        'data': {
            'message': f'已销毁 {item["name"]}',
            'inventory': player['inventory']
        }
    }

def can_equip_item(item, slot_type):
    item_id = item.get('id', '')
    item_obj = item_system.get_item(item_id)
    if not item_obj:
        return False
    return item_system.can_equip(item_obj, slot_type)

async def handle_expand_inventory(player, user_id=None, save_func=None):
    """
    扩充背包容量。
    :param player: 玩家对象
    :param user_id: 玩家ID
    :param save_func: 保存函数
    """
    current_capacity = player.get('inventory_capacity', 50)
    cost = 1000
    if player['money'] < cost:
        return {'type': 'error', 'data': {'message': '银两不足'}}
    if current_capacity >= 100:
        return {'type': 'error', 'data': {'message': '背包已达到最大容量'}}
    player['money'] -= cost
    player['inventory_capacity'] = current_capacity + 10
    if save_func and user_id:
        await save_func(user_id, player)
    return {
        'type': 'expand_success',
        'data': {
            'message': '背包扩充成功',
            'capacity': player['inventory_capacity'],
            'inventory': player['inventory']
        }
    } 