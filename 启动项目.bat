@echo off
chcp 65001
echo ========================================
echo           仗剑江湖行 - 启动助手
echo ========================================
echo.
echo 检测到您遇到"未找到app.json"错误
echo 这是因为您的项目是uni-app项目，需要先编译
echo.
echo 解决方案：
echo.
echo 1. 使用HBuilderX（推荐）：
echo    - 下载HBuilderX: https://www.dcloud.io/hbuilderx.html
echo    - 打开项目文件夹: D:\zjjhx\仗剑江湖行
echo    - 点击"运行" -> "运行到小程序模拟器" -> "微信开发者工具"
echo.
echo 2. 使用微信开发者工具：
echo    - 项目目录选择: D:\zjjhx\仗剑江湖行\unpackage\dist\dev\mp-weixin\
echo    - 注意：需要先通过HBuilderX编译生成此目录
echo.
echo 3. 检查Node.js环境：
echo    - 您的系统未安装Node.js，无法使用npm命令
echo    - 建议安装Node.js或直接使用HBuilderX
echo.
echo ========================================
echo 按任意键退出...
pause > nul 