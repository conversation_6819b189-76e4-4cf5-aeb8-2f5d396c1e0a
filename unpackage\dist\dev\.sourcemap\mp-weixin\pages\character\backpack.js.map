{"version": 3, "file": "backpack.js", "sources": ["pages/character/backpack.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2hhcmFjdGVyL2JhY2twYWNrLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"backpack-container\">\r\n\t\t<!-- 调试信息 -->\r\n\t\t<view style=\"background: red; color: white; padding: 10rpx; margin: 10rpx;\">\r\n\t\t\t<text>调试信息：背包页面已加载</text>\r\n\t\t\t<text>背包数据长度：{{ inventoryData.length }}</text>\r\n\t\t\t<text>背包容量：{{ inventoryCapacity }}</text>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 背包头部 -->\r\n\t\t<view class=\"backpack-header\">\r\n\t\t\t<text class=\"backpack-title\">🎒 背包</text>\r\n\t\t\t<text class=\"backpack-info\">{{ inventoryData.length }}/{{ inventoryCapacity }}</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 背包网格 -->\r\n\t\t<view class=\"backpack-grid\">\r\n\t\t\t<view \r\n\t\t\t\tv-for=\"(item, index) in inventoryData\" \r\n\t\t\t\t:key=\"index\" \r\n\t\t\t\tclass=\"item-slot\"\r\n\t\t\t\t:class=\"getItemQualityClass(item)\"\r\n\t\t\t\t@click=\"handleItemClick(item, index)\"\r\n\t\t\t>\r\n\t\t\t\t<text class=\"item-icon\">{{ item.icon || '📦' }}</text>\r\n\t\t\t\t<text class=\"item-name\">{{ item.name || item.名称 }}</text>\r\n\t\t\t\t<!-- 移除类型显示 -->\r\n\t\t\t\t<text v-if=\"item.数量 > 1 || item.quantity > 1\" class=\"item-quantity\">{{ item.数量 || item.quantity }}</text>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 空槽位 -->\r\n\t\t\t<view \r\n\t\t\t\tv-for=\"i in Math.max(0, inventoryCapacity - inventoryData.length)\" \r\n\t\t\t\t:key=\"`empty-${i}`\" \r\n\t\t\t\tclass=\"item-slot empty\"\r\n\t\t\t>\r\n\t\t\t\t<text class=\"empty-text\">空</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 操作按钮 -->\r\n\t\t<view class=\"backpack-actions\">\r\n\t\t\t<button class=\"action-btn expand-btn\" @click=\"expandInventory\">\r\n\t\t\t\t<text>📦 扩充背包</text>\r\n\t\t\t</button>\r\n\t\t\t<button class=\"action-btn sort-btn\" @click=\"sortInventory\">\r\n\t\t\t\t<text>🔄 整理背包</text>\r\n\t\t\t</button>\r\n\t\t\t<button \r\n\t\t\t\tclass=\"action-btn clear-btn\" \r\n\t\t\t\t@click=\"clearInventory\"\r\n\t\t\t\t:disabled=\"inventoryData.length === 0 || isLoading\"\r\n\t\t\t>\r\n\t\t\t\t<text>{{ isLoading ? '处理中...' : '🗑️ 清空背包' }}</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 返回按钮 -->\r\n\t\t<view class=\"backpack-actions back-actions\">\r\n\t\t\t<button class=\"action-btn back-btn\" @click=\"goBack\">\r\n\t\t\t\t<text>⬅️ 返回角色</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 物品详情弹窗 -->\r\n\t\t<view v-if=\"showItemDetail\" class=\"item-detail-modal\" @click=\"closeItemDetail\">\r\n\t\t\t<view class=\"item-detail-content\" @click.stop>\r\n\t\t\t\t<view class=\"item-detail-header\">\r\n\t\t\t\t\t<text class=\"item-detail-title\">{{ selectedItem.name || selectedItem.名称 }}</text>\r\n\t\t\t\t\t<text class=\"item-detail-quality\">{{ getQualityText(selectedItem.品质 || selectedItem.quality) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"item-detail-info\">\r\n\t\t\t\t\t<text class=\"item-detail-type\">类型：{{ getTypeText(selectedItem.类型 || selectedItem.type) }}</text>\r\n\t\t\t\t\t<text class=\"item-detail-quality-text\">品质：{{ getQualityText(selectedItem.品质 || selectedItem.quality) }}</text>\r\n\t\t\t\t\t<text v-if=\"selectedItem.数量 > 1 || selectedItem.quantity > 1\" class=\"item-detail-quantity\">数量：{{ selectedItem.数量 || selectedItem.quantity }}</text>\r\n\t\t\t\t\t<text v-if=\"selectedItem.攻击 || selectedItem.attack\" class=\"item-detail-stat\">攻击：{{ selectedItem.攻击 || selectedItem.attack }}</text>\r\n\t\t\t\t\t<text v-if=\"selectedItem.防御 || selectedItem.defense\" class=\"item-detail-stat\">防御：{{ selectedItem.防御 || selectedItem.defense }}</text>\r\n\t\t\t\t\t<text v-if=\"selectedItem.气血 || selectedItem.hp\" class=\"item-detail-stat\">气血：{{ selectedItem.气血 || selectedItem.hp }}</text>\r\n\t\t\t\t\t<text v-if=\"selectedItem.内力 || selectedItem.mp\" class=\"item-detail-stat\">内力：{{ selectedItem.内力 || selectedItem.mp }}</text>\r\n\t\t\t\t\t<text v-if=\"selectedItem.精力 || selectedItem.energy\" class=\"item-detail-stat\">精力：{{ selectedItem.精力 || selectedItem.energy }}</text>\r\n\t\t\t\t\t<text v-if=\"selectedItem.description || selectedItem.描述\" class=\"item-detail-desc\">{{ selectedItem.description || selectedItem.描述 }}</text>\r\n\t\t\t\t\t<!-- 新增：json格式展示所有字段 -->\r\n\t\t\t\t\t<view style=\"margin-top: 20rpx; background: #f8f8f8; padding: 10rpx; border-radius: 8rpx;\">\r\n\t\t\t\t\t\t<text style=\"font-size:24rpx;color:#888;\">所有字段：</text>\r\n\t\t\t\t\t\t<text style=\"font-size:22rpx;word-break:break-all;\">{{ JSON.stringify(selectedItem, null, 2) }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"item-detail-actions\">\r\n\t\t\t\t\t<button \r\n\t\t\t\t\t\tv-if=\"canEquip(selectedItem)\" \r\n\t\t\t\t\t\tclass=\"detail-action-btn equip-btn\" \r\n\t\t\t\t\t\t@click=\"equipItem\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text>⚔️ 装备</text>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<button \r\n\t\t\t\t\t\tv-if=\"canUse(selectedItem)\" \r\n\t\t\t\t\t\tclass=\"detail-action-btn use-btn\" \r\n\t\t\t\t\t\t@click=\"useItem\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<text>💊 使用</text>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<button class=\"detail-action-btn destroy-btn\" @click=\"destroyItem\">\r\n\t\t\t\t\t\t<text>🗑️ 销毁</text>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<button class=\"detail-action-btn cancel-btn\" @click=\"closeItemDetail\">\r\n\t\t\t\t\t\t<text>❌ 取消</text>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport wsManager from '../../utils/websocket.js'\r\nimport gameState from '@/utils/gameState.js'\r\nimport { gameUtils } from '../../utils/gameData.js'\r\n\r\nexport default {\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tinventoryData: [],\r\n\t\t\tinventoryCapacity: 50,\r\n\t\t\tshowItemDetail: false,\r\n\t\t\tselectedItem: null,\r\n\t\t\tselectedIndex: -1,\r\n\t\t\tisLoading: false,\r\n\t\t\t// 新增：物品配置\r\n\t\t\titemsConfig: {},\r\n\t\t}\r\n\t},\r\n\t\r\n\tonLoad() {\r\n\t\tconsole.log('[背包页面] onLoad 被调用');\r\n\t\tthis.loadInventoryData();\r\n\t\tthis.loadItemsConfig(); // 新增\r\n\t},\r\n\t\r\n\tonShow() {\r\n\t\tconsole.log('[背包页面] onShow 被调用');\r\n\t\tthis.loadInventoryData();\r\n\t},\r\n\t\r\n\tonUnload() {\r\n\t\tthis.cleanupEventListeners();\r\n\t},\r\n\t\r\n\tmethods: {\r\n\t\tasync loadInventoryData() {\r\n\t\t\ttry {\r\n\t\t\t\tconsole.log('[背包页面] 开始加载背包数据');\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\r\n\t\t\t\tconst response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'get_inventory_data',\r\n\t\t\t\t\tdata: {}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tconsole.log('[背包页面] 收到响应:', response);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (response.type === 'inventory_data') {\r\n\t\t\t\t\tconsole.log('收到背包数据:', response.data);\r\n\t\t\t\t\tthis.inventoryData = response.data.inventory || [];\r\n\t\t\t\t\tthis.inventoryCapacity = response.data.capacity || 50;\r\n\t\t\t\t\tconsole.log('[背包页面] 背包数据已更新:', this.inventoryData);\r\n\t\t\t\t} else if (response.type === 'error') {\r\n\t\t\t\t\tconsole.error('WebSocket错误:', response.data);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 检查是否是背包已满的错误\r\n\t\t\t\t\tif (response.data && response.data.message && response.data.message.includes('背包已满')) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: response.data.message,\r\n\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\tduration: 3000\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '网络错误，请重试',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('加载背包数据失败:', error);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '加载失败: ' + (error.message || '未知错误'),\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\thandleItemClick(item, index) {\r\n\t\t\tconsole.log('[背包] 选中物品:', item, '索引:', index);\r\n\t\t\t// 合并itemsConfig属性，强制用 config.type（英文）覆盖 item.type\r\n\t\t\tconst config = this.itemsConfig[item.id] || {};\r\n\t\t\tconst type = config.type || item.type || '';\r\n\t\t\tconst sellable = (typeof config.sellable !== 'undefined' ? config.sellable : item.sellable) ? true : false;\r\n\t\t\tthis.selectedItem = { ...item, ...config, type, sellable };\r\n\t\t\tthis.selectedIndex = index;\r\n\t\t\tthis.showItemDetail = true;\r\n\t\t},\r\n\t\t\r\n\t\tcloseItemDetail() {\r\n\t\t\tthis.showItemDetail = false;\r\n\t\t\tthis.selectedItem = null;\r\n\t\t\tthis.selectedIndex = -1;\r\n\t\t},\r\n\t\t\r\n\t\tgetItemQualityClass(item) {\r\n\t\t\tif (!item || !item.品质) return 'quality-normal';\r\n\t\t\tswitch (item.品质) {\r\n\t\t\t\tcase 'common': return 'quality-common';\r\n\t\t\t\tcase 'uncommon': return 'quality-uncommon';\r\n\t\t\t\tcase 'rare': return 'quality-rare';\r\n\t\t\t\tcase 'epic': return 'quality-epic';\r\n\t\t\t\tcase 'legendary': return 'quality-legendary';\r\n\t\t\t\tdefault: return 'quality-normal';\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tgetQualityText(quality) {\r\n\t\t\tconst qualityMap = {\r\n\t\t\t\t'common': '普通',\r\n\t\t\t\t'uncommon': '优秀',\r\n\t\t\t\t'rare': '稀有',\r\n\t\t\t\t'epic': '史诗',\r\n\t\t\t\t'legendary': '传说',\r\n\t\t\t\t'mythic': '神话'\r\n\t\t\t};\r\n\t\t\treturn qualityMap[quality] || '普通';\r\n\t\t},\r\n\t\t\r\n\t\tgetTypeText(type) {\r\n\t\t\tconst typeMap = {\r\n\t\t\t\t'weapon': '武器',\r\n\t\t\t\t'helmet': '头盔',\r\n\t\t\t\t'necklace': '项链',\r\n\t\t\t\t'armor': '衣服',\r\n\t\t\t\t'cloak': '披风',\r\n\t\t\t\t'pants': '裤子',\r\n\t\t\t\t'shoes': '鞋子',\r\n\t\t\t\t'bracelet': '手镯',\r\n\t\t\t\t'ring': '戒指',\r\n\t\t\t\t'shield': '盾牌',\r\n\t\t\t\t'consumable': '消耗品',\r\n\t\t\t\t'medicine': '药品',\r\n\t\t\t\t'pill': '丹药',\r\n\t\t\t\t'material': '材料',\r\n\t\t\t\t'ore': '矿石',\r\n\t\t\t\t'wood': '木材',\r\n\t\t\t\t'herb': '草药',\r\n\t\t\t\t'fur': '兽皮',\r\n\t\t\t\t'tool': '工具',\r\n\t\t\t\t'pickaxe': '矿镐',\r\n\t\t\t\t'axe': '斧头',\r\n\t\t\t\t'sickle': '镰刀',\r\n\t\t\t\t'knife': '小刀',\r\n\t\t\t\t'special': '特殊',\r\n\t\t\t\t'quest': '任务',\r\n\t\t\t\t'currency': '货币'\r\n\t\t\t};\r\n\t\t\treturn typeMap[type] || type;\r\n\t\t},\r\n\t\t\r\n\t\tcanEquip(item) {\r\n\t\t\t// 只支持英文类型，且采集工具不显示装备按钮\r\n\t\t\tconst types = [\r\n\t\t\t\t'weapon', 'helmet', 'necklace', 'armor', 'cloak', 'pants', 'shoes',\r\n\t\t\t\t'bracelet', 'ring', 'shield', 'medal', 'accessory'\r\n\t\t\t];\r\n\t\t\treturn (item.equipable === 1 || item.equipable === true) && item.type !== 'gather_tool' || types.includes(item.type);\r\n\t\t},\r\n\t\t\r\n\t\tcanUse(item) {\r\n\t\t\t// 支持 usable 字段\r\n\t\t\treturn item.usable === 1 || item.usable === true || ['consumable', 'medicine', 'pill'].includes(item.type);\r\n\t\t},\r\n\t\t\r\n\t\tasync equipItem() {\r\n\t\t\tconsole.log('[装备] 当前selectedItem:', this.selectedItem, 'selectedIndex:', this.selectedIndex);\r\n\t\t\tif (!this.selectedItem || !this.selectedItem.unique_id) {\r\n\t\t\t\tconsole.warn('[装备] 缺少unique_id，无法发送装备请求');\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\ttry {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\tconst slotType = this.getDefaultSlot(this.selectedItem.type);\r\n\t\t\t\tconsole.log('[装备] 发送equip_item:', {\r\n\t\t\t\t\tunique_id: this.selectedItem.unique_id,\r\n\t\t\t\t\tslot_type: slotType\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\r\n\t\t\t\tconst response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'equip_item',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tunique_id: this.selectedItem.unique_id,\r\n\t\t\t\t\t\ttype: this.selectedItem.type,\r\n\t\t\t\t\t\tsellable: this.selectedItem.sellable,\r\n\t\t\t\t\t\tslot_type: slotType\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (response.type === 'equip_success') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '装备成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.closeItemDetail();\r\n\t\t\t\t\t// 直接使用后端返回的更新后的背包数据\r\n\t\t\t\t\tif (response.data.inventory) {\r\n\t\t\t\t\t\tthis.inventoryData = response.data.inventory;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 如果没有返回背包数据，则重新请求\r\n\t\t\t\t\t\tthis.loadInventoryData();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (response.type === 'equip_failed') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '装备失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 装备失败时也关闭弹窗\r\n\t\t\t\t\tthis.closeItemDetail();\r\n\t\t\t\t} else if (response.type === 'error') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '装备失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.closeItemDetail();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('装备失败:', error);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '装备失败: ' + (error.message || '未知错误'),\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t// 网络错误时也关闭弹窗\r\n\t\t\t\tthis.closeItemDetail();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tgetDefaultSlot(itemType) {\r\n\t\t\t// 支持所有新装备类型，戒指/手镯有两个槽位，优先第一个\r\n\t\t\tconst slotMapping = {\r\n\t\t\t\t'weapon': 'main_hand',\r\n\t\t\t\t'helmet': 'helmet',\r\n\t\t\t\t'necklace': 'necklace',\r\n\t\t\t\t'armor': 'armor',\r\n\t\t\t\t'cloak': 'cloak',\r\n\t\t\t\t'pants': 'pants',\r\n\t\t\t\t'shoes': 'shoes',\r\n\t\t\t\t'bracelet': ['bracelet1', 'bracelet2'],\r\n\t\t\t\t'ring': ['ring1', 'ring2'],\r\n\t\t\t\t'shield': 'main_hand',\r\n\t\t\t\t'medal': 'medal',\r\n\t\t\t\t'accessory': ['bracelet1', 'bracelet2'] // 饰品按手镯槽处理\r\n\t\t\t};\r\n\t\t\tconst player = gameState.playerData || {};\r\n\t\t\t// 优先空槽，已满则替换第一个\r\n\t\t\tif (itemType === 'bracelet' || itemType === 'accessory') {\r\n\t\t\t\tif (player.equipment && !player.equipment.bracelet1) return 'bracelet1';\r\n\t\t\t\tif (player.equipment && !player.equipment.bracelet2) return 'bracelet2';\r\n\t\t\t\treturn 'bracelet1';\r\n\t\t\t}\r\n\t\t\tif (itemType === 'ring') {\r\n\t\t\t\tif (player.equipment && !player.equipment.ring1) return 'ring1';\r\n\t\t\t\tif (player.equipment && !player.equipment.ring2) return 'ring2';\r\n\t\t\t\treturn 'ring1';\r\n\t\t\t}\r\n\t\t\tconst slot = slotMapping[itemType];\r\n\t\t\tif (Array.isArray(slot)) return slot[0];\r\n\t\t\treturn slot || 'main_hand';\r\n\t\t},\r\n\t\t\r\n\t\tuseItem() {\r\n\t\t\tif (!this.selectedItem || this.selectedIndex === -1) return;\r\n\t\t\t\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '使用物品',\r\n\t\t\t\tcontent: `确定要使用 ${this.selectedItem.name || this.selectedItem.名称} 吗？`,\r\n\t\t\t\tshowCancel: true,\r\n\t\t\t\tcancelText: '取消',\r\n\t\t\t\tconfirmText: '使用',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.sendUseItem();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tasync sendUseItem() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\r\n\t\t\t\tconst response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'use_item',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tunique_id: this.selectedItem.unique_id,\r\n\t\t\t\t\t\ttype: this.selectedItem.type,\r\n\t\t\t\t\t\tsellable: this.selectedItem.sellable\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (response.type === 'use_item_success') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '使用成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.closeItemDetail();\r\n\t\t\t\t\t// 直接使用后端返回的更新后的背包数据\r\n\t\t\t\t\tif (response.data.inventory) {\r\n\t\t\t\t\t\tthis.inventoryData = response.data.inventory;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 如果没有返回背包数据，则重新请求\r\n\t\t\t\t\t\tthis.loadInventoryData();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (response.type === 'use_item_failed') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '使用失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 使用失败时也关闭弹窗\r\n\t\t\t\t\tthis.closeItemDetail();\r\n\t\t\t\t} else if (response.type === 'error') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '使用失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.closeItemDetail();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('使用物品失败:', error);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '使用失败: ' + (error.message || '未知错误'),\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t// 网络错误时也关闭弹窗\r\n\t\t\t\tthis.closeItemDetail();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tdestroyItem() {\r\n\t\t\tif (!this.selectedItem || this.selectedIndex === -1) return;\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '销毁物品',\r\n\t\t\t\tcontent: `确定要销毁 ${this.selectedItem.name || this.selectedItem.名称} 吗？此操作不可撤销！`,\r\n\t\t\t\tshowCancel: true,\r\n\t\t\t\tcancelText: '取消',\r\n\t\t\t\tconfirmText: '销毁',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.sendDestroyItem();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tasync sendDestroyItem() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\r\n\t\t\t\tconst response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'destroy_item',\r\n\t\t\t\t\tdata: {\r\n\t\t\t\t\t\tunique_id: this.selectedItem.unique_id,\r\n\t\t\t\t\t\ttype: this.selectedItem.type,\r\n\t\t\t\t\t\tsellable: this.selectedItem.sellable\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (response.type === 'destroy_item_success') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '销毁成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 强制关闭物品详情窗口\r\n\t\t\t\t\tthis.showItemDetail = false;\r\n\t\t\t\t\tthis.selectedItem = null;\r\n\t\t\t\t\tthis.selectedIndex = -1;\r\n\t\t\t\t\t// 直接使用后端返回的更新后的背包数据\r\n\t\t\t\t\tif (response.data.inventory) {\r\n\t\t\t\t\t\tthis.inventoryData = response.data.inventory;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 如果没有返回背包数据，则重新请求\r\n\t\t\t\t\t\tthis.loadInventoryData();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (response.type === 'destroy_item_failed') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '销毁失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 销毁失败时也关闭弹窗\r\n\t\t\t\t\tthis.closeItemDetail();\r\n\t\t\t\t} else if (response.type === 'error') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '销毁失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.closeItemDetail();\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('销毁物品失败:', error);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '销毁失败: ' + (error.message || '未知错误'),\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t\t// 网络错误时也关闭弹窗\r\n\t\t\t\tthis.closeItemDetail();\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tasync expandInventory() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\r\n\t\t\t\tconst response = await gameUtils.sendMessage({\r\n\t\t\t\t\ttype: 'expand_inventory',\r\n\t\t\t\t\tdata: {}\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t\r\n\t\t\t\tif (response.type === 'expand_success') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '扩充背包成功',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 直接使用后端返回的更新后的背包数据\r\n\t\t\t\t\tif (response.data.inventory) {\r\n\t\t\t\t\t\tthis.inventoryData = response.data.inventory;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (response.data.capacity) {\r\n\t\t\t\t\t\tthis.inventoryCapacity = response.data.capacity;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 如果没有返回背包数据，则重新请求\r\n\t\t\t\t\tif (!response.data.inventory) {\r\n\t\t\t\t\t\tthis.loadInventoryData();\r\n\t\t\t\t\t}\r\n\t\t\t\t} else if (response.type === 'expand_failed') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '扩充背包失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else if (response.type === 'error') {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: response.data.message || '扩充背包失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('扩充背包失败:', error);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '扩充失败: ' + (error.message || '未知错误'),\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tsortInventory() {\r\n\t\t\t// 按类型和品质排序\r\n\t\t\tthis.inventoryData.sort((a, b) => {\r\n\t\t\t\t// 先按类型排序\r\n\t\t\t\tconst typeOrder = ['weapon', 'shield', 'helmet', 'necklace', 'armor', 'cloak', 'pants', 'shoes', 'bracelet', 'ring', 'consumable', 'medicine', 'pill', 'material', 'ore', 'wood', 'herb', 'fur', 'tool', 'pickaxe', 'axe', 'sickle', 'knife', 'special', 'quest', 'currency'];\r\n\t\t\t\tconst aTypeIndex = typeOrder.indexOf(a.type) || 999;\r\n\t\t\t\tconst bTypeIndex = typeOrder.indexOf(b.type) || 999;\r\n\t\t\t\tif (aTypeIndex !== bTypeIndex) {\r\n\t\t\t\t\treturn aTypeIndex - bTypeIndex;\r\n\t\t\t\t}\r\n\t\t\t\t// 再按品质排序\r\n\t\t\t\tconst qualityOrder = ['mythic', 'legendary', 'epic', 'rare', 'uncommon', 'common'];\r\n\t\t\t\tconst aQualityIndex = qualityOrder.indexOf(a.品质 || a.quality) || 999;\r\n\t\t\t\tconst bQualityIndex = qualityOrder.indexOf(b.品质 || b.quality) || 999;\r\n\t\t\t\treturn aQualityIndex - bQualityIndex;\r\n\t\t\t});\r\n\t\t\t\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '背包已整理',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tclearInventory() {\r\n\t\t\t// 确认清空背包\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '确认清空背包',\r\n\t\t\t\tcontent: `确定要清空背包中的所有物品吗？\\n此操作不可撤销！\\n当前背包中有 ${this.inventoryData.length} 个物品。`,\r\n\t\t\t\tconfirmText: '确认清空',\r\n\t\t\t\tconfirmColor: '#F44336',\r\n\t\t\t\tcancelText: '取消',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\tthis.performClearInventory();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\t\r\n\t\tasync performClearInventory() {\r\n\t\t\ttry {\r\n\t\t\t\tthis.isLoading = true;\r\n\t\t\t\t\r\n\t\t\t\t// 显示开始清空的提示\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '开始清空背包...',\r\n\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\tduration: 1000\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 逐个销毁所有物品\r\n\t\t\t\tconst itemsToDestroy = [...this.inventoryData];\r\n\t\t\t\tlet destroyedCount = 0;\r\n\t\t\t\t\r\n\t\t\t\tfor (let i = 0; i < itemsToDestroy.length; i++) {\r\n\t\t\t\t\tconst item = itemsToDestroy[i];\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\t// 使用gameUtils.sendMessage发送请求\r\n\t\t\t\t\t\tawait gameUtils.sendMessage({\r\n\t\t\t\t\t\t\ttype: 'destroy_item',\r\n\t\t\t\t\t\t\tdata: {\r\n\t\t\t\t\t\t\t\tunique_id: item.unique_id || item.id,\r\n\t\t\t\t\t\t\t\tslot: i\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tdestroyedCount++;\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 每销毁5个物品显示一次进度\r\n\t\t\t\t\t\tif (destroyedCount % 5 === 0 || destroyedCount === itemsToDestroy.length) {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: `已清空 ${destroyedCount}/${itemsToDestroy.length} 个物品`,\r\n\t\t\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t\t\t\tduration: 1000\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 添加小延迟避免请求过于频繁\r\n\t\t\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 100));\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error('销毁物品失败:', error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 等待一段时间后刷新背包数据\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.loadInventoryData();\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: `背包清空完成！共清空 ${destroyedCount} 个物品`,\r\n\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t});\r\n\t\t\t\t\tthis.isLoading = false;\r\n\t\t\t\t}, 1500);\r\n\t\t\t\t\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('清空背包失败:', error);\r\n\t\t\t\tthis.isLoading = false;\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '清空失败: ' + (error.message || '未知错误'),\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\t\r\n\t\tasync loadItemsConfig() {\r\n\t\t\tthis.itemsConfig = await gameState.getItemsConfig();\r\n\t\t},\r\n\t\t\r\n\t\tgoBack() {\r\n\t\t\tuni.navigateBack({\r\n\t\t\t\tdelta: 1\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.backpack-container {\r\n\tpadding: 16rpx;\r\n\tbackground: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%);\r\n\tmin-height: 100vh;\r\n}\r\n\r\n.backpack-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 16rpx;\r\n\tbackground: rgba(255,255,255,0.9);\r\n\tborder-radius: 16rpx;\r\n\tpadding: 16rpx;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);\r\n}\r\n\r\n.backpack-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.backpack-info {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tbackground: rgba(0,0,0,0.05);\r\n\tpadding: 8rpx 12rpx;\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.backpack-grid {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));\r\n\tgap: 8rpx;\r\n\tmargin-bottom: 16rpx;\r\n\tmax-height: 60vh;\r\n\toverflow-y: auto;\r\n\tpadding-right: 8rpx;\r\n}\r\n\r\n.item-slot {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tpadding: 8rpx 6rpx;\r\n\tbackground: rgba(255,255,255,0.9);\r\n\tborder-radius: 8rpx;\r\n\tborder: 2rpx solid #dee2e6;\r\n\ttransition: all 0.3s ease;\r\n\tmin-height: 80rpx;\r\n\tjustify-content: center;\r\n\tposition: relative;\r\n\twidth: 100%;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.item-slot:hover {\r\n\ttransform: translateY(-2rpx);\r\n\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);\r\n\tborder-color: #667eea;\r\n}\r\n\r\n.item-slot.empty {\r\n\tbackground: rgba(255,255,255,0.5);\r\n\tborder-style: dashed;\r\n\tborder-color: #ccc;\r\n}\r\n\r\n.item-icon {\r\n\tfont-size: 32rpx;\r\n\twidth: 48rpx;\r\n\theight: 48rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tborder-radius: 8rpx;\r\n\tborder: 2rpx solid #ddd;\r\n\tbackground: #fff;\r\n\tbox-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);\r\n}\r\n\r\n.item-name {\r\n\tfont-size: 18rpx;\r\n\tcolor: #333;\r\n\tmargin-top: 4rpx;\r\n\ttext-align: center;\r\n\toverflow: hidden;\r\n\ttext-overflow: ellipsis;\r\n\twhite-space: nowrap;\r\n\tmax-width: 100%;\r\n\tfont-weight: 500;\r\n\tline-height: 1.2;\r\n\tword-break: break-all;\r\n}\r\n\r\n.item-quantity {\r\n\tposition: absolute;\r\n\ttop: 2rpx;\r\n\tright: 2rpx;\r\n\tbackground: rgba(0,0,0,0.7);\r\n\tcolor: white;\r\n\tfont-size: 18rpx;\r\n\tpadding: 1rpx 4rpx;\r\n\tborder-radius: 6rpx;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 18rpx;\r\n\tcolor: #ccc;\r\n\tfont-style: italic;\r\n}\r\n\r\n/* 品质样式 */\r\n.quality-common {\r\n\tborder-color: #9e9e9e;\r\n}\r\n\r\n.quality-uncommon {\r\n\tborder-color: #4caf50;\r\n}\r\n\r\n.quality-rare {\r\n\tborder-color: #2196f3;\r\n}\r\n\r\n.quality-epic {\r\n\tborder-color: #9c27b0;\r\n}\r\n\r\n.quality-legendary {\r\n\tborder-color: #ff9800;\r\n}\r\n\r\n.quality-mythic {\r\n\tborder-color: #f44336;\r\n}\r\n\r\n.backpack-actions {\r\n\tdisplay: flex;\r\n\tgap: 16rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.back-actions {\r\n\tmargin-top: 20rpx;\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.action-btn {\r\n\tflex: 1;\r\n\theight: 56rpx;\r\n\tborder: none;\r\n\tborder-radius: 28rpx;\r\n\tcolor: white;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\ttransition: all 0.3s ease;\r\n\tbox-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);\r\n}\r\n\r\n.expand-btn {\r\n\tbackground: linear-gradient(135deg, #4CAF50, #388E3C);\r\n}\r\n\r\n.sort-btn {\r\n\tbackground: linear-gradient(135deg, #2196F3, #1976D2);\r\n}\r\n\r\n.clear-btn {\r\n\tbackground: linear-gradient(135deg, #F44336, #D32F2F);\r\n}\r\n\r\n.clear-btn[disabled] {\r\n\tbackground: linear-gradient(135deg, #BDBDBD, #9E9E9E);\r\n\tcolor: #757575;\r\n\tcursor: not-allowed;\r\n\topacity: 0.6;\r\n}\r\n\r\n.back-btn {\r\n\tbackground: linear-gradient(135deg, #9E9E9E, #757575);\r\n}\r\n\r\n.action-btn:active {\r\n\ttransform: scale(0.95);\r\n\tbox-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);\r\n}\r\n\r\n/* 物品详情弹窗 */\r\n.item-detail-modal {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\tright: 0;\r\n\tbottom: 0;\r\n\tbackground: rgba(0,0,0,0.5);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tz-index: 1000;\r\n}\r\n\r\n.item-detail-content {\r\n\tbackground: white;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 24rpx;\r\n\tmargin: 32rpx;\r\n\tmax-width: 600rpx;\r\n\twidth: 100%;\r\n\tbox-shadow: 0 8rpx 24rpx rgba(0,0,0,0.3);\r\n}\r\n\r\n.item-detail-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 16rpx;\r\n\tpadding-bottom: 12rpx;\r\n\tborder-bottom: 2rpx solid #eee;\r\n}\r\n\r\n.item-detail-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.item-detail-quality {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tbackground: rgba(0,0,0,0.05);\r\n\tpadding: 4rpx 8rpx;\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.item-detail-quality-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.item-detail-info {\r\n\tmargin-bottom: 24rpx;\r\n}\r\n\r\n.item-detail-info text {\r\n\tdisplay: block;\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.item-detail-desc {\r\n\tfont-style: italic;\r\n\tcolor: #888;\r\n\tmargin-top: 12rpx;\r\n\tpadding: 12rpx;\r\n\tbackground: rgba(0,0,0,0.05);\r\n\tborder-radius: 8rpx;\r\n}\r\n\r\n.item-detail-actions {\r\n\tdisplay: grid;\r\n\tgrid-template-columns: repeat(2, 1fr);\r\n\tgap: 12rpx;\r\n}\r\n\r\n.detail-action-btn {\r\n\theight: 48rpx;\r\n\tborder: none;\r\n\tborder-radius: 24rpx;\r\n\tcolor: white;\r\n\tfont-size: 24rpx;\r\n\tfont-weight: bold;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.equip-btn {\r\n\tbackground: linear-gradient(135deg, #4CAF50, #388E3C);\r\n}\r\n\r\n.use-btn {\r\n\tbackground: linear-gradient(135deg, #2196F3, #1976D2);\r\n}\r\n\r\n.destroy-btn {\r\n\tbackground: linear-gradient(135deg, #F44336, #D32F2F);\r\n}\r\n\r\n.cancel-btn {\r\n\tbackground: linear-gradient(135deg, #9E9E9E, #757575);\r\n}\r\n\r\n.detail-action-btn:active {\r\n\ttransform: scale(0.95);\r\n}\r\n\r\n/* 媒体查询 - 小屏幕设备 */\r\n@media screen and (max-width: 375px) {\r\n\t.backpack-grid {\r\n\t\tgrid-template-columns: repeat(4, 1fr);\r\n\t\tgap: 6rpx;\r\n\t}\r\n\t\r\n\t.item-slot {\r\n\t\tmin-height: 70rpx;\r\n\t\tpadding: 6rpx 4rpx;\r\n\t}\r\n\t\r\n\t.item-icon {\r\n\t\tfont-size: 28rpx;\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\t\r\n\t.item-name {\r\n\t\tfont-size: 16rpx;\r\n\t}\r\n}\r\n\r\n/* 媒体查询 - 中等屏幕设备 */\r\n@media screen and (min-width: 376px) and (max-width: 750px) {\r\n\t.backpack-grid {\r\n\t\tgrid-template-columns: repeat(5, 1fr);\r\n\t}\r\n}\r\n\r\n/* 媒体查询 - 大屏幕设备 */\r\n@media screen and (min-width: 751px) {\r\n\t.backpack-grid {\r\n\t\tgrid-template-columns: repeat(6, 1fr);\r\n\t\tmax-height: 70vh;\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/zjjhx/仗剑江湖行/pages/character/backpack.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "gameUtils", "gameState"], "mappings": ";;;;;AAyHA,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,eAAe,CAAE;AAAA,MACjB,mBAAmB;AAAA,MACnB,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,WAAW;AAAA;AAAA,MAEX,aAAa,CAAE;AAAA,IAChB;AAAA,EACA;AAAA,EAED,SAAS;AACRA,kBAAAA,0DAAY,mBAAmB;AAC/B,SAAK,kBAAiB;AACtB,SAAK,gBAAe;AAAA,EACpB;AAAA,EAED,SAAS;AACRA,kBAAAA,0DAAY,mBAAmB;AAC/B,SAAK,kBAAiB;AAAA,EACtB;AAAA,EAED,WAAW;AACV,SAAK,sBAAqB;AAAA,EAC1B;AAAA,EAED,SAAS;AAAA,IACR,MAAM,oBAAoB;AACzB,UAAI;AACHA,sBAAAA,MAAA,MAAA,OAAA,uCAAY,iBAAiB;AAC7B,aAAK,YAAY;AAGjB,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,QACR,CAAC;AAEDD,sBAAA,MAAA,MAAA,OAAA,uCAAY,gBAAgB,QAAQ;AACpC,aAAK,YAAY;AAEjB,YAAI,SAAS,SAAS,kBAAkB;AACvCA,wBAAY,MAAA,MAAA,OAAA,uCAAA,WAAW,SAAS,IAAI;AACpC,eAAK,gBAAgB,SAAS,KAAK,aAAa,CAAA;AAChD,eAAK,oBAAoB,SAAS,KAAK,YAAY;AACnDA,kFAAY,mBAAmB,KAAK,aAAa;AAAA,QAClD,WAAW,SAAS,SAAS,SAAS;AACrCA,wBAAA,MAAA,MAAA,SAAA,uCAAc,gBAAgB,SAAS,IAAI;AAG3C,cAAI,SAAS,QAAQ,SAAS,KAAK,WAAW,SAAS,KAAK,QAAQ,SAAS,MAAM,GAAG;AACrFA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO,SAAS,KAAK;AAAA,cACrB,MAAM;AAAA,cACN,UAAU;AAAA,YACX,CAAC;AAAA,iBACK;AACNA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACP,CAAC;AAAA,UACF;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,uCAAA,aAAa,KAAK;AAChC,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM,WAAW;AAAA,UACpC,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,gBAAgB,MAAM,OAAO;AAC5BA,0BAAA,MAAA,OAAA,uCAAY,cAAc,MAAM,OAAO,KAAK;AAE5C,YAAM,SAAS,KAAK,YAAY,KAAK,EAAE,KAAK;AAC5C,YAAM,OAAO,OAAO,QAAQ,KAAK,QAAQ;AACzC,YAAM,YAAY,OAAO,OAAO,aAAa,cAAc,OAAO,WAAW,KAAK,YAAY,OAAO;AACrG,WAAK,eAAe,EAAE,GAAG,MAAM,GAAG,QAAQ,MAAM;AAChD,WAAK,gBAAgB;AACrB,WAAK,iBAAiB;AAAA,IACtB;AAAA,IAED,kBAAkB;AACjB,WAAK,iBAAiB;AACtB,WAAK,eAAe;AACpB,WAAK,gBAAgB;AAAA,IACrB;AAAA,IAED,oBAAoB,MAAM;AACzB,UAAI,CAAC,QAAQ,CAAC,KAAK;AAAI,eAAO;AAC9B,cAAQ,KAAK,IAAE;AAAA,QACd,KAAK;AAAU,iBAAO;AAAA,QACtB,KAAK;AAAY,iBAAO;AAAA,QACxB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAQ,iBAAO;AAAA,QACpB,KAAK;AAAa,iBAAO;AAAA,QACzB;AAAS,iBAAO;AAAA,MACjB;AAAA,IACA;AAAA,IAED,eAAe,SAAS;AACvB,YAAM,aAAa;AAAA,QAClB,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,aAAa;AAAA,QACb,UAAU;AAAA;AAEX,aAAO,WAAW,OAAO,KAAK;AAAA,IAC9B;AAAA,IAED,YAAY,MAAM;AACjB,YAAM,UAAU;AAAA,QACf,UAAU;AAAA,QACV,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,OAAO;AAAA,QACP,UAAU;AAAA,QACV,SAAS;AAAA,QACT,WAAW;AAAA,QACX,SAAS;AAAA,QACT,YAAY;AAAA;AAEb,aAAO,QAAQ,IAAI,KAAK;AAAA,IACxB;AAAA,IAED,SAAS,MAAM;AAEd,YAAM,QAAQ;AAAA,QACb;AAAA,QAAU;AAAA,QAAU;AAAA,QAAY;AAAA,QAAS;AAAA,QAAS;AAAA,QAAS;AAAA,QAC3D;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAU;AAAA,QAAS;AAAA;AAExC,cAAQ,KAAK,cAAc,KAAK,KAAK,cAAc,SAAS,KAAK,SAAS,iBAAiB,MAAM,SAAS,KAAK,IAAI;AAAA,IACnH;AAAA,IAED,OAAO,MAAM;AAEZ,aAAO,KAAK,WAAW,KAAK,KAAK,WAAW,QAAQ,CAAC,cAAc,YAAY,MAAM,EAAE,SAAS,KAAK,IAAI;AAAA,IACzG;AAAA,IAED,MAAM,YAAY;AACjBA,oBAAAA,MAAA,MAAA,OAAA,uCAAY,wBAAwB,KAAK,cAAc,kBAAkB,KAAK,aAAa;AAC3F,UAAI,CAAC,KAAK,gBAAgB,CAAC,KAAK,aAAa,WAAW;AACvDA,sBAAAA,MAAA,MAAA,QAAA,uCAAa,2BAA2B;AACxC;AAAA,MACD;AACA,UAAI;AACH,aAAK,YAAY;AACjB,cAAM,WAAW,KAAK,eAAe,KAAK,aAAa,IAAI;AAC3DA,sBAAAA,MAAY,MAAA,OAAA,uCAAA,sBAAsB;AAAA,UACjC,WAAW,KAAK,aAAa;AAAA,UAC7B,WAAW;AAAA,QACZ,CAAC;AAGD,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM;AAAA,YACL,WAAW,KAAK,aAAa;AAAA,YAC7B,MAAM,KAAK,aAAa;AAAA,YACxB,UAAU,KAAK,aAAa;AAAA,YAC5B,WAAW;AAAA,UACZ;AAAA,QACD,CAAC;AAED,aAAK,YAAY;AAEjB,YAAI,SAAS,SAAS,iBAAiB;AACtCD,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AACD,eAAK,gBAAe;AAEpB,cAAI,SAAS,KAAK,WAAW;AAC5B,iBAAK,gBAAgB,SAAS,KAAK;AAAA,iBAC7B;AAEN,iBAAK,kBAAiB;AAAA,UACvB;AAAA,QACD,WAAW,SAAS,SAAS,gBAAgB;AAC5CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAED,eAAK,gBAAe;AAAA,QACrB,WAAW,SAAS,SAAS,SAAS;AACrCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AACD,eAAK,gBAAe;AAAA,QACrB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,uCAAc,SAAS,KAAK;AAC5B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM,WAAW;AAAA,UACpC,MAAM;AAAA,QACP,CAAC;AAED,aAAK,gBAAe;AAAA,MACrB;AAAA,IACA;AAAA,IAED,eAAe,UAAU;AAExB,YAAM,cAAc;AAAA,QACnB,UAAU;AAAA,QACV,UAAU;AAAA,QACV,YAAY;AAAA,QACZ,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,SAAS;AAAA,QACT,YAAY,CAAC,aAAa,WAAW;AAAA,QACrC,QAAQ,CAAC,SAAS,OAAO;AAAA,QACzB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,aAAa,CAAC,aAAa,WAAW;AAAA;AAAA;AAEvC,YAAM,SAASE,gBAAAA,UAAU,cAAc;AAEvC,UAAI,aAAa,cAAc,aAAa,aAAa;AACxD,YAAI,OAAO,aAAa,CAAC,OAAO,UAAU;AAAW,iBAAO;AAC5D,YAAI,OAAO,aAAa,CAAC,OAAO,UAAU;AAAW,iBAAO;AAC5D,eAAO;AAAA,MACR;AACA,UAAI,aAAa,QAAQ;AACxB,YAAI,OAAO,aAAa,CAAC,OAAO,UAAU;AAAO,iBAAO;AACxD,YAAI,OAAO,aAAa,CAAC,OAAO,UAAU;AAAO,iBAAO;AACxD,eAAO;AAAA,MACR;AACA,YAAM,OAAO,YAAY,QAAQ;AACjC,UAAI,MAAM,QAAQ,IAAI;AAAG,eAAO,KAAK,CAAC;AACtC,aAAO,QAAQ;AAAA,IACf;AAAA,IAED,UAAU;AACT,UAAI,CAAC,KAAK,gBAAgB,KAAK,kBAAkB;AAAI;AAErDF,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,aAAa,QAAQ,KAAK,aAAa,EAAE;AAAA,QAChE,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,YAAW;AAAA,UACjB;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAED,MAAM,cAAc;AACnB,UAAI;AACH,aAAK,YAAY;AAGjB,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM;AAAA,YACL,WAAW,KAAK,aAAa;AAAA,YAC7B,MAAM,KAAK,aAAa;AAAA,YACxB,UAAU,KAAK,aAAa;AAAA,UAC7B;AAAA,QACD,CAAC;AAED,aAAK,YAAY;AAEjB,YAAI,SAAS,SAAS,oBAAoB;AACzCD,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AACD,eAAK,gBAAe;AAEpB,cAAI,SAAS,KAAK,WAAW;AAC5B,iBAAK,gBAAgB,SAAS,KAAK;AAAA,iBAC7B;AAEN,iBAAK,kBAAiB;AAAA,UACvB;AAAA,QACD,WAAW,SAAS,SAAS,mBAAmB;AAC/CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAED,eAAK,gBAAe;AAAA,QACrB,WAAW,SAAS,SAAS,SAAS;AACrCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AACD,eAAK,gBAAe;AAAA,QACrB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,uCAAc,WAAW,KAAK;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM,WAAW;AAAA,UACpC,MAAM;AAAA,QACP,CAAC;AAED,aAAK,gBAAe;AAAA,MACrB;AAAA,IACA;AAAA,IAED,cAAc;AACb,UAAI,CAAC,KAAK,gBAAgB,KAAK,kBAAkB;AAAI;AACrDA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS,SAAS,KAAK,aAAa,QAAQ,KAAK,aAAa,EAAE;AAAA,QAChE,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,gBAAe;AAAA,UACrB;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAED,MAAM,kBAAkB;AACvB,UAAI;AACH,aAAK,YAAY;AAGjB,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM;AAAA,YACL,WAAW,KAAK,aAAa;AAAA,YAC7B,MAAM,KAAK,aAAa;AAAA,YACxB,UAAU,KAAK,aAAa;AAAA,UAC7B;AAAA,QACD,CAAC;AAED,aAAK,YAAY;AAEjB,YAAI,SAAS,SAAS,wBAAwB;AAC7CD,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAED,eAAK,iBAAiB;AACtB,eAAK,eAAe;AACpB,eAAK,gBAAgB;AAErB,cAAI,SAAS,KAAK,WAAW;AAC5B,iBAAK,gBAAgB,SAAS,KAAK;AAAA,iBAC7B;AAEN,iBAAK,kBAAiB;AAAA,UACvB;AAAA,mBACU,SAAS,SAAS,uBAAuB;AACnDA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAED,eAAK,gBAAe;AAAA,QACrB,WAAW,SAAS,SAAS,SAAS;AACrCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AACD,eAAK,gBAAe;AAAA,QACrB;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,uCAAc,WAAW,KAAK;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM,WAAW;AAAA,UACpC,MAAM;AAAA,QACP,CAAC;AAED,aAAK,gBAAe;AAAA,MACrB;AAAA,IACA;AAAA,IAED,MAAM,kBAAkB;AACvB,UAAI;AACH,aAAK,YAAY;AAGjB,cAAM,WAAW,MAAMC,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,QACR,CAAC;AAED,aAAK,YAAY;AAEjB,YAAI,SAAS,SAAS,kBAAkB;AACvCD,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAED,cAAI,SAAS,KAAK,WAAW;AAC5B,iBAAK,gBAAgB,SAAS,KAAK;AAAA,UACpC;AACA,cAAI,SAAS,KAAK,UAAU;AAC3B,iBAAK,oBAAoB,SAAS,KAAK;AAAA,UACxC;AAEA,cAAI,CAAC,SAAS,KAAK,WAAW;AAC7B,iBAAK,kBAAiB;AAAA,UACvB;AAAA,QACD,WAAW,SAAS,SAAS,iBAAiB;AAC7CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAAA,QACF,WAAW,SAAS,SAAS,SAAS;AACrCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,SAAS,KAAK,WAAW;AAAA,YAChC,MAAM;AAAA,UACP,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,uCAAc,WAAW,KAAK;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM,WAAW;AAAA,UACpC,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,gBAAgB;AAEf,WAAK,cAAc,KAAK,CAAC,GAAG,MAAM;AAEjC,cAAM,YAAY,CAAC,UAAU,UAAU,UAAU,YAAY,SAAS,SAAS,SAAS,SAAS,YAAY,QAAQ,cAAc,YAAY,QAAQ,YAAY,OAAO,QAAQ,QAAQ,OAAO,QAAQ,WAAW,OAAO,UAAU,SAAS,WAAW,SAAS,UAAU;AAC5Q,cAAM,aAAa,UAAU,QAAQ,EAAE,IAAI,KAAK;AAChD,cAAM,aAAa,UAAU,QAAQ,EAAE,IAAI,KAAK;AAChD,YAAI,eAAe,YAAY;AAC9B,iBAAO,aAAa;AAAA,QACrB;AAEA,cAAM,eAAe,CAAC,UAAU,aAAa,QAAQ,QAAQ,YAAY,QAAQ;AACjF,cAAM,gBAAgB,aAAa,QAAQ,EAAE,MAAM,EAAE,OAAO,KAAK;AACjE,cAAM,gBAAgB,aAAa,QAAQ,EAAE,MAAM,EAAE,OAAO,KAAK;AACjE,eAAO,gBAAgB;AAAA,MACxB,CAAC;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,MAAM;AAAA,MACP,CAAC;AAAA,IACD;AAAA,IAED,iBAAiB;AAEhBA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA;AAAA,SAAqC,KAAK,cAAc,MAAM;AAAA,QACvE,aAAa;AAAA,QACb,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB,iBAAK,sBAAqB;AAAA,UAC3B;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA,IAED,MAAM,wBAAwB;AAC7B,UAAI;AACH,aAAK,YAAY;AAGjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAGD,cAAM,iBAAiB,CAAC,GAAG,KAAK,aAAa;AAC7C,YAAI,iBAAiB;AAErB,iBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC/C,gBAAM,OAAO,eAAe,CAAC;AAC7B,cAAI;AAEH,kBAAMC,eAAAA,UAAU,YAAY;AAAA,cAC3B,MAAM;AAAA,cACN,MAAM;AAAA,gBACL,WAAW,KAAK,aAAa,KAAK;AAAA,gBAClC,MAAM;AAAA,cACP;AAAA,YACD,CAAC;AACD;AAGA,gBAAI,iBAAiB,MAAM,KAAK,mBAAmB,eAAe,QAAQ;AACzED,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO,OAAO,cAAc,IAAI,eAAe,MAAM;AAAA,gBACrD,MAAM;AAAA,gBACN,UAAU;AAAA,cACX,CAAC;AAAA,YACF;AAGA,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAAA,UAEpD,SAAO,OAAO;AACfA,sFAAc,WAAW,KAAK;AAAA,UAC/B;AAAA,QACD;AAGA,mBAAW,MAAM;AAChB,eAAK,kBAAiB;AACtBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,cAAc,cAAc;AAAA,YACnC,MAAM;AAAA,YACN,UAAU;AAAA,UACX,CAAC;AACD,eAAK,YAAY;AAAA,QACjB,GAAE,IAAI;AAAA,MAEN,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,uCAAc,WAAW,KAAK;AAC9B,aAAK,YAAY;AACjBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,YAAY,MAAM,WAAW;AAAA,UACpC,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,MAAM,kBAAkB;AACvB,WAAK,cAAc,MAAME,gBAAS,UAAC,eAAc;AAAA,IACjD;AAAA,IAED,SAAS;AACRF,oBAAAA,MAAI,aAAa;AAAA,QAChB,OAAO;AAAA,MACR,CAAC;AAAA,IACF;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACprBA,GAAG,WAAW,eAAe;"}