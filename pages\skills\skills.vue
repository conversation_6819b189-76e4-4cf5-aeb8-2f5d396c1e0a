<template>
	<view class="container">
		<!-- 自定义修炼loading -->
		<view v-if="showTrainLoading" class="train-loading-mask">
			<view class="train-loading-content">
				<view class="train-spinner"></view>
				<text class="train-loading-text">正在修炼...</text>
			</view>
		</view>
		<!-- 修炼结果提示区 -->
		<view v-if="trainResultMsg" class="train-result-msg" style="background:#fffbe6;color:#ad6800;padding:8px 12px;margin-bottom:12px;border-radius:6px;font-size:14px;">
			{{ trainResultMsg }}
		</view>
		<!-- 三大选项卡 -->
		<view class="category-tabs">
			<view class="tab-item" :class="{ active: activeTab === 'equip' }" @click="switchTab('equip')">
				<text class="tab-text">使用武功</text>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 'skills' }" @click="switchTab('skills')">
				<text class="tab-text">武功技能</text>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 'life' }" @click="switchTab('life')">
				<text class="tab-text">生活技能</text>
			</view>
		</view>

		<!-- 使用武功：分类型展示，每类可装备一个 -->
		<view v-if="Object.values(martialSkillsByType).some(arr => arr.length > 0) && activeTab === 'equip'">
			<view v-for="typeObj in martialTypes" :key="typeObj.key" class="skill-group">
				<view class="skill-item">
					<view class="skill-info">
						<view class="title-selector-row">
							<text class="group-title">{{ typeObj.label }}</text>
							<view class="equip-selector">
								<button class="martial-select-tag" @click="openMartialSelect(typeObj.key)" :disabled="getMartialsByType(typeObj.key).length === 0">
									<text class="martial-select-tag-text">
										{{ getSelectedMartialName(typeObj.key) || (getMartialsByType(typeObj.key).length === 0 ? '未装备' : '请选择武功') }}
										{{ getSelectedMartial(typeObj.key) && isMartialEquipped(getSelectedMartial(typeObj.key)) ? '(已装备)' : '' }}
									</text>
								</button>
								<button 
									v-if="getSelectedMartial(typeObj.key) && isMartialEquipped(getSelectedMartial(typeObj.key))"
									class="martial-clear-btn"
									@click="unequipMartial(typeObj.key)"
								>
									×
								</button>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-else-if="activeTab === 'equip'" style="text-align:center;color:#aaa;padding:32px 0;">暂无武功数据</view>

		<!-- 武功技能：展示所有已学会武功 -->
		<view v-if="activeTab === 'skills'">
			<view v-if="learnedMartials.length">
				<view class="skill-group">
					<view class="group-header">
						<text class="group-title">已学会武功技能（{{ learnedMartialCount }}）</text>
					</view>
					<view v-for="skill in learnedMartials" :key="skill.名称 || skill.name" class="skill-item">
						<view class="skill-info">
							<text class="skill-name">{{ skill.名称 || skill.name }}</text>
							<text class="skill-level">Lv.{{ skill.等级 || skill.level }}</text>
							<text class="skill-school" v-if="skill.门派 || (skill.school && skill.school !== '无门派')">{{ skill.门派 || skill.school }}</text>
						</view>
						<button class="detail-btn" @click="showSkillDetail(skill)">详情</button>
					</view>
					<view v-if="learnedMartials.length === 0" class="empty-skills">暂无已学会武功</view>
				</view>
			</view>
			<view v-else style="text-align:center;color:#aaa;padding:32px 0;">暂无武功技能</view>
		</view>


		<!-- 生活技能：展示所有生活技能 -->
		<view v-if="activeTab === 'life'">
			<view v-if="lifeSkills.length">
				<view class="skill-group">
					<view class="group-header">
						<text class="group-title">生活技能</text>
					</view>
					<view v-for="skill in lifeSkills" :key="skill.名称 || skill.name" class="skill-item">
						<view class="skill-info">
							<text class="skill-name">{{ skill.名称 || skill.name }}</text>
							<text class="skill-level">Lv.{{ skill.等级 || skill.level }}</text>
						</view>
						<button class="detail-btn" @click="showSkillDetail(skill)">详情</button>
					</view>
					<view v-if="lifeSkills.length === 0" class="empty-skills">暂无生活技能</view>
				</view>
			</view>
			<view v-else style="text-align:center;color:#aaa;padding:32px 0;">暂无生活技能</view>
		</view>

		<!-- 技能详情弹窗 -->
		<view class="modal-overlay" v-if="showDetail" @click="closeDetail">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">武学详情</text>
					<text class="modal-close" @click="closeDetail">×</text>
				</view>
				<view class="modal-body" v-if="selectedSkill">
					<view class="detail-header">
						<text class="detail-name">{{ selectedSkill.名称 || selectedSkill.name }}</text>
						<view class="detail-status">
							<text class="detail-level">{{ selectedSkill.等级 || selectedSkill.level }}级</text>
							<text class="detail-school" v-if="selectedSkill.门派 || (selectedSkill.school && selectedSkill.school !== '无门派')">{{ selectedSkill.门派 || selectedSkill.school }}</text>
							<text class="detail-equipped" v-if="selectedSkill.装备 || selectedSkill.equipped">已装备</text>
						</view>
					</view>
					<text class="detail-desc">{{ selectedSkill.描述 || selectedSkill.description }}</text>
					<view class="detail-progress" v-if="selectedSkill.等级 > 0">
						<text class="progress-title">进度:</text>
						<view class="progress-bar">
							<view class="progress-bg">
								<view class="progress-fill" :style="{ width: getSkillProgress(selectedSkill) + '%' }"></view>
							</view>
							<text class="progress-text">{{ selectedSkill.经验 || selectedSkill.exp }}/{{ selectedSkill.最大经验 || selectedSkill.maxExp }}</text>
						</view>
					</view>
					<view class="detail-effects" v-if="selectedSkill.等级 > 0">
						<text class="effects-title">当前效果:</text>
						<text class="effects-text">{{ getSkillEffects(selectedSkill) }}</text>
					</view>



					<!-- 武功特效展示 -->
					<view class="detail-special-effects" v-if="getSkillSpecialEffects(selectedSkill)">
						<text class="special-effects-title">武功特效:</text>
						<text class="special-effects-text">{{ getSkillSpecialEffects(selectedSkill) }}</text>
					</view>


					<view class="detail-category">
						<text class="category-title">升级经验:</text>
						<text class="category-text">
							{{ (selectedSkill.exp || selectedSkill.经验 || 0) }} / {{ getSkillLevelUpExp(selectedSkill) }}
						</text>
					</view>
					<!-- 招式列表展示 -->
					<view class="detail-moves" v-if="getSkillMovesList(selectedSkill).length > 0">
						<text class="moves-title">招式列表:</text>
						<view class="moves-list">
							<view
								class="move-item"
								v-for="move in getSkillMovesList(selectedSkill)"
								:key="move.name"
								:class="{ 'move-unlocked': move.unlocked, 'move-locked': !move.unlocked }"
							>
								<view class="move-header">
									<text class="move-name">{{ move.name }}</text>
									<view class="move-status-badge" :class="{ 'status-unlocked': move.unlocked, 'status-locked': !move.unlocked }">
										{{ move.unlocked ? '已解锁' : '未解锁' }}
									</view>
								</view>
								<view class="move-details">
									<text class="move-unlock-condition">解锁条件: {{ move.unlock_level }}级</text>
									<text class="move-attack" v-if="move.attack">攻击力: +{{ move.attack }}</text>
									<text class="move-defense" v-if="move.defense">防御力: +{{ move.defense }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeDetail">关闭</button>
					<button
						class="modal-btn confirm-btn"
						@click="onTrainMartialClick"
						:disabled="!isAuthed || !selectedSkill.unlocked || player.skill_points <= 0"
						v-if="!selectedSkill.isConfig"
					>
						修炼
					</button>
				</view>
				<!-- 修炼结果提示区（弹窗内） -->
				<view v-if="trainResultMsg" style="background:#fffbe6;color:#ad6800;padding:6px 10px;margin-top:8px;border-radius:6px;font-size:13px;">
					{{ trainResultMsg }}
				</view>
			</view>
		</view>

		<!-- 招式弹窗 -->
		<view class="modal-overlay" v-if="showMovesModal" @click="closeMovesModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">{{ selectedSkill?.名称 || selectedSkill?.name }} - 招式</text>
					<text class="modal-close" @click="closeMovesModal">×</text>
				</view>
				<view class="modal-body" v-if="selectedSkill">
					<view class="moves-container">
						<view class="move-item" v-for="(move, index) in selectedSkill.招式 || selectedSkill.moves" :key="index">
							<text class="move-name">{{ move }}</text>
							<text class="move-desc">{{ getMoveDescription(selectedSkill.名称 || selectedSkill.name, move) }}</text>
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeMovesModal">关闭</button>
				</view>
			</view>
		</view>

		<!-- 武功选择弹窗 -->
		<view v-if="showMartialSelect" class="martial-select-modal-mask" @click="closeMartialSelect">
			<view class="martial-select-modal" @click.stop>
				<view class="martial-select-title">选择{{ martialSelectTypeLabel }}武功</view>
				<scroll-view class="martial-select-list" scroll-y="true">
					<view v-for="(skill, idx) in getMartialsByType(martialSelectType)" :key="skill.name" class="martial-select-item" :class="{ selected: skill.name === selectedMartials[martialSelectType] }" @click="selectMartial(skill, martialSelectType)">
						<view class="martial-select-header">
							<text class="martial-select-name">{{ skill.name }}</text>
							<view class="martial-select-status">
								<text class="martial-select-level">Lv.{{ skill.level || skill.等级 || 1 }}</text>
								<text class="martial-select-quality">{{ skill.quality || skill.品质 || '普通' }}品质</text>
								<text v-if="isMartialEquipped(skill)" class="martial-select-equipped">已装备</text>
							</view>
						</view>
						<text class="martial-select-desc">{{ skill.desc || skill.描述 || '' }}</text>
					</view>
					<view v-if="getMartialsByType(martialSelectType).length === 0" class="martial-select-empty">暂无可选武功</view>
				</scroll-view>
				<button class="martial-select-cancel" @click="closeMartialSelect">取消</button>
			</view>
		</view>

		<!-- 修炼日志弹窗 -->
		<view v-if="showTrainResultModal" class="modal-overlay" style="z-index:9999;">
			<view class="modal-content" style="max-width:420px;min-width:320px;">
				<view class="modal-header">
					<text class="modal-title">修炼日志</text>
					<text class="modal-close" @click="closeTrainResultModal">×</text>
				</view>
				<scroll-view scroll-y="true" :scroll-top="trainLogScrollTop" class="modal-body train-log-scroll" style="max-height:260px;">
					<view v-for="(msg, idx) in trainLog" :key="idx" style="color:#ad6800;font-size:14px;margin-bottom:6px;line-height:1.6;text-align:left;word-break:break-all;white-space:normal;">{{ msg }}</view>
				</scroll-view>
				<view class="modal-footer">
					<button class="modal-btn confirm-btn" @click="closeTrainResultModal">关闭</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import gameState from '../../utils/gameState.js'
import { gameUtils } from '../../utils/gameData.js'
import wsManager from '../../utils/websocket.js'

export default {
	data() {
		return {
			player: {},
			selectedMartials: {}, // 每种类型选中的武功
			martialConfigs: [], // 武功配置数据
			martialTypes: [
				{ key: '剑法', label: '剑法' },
				{ key: '刀法', label: '刀法' },
				{ key: '空手', label: '空手' },
				{ key: '招架', label: '招架' },
				{ key: '轻功', label: '轻功' },
				{ key: '内功', label: '内功' },
				{ key: '暗器', label: '暗器' }
			],
			activeTab: 'equip', // 新增，默认显示"使用武功"tab
			showDetail: false,
			showMovesModal: false,
			selectedSkill: null,
			isAuthed: false,
			showMartialSelect: false,
			martialSelectType: '',
			martialSelectTypeLabel: '',
			bonusSummary: {}, // 新增：后端增益摘要
			trainResultMsg: '', // 新增：修炼结果提示
			showTrainResultModal: false, // 修炼日志弹窗显示
			trainLog: [], // 修炼日志
			trainLogScrollTop: 0, // 修炼日志滚动位置
			showTrainLoading: false, // 自定义修炼loading
			trainLogTimeout: null, // 修炼日志超时定时器
			trainFinished: false, // 修炼是否已完成
		}
	},
	
	computed: {
		// "使用武功"tab：每类显示所有已解锁武功，直接用后端分组
		martialSkillsByType() {
			if (!this.player || !this.player.martial_skills) return {};
			const grouped = {};
			this.player.martial_skills.forEach(skill => {
				const type = skill.type || skill.类型 || skill.类别 || '其他';
				if (!grouped[type]) grouped[type] = [];
				grouped[type].push(skill);
				// 可招架武功额外加入"招架"分组
				if (skill['是否可招架'] === '是') {
					if (!grouped['招架']) grouped['招架'] = [];
					grouped['招架'].push(skill);
				}
			});

			return grouped;
		},
		// 武功技能（不含生活技能，且读书/写字归为生活技能）
		learnedMartials() {
			return (this.player && this.player.martial_skills)
				? this.player.martial_skills.filter(skill => {
					const name = skill.name || skill.名称 || '';
					const isLife = this.isLifeSkill(skill) || name.includes('读书') || name.includes('写字');
					return (skill.unlocked || skill.解锁) && !isLife;
				})
				: [];
		},
		// 生活技能（含读书/写字）
		lifeSkills() {
			return (this.player && this.player.martial_skills)
				? this.player.martial_skills.filter(skill => {
					const name = skill.name || skill.名称 || '';
					return (skill.unlocked || skill.解锁) && (this.isLifeSkill(skill) || name.includes('读书') || name.includes('写字'));
				})
				: [];
		},
		learnedMartialCount() {
			return this.learnedMartials.length;
		}
	},
	
	onLoad() {
		this.isAuthed = gameState.isAuthenticated ? gameState.isAuthenticated() : gameState.isAuthed;
		this.loadMartialSkills();
		this.loadMartialConfigs(); // 加载武功配置
		this.fetchBonusSummary();
		if (!this._gameStateCallback) {
			this._gameStateCallback = (type, state) => {
				if (type === 'player') {
					this.loadMartialSkills();
				}
			};
			gameState.updateCallbacks.push(this._gameStateCallback);
		}
	},
	onUnload() {
		// 页面卸载时移除监听，防止内存泄漏
		if (this._gameStateCallback) {
			const idx = gameState.updateCallbacks.indexOf(this._gameStateCallback);
			if (idx >= 0) gameState.updateCallbacks.splice(idx, 1);
			this._gameStateCallback = null;
		}
		// 修炼期间禁止断开 WebSocket
		if (this.showTrainLoading || this.showTrainResultModal) {
			return;
		}
		// 不再主动断开 WebSocket，避免切换页面时断开
	},
	onHide() {
		// 修炼期间禁止断开 WebSocket
		if (this.showTrainLoading || this.showTrainResultModal) {
			return;
		}
		// 不再主动断开 WebSocket，避免切换页面时断开
	},
	
	onShow() {
		// 检查 WebSocket 连接
		if (!wsManager.isConnected) {
			wsManager.connect().then(() => {
				if (!wsManager.isAuthed && wsManager.autoAuthenticate) {
					wsManager.autoAuthenticate();
				}
				setTimeout(() => {
					if (gameState.requestAllData) gameState.requestAllData();
				}, 500);
			});
		} else if (!wsManager.isAuthed && wsManager.autoAuthenticate) {
			wsManager.autoAuthenticate();
			setTimeout(() => {
				if (gameState.requestAllData) gameState.requestAllData();
			}, 500);
		}
		// 原有 onShow 逻辑
		this.isAuthed = gameState.isAuthenticated ? gameState.isAuthenticated() : gameState.isAuthed;
		this.loadMartialSkills();
		this.loadMartialConfigs(); // 加载武功配置
		this.fetchBonusSummary();
	},
	
	mounted() {
		// 监听后端推送的 train_martial_log 消息
		if (typeof wsManager !== 'undefined' && wsManager.on) {
			wsManager.on('train_martial_log', (data) => {
				if (data && data.message) {
					this.appendTrainLog(data.message);
					this.resetTrainLogTimeout(); // 每次收到日志重置超时定时器
					// 检查是否为修炼结束
					if (data.message.includes('修炼总结') || data.message.includes('修炼结束')) {
						this.showTrainLoading = false;
						this.trainFinished = true; // 标记已完成
						this.clearTrainLogTimeout();
					}
				}
			});
		}
		// 新增：监听重连事件，关闭所有弹窗
		uni.$on && uni.$on('ws_reconnected', this.handleReconnectCloseAllPopups);
	},
	
	methods: {
		async loadMartialSkills() {
			this.player = Object.assign({}, gameState.getPlayerData());
			// 兼容后端返回 martial_skills 为对象的情况
			if (this.player && this.player.martial_skills && !Array.isArray(this.player.martial_skills)) {
				this.player.martial_skills = Object.values(this.player.martial_skills);
			}
			this.syncSelectedMartials();
			this.updateData();
		},

		updateData() {
			if (!this.player) this.player = {};
			this.$forceUpdate();
		},
		
		convertMartialData(martialSkills) {
			const allSkills = []
			// 遍历所有武功分类
			Object.keys(martialSkills).forEach(category => {
				const skills = martialSkills[category] || []
				skills.forEach(skill => {
					allSkills.push({
						...skill,
						类别: this.getSkillCategory(skill.名称 || skill.name),
						门派: this.getSkillSchool(skill.名称 || skill.name),
						等级: this.getSkillLevel(skill.名称 || skill.name),
						描述: this.getSkillDescription(skill.名称 || skill.name),
						// 不再覆盖 moves/招式/招式列表
						效果: this.getSkillEffects(skill.名称 || skill.name)
					})
				})
			})
			return allSkills
		},
		
		getMartialsByType(type) {
			if (!this.player || !this.player.martial_skills) return [];
			let arr = this.martialSkillsByType[type] || [];
			if (!Array.isArray(arr) && typeof arr === 'object') {
				arr = Object.values(arr);
			}
			if (type === '招架') {
				arr = arr.filter(skill => skill['是否可招架'] === '是' || skill.is_blockable === true);
			}
			// 过滤掉所有"基本"开头的基础类武功
			return arr.filter(skill => (skill.unlocked || skill.解锁) && !(skill.name || skill.名称 || '').startsWith('基本'));
		},
		
		// 获取选中武功的索引
		getSelectedMartialIndex(type) {
			const selectedName = this.selectedMartials[type]
			if (!selectedName) return 0
			const martials = this.getMartialsByType(type)
			const index = martials.findIndex(skill => skill.name === selectedName)
			return index >= 0 ? index : 0
		},
		
		// 获取选中武功的名称
		getSelectedMartialName(type) {
			const selectedName = this.selectedMartials[type]
			if (!selectedName) return '未装备'
			const martial = this.getMartialsByType(type).find(skill => skill.name === selectedName)
			return martial ? martial.name : '未装备'
		},
		
		// 获取选中的武功对象
		getSelectedMartial(type) {
			const selectedName = this.selectedMartials[type]
			if (!selectedName) return null
			const result = this.getMartialsByType(type).find(skill => skill.name === selectedName) || null
			return result
		},
		
		// 检查武功是否已装备
		isMartialEquipped(skill) {
			if (!skill) return false;
			// 检查中英文字段
			const isEquipped = !!(skill.equipped || skill.装备);

			return isEquipped;
		},
		
		// 处理武功选择
		onMartialSelect(event, type) {
			const index = event.detail.value
			const martials = this.getMartialsByType(type)
			if (martials[index]) {
				this.selectedMartials[type] = martials[index].name
			}
		},
		
		async fetchPlayerDataFromServer() {
			const res = await gameUtils.sendMessage({ type: 'get_player_data', data: {} });
			if (res.type === 'player_data') {
				// 更新 gameState，触发通知机制
				gameState.setPlayerData(res.data);
				// 重新获取最新数据并更新界面
				this.updateData();
				
				// 确保视图更新
				setTimeout(() => {
					this.$forceUpdate();
				}, 50);
			}
		},

		async equipMartial(type, skill) {
			if (!this.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' })
				return
			}
			if (!skill.unlocked) {
				uni.showToast({ title: '该武学尚未解锁', icon: 'none' })
				return
			}
			try {
				const isCurrentlyEquipped = this.isMartialEquipped(skill);
				const action = isCurrentlyEquipped ? 'unequip_martial' : 'use_martial';
				const skillName = skill.name || skill.名称;
				const response = await gameUtils.sendMessage({
					type: action,
					data: { skill_name: skillName }
				});
				if (response && response.type === action + '_success') {
					await this.fetchPlayerDataFromServer(); // 强制拉取最新数据
					this.syncSelectedMartials();
					this.updateData();
					this.$forceUpdate();
					await this.fetchBonusSummary();

					uni.showToast({ title: response.data.message || '装备成功', icon: 'success' });
				} else {
					console.warn('装备失败:', response && response.data && response.data.message);
					uni.showToast({ title: (response && response.data && response.data.message) || '操作失败', icon: 'none' });
				}
			} catch (error) {
				uni.showToast({ title: '装备失败', icon: 'none' });
			}
		},
		
		// 卸下指定类型的武功
		async unequipMartial(type) {

			if (!this.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' })
				return
			}
			
			const skill = this.getSelectedMartial(type);
			if (!skill) {
				uni.showToast({ title: '未选择武功', icon: 'none' })
				return
			}
			
			if (!this.isMartialEquipped(skill)) {
				uni.showToast({ title: '该武功未装备', icon: 'none' })
				return
			}
			
			try {
				const skillName = skill.name || skill.名称;

				
				// 显示加载提示
				uni.showLoading({
					title: '正在卸载...',
					mask: true
				});
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'unequip_martial',
					data: { skill_name: skillName }
				});
				
				// 隐藏加载提示
				uni.hideLoading();
				
				if (response && response.type === 'unequip_martial_success') {

					// 直接使用后端返回的player数据，确保数据一致性
					if (response.data.player) {
						// 确保player中的martial_skills为list格式
						if (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {
							response.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map(name => {
								return { name, ...response.data.player.martial_skills[name] };
							});
						}
						// 更新本地数据
						this.player = Object.assign({}, response.data.player);
						gameState.player = response.data.player;
						await this.fetchPlayerDataFromServer(); // 强制拉取最新数据
						this.syncSelectedMartials();
						this.updateData();
						
						// 手动设置装备状态
						const updatedSkill = this.player.martial_skills.find(s => s.name === skillName || s.名称 === skillName);
						if (updatedSkill) {

							updatedSkill.equipped = false;
							if (updatedSkill.装备 !== undefined) {
								updatedSkill.装备 = false;
							}
						}
						
						// 更新界面
						this.updateData();
						await this.fetchBonusSummary();
						
						// 确保视图更新
						setTimeout(() => {
							this.$forceUpdate();
						}, 50);
						
						// 显示成功提示
						uni.showToast({ 
							title: response.data.message || `成功卸下${skillName}`, 
							icon: 'success' 
						});
					}
				} else {
					// 显示错误提示
					uni.showToast({ 
						title: (response && response.data && response.data.message) || '卸下失败', 
						icon: 'none' 
					});
				}
			} catch (error) {
				uni.hideLoading();
				console.error('卸载武功失败:', error);
				uni.showToast({ title: '操作异常', icon: 'none' });
			}
		},
		
		getSkillCategory(skillName) {
			// 根据武功名称判断分类
			const categoryMap = {
				'基本拳法': '拳法',
				'太极拳': '拳法',
				'基本剑法': '剑法',
				'独孤九剑': '剑法',
				'凌波微步': '轻功',
				'神行百变': '轻功',
				'九阳神功': '内功',
				'九阴真经': '内功',
				'降龙十八掌': '掌法',
				'打狗棒法': '棍法',
				'采药': '生活技能',
				'伐木': '生活技能',
				'挖矿': '生活技能',
				'剥皮': '生活技能'
			}
			return categoryMap[skillName] || '其他'
		},
		
		getSkillSchool(skillName) {
			// 根据武功名称判断门派
			const schoolMap = {
				'独孤九剑': '华山',
				'太极拳': '武当',
				'九阳神功': '少林',
				'九阴真经': '古墓派',
				'降龙十八掌': '丐帮',
				'打狗棒法': '丐帮',
				'凌波微步': '逍遥派',
				'神行百变': '逍遥派'
			}
			return schoolMap[skillName] || '无门派'
		},
		
		getSkillLevel(skillName) {
			// 根据武功名称判断等级
			const levelMap = {
				'基本拳法': '基础',
				'基本剑法': '基础',
				'太极拳': '高级',
				'独孤九剑': '绝学',
				'凌波微步': '绝学',
				'神行百变': '绝学',
				'九阳神功': '绝学',
				'九阴真经': '绝学',
				'降龙十八掌': '绝学',
				'打狗棒法': '绝学'
			}
			return levelMap[skillName] || '基础'
		},
		
		getSkillDescription(skillName) {
			// 根据武功名称获取描述
			const descMap = {
				'基本拳法': '最基础的拳法，习武之初必学',
				'太极拳': '武当派绝学，以柔克刚',
				'独孤九剑': '华山派绝学，破尽天下武功',
				'凌波微步': '逍遥派绝学，身法如鬼魅',
				'九阳神功': '少林派绝学，内力深厚',
				'九阴真经': '古墓派绝学，内力深厚',
				'降龙十八掌': '丐帮绝学，掌力雄浑',
				'打狗棒法': '丐帮绝学，棒法精妙',
				'采药': '采集草药，用于炼药',
				'伐木': '砍伐树木，用于制作装备',
				'挖矿': '挖掘矿石，用于制作装备',
				'剥皮': '剥取动物皮毛，用于制作装备'
			}
			return descMap[skillName] || '武功描述待补充'
		},
		
		getSkillMoves(skillName) {
			// 根据武功名称获取招式列表
			const movesMap = {
				'基本拳法': ['基本拳法1', '基本拳法2', '基本拳法3', '基本拳法4', '基本拳法5'],
				'太极拳': ['白鹤亮翅', '野马分鬃', '搂膝拗步', '倒卷肱', '揽雀尾'],
				'独孤九剑': ['总诀式', '破剑式', '破刀式', '破枪式', '破鞭式', '破索式', '破掌式', '破箭式', '破气式'],
				'凌波微步': ['凌波微步1', '凌波微步2', '凌波微步3', '凌波微步4'],
				'九阳神功': ['九阳护体', '九阳真气', '九阳神功'],
				'九阴真经': ['九阴护体', '九阴真气', '九阴神功'],
				'降龙十八掌': ['亢龙有悔', '飞龙在天', '见龙在田', '鸿渐于陆', '潜龙勿用'],
				'打狗棒法': ['棒打双犬', '棒打狗头', '棒打狗腿', '棒打狗尾'],
				'采药': ['采药1', '采药2', '采药3'],
				'伐木': ['伐木1', '伐木2', '伐木3'],
				'挖矿': ['挖矿1', '挖矿2', '挖矿3'],
				'剥皮': ['剥皮1', '剥皮2', '剥皮3']
			}
			return movesMap[skillName] || []
		},
		
		getMoveDescription(skillName, moveName) {
			// 根据武功和招式名称获取描述
			const descMap = {
				'基本拳法': {
					'基本拳法1': '对准敌人的胸口打出一拳！',
					'基本拳法2': '双拳齐出，敌人连连后退！',
					'基本拳法3': '对准敌人的腹部一拳！',
					'基本拳法4': '对准敌人的头部一拳！',
					'基本拳法5': '对准敌人的腿部一拳！'
				},
				'太极拳': {
					'白鹤亮翅': '一式「白鹤亮翅」，双手成白鹤亮翅之势，敌人连连后退！',
					'野马分鬃': '一式「野马分鬃」，双手成野马分鬃之势，敌人连连后退！',
					'搂膝拗步': '一式「搂膝拗步」，双手成搂膝拗步之势，敌人连连后退！',
					'倒卷肱': '一式「倒卷肱」，双手成倒卷肱之势，敌人连连后退！',
					'揽雀尾': '一式「揽雀尾」，双手成揽雀尾之势，敌人连连后退！'
				},
				'独孤九剑': {
					'总诀式': '一式「总诀式」，剑势如虹，敌人连连后退！',
					'破剑式': '一式「破剑式」，专门破解剑法，敌人连连后退！',
					'破刀式': '一式「破刀式」，专门破解刀法，敌人连连后退！',
					'破枪式': '一式「破枪式」，专门破解枪法，敌人连连后退！',
					'破鞭式': '一式「破鞭式」，专门破解鞭法，敌人连连后退！'
				}
			}
			return descMap[skillName]?.[moveName] || '招式描述待补充'
		},
		
		getSkillEffects(skill) {
			if (!skill || skill.等级 === '基础') return '暂无效果'
			
			const effects = []
			const level = skill.等级
			
			// 根据武功类型和等级计算效果
			if (skill.类别 === '拳法') {
				effects.push(`攻击 +${Math.floor(level * 1.5)}`)
				effects.push(`力量 +${Math.floor(level / 10)}`)
			} else if (skill.类别 === '剑法') {
				effects.push(`攻击 +${Math.floor(level * 2)}`)
				effects.push(`悟性 +${Math.floor(level / 10)}`)
			} else if (skill.类别 === '轻功') {
				effects.push(`防御 +${Math.floor(level * 1.5)}`)
				effects.push(`闪避 +${Math.floor(level * 1.2)}`)
				effects.push(`身法 +${Math.floor(level / 10)}`)
			} else if (skill.类别 === '内功') {
				effects.push(`气血 +${Math.floor(level * 3)}`)
				effects.push(`内力 +${Math.floor(level * 2)}`)
				effects.push(`根骨 +${Math.floor(level / 10)}`)
			}
			
			return effects.length > 0 ? effects.join(', ') : '暂无效果'
		},

		// 获取武功特效
		getSkillSpecialEffects(skill) {
			if (!skill) return ''

			// 从武功配置中获取特效信息
			const skillName = skill.名称 || skill.name
			const martialConfig = this.getMartialConfigByName(skillName)

			if (martialConfig && martialConfig['武功特效'] && martialConfig['武功特效'] !== '无') {
				return martialConfig['武功特效']
			}

			return ''
		},

		// 获取招式列表
		getSkillMovesList(skill) {
			if (!skill) return []

			const skillName = skill.名称 || skill.name
			const currentLevel = skill.等级 || skill.level || 0
			const martialConfig = this.getMartialConfigByName(skillName)

			if (martialConfig && martialConfig['招式列表']) {
				return martialConfig['招式列表'].map(move => ({
					name: move['名称'] || move.name,
					unlock_level: move['解锁等级'] || move.unlock_level || 0,
					attack: move['攻击'] || move.attack,
					defense: move['防御'] || move.defense,
					unlocked: currentLevel >= (move['解锁等级'] || move.unlock_level || 0)
				}))
			}

			// 如果没有配置，返回空数组
			return []
		},

		// 根据武功名称获取配置信息
		getMartialConfigByName(skillName) {
			if (!skillName || !this.martialConfigs || !Array.isArray(this.martialConfigs)) {
				return null
			}

			// 直接查找匹配的武功名称
			for (const config of this.martialConfigs) {
				if (config['武功名'] === skillName || config.name === skillName) {
					return config
				}
			}

			return null
		},

		// 加载武功配置数据
		async loadMartialConfigs() {

			try {
				const response = await gameUtils.sendMessage({
					type: 'get_martial_configs',
					data: {}
				})

				// 修正响应类型检查，后端返回的是 get_martial_configs_success
				if (response && response.type === 'get_martial_configs_success' && response.data) {
					this.martialConfigs = response.data.configs || []
				} else {
					this.martialConfigs = []
				}
			} catch (error) {
				console.error('前端加载武功配置失败:', error)
				// 如果加载失败，使用空数组
				this.martialConfigs = []
			}
		},
		
		switchTab(tab) {
			this.activeTab = tab
		},
		
		onLevelChange(e) {
			this.levelIndex = e.detail.value
		},
		
		onSchoolChange(e) {
			this.schoolIndex = e.detail.value
		},
		
		getCategoryTitle() {
			if (this.activeTab === 'all') return '全部武功'
			return this.activeTab
		},
		
		getCategoryDescription() {
			const descriptions = {
				'all': '所有可学习的武功',
				'拳法': '拳法类武功，以拳为主',
				'剑法': '剑法类武功，以剑为主',
				'轻功': '轻功类武功，提升身法',
				'内功': '内功类武功，提升内力'
			}
			return descriptions[this.activeTab] || '武功描述'
		},
		
		getSkillProgress(skill) {
			if (skill.最大经验 === 0) return 0
			return Math.min((skill.经验 / skill.最大经验) * 100, 100)
		},
		
		canUseMartial(skill) {
			// 基础武功不能使用/装备
			return skill.等级 !== '基础' && skill.解锁 || skill.unlocked
		},
		
		async toggleMartialUse(skill) {
			if (!this.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' })
				return
			}
			if (!skill.解锁 && !skill.unlocked) {
				uni.showToast({ title: '该武学尚未解锁', icon: 'none' })
				return
			}
			try {
				const action = (skill.装备 || skill.equipped) ? 'unequip_martial' : 'use_martial'
				const skillName = skill.名称 || skill.name;
				
				const response = await gameUtils.sendMessage({
					type: action,
					data: { skill_name: skillName }
				})
				if (response.type === action + '_success') {
					// 直接使用后端返回的 player 数据，确保数据一致性
					if (response.data.player) {
						// 确保 player 中的 martial_skills 为 list 格式
						if (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {
							response.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map(name => {
								return { name, ...response.data.player.martial_skills[name] }
							});
						}
						// 直接更新本地数据，不触发 gameState 通知，避免循环调用
						this.player = Object.assign({}, response.data.player);
						gameState.player = response.data.player;
						await this.fetchPlayerDataFromServer(); // 强制拉取最新数据
						this.syncSelectedMartials();
						this.updateData();
						this.$forceUpdate();
					} else {
						// 如果没有返回 player 数据，则重新请求
						await this.fetchPlayerDataFromServer();
					}
					await this.fetchBonusSummary();
					this.$forceUpdate();
					uni.showToast({ title: response.data.message, icon: 'success' })
				} else {
					uni.showToast({ title: response.data.message, icon: 'none' })
				}
			} catch (error) {
				console.error('武学操作失败:', error)
				uni.showToast({ title: '操作失败', icon: 'none' })
			}
		},
		
		async showSkillDetail(skill) {
			// 如果配置为空，尝试重新加载
			if (this.martialConfigs.length === 0) {
				await this.loadMartialConfigs()
			}

			this.selectedSkill = skill
			this.showDetail = true
		},


		
		closeDetail() {
			this.showDetail = false
			this.selectedSkill = null
		},
		
		showMoves(skill) {
			this.selectedSkill = skill
			this.showMovesModal = true
		},
		
		closeMovesModal() {
			this.showMovesModal = false
			this.selectedSkill = null
		},
		
		onTrainMartialClick() {
			this.trainMartial(this.selectedSkill);
		},
		async trainMartial(skill) {
			if (!this.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' })
				return
			}
			if (!skill.解锁 && !skill.unlocked) {
				uni.showToast({ title: '该武学尚未解锁', icon: 'none' })
				return
			}
			if (this.player.skill_points <= 0) {
				uni.showToast({ title: '武学点不足', icon: 'none' })
				return
			}
			try {
				this.showTrainLoading = true;
				if (!this.showTrainResultModal) {
					this.trainLog = [];
					this.showTrainResultModal = true;
				}
				this.resetTrainLogTimeout(); // 修炼开始时启动超时定时器
				// 直接发送，不 await，不处理返回值，完全依赖日志推送
				gameUtils.sendMessage({
					type: 'train_martial',
					data: { name: skill.name || skill.名称 }
				});
			} catch (error) {
				console.error('【调试】修炼异常', error);
				this.appendTrainLog('修炼失败');
				uni.showToast({ title: '修炼失败', icon: 'none' });
				this.showTrainLoading = false;
				this.clearTrainLogTimeout();
			}
			// finally 里不再关闭 loading，由日志监听控制
		},
		isLifeSkill(skill) {
			// 仅用于装备等过滤，实际展示已用lifeSkills
			const skillName = skill.名称 || skill.name || ''
			return skill.类别 === '生活技能' || skill.类别 === '生活类' || skillName.includes('采') || skillName.includes('药') || skillName.includes('伐木') || skillName.includes('挖矿') || skillName.includes('剥皮')
		},
		openMartialSelect(type) {
			this.martialSelectType = type
			const found = this.martialTypes.find(t => t.key === type)
			this.martialSelectTypeLabel = found ? found.label : type
			this.showMartialSelect = true
		},
		closeMartialSelect() {
			this.showMartialSelect = false
		},
		async selectMartial(skill, type) {

			this.selectedMartials[type] = skill.name;
			this.showMartialSelect = false;
			
			// 选择武功后自动装备
			if (skill.unlocked || skill.解锁) {
				// 如果已装备，不需要重复装备
				if (this.isMartialEquipped(skill)) {

					return;
				}
				
				try {
					const skillName = skill.name || skill.名称;
					
					// 显示加载提示
					uni.showLoading({
						title: '正在装备...',
						mask: true
					});
					
					// 使用gameUtils.sendMessage发送请求并处理响应
					const response = await gameUtils.sendMessage({
						type: 'use_martial',
						data: { skill_name: skillName }
					});
					
					// 隐藏加载提示
					uni.hideLoading();
					
					if (response && response.type === 'use_martial_success') {

						// 直接使用后端返回的player数据，确保数据一致性
						if (response.data.player) {
							// 确保player中的martial_skills为list格式
							if (response.data.player.martial_skills && !Array.isArray(response.data.player.martial_skills)) {
								response.data.player.martial_skills = Object.keys(response.data.player.martial_skills).map(name => {
									return { name, ...response.data.player.martial_skills[name] };
								});
							}
							// 更新本地数据
							this.player = Object.assign({}, response.data.player);
							gameState.player = response.data.player;
							await this.fetchPlayerDataFromServer(); // 强制拉取最新数据
							this.syncSelectedMartials();
							this.updateData();
							
							// 手动设置装备状态
							const updatedSkill = this.player.martial_skills.find(s => s.name === skillName || s.名称 === skillName);
							if (updatedSkill) {

								updatedSkill.equipped = true;
								if (updatedSkill.装备 !== undefined) {
									updatedSkill.装备 = true;
								}
							}
							
							// 更新界面
							this.updateData();
							await this.fetchBonusSummary();
							
							// 确保视图更新
							setTimeout(() => {
								this.$forceUpdate();
							}, 50);
							
							// 显示成功提示
							uni.showToast({ 
								title: response.data.message || `成功装备${skillName}`, 
								icon: 'success' 
							});
						}
					} else {
						// 显示错误提示
						uni.showToast({ 
							title: (response && response.data && response.data.message) || '装备失败', 
							icon: 'none' 
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('装备武功失败:', error);
					uni.showToast({ title: '操作异常', icon: 'none' });
				}
			} else {
				uni.showToast({ title: '该武学尚未解锁', icon: 'none' });
			}
		},
		async fetchBonusSummary() {
			try {
				const res = await gameUtils.sendMessage({
					type: 'get_bonus_summary',
					data: {}
				});
				if (res.type === 'bonus_summary') {
					this.bonusSummary = res.data;
				}
			} catch (e) {
				console.warn('获取增益摘要失败', e);
			}
		},
		isBasicMartial(skill) {
			// 判断是否为基础武功
			const basicNames = ['基本剑法', '基本拳法', '基本刀法', '基本棍法', '基本招架', '基本轻功', '基础内功', '基础暗器', '基本暗器法'];
			return basicNames.includes(skill.name) || basicNames.includes(skill.名称);
		},
		syncSelectedMartials() {
			this.selectedMartials = {};
			if (this.player && this.player.martial_skills) {
				this.player.martial_skills.forEach(skill => {
					if (skill.equipped) {
						const type = skill.type || skill.类型 || skill.类别 || '其他';
						this.selectedMartials[type] = skill.name;
					}
				});
			}
		},
		getSkillLevelUpExp(skill) {
			// 经验系数，优先用 skill.maxExp，如果没有则用品质推断
			if (skill.maxExp) return skill.maxExp;

			// 检查是否是生活技能
			const isLifeSkill = this.isLifeSkill(skill);
			const quality = skill.quality || skill.品质 || '普通';
			const level = skill.level || skill.等级 || 0;

			let coefficient = 50;
			if (isLifeSkill) {
				// 生活技能使用固定系数60，与采集系统保持一致
				coefficient = 60;
			} else {
				// 武功技能根据品质设置系数
				if (quality === '稀有') coefficient = 80;
				else if (quality === '绝世') coefficient = 120;
				else if (quality === '传说') coefficient = 200;
			}

			return coefficient * Math.pow(level + 1, 2);
		},
		closeTrainResultModal() {
			this.showTrainResultModal = false;
			this.trainLog = [];
			this.clearTrainLogTimeout();
		},
		appendTrainLog(msg) {
			this.trainLog.push(msg);
			this.$nextTick(() => {
				this.trainLogScrollTop = 99999 + this.trainLog.length;
			});
		},
		resetTrainLogTimeout() {
			this.clearTrainLogTimeout();
			this.trainLogTimeout = setTimeout(() => {
				if (this.trainFinished) return; // 已完成不再提示超时
				this.showTrainLoading = false;
				this.appendTrainLog('【提示】修炼超时，请检查网络或稍后重试。');
				uni.showToast({ title: '修炼超时，请检查网络', icon: 'none' });
			}, 10000); // 10秒无日志判定超时
		},
		clearTrainLogTimeout() {
			if (this.trainLogTimeout) {
				clearTimeout(this.trainLogTimeout);
				this.trainLogTimeout = null;
			}
			this.trainFinished = false; // 关闭弹窗时重置
		},
		handleReconnectCloseAllPopups() {
			this.showSkillDetail = false;
			this.showTrainResultModal = false;
			this.showTrainLoading = false;
			this.trainLog = [];
			// 其它需要关闭的弹窗/状态可在此补充
		},
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
	background: #f5f7fa;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 30rpx;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
}

.energy-info {
	display: flex;
	align-items: center;
}

.energy-label {
	color: #fff;
	font-size: 28rpx;
	margin-right: 10rpx;
}

.energy-value {
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
}

.skill-count {
	background: rgba(255,255,255,0.2);
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
}

.count-text {
	color: #fff;
	font-size: 24rpx;
}

.category-tabs {
	display: flex;
	background: #fff;
	border-radius: 20rpx;
	padding: 10rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 20rpx 10rpx;
	border-radius: 15rpx;
	transition: all 0.3s;
}

.tab-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tab-text {
	font-size: 28rpx;
	color: #333;
}

.tab-item.active .tab-text {
	color: #fff;
}

.filter-section {
	background: #fff;
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.filter-row {
	display: flex;
	gap: 20rpx;
}

.filter-item {
	flex: 1;
	display: flex;
	align-items: center;
}

.filter-label {
	font-size: 28rpx;
	color: #333;
	margin-right: 10rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	padding: 10rpx 20rpx;
	border-radius: 10rpx;
}

.skills-list {
	height: calc(100vh - 400rpx);
}

.skill-group {
	margin-bottom: 24px;
}

.group-header {
	background: #fff;
	padding: 30rpx;
	border-radius: 20rpx 20rpx 0 0;
	border-bottom: 2rpx solid #f0f0f0;
}

.group-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 0;
	line-height: 60rpx;
}

.group-desc {
	font-size: 24rpx;
	color: #666;
}

.skill-item {
	background: #fff;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.skill-item:last-child {
	border-bottom: none;
	border-radius: 0 0 20rpx 20rpx;
}

.skill-info {
	flex: 1;
	margin-right: 20rpx;
}

.skill-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.skill-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.skill-status-group {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.skill-level-text {
	font-size: 24rpx;
	color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	margin-bottom: 5rpx;
}

.skill-school {
	font-size: 20rpx;
	color: #1890ff;
	background: rgba(24, 144, 255, 0.1);
	padding: 3rpx 10rpx;
	border-radius: 10rpx;
	margin-bottom: 5rpx;
}

.skill-equipped {
	font-size: 20rpx;
	color: #52c41a;
	background: rgba(82, 196, 26, 0.1);
	padding: 3rpx 10rpx;
	border-radius: 10rpx;
}

.skill-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 15rpx;
	line-height: 1.4;
}

.skill-progress {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.progress-bg {
	flex: 1;
	height: 20rpx;
	background: #f0f0f0;
	border-radius: 10rpx;
	margin-right: 15rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	border-radius: 10rpx;
	transition: width 0.3s;
}

.progress-text {
	font-size: 22rpx;
	color: #666;
	min-width: 80rpx;
}

.skill-status {
	margin-top: 10rpx;
}

.skill-unlock {
	font-size: 22rpx;
	color: #52c41a;
}

.skill-locked {
	font-size: 22rpx;
	color: #ff4d4f;
}

.skill-actions {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.action-btn {
	padding: 15rpx 30rpx;
	border-radius: 15rpx;
	font-size: 24rpx;
	border: none;
}

.study-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.study-btn:disabled {
	background: #ccc;
	color: #999;
}

.use-btn {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
	color: #fff;
}

.use-btn.unequip {
	background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
}

.use-btn:disabled {
	background: #ccc;
	color: #999;
}

.moves-btn {
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	color: #fff;
}

.moves-btn:disabled {
	background: #ccc;
	color: #999;
}

.empty-skills {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}

.modal-content {
	background: #fff;
	border-radius: 20rpx;
	width: 85%;
	max-width: 650rpx;
	max-height: 90vh;
	overflow: hidden;
	box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.15);
	display: flex;
	flex-direction: column;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
	flex-shrink: 0;
	background: #fff;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 40rpx;
	color: #999;
	cursor: pointer;
}

.modal-body {
	padding: 30rpx;
	flex: 1;
	overflow-y: auto;
	background: #fafafa;
	min-height: 0;
}

.detail-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.detail-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.detail-status {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.detail-level {
	font-size: 28rpx;
	color: #667eea;
	margin-bottom: 5rpx;
}

.detail-school {
	font-size: 24rpx;
	color: #1890ff;
	background: rgba(24, 144, 255, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
	margin-bottom: 5rpx;
}

.detail-equipped {
	font-size: 24rpx;
	color: #52c41a;
	background: rgba(82, 196, 26, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
}

.detail-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 30rpx;
}

.detail-progress {
	margin-bottom: 30rpx;
}

.progress-title {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.progress-bar {
	display: flex;
	align-items: center;
}

.detail-effects {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f6ffed;
	border-radius: 10rpx;
	border-left: 4rpx solid #52c41a;
}

.effects-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}

.effects-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

/* 武功特效样式 */
.detail-special-effects {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
	border-radius: 12rpx;
	border-left: 4rpx solid #1890ff;
	box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.1);
}

.special-effects-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 12rpx;
	display: block;
}

.special-effects-text {
	font-size: 26rpx;
	color: #1890ff;
	font-weight: bold;
	background: rgba(24, 144, 255, 0.1);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	display: inline-block;
}

.detail-category {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f0f8ff;
	border-radius: 10rpx;
	border-left: 4rpx solid #1890ff;
}

.category-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}

.category-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.detail-moves {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #fff7e6;
	border-radius: 10rpx;
	border-left: 4rpx solid #faad14;
}

.moves-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 15rpx;
	display: block;
}

.moves-list {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.move-item {
	background: #ffffff;
	border-radius: 12rpx;
	padding: 20rpx;
	border: 1rpx solid #e8e8e8;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
}

.move-item.move-unlocked {
	border-left: 4rpx solid #52c41a;
	background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
}

.move-item.move-locked {
	border-left: 4rpx solid #d9d9d9;
	background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
	opacity: 0.7;
}

.move-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
}

.move-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.move-status-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 22rpx;
	font-weight: bold;
}

.move-status-badge.status-unlocked {
	background: #f6ffed;
	color: #52c41a;
	border: 1rpx solid #b7eb8f;
}

.move-status-badge.status-locked {
	background: #fafafa;
	color: #8c8c8c;
	border: 1rpx solid #d9d9d9;
}

.move-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.move-unlock-condition {
	font-size: 24rpx;
	color: #666;
}

.move-attack, .move-defense {
	font-size: 24rpx;
	color: #1890ff;
	font-weight: bold;
}

.moves-container {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.moves-container .move-item {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 10rpx;
	border-left: 4rpx solid #667eea;
}

.move-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}

.move-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.modal-footer {
	display: flex;
	padding: 30rpx;
	border-top: 2rpx solid #f0f0f0;
	gap: 20rpx;
	background: #fff;
	border-radius: 0 0 20rpx 20rpx;
	flex-shrink: 0;
	min-height: 120rpx;
}

.modal-btn {
	flex: 1;
	padding: 24rpx 20rpx;
	border-radius: 15rpx;
	font-size: 30rpx;
	border: none;
	font-weight: 500;
	min-height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.cancel-btn {
	background: #f0f0f0;
	color: #666;
}

.confirm-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.confirm-btn:disabled {
	background: #ccc;
}

.use-btn {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
	color: #fff;
}

.use-btn.unequip {
	background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
}

/* 标题和选择器同行样式 */
.title-selector-row {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 0;
}

.group-title {
	font-size: 17px;
	font-weight: bold;
	color: #7a5c2e;
	min-width: 64px;   /* 固定宽度，保证对齐 */
	text-align: right;
	margin-right: 24px;
	flex-shrink: 0;
}

.equip-selector {
	display: flex;
	align-items: center;
	justify-content: flex-start;
	gap: 8px;
	flex: 1;
}

.title-selector-row .skill-actions {
	flex: 0 0 auto;
}

/* 使用武功选择器样式 */
.equip-selector {
	margin: 0;
	display: flex;
	align-items: center;
}
.picker-display {
	display: flex;
	align-items: center;
	border: 1px solid #e0e0e0;
	border-radius: 8px;
	padding: 0 8px;
	font-size: 12px;
	background: #fafbfc;
	min-width: 60px;
	height: 28px;
	transition: border 0.2s, box-shadow 0.2s, background 0.2s;
}
.picker-display:active, .picker-display:focus {
	border: 1px solid #b6b6b6;
	background: #f5f7fa;
}
.picker-text {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.picker-arrow {
	margin-left: 2px;
	font-size: 11px;
	color: #aaa;
}
/* 装备按钮样式 */
.equip-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	height: 60rpx;
	padding: 12rpx 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 15rpx;
	font-size: 24rpx;
	font-weight: 600;
	border: none;
}

.equip-btn.equipped {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}
.martial-info {
	display: none;
}
/* 增益效果样式 */
.martial-bonus {
	margin-top: 15rpx;
	display: flex;
	align-items: flex-start;
}

.martial-bonus-title {
	font-size: 24rpx;
	color: #666;
	margin-right: 15rpx;
	white-space: nowrap;
	font-weight: 600;
}

.martial-bonus-content {
	font-size: 22rpx;
	color: #52c41a;
	line-height: 1.4;
	flex: 1;
	word-break: keep-all;
	white-space: normal;
}
.martial-select-modal-mask {
	position: fixed;
	left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.18);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}
.martial-select-modal {
	background: #ffffff;
	border-radius: 20rpx;
	min-width: 600rpx;
	max-width: 90vw;
	max-height: 70vh;
	box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
	display: flex;
	flex-direction: column;
	align-items: stretch;
	padding: 40rpx 30rpx 30rpx 30rpx;
	border: 2rpx solid #e2e8f0;
}
.martial-select-title {
	font-size: 32rpx;
	font-weight: 700;
	color: #1e293b;
	margin-bottom: 20rpx;
	text-align: center;
}
.martial-select-list {
	flex: 1;
	min-height: 120px;
	max-height: 40vh;
	overflow-y: auto;
	margin-bottom: 10px;
}
.martial-select-item {
	display: flex;
	flex-direction: column;
	padding: 20rpx;
	border-radius: 12rpx;
	margin-bottom: 12rpx;
	background: #f8fafc;
	cursor: pointer;
	transition: all 0.2s ease;
	border: 2rpx solid #e2e8f0;
}
.martial-select-item.selected, .martial-select-item:hover {
	background: #e6f7ff;
	border-color: #667eea;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.1);
}
.martial-select-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 8rpx;
}

.martial-select-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #2d3748;
	flex: 1;
}

.martial-select-status {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.martial-select-level {
	font-size: 24rpx;
	color: #667eea;
	font-weight: 500;
	margin-bottom: 4rpx;
}

.martial-select-quality {
	font-size: 24rpx;
	color: #d69e2e;
	font-weight: 500;
	margin-bottom: 8rpx;
}

.martial-select-desc {
	font-size: 24rpx;
	color: #4a5568;
	line-height: 1.5;
	font-weight: 400;
}

.martial-select-equipped {
	font-size: 22rpx;
	color: #52c41a;
	background: rgba(82, 196, 26, 0.1);
	padding: 4rpx 12rpx;
	border-radius: 10rpx;
	margin-top: 8rpx;
}
.martial-select-empty {
	color: #aaa;
	font-size: 13px;
	text-align: center;
	margin: 16px 0;
}
.martial-select-cancel {
	margin-top: 20rpx;
	background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
	color: #4a5568;
	border-radius: 50rpx;
	border: 2rpx solid #e2e8f0;
	font-size: 28rpx;
	font-weight: 600;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}
.martial-select-cancel:active {
	background: linear-gradient(135deg, #edf2f7 0%, #e2e8f0 100%);
	transform: scale(0.98);
}
.martial-select-tag {
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 16px;
	background: #f8f5e6;
	border: 1.5px solid #c9a86b;
	font-family: 'FZSongKeBenXian-Z07S', '仿宋', serif;
	font-size: 15px;
	color: #7a5c2e;
	padding: 4px 18px;
	margin-right: 4px;
	cursor: pointer;
	transition: all 0.2s ease;
	margin: 0;
}
.martial-select-tag:active, .martial-select-tag:focus {
	background: #e2e8f0;
	border-color: #667eea;
	transform: scale(0.98);
}

.martial-select-tag:disabled {
	background: #f1f5f9;
	border-color: #cbd5e0;
	color: #94a3b8;
	cursor: not-allowed;
	transform: none;
}

.martial-select-tag:disabled:active, .martial-select-tag:disabled:focus {
	background: #f1f5f9;
	border-color: #cbd5e0;
	transform: none;
}
.martial-select-tag-text {
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: center;
	font-weight: 500;
}
.martial-select-tag-arrow {
	margin-left: 6px;
	font-size: 13px;
	color: #aaa;
}
.martial-clear-btn {
	width: 22px;
	height: 22px;
	line-height: 20px;
	border-radius: 50%;
	background: #fff0f0;
	color: #d9534f;
	font-size: 16px;
	font-weight: bold;
	padding: 0;
	margin-left: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #e0b4b4;
	flex-shrink: 0;
}

.martial-list {
  margin: 24rpx 0 0 0;
}
.martial-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f8fa;
  border-radius: 16rpx;
  margin-bottom: 18rpx;
  padding: 0 18rpx;
  height: 72rpx;
}
.martial-type {
  font-size: 28rpx;
  color: #3e2c13;
  font-weight: bold;
  width: 100rpx;
  text-align: left;
}
.martial-btn {
  flex: 1;
  margin-left: 18rpx;
  font-size: 26rpx;
  border-radius: 12rpx;
  border: none;
  outline: none;
  text-align: center;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: 'STKaiti', 'KaiTi', 'FZKai-Z03', '楷体', serif;
}
.martial-btn.equipped {
  background: linear-gradient(90deg, #bfa76a 0%, #f8f5e6 100%);
  color: #3e2c13;
  font-weight: bold;
  border: 2rpx solid #bfa76a;
}
.martial-btn.unequipped {
  background: #f0f0f0;
  color: #bbb;
  border: 2rpx dashed #e0cda2;
}

.train-log-scroll {
  padding: 0 12px;
  box-sizing: border-box;
}

.train-loading-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.25);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.train-loading-content {
  background: #fff;
  border-radius: 16px;
  padding: 32px 36px 24px 36px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.train-spinner {
  width: 36px;
  height: 36px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #a084fa;
  border-radius: 50%;
  animation: train-spin 1s linear infinite;
  margin-bottom: 16px;
}
@keyframes train-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.train-loading-text {
  font-size: 16px;
  color: #7a5c2e;
  margin-top: 2px;
  letter-spacing: 1px;
}

</style> 