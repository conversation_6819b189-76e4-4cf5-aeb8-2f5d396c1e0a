#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务系统模块
负责处理任务相关功能，包括任务接取、完成、奖励等
"""

import json
import logging
import aiosqlite
import os
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, List
from websockets.server import WebSocketServerProtocol

logger = logging.getLogger(__name__)


class QuestSystem:
    def __init__(self, db_path: str, game_server):
        self.db_path = db_path
        self.game_server = game_server
        
        # 加载任务配置
        self.quests_config = self.load_quests_config()

    def load_quests_config(self) -> dict:
        """加载任务配置"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), 'config', 'quests.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载任务配置失败: {e}")
            return {"quests": {}, "npc_quests": {}}

    async def init_quest_tables(self):
        """初始化任务相关数据表"""
        async with aiosqlite.connect(self.db_path) as db:
            # 创建玩家任务表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS player_quests (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    quest_id TEXT NOT NULL,
                    status TEXT DEFAULT 'active',
                    progress TEXT DEFAULT '{}',
                    accepted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    completed_at TIMESTAMP,
                    last_reset TIMESTAMP
                )
            ''')
            
            # 创建任务进度表
            await db.execute('''
                CREATE TABLE IF NOT EXISTS quest_progress (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    quest_id TEXT NOT NULL,
                    objective_type TEXT NOT NULL,
                    target TEXT NOT NULL,
                    current_count INTEGER DEFAULT 0,
                    required_count INTEGER NOT NULL,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            await db.commit()

    async def handle_quest_action(self, data: dict, websocket: WebSocketServerProtocol) -> dict:
        """处理任务相关操作"""
        try:
            user_id = getattr(websocket, 'user_id', None)
            if not user_id:
                return {'type': 'error', 'data': {'message': '未认证'}}

            action = data.get('action')
            
            if action == 'get_available_quests':
                return await self.get_available_quests(data, user_id)
            elif action == 'accept_quest':
                return await self.accept_quest(data, user_id)
            elif action == 'get_active_quests':
                return await self.get_active_quests(user_id)
            elif action == 'complete_quest':
                return await self.complete_quest(data, user_id)
            elif action == 'abandon_quest':
                return await self.abandon_quest(data, user_id)
            elif action == 'get_npc_quests':
                return await self.get_npc_quests(data, user_id)
            else:
                return {'type': 'error', 'data': {'message': '未知操作'}}

        except Exception as e:
            logger.error(f"处理任务操作失败: {e}")
            return {'type': 'error', 'data': {'message': f'操作失败: {str(e)}'}}

    async def get_npc_quests(self, data: dict, user_id: str) -> dict:
        """获取NPC的任务列表"""
        try:
            npc_name = data.get('npc_name')
            if not npc_name:
                return {'type': 'error', 'data': {'message': '请指定NPC名称'}}

            player = self.game_server.player_data.get(str(user_id))
            if not player:
                return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

            # 获取该NPC的任务列表
            npc_quest_ids = self.quests_config.get('npc_quests', {}).get(npc_name, [])
            
            available_quests = []
            completed_quests = []
            
            await self.init_quest_tables()
            
            async with aiosqlite.connect(self.db_path) as db:
                for quest_id in npc_quest_ids:
                    quest_config = self.quests_config['quests'].get(quest_id)
                    if not quest_config:
                        continue
                    
                    # 检查任务状态
                    async with db.execute('''
                        SELECT status, completed_at, last_reset FROM player_quests 
                        WHERE user_id = ? AND quest_id = ?
                        ORDER BY accepted_at DESC LIMIT 1
                    ''', (user_id, quest_id)) as cursor:
                        quest_record = await cursor.fetchone()
                    
                    # 检查任务是否可接取
                    can_accept = self.can_accept_quest(quest_config, player, quest_record)
                    
                    quest_info = {
                        'quest_id': quest_id,
                        'name': quest_config.get('name'),
                        'description': quest_config.get('description'),
                        'type': quest_config.get('type'),
                        'level_requirement': quest_config.get('level_requirement', 1),
                        'rewards': quest_config.get('rewards', {}),
                        'can_accept': can_accept,
                        'status': quest_record[0] if quest_record else 'not_started'
                    }
                    
                    if quest_record and quest_record[0] == 'completed':
                        completed_quests.append(quest_info)
                    elif can_accept:
                        available_quests.append(quest_info)

            return {
                'type': 'npc_quests_success',
                'data': {
                    'npc_name': npc_name,
                    'available_quests': available_quests,
                    'completed_quests': completed_quests
                }
            }

        except Exception as e:
            logger.error(f"获取NPC任务失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取NPC任务失败: {str(e)}'}}

    def can_accept_quest(self, quest_config: dict, player: dict, quest_record: tuple = None) -> bool:
        """检查是否可以接取任务"""
        # 检查等级要求
        if quest_config.get('level_requirement', 1) > player.get('level', 1):
            return False
        
        # 检查前置任务
        prerequisites = quest_config.get('prerequisites', [])
        if prerequisites:
            # TODO: 检查前置任务是否完成
            pass
        
        # 检查是否已完成且不可重复
        if quest_record:
            status = quest_record[0]
            if status == 'completed' and not quest_config.get('repeatable', False):
                return False
            
            # 检查重复任务的冷却时间
            if status == 'completed' and quest_config.get('repeatable', False):
                cooldown = quest_config.get('cooldown', 0)
                if cooldown > 0 and quest_record[1]:  # completed_at
                    completed_time = datetime.fromisoformat(quest_record[1].replace('Z', '+00:00'))
                    if (datetime.now() - completed_time).total_seconds() < cooldown:
                        return False
            
            # 检查每日任务重置
            if quest_config.get('daily', False):
                last_reset = quest_record[2] if len(quest_record) > 2 else None
                if last_reset:
                    last_reset_time = datetime.fromisoformat(last_reset.replace('Z', '+00:00'))
                    if last_reset_time.date() == datetime.now().date():
                        return False
        
        return True

    async def accept_quest(self, data: dict, user_id: str) -> dict:
        """接取任务"""
        try:
            quest_id = data.get('quest_id')
            if not quest_id:
                return {'type': 'error', 'data': {'message': '请指定任务ID'}}

            quest_config = self.quests_config['quests'].get(quest_id)
            if not quest_config:
                return {'type': 'error', 'data': {'message': '任务不存在'}}

            player = self.game_server.player_data.get(str(user_id))
            if not player:
                return {'type': 'error', 'data': {'message': '玩家数据不存在'}}

            await self.init_quest_tables()

            async with aiosqlite.connect(self.db_path) as db:
                # 检查是否已接取该任务
                async with db.execute('''
                    SELECT status FROM player_quests 
                    WHERE user_id = ? AND quest_id = ? AND status = 'active'
                ''', (user_id, quest_id)) as cursor:
                    active_quest = await cursor.fetchone()
                
                if active_quest:
                    return {'type': 'error', 'data': {'message': '你已经接取了这个任务'}}

                # 检查是否可以接取
                async with db.execute('''
                    SELECT status, completed_at, last_reset FROM player_quests 
                    WHERE user_id = ? AND quest_id = ?
                    ORDER BY accepted_at DESC LIMIT 1
                ''', (user_id, quest_id)) as cursor:
                    quest_record = await cursor.fetchone()

                if not self.can_accept_quest(quest_config, player, quest_record):
                    return {'type': 'error', 'data': {'message': '不满足任务接取条件'}}

                # 接取任务
                await db.execute('''
                    INSERT INTO player_quests (user_id, quest_id, status, progress)
                    VALUES (?, ?, 'active', '{}')
                ''', (user_id, quest_id))

                # 初始化任务进度
                objectives = quest_config.get('objectives', {})
                for obj_type, obj_data in objectives.items():
                    target = obj_data.get('target', '')
                    required_count = obj_data.get('count', 1)
                    
                    await db.execute('''
                        INSERT INTO quest_progress (user_id, quest_id, objective_type, target, current_count, required_count)
                        VALUES (?, ?, ?, ?, 0, ?)
                    ''', (user_id, quest_id, obj_type, target, required_count))

                await db.commit()

                logger.info(f"玩家 {player.get('character_name', user_id)} 接取任务 {quest_config['name']}")

                return {
                    'type': 'accept_quest_success',
                    'data': {
                        'message': f'成功接取任务：{quest_config["name"]}',
                        'quest': quest_config
                    }
                }

        except Exception as e:
            logger.error(f"接取任务失败: {e}")
            return {'type': 'error', 'data': {'message': f'接取任务失败: {str(e)}'}}

    async def get_active_quests(self, user_id: str) -> dict:
        """获取玩家当前活跃任务"""
        try:
            await self.init_quest_tables()

            async with aiosqlite.connect(self.db_path) as db:
                async with db.execute('''
                    SELECT quest_id, progress, accepted_at FROM player_quests 
                    WHERE user_id = ? AND status = 'active'
                    ORDER BY accepted_at DESC
                ''', (user_id,)) as cursor:
                    quest_records = await cursor.fetchall()

                active_quests = []
                for quest_id, progress, accepted_at in quest_records:
                    quest_config = self.quests_config['quests'].get(quest_id)
                    if not quest_config:
                        continue

                    # 获取任务进度
                    async with db.execute('''
                        SELECT objective_type, target, current_count, required_count 
                        FROM quest_progress 
                        WHERE user_id = ? AND quest_id = ?
                    ''', (user_id, quest_id)) as cursor:
                        progress_records = await cursor.fetchall()

                    quest_progress = {}
                    for obj_type, target, current, required in progress_records:
                        quest_progress[obj_type] = {
                            'target': target,
                            'current': current,
                            'required': required,
                            'completed': current >= required
                        }

                    active_quests.append({
                        'quest_id': quest_id,
                        'name': quest_config.get('name'),
                        'description': quest_config.get('description'),
                        'type': quest_config.get('type'),
                        'progress': quest_progress,
                        'accepted_at': accepted_at,
                        'can_complete': all(p['completed'] for p in quest_progress.values())
                    })

                return {
                    'type': 'active_quests_success',
                    'data': {
                        'quests': active_quests
                    }
                }

        except Exception as e:
            logger.error(f"获取活跃任务失败: {e}")
            return {'type': 'error', 'data': {'message': f'获取活跃任务失败: {str(e)}'}}

    async def update_quest_progress(self, user_id: str, objective_type: str, target: str, count: int = 1):
        """更新任务进度"""
        try:
            await self.init_quest_tables()

            async with aiosqlite.connect(self.db_path) as db:
                # 查找相关的活跃任务
                async with db.execute('''
                    SELECT DISTINCT pq.quest_id 
                    FROM player_quests pq
                    JOIN quest_progress qp ON pq.user_id = qp.user_id AND pq.quest_id = qp.quest_id
                    WHERE pq.user_id = ? AND pq.status = 'active' 
                    AND qp.objective_type = ? AND (qp.target = ? OR qp.target = '任意怪物')
                ''', (user_id, objective_type, target)) as cursor:
                    quest_ids = await cursor.fetchall()

                for (quest_id,) in quest_ids:
                    # 更新进度
                    await db.execute('''
                        UPDATE quest_progress 
                        SET current_count = current_count + ?, updated_at = ?
                        WHERE user_id = ? AND quest_id = ? AND objective_type = ? 
                        AND (target = ? OR target = '任意怪物')
                        AND current_count < required_count
                    ''', (count, datetime.now().isoformat(), user_id, quest_id, objective_type, target))

                await db.commit()

        except Exception as e:
            logger.error(f"更新任务进度失败: {e}")

    async def complete_quest(self, data: dict, user_id: str) -> dict:
        """完成任务"""
        try:
            quest_id = data.get('quest_id')
            if not quest_id:
                return {'type': 'error', 'data': {'message': '请指定任务ID'}}

            quest_config = self.quests_config['quests'].get(quest_id)
            if not quest_config:
                return {'type': 'error', 'data': {'message': '任务不存在'}}

            await self.init_quest_tables()

            async with aiosqlite.connect(self.db_path) as db:
                # 检查任务状态和进度
                async with db.execute('''
                    SELECT objective_type, target, current_count, required_count 
                    FROM quest_progress 
                    WHERE user_id = ? AND quest_id = ?
                ''', (user_id, quest_id)) as cursor:
                    progress_records = await cursor.fetchall()

                if not progress_records:
                    return {'type': 'error', 'data': {'message': '任务不存在或未接取'}}

                # 检查是否所有目标都完成
                all_completed = all(current >= required for _, _, current, required in progress_records)
                if not all_completed:
                    return {'type': 'error', 'data': {'message': '任务目标尚未完成'}}

                # 给予奖励
                rewards = quest_config.get('rewards', {})
                player = self.game_server.player_data.get(str(user_id))
                if player:
                    # 经验奖励
                    if 'exp' in rewards:
                        player['exp'] = player.get('exp', 0) + rewards['exp']
                    
                    # 银两奖励
                    if 'silver' in rewards:
                        player['silver'] = player.get('silver', 0) + rewards['silver']
                    
                    # 物品奖励
                    if 'items' in rewards:
                        # TODO: 添加物品到背包
                        pass

                    # 更新数据库
                    await db.execute('''
                        UPDATE players SET exp = ?, silver = ? WHERE user_id = ?
                    ''', (player['exp'], player['silver'], user_id))

                # 标记任务完成
                await db.execute('''
                    UPDATE player_quests 
                    SET status = 'completed', completed_at = ?
                    WHERE user_id = ? AND quest_id = ? AND status = 'active'
                ''', (datetime.now().isoformat(), user_id, quest_id))

                await db.commit()

                logger.info(f"玩家 {player.get('character_name', user_id)} 完成任务 {quest_config['name']}")

                return {
                    'type': 'complete_quest_success',
                    'data': {
                        'message': f'任务完成：{quest_config["name"]}',
                        'rewards': rewards
                    }
                }

        except Exception as e:
            logger.error(f"完成任务失败: {e}")
            return {'type': 'error', 'data': {'message': f'完成任务失败: {str(e)}'}}

    async def abandon_quest(self, data: dict, user_id: str) -> dict:
        """放弃任务"""
        try:
            quest_id = data.get('quest_id')
            if not quest_id:
                return {'type': 'error', 'data': {'message': '请指定任务ID'}}

            await self.init_quest_tables()

            async with aiosqlite.connect(self.db_path) as db:
                # 删除任务记录
                await db.execute('''
                    DELETE FROM player_quests 
                    WHERE user_id = ? AND quest_id = ? AND status = 'active'
                ''', (user_id, quest_id))
                
                # 删除进度记录
                await db.execute('''
                    DELETE FROM quest_progress 
                    WHERE user_id = ? AND quest_id = ?
                ''', (user_id, quest_id))

                await db.commit()

                return {
                    'type': 'abandon_quest_success',
                    'data': {
                        'message': '任务已放弃'
                    }
                }

        except Exception as e:
            logger.error(f"放弃任务失败: {e}")
            return {'type': 'error', 'data': {'message': f'放弃任务失败: {str(e)}'}}
