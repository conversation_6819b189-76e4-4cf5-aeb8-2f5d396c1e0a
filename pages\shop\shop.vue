<template>

	<view class="container">
		<!-- 顶部信息 -->
		<view class="header">
			<view class="money-info">
				<text class="money-label">银两:</text>
				<text class="money-value">{{ formatNumber(money) }}</text>
			</view>
			<view class="shop-type">
				<text class="shop-name">{{ currentShop.name }}</text>
			</view>
		</view>

		<!-- 商店切换 -->
		<!-- <view class="shop-tabs"> ... </view> -->

		<!-- 市场功能 -->
		<view v-if="currentShopType === 'market'" class="market-section">
			<view class="market-actions">
				<button class="market-btn" @click="listItem">
					<text class="btn-text">上架物品</text>
				</button>
				<button class="market-btn" @click="showMyOrders">
					<text class="btn-text">订单列表</text>
				</button>
			</view>
			<scroll-view class="order-list-scroll" scroll-y="true">
				<view class="order-list">
					<view v-if="marketList.length === 0" class="market-empty">暂无玩家上架物品</view>
					<view v-for="order in marketList" :key="order.id" class="order-card">
						<image
							class="item-img"
							:src="order.item && order.item.img ? order.item.img : '/static/logo.png'"
							mode="aspectFill"
						/>
						<view class="order-info">
							<view class="item-name">{{ order.item && order.item.name ? order.item.name : '未知物品' }}</view>
							<view class="item-detail">
								<text>单价：</text><text class="price">{{ order.price }}</text>
								<text> 数量：</text><text>{{ order.quantity }}</text>
							</view>
							<view class="seller">卖家：{{ order.seller }}</view>
						</view>
						<button class="buy-btn" @click="buyMarketItem(order)">购买</button>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 商品列表 -->
		<scroll-view v-else class="goods-list" scroll-y="true">
			<view
				class="goods-item simple-row"
				v-for="(item, index) in currentShop.goods"
				:key="index"
				@click="showItemDetail(item)"
			>
				<text class="item-name">{{ item.name }}</text>
				<text class="item-quality" :style="{ color: getQualityColor(item.quality) }">
					{{ getQualityName(item.quality) }}
				</text>
				<!-- 类型显示直接用 item.type，不再用 getTypeName(item.类型 || item.type) -->
				<text class="item-type">{{ item.type }}</text>
				<text class="item-price main-price">{{ item.price }} 银两</text>
				<button class="buy-btn" @click.stop="buyItem(item)">购买</button>
			</view>
			<view class="empty-goods" v-if="currentShop.goods.length === 0">
				<text>暂无商品</text>
			</view>
		</scroll-view>

		<!-- 物品详情弹窗 -->
		<view class="modal-overlay" v-if="showDetail" @click="closeDetail">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">商品详情</text>
					<text class="modal-close" @click="closeDetail">×</text>
				</view>
				<view class="modal-body" v-if="selectedItem">
					<text class="detail-name">{{ selectedItem.name }}</text>
					<text class="detail-quality" :style="{ color: getQualityColor(selectedItem.quality) }">
						品质: {{ getQualityName(selectedItem.quality) }}
					</text>
					<text class="detail-type">类型: {{ selectedItem.type }}</text>
					<text class="detail-desc" v-if="selectedItem.description">{{ selectedItem.description }}</text>

					<!-- 装备属性 -->
					<view class="detail-stats" v-if="selectedItem.attack || selectedItem.defense || selectedItem.hp || selectedItem.mp">
						<text class="stats-title">属性加成:</text>
						<text v-if="selectedItem.attack">攻击: {{ selectedItem.attack }}</text>
						<text v-if="selectedItem.defense">防御: {{ selectedItem.defense }}</text>
						<text v-if="selectedItem.hp">气血: {{ selectedItem.hp }}</text>
						<text v-if="selectedItem.mp">内力: {{ selectedItem.mp }}</text>
					</view>

					<view class="detail-price">
						<text class="price-title">价格:</text>
						<text class="price-value">{{ selectedItem.price }} 银两</text>
					</view>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeDetail">关闭</button>
					<button class="modal-btn confirm-btn" @click="buyItem(selectedItem)">
						购买
					</button>
				</view>
			</view>
		</view>

		<!-- 上架弹窗 -->
		<view v-if="showListModal" class="modal-overlay" @click="showListModal=false">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">上架物品</text>
					<text class="modal-close" @click="showListModal=false">×</text>
				</view>
				<view class="modal-body">
					<view v-if="!listItemData">
						<view v-if="filteredMyItems.length===0" class="empty-items">暂无可上架物品</view>
						<scroll-view class="list-select-list" scroll-y="true">
							<view v-for="item in filteredMyItems" :key="item.id" class="list-select-item" @click="selectListItem(item)">
								<text class="item-name">{{ item.name }}</text>
								<text class="item-quality" :style="{ color: getQualityColor(item.quality) }">{{ getQualityName(item.quality) }}</text>
								<text class="item-quantity" v-if="item.quantity>1">x{{ item.quantity }}</text>
							</view>
						</scroll-view>
					</view>
					<view v-else class="price-input-section">
						<view class="selected-item-info">
							<text class="selected-item-label">选中物品：</text>
							<text class="selected-item-name">{{ listItemData.name }}</text>
							<text class="selected-item-quality" :style="{ color: getQualityColor(listItemData.quality) }">
								{{ getQualityName(listItemData.quality) }}
							</text>
							<text class="item-stock">库存：{{ listItemData.quantity || 1 }}</text>
						</view>

						<view class="quantity-input-wrapper">
							<text class="quantity-label">出售数量：</text>
							<view class="quantity-controls">
								<button class="quantity-btn" @click="decreaseQuantity">-</button>
								<input
									class="quantity-input"
									v-model="listQuantity"
									type="number"
									:max="listItemData.quantity || 1"
									min="1"
								/>
								<button class="quantity-btn" @click="increaseQuantity">+</button>
							</view>
						</view>

						<view class="price-input-wrapper">
							<text class="price-label">单价（银两）：</text>
							<input
								class="price-input"
								v-model="listPrice"
								type="number"
								placeholder="请输入单价"
							/>
						</view>

						<view class="total-price-info" v-if="listPrice && listQuantity">
							<text class="total-label">总价：</text>
							<text class="total-value">{{ (listPrice * listQuantity) || 0 }} 银两</text>
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="cancelListItem">取消</button>
					<button class="modal-btn confirm-btn" v-if="listItemData" @click="confirmListItem">确认上架</button>
				</view>
			</view>
		</view>

		<!-- 订单列表弹窗 -->
		<view v-if="showMyOrdersModal" class="modal-overlay" @click="showMyOrdersModal=false">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">我的订单</text>
					<text class="modal-close" @click="showMyOrdersModal=false">×</text>
				</view>
				<view class="modal-body">
					<scroll-view class="order-list-scroll" scroll-y="true">
						<view class="order-list">
							<view v-if="myOrderList.length === 0" class="market-empty">暂无订单</view>
							<view v-for="order in myOrderList" :key="order.id" class="order-card">
								<image
									class="item-img"
									:src="order.item && order.item.img ? order.item.img : '/static/logo.png'"
									mode="aspectFill"
								/>
								<view class="order-info">
									<view class="item-name">{{ order.item && order.item.name ? order.item.name : '未知物品' }}</view>
									<view class="item-detail">
										<text>单价：</text><text class="price">{{ order.price }}</text>
										<text> 数量：</text><text>{{ order.quantity }}</text>
									</view>
									<view class="seller">卖家：{{ order.seller }}</view>
									<view class="order-time" :class="{ 'time-warning': getTimeRemaining(order) < 24 }">
										剩余: {{ formatTimeRemaining(order) }}
									</view>
								</view>
								<!-- 可加“下架”按钮等操作 -->
								<button class="unlist-btn" @click="unlinkOrder(order)">下架</button>
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="showMyOrdersModal=false">关闭</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import gameState from '../../utils/gameState.js'
import { gameUtils } from '../../utils/gameData.js'
import wsManager from '../../utils/websocket.js'

export default {
	data() {
		return {
			money: 0,
			myItems: [],
			currentShopType: 'market',
			showDetail: false,
			selectedItem: null,
			npcId: 'changan_coalboss',
			shops: [
				{
					type: 'market',
					name: '市场',
					goods: []
				}
			],
			loading: false,
			// 新增：物品配置
			itemsConfig: {},
			marketList: [], // 玩家市场物品
			showListModal: false, // 上架弹窗
			listItemData: null,   // 当前要上架的物品
			listPrice: '',        // 上架价格
			listQuantity: 1,     // 上架数量
			listTab: 'item', // 上架弹窗的tab，'item'或'martial'
			myMartials: [], // 玩家武功列表
			listMartialData: null, // 当前要上架的武功
			showMyOrdersModal: false, // 控制订单弹窗显示
			myOrderList: [], // 当前玩家订单
		}
	},

	computed: {
		currentShop() {
			return this.shops[0];
		},
		filteredMyItems() {
			// 合并物品配置后再过滤
			return this.myItems
				.map(item => {
					const config = this.itemsConfig[item.id] || {};
					return { ...item, ...config };
				})
				.filter(item =>
					(item.is_sellable === undefined && item.sellable === undefined) ||
					item.is_sellable === true ||
					item.is_sellable === 1 ||
					item.sellable === true ||
					item.sellable === 1
				);
		}
	},

	// 处理NPC商店信息
	async handleNpcShopInfo(npcInfo) {
		try {
			console.log('处理NPC商店信息:', npcInfo)

			// 显示NPC商店提示
			uni.showToast({
				title: `进入${npcInfo.npc_name}的商店`,
				icon: 'none',
				duration: 2000
			})

			// 根据tab类型处理
			if (npcInfo.tab === 'buy') {
				// 获取NPC商店物品
				await this.fetchNpcShopItems(npcInfo.npc_name)
			} else if (npcInfo.tab === 'sell') {
				// 显示出售界面提示
				uni.showModal({
					title: npcInfo.npc_name,
					content: '请在背包中选择要出售的物品',
					showCancel: false,
					success: () => {
						// 跳转到背包页面
						uni.navigateTo({
							url: '/pages/character/backpack?mode=sell&npc=' + encodeURIComponent(npcInfo.npc_name)
						})
					}
				})
			}
		} catch (error) {
			console.error('处理NPC商店信息失败:', error)
			uni.showToast({
				title: '商店加载失败',
				icon: 'none'
			})
		}
	},

	// 获取NPC商店物品
	async fetchNpcShopItems(npcName) {
		try {
			const response = await gameUtils.sendMessage({
				type: 'npc_function',
				npc_name: npcName,
				function: 'shop',
				data: { action: 'list' }
			})

			if (response && response.type === 'shop_items') {
				console.log('获取到NPC商店物品:', response.data.items)

				// 显示NPC商店物品
				this.showNpcShopItems(response.data.items, npcName)
			} else {
				console.error('获取NPC商店物品失败:', response)
				uni.showToast({
					title: '获取商店物品失败',
					icon: 'none'
				})
			}
		} catch (error) {
			console.error('获取NPC商店物品异常:', error)
			uni.showToast({
				title: '网络错误',
				icon: 'none'
			})
		}
	},

	// 显示NPC商店物品
	showNpcShopItems(items, npcName) {
		// 这里可以切换到NPC商店模式
		// 暂时使用模态框显示
		const itemList = items.map(item =>
			`${item.item_id}: ${item.price}银两 (库存:${item.stock})`
		)

		uni.showActionSheet({
			itemList: itemList.slice(0, 6), // 最多显示6个
			success: async (res) => {
				const selectedItem = items[res.tapIndex]
				await this.buyNpcItem(selectedItem, npcName)
			}
		})
	},

	// 购买NPC物品
	async buyNpcItem(item, npcName) {
		try {
			// 询问购买数量
			const quantity = await this.askQuantity(item.item_id, item.price)
			if (quantity <= 0) return

			const response = await gameUtils.sendMessage({
				type: 'npc_function',
				npc_name: npcName,
				function: 'shop',
				data: {
					action: 'buy',
					item_id: item.item_id,
					quantity: quantity
				}
			})

			if (response && response.type === 'buy_success') {
				uni.showToast({
					title: '购买成功',
					icon: 'success'
				})

				// 刷新玩家数据
				this.requestPlayerData()
				this.updateMoney()
			} else if (response && response.type === 'error') {
				uni.showToast({
					title: response.data.message,
					icon: 'none'
				})
			}
		} catch (error) {
			console.error('购买NPC物品失败:', error)
			uni.showToast({
				title: '购买失败',
				icon: 'none'
			})
		}
	},

	// 询问购买数量
	askQuantity(itemId, price) {
		return new Promise((resolve) => {
			uni.showModal({
				title: '购买数量',
				content: `${itemId} 单价:${price}银两\n请输入购买数量:`,
				editable: true,
				placeholderText: '1',
				success: (res) => {
					if (res.confirm) {
						const quantity = parseInt(res.content) || 1
						resolve(quantity > 0 ? quantity : 1)
					} else {
						resolve(0)
					}
				}
			})
		})
	},

	onLoad() {
		this.requestPlayerData()
		this.updateMoney()
		this.updateData()
		this.fetchShopGoods('changan_coalboss')
		this.loadItemsConfig() // 新增
		this.refreshMarket()
	},

	onShow() {
		this.requestPlayerData()
		this.updateMoney()
		this.updateData()
		this.refreshMarket() // 新增，确保每次页面显示都刷新订单
	},

	methods: {
		async requestPlayerData() {
			try {
				const response = await gameUtils.sendMessage({
					type: 'get_player_data',
					data: {}
				});

				if (response && response.type === 'player_data' && response.data) {
					// 更新 gameState 中的玩家数据
					gameState.player = response.data;

					// 更新银两显示
					if (typeof response.data.money === 'number') {
						this.money = response.data.money;
						gameState.money = response.data.money; // 同步到 gameState
					}

					// 更新背包
					if (response.data.inventory) {
						gameState.inventory = response.data.inventory;
						this.myItems = [...response.data.inventory];
					}
				}
			} catch (error) {
				console.error('获取玩家数据失败:', error);
			}
		},

		updateData() {
			this.money = gameState.money
			this.myItems = [...gameState.inventory]
		},

		formatNumber(num) {
			return gameUtils.formatNumber(num)
		},

		getQualityColor(quality) {
			return gameUtils.getQualityColor(quality)
		},

		getQualityName(quality) {
			const qualities = {
				'common': '普通',
				'uncommon': '精品',
				'rare': '稀有',
				'epic': '史诗',
				'legendary': '传说',
				'mythic': '仙品'
			}
			return qualities[quality] || '普通'
		},

		getTypeName(type) {
			const types = {
				'weapon': '武器',
				'armor': '护甲',
				'necklace': '项链',
				'bracelet': '手镯',
				'mount': '坐骑',
				'material': '材料',
				'herb': '草药'
			}
			return types[type] || '未知'
		},

		getSellPrice(item) {
			const prices = {
				'common': 10,
				'uncommon': 50,
				'rare': 200,
				'epic': 1000,
				'legendary': 5000,
				'mythic': 10000
			}

			const basePrice = prices[item.quality] || 10
			return basePrice * (item.quantity || 1)
		},

		switchShop(shopType) {
			this.currentShopType = shopType
			if (shopType === 'equipment') this.npcId = 'changan_coalboss'
			else if (shopType === 'herb') this.npcId = 'changan_herbboss'
			else if (shopType === 'material') this.npcId = 'changan_materialboss'
			else this.npcId = ''
			// 自动请求对应商店数据
			if (this.npcId) {
				this.fetchShopGoods(this.npcId)
			}
		},

		showItemDetail(item) {
			this.selectedItem = item
			this.showDetail = true
		},

		closeDetail() {
			this.showDetail = false
			this.selectedItem = null
		},

		async buyItem(item) {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}
			if (this.money < item.price) {
				uni.showToast({
					title: '银两不足',
					icon: 'none'
				})
				return
			}

			try {
				// 购买物品时，强制带 type（英文）、sellable 字段
				const type = item.type || '';
				const sellable = (typeof item.sellable !== 'undefined' ? item.sellable : true) ? true : false;
				const response = await this.$request({
					type: 'shop_action',
					data: {
						action: 'buy',
						item_id: item.id,
						quantity: 1, // 购买数量
						type,
						sellable
					}
				});

				// 检查响应是否包含错误
				if (response && response.type === 'error') {
					uni.showToast({
						title: response.data.message || '购买失败',
						icon: 'none',
						duration: 3000
					});
					return;
				}

				// 等待后端返回player_data和inventory_data自动刷新
				uni.showToast({
					title: '购买成功',
					icon: 'success'
				});
				this.closeDetail();
			} catch (error) {
				uni.showToast({
					title: '购买失败，请重试',
					icon: 'none'
				});
			}
		},

		sellMyItem(item) {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}
			const sellPrice = this.getSellPrice(item)

			uni.showModal({
				title: '确认出售',
				content: `确定要出售 ${item.name} 吗？\n获得: ${sellPrice} 银两`,
				success: (res) => {
					if (res.confirm) {
						gameState.money += sellPrice
						gameState.removeItem(item.id, item.quantity || 1)
						this.updateData()
						gameState.save()

						uni.showToast({
							title: '出售成功',
							icon: 'success'
						})
					}
				}
			})
		},

		async listItem() {
			try {
				await this.fetchPlayerInventory();
			} catch (e) {
			}
			this.updateData();
			this.listItemData = null;
			this.listPrice = '';
			this.showListModal = true;
		},

		async fetchPlayerInventory() {
			const resp = await gameUtils.sendMessage({
				type: 'get_inventory_data'
			});
			let inventory;
			if (resp && resp.data && Array.isArray(resp.data.inventory)) {
				inventory = resp.data.inventory;
			} else if (resp && Array.isArray(resp.inventory)) {
				inventory = resp.inventory;
			} else if (resp && resp.data && Array.isArray(resp.data)) {
				inventory = resp.data;
			}
			if (Array.isArray(inventory)) {
				gameState.inventory = inventory;
			}
		},

		// 取消上架
		cancelListItem() {
			this.showListModal = false;
			this.listItemData = null;
			this.listPrice = '';
			this.listQuantity = 1;
		},

		// 增加数量
		increaseQuantity() {
			const maxQuantity = this.listItemData?.quantity || 1;
			if (this.listQuantity < maxQuantity) {
				this.listQuantity++;
			}
		},

		// 减少数量
		decreaseQuantity() {
			if (this.listQuantity > 1) {
				this.listQuantity--;
			}
		},

		async confirmListItem() {
			if (!this.listItemData || !this.listPrice || !this.listQuantity) {
				uni.showToast({ title: '请选择物品、输入价格和数量', icon: 'none' });
				return;
			}

			// 验证数量
			const maxQuantity = this.listItemData.quantity || 1;
			if (this.listQuantity > maxQuantity) {
				uni.showToast({ title: `数量不能超过库存${maxQuantity}`, icon: 'none' });
				return;
			}

			if (this.listQuantity < 1) {
				uni.showToast({ title: '数量不能小于1', icon: 'none' });
				return;
			}

			// 显示加载提示
			uni.showLoading({ title: '上架中...' });

			try {
				const resp = await gameUtils.sendMessage({
					type: 'market_action',
					data: {
						action: 'list',
						item_id: this.listItemData.id,
						price: Number(this.listPrice),
						quantity: Number(this.listQuantity)
					}
				});

				// 隐藏加载提示
				uni.hideLoading();



				if (resp && (resp.type === 'success' || resp.type === 'market_action_success')) {
					// 显示成功提示
					uni.showToast({ title: resp.data.message || '上架成功', icon: 'success' });

					// 关闭窗口和重置数据
					this.showListModal = false;
					this.listItemData = null;
					this.listPrice = '';
					this.listQuantity = 1;

					// 异步刷新数据，不阻塞UI
					this.refreshDataAfterList();
				} else if (resp && (resp.type === 'error' || resp.type === 'market_action_failed')) {
					uni.showToast({ title: resp.data.message || '上架失败', icon: 'none' });
				} else if (resp && resp.type && resp.type.includes('timeout')) {
					// 超时情况，可能已经成功，关闭窗口并刷新
					uni.showToast({ title: '请求超时，请检查是否上架成功', icon: 'none' });
					this.showListModal = false;
					this.listItemData = null;
					this.listPrice = '';
					this.listQuantity = 1;
					this.refreshDataAfterList();
				} else {
					console.error('未知响应格式:', resp);
					uni.showToast({ title: '上架失败，响应格式错误', icon: 'none' });
				}
			} catch (error) {
				// 隐藏加载提示
				uni.hideLoading();
				console.error('上架失败:', error);
				uni.showToast({ title: '网络错误，请重试', icon: 'none' });

				// 网络错误也关闭窗口，避免卡住
				this.showListModal = false;
				this.listItemData = null;
				this.listPrice = '';
				this.listQuantity = 1;
			}
		},

		// 上架后异步刷新数据
		async refreshDataAfterList() {
			try {
				// 并行执行刷新操作，减少等待时间
				await Promise.all([
					this.refreshMarket(),
					this.requestPlayerData()
				]);
			} catch (error) {
				console.error('刷新数据失败:', error);
				// 静默失败，不影响用户体验
			}
		},

		async refreshMarket() {
			const resp = await gameUtils.sendMessage({
				type: 'market_action',
				data: { action: 'get_market_list' }
			});
			const list = (resp && resp.data && resp.data.list) || [];
			this.marketList = list.map(order => ({
				...order,
				item: order.item || { name: '未知物品', quality: 'common' }
			}));

		},

		async buyMarketItem(order) {
			try {
				const resp = await gameUtils.sendMessage({
					type: 'market_action',
					data: { action: 'buy', order_id: Number(order.id) }
				});

				if (resp && (resp.type === 'success' || resp.type === 'market_action_success')) {
					uni.showToast({ title: resp.data.message || '购买成功', icon: 'success' });
					// 刷新市场和玩家数据
					await this.refreshMarket();
					await this.requestPlayerData(); // 重新获取玩家数据，包括银两
				} else {
					uni.showToast({ title: resp?.data?.message || '购买失败', icon: 'none' });
				}
			} catch (error) {
				console.error('购买失败:', error);
				uni.showToast({ title: '购买失败', icon: 'none' });
			}
		},

		fetchShopGoods(npcId) {
			this.loading = true;
			gameUtils.sendMessage({
				type: 'shop_action',
				data: { npc_id: npcId }
			}).then(response => {
				if (response && response.type === 'shop_items' && response.data && response.data.items) {
					this.shops[0].goods = response.data.items.map(item => ({
						id: item.id,
						name: item.name,
						description: item.desc,
						icon: item.icon,
						price: item.price,
						stock: item.stock,
						attack: item.attack,
						defense: item.defense,
						hp: item.hp,
						mp: item.mp,
						type: item.type,
						quality: item.quality
					}));
				}
				this.loading = false;
			}).catch(error => {
			});
		},

		updateMoney() {
			// 优先从player对象获取最新银两
			if (gameState.player && typeof gameState.player.money === 'number') {
				this.money = gameState.player.money;
				gameState.money = gameState.player.money; // 同步到 gameState.money
			} else if (typeof gameState.money === 'number') {
				this.money = gameState.money;
			} else {
				this.money = 0; // 默认值
			}
		},

		destroyed() {
			// 不再需要移除监听器，因为我们使用gameUtils.sendMessage
		},

		async loadItemsConfig() {
			this.itemsConfig = await gameState.getItemsConfig();
		},
		// 可在需要时通过 this.itemsConfig[itemId] 获取物品详情

		selectListItem(item) {
			this.listItemData = item;
			this.listPrice = '';
			this.listQuantity = 1; // 初始化数量为1
		},

		selectListMartial(martial) {
			this.listMartialData = martial;
			this.listPrice = '';
		},

		async fetchMyMartials() {
			const resp = await gameUtils.sendMessage({
				type: 'get_player_martials'
			});
			this.myMartials = resp.martials || [];
		},

		async loadMyMartials() {
			await this.fetchMyMartials();
		},

		async showMyOrders() {
			// 先刷新市场数据
			await this.refreshMarket();

			const player = gameState.player || {};
			const myName = player.name || player.username || '';
			// 只展示当前玩家上架的前10个订单
			this.myOrderList = this.marketList.filter(order => order.seller === myName).slice(0, 10);
			this.showMyOrdersModal = true;
		},

		// 下架订单
		async unlinkOrder(order) {
			try {
				uni.showModal({
					title: '确认下架',
					content: `确定要下架 ${order.item.name} 吗？物品将返还到背包。`,
					success: async (res) => {
						if (res.confirm) {
							const resp = await gameUtils.sendMessage({
								type: 'market_action',
								data: {
									action: 'unlist',
									order_id: order.id
								}
							});

							if (resp && (resp.type === 'success' || resp.type === 'market_action_success')) {
								uni.showToast({ title: resp.data.message || '下架成功', icon: 'success' });

								// 刷新市场和订单列表，更新玩家数据
								await this.refreshMarket();
								await this.requestPlayerData(); // 重新获取玩家数据，包括背包

								// 重新加载我的订单列表
								const myName = gameState.player?.name || gameState.name || '未知玩家';
								const oldCount = this.myOrderList.length;
								this.myOrderList = this.marketList.filter(order => order.seller === myName).slice(0, 10);
							} else {
								uni.showToast({ title: resp?.data?.message || '下架失败', icon: 'none' });
							}
						}
					}
				});
			} catch (error) {
				console.error('下架失败:', error);
				uni.showToast({ title: '下架失败', icon: 'none' });
			}
		},

		// 计算剩余时间（小时）
		getTimeRemaining(order) {
			if (!order.expires_at) return 72; // 如果没有过期时间，默认3天
			const now = Date.now() / 1000; // 转换为秒
			const remaining = (order.expires_at - now) / 3600; // 转换为小时
			return Math.max(0, remaining);
		},

		// 格式化剩余时间显示
		formatTimeRemaining(order) {
			const hours = this.getTimeRemaining(order);
			if (hours <= 0) return '已过期';
			if (hours < 1) return '不足1小时';
			if (hours < 24) return `${Math.floor(hours)}小时`;
			const days = Math.floor(hours / 24);
			const remainingHours = Math.floor(hours % 24);
			return `${days}天${remainingHours}小时`;
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	padding-bottom: 140rpx; /* 为tabBar留出空间 */
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.money-info {
	display: flex;
	align-items: center;
}

.money-label {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
}

.money-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.shop-name {
	font-size: 28rpx;
	color: #666;
}

.shop-tabs {
	display: flex;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 10rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 15rpx;
	border-radius: 15rpx;
	transition: all 0.3s ease;
}

.tab-item.active {
	background: linear-gradient(135deg, #667eea, #764ba2);
}

.tab-text {
	font-size: 28rpx;
	color: #333;
}

.tab-item.active .tab-text {
	color: white;
}

.market-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	flex: 1;
	margin-bottom: 20rpx;
	display: flex;
	flex-direction: column;
	min-height: 0;
}

.market-info {
	margin-bottom: 20rpx;
}

.market-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.market-desc {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.market-actions {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.market-btn {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 8rpx 16rpx;
	font-size: 24rpx;
}

.btn-text {
	font-size: 28rpx;
	font-weight: bold;
	color: white;
}

.market-list {
	height: 300rpx;
}

.market-empty {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

.goods-list {
	height: 400rpx;
	padding: 20rpx;
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.goods-item.simple-row {
	display: grid;
	grid-template-columns: 180rpx 70rpx 30rpx 110rpx minmax(120rpx, 1fr);
	align-items: center;
	padding: 16rpx 0;
	border-bottom: 1px solid #eee;
	background: rgba(255,255,255,0.92);
	border-radius: 8rpx;
	margin-bottom: 8rpx;
}

.item-name {
	font-size: 30rpx;
	font-weight: bold;
	margin-right: 0;
	max-width: 180rpx;
	flex-shrink: 0;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.item-quality {
	font-size: 26rpx;
	margin-right: 0;
	width: 70rpx;
	flex-shrink: 0;
	text-align: center;
}

.item-type {
	font-size: 26rpx;
	color: #666;
	margin-right: 0;
	width: 30rpx;
	flex-shrink: 0;
	text-align: center;
}

.item-sep {
	color: #bbb;
	margin: 0 10rpx;
	font-size: 26rpx;
}

.item-price, .main-price {
	display: inline-block;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	vertical-align: middle;
	font-size: 26rpx;
}

.buy-btn {
	font-size: 26rpx;
	padding: 8rpx 24rpx;
	background: linear-gradient(90deg, #5a8dee 60%, #42e9f6 100%);
	color: #fff;
	border-radius: 8rpx;
	justify-self: end;
	min-width: 120rpx;
	box-shadow: 0 2rpx 8rpx rgba(90,141,238,0.12);
	transition: background 0.2s;
}

.buy-btn:active {
	background: #3a6fd6;
}

.empty-goods {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

.my-items-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	flex: 1;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.section-subtitle {
	font-size: 24rpx;
	color: #666;
}

.my-items-list {
	height: 300rpx;
}

.my-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: white;
	border-radius: 15rpx;
	margin-bottom: 10rpx;
}

.my-item:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.item-quantity {
	font-size: 24rpx;
	color: #666;
	margin-left: 10rpx;
}

.sell-price {
	text-align: right;
}

.empty-items {
	text-align: center;
	padding: 60rpx 20rpx;
	color: #8e8e93;
	font-size: 30rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 16rpx;
	margin: 20rpx 0;
	border: 2rpx dashed #dee2e6;
}

.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.6);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	backdrop-filter: blur(4rpx);
}

.modal-content {
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	border-radius: 24rpx;
	width: 85%;
	max-width: 650rpx;
	max-height: 85vh;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 40rpx 30rpx 30rpx 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	position: relative;
}

.modal-title {
	font-size: 38rpx;
	font-weight: bold;
	color: white;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.modal-close {
	font-size: 44rpx;
	color: rgba(255, 255, 255, 0.8);
	line-height: 1;
	padding: 10rpx;
	border-radius: 50%;
	transition: all 0.3s ease;
}

.modal-close:hover {
	background: rgba(255, 255, 255, 0.2);
	color: white;
}

.modal-body {
	padding: 40rpx 30rpx;
	max-height: 500rpx;
	overflow-y: auto;
	background: rgba(255, 255, 255, 0.95);
}

.detail-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.detail-quality,
.detail-type {
	font-size: 28rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.detail-desc {
	font-size: 26rpx;
	color: #999;
	display: block;
	margin-bottom: 20rpx;
	line-height: 1.5;
}

.detail-stats {
	margin-bottom: 20rpx;
}

.stats-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.detail-stats text {
	font-size: 26rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}

.detail-price {
	display: flex;
	align-items: center;
}

.price-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-right: 10rpx;
}

.price-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #e74c3c;
}

.modal-footer {
	display: flex;
	padding: 30rpx;
	background: rgba(248, 249, 250, 0.8);
	border-top: 1rpx solid rgba(0, 0, 0, 0.05);
	gap: 20rpx;
}

.modal-btn {
	flex: 1;
	padding: 24rpx 20rpx;
	border: none;
	border-radius: 20rpx;
	font-size: 30rpx;
	font-weight: bold;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.cancel-btn {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	color: #6c757d;
	border: 1rpx solid rgba(108, 117, 125, 0.2);
}

.cancel-btn:hover {
	background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
	transform: translateY(-2rpx);
}

.confirm-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3);
}

.confirm-btn:hover {
	background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
	transform: translateY(-2rpx);
	box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
}

.order-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	padding: 20rpx;
}
.order-card {
	display: flex;
	align-items: center;
	background: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
	padding: 20rpx;
	gap: 20rpx;
	position: relative;
}
.item-img {
	width: 100rpx;
	height: 100rpx;
	border-radius: 12rpx;
	background: #f5f5f5;
	object-fit: cover;
}
.order-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}
.item-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.item-detail {
	font-size: 26rpx;
	color: #666;
}
.price {
	color: #e43d33;
	font-weight: bold;
}
.seller {
	font-size: 24rpx;
	color: #999;
}
.buy-btn {
	background: linear-gradient(90deg, #ffb347, #ffcc33);
	color: #fff;
	border: none;
	border-radius: 24rpx;
	padding: 0 32rpx;
	font-size: 28rpx;
	height: 60rpx;
	line-height: 60rpx;
}

.list-tabs {
	display: flex;
	justify-content: space-around;
	margin-bottom: 20rpx;
	padding: 10rpx 0;
	border-bottom: 1rpx solid #eee;
}

.list-tab {
	font-size: 28rpx;
	color: #666;
	padding: 10rpx 20rpx;
	border-bottom: 2rpx solid transparent;
}

.list-tab.active {
	color: #333;
	border-bottom-color: #667eea;
	font-weight: bold;
}

.list-select-list {
	max-height: 500rpx;
	min-height: 300rpx;
	overflow-y: auto;
	background: rgba(255, 255, 255, 0.8);
	border-radius: 16rpx;
	padding: 10rpx;
	margin: 20rpx 0;
}

.list-select-item {
	display: flex;
	align-items: center;
	padding: 20rpx 16rpx;
	margin: 8rpx 0;
	background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
	border-radius: 12rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.list-select-item:hover {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.2);
}

.list-select-item:last-child {
	margin-bottom: 0;
}

.item-name {
	flex: 1;
}

/* 价格输入区域样式 */
.price-input-section {
	padding: 20rpx 0;
}

.selected-item-info {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 24rpx;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
}

.selected-item-label {
	font-size: 28rpx;
	color: #6c757d;
	margin-right: 12rpx;
}

.selected-item-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-right: 12rpx;
}

.selected-item-quality {
	font-size: 26rpx;
	font-weight: bold;
}

.price-input-wrapper {
	display: flex;
	align-items: center;
	background: white;
	border-radius: 16rpx;
	padding: 20rpx;
	border: 2rpx solid #e9ecef;
	transition: all 0.3s ease;
}

.price-input-wrapper:focus-within {
	border-color: #667eea;
	box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}

.price-label {
	font-size: 30rpx;
	color: #495057;
	margin-right: 16rpx;
	white-space: nowrap;
}

.price-input {
	flex: 1;
	font-size: 32rpx;
	color: #333;
	border: none;
	outline: none;
	background: transparent;
}

.item-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	margin-right: 10rpx;
}

.item-quality {
	font-size: 26rpx;
	color: #666;
	flex-shrink: 0;
}

.item-quantity {
	font-size: 24rpx;
	color: #999;
	flex-shrink: 0;
}

.item-level {
	font-size: 24rpx;
	color: #999;
	flex-shrink: 0;
}

.order-list-scroll {
	flex: 1;
	min-height: 200rpx;
	overflow-y: auto;
	margin-bottom: 10rpx;
}

/* 下架按钮样式 */
.unlist-btn {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
	color: #fff;
	border: none;
	border-radius: 12rpx;
	padding: 12rpx 20rpx;
	font-size: 24rpx;
	font-weight: 500;
	min-width: 80rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.unlist-btn:active {
	background: linear-gradient(135deg, #ee5a52 0%, #dd4b39 100%);
}

/* 订单时间样式 */
.order-time {
	font-size: 22rpx;
	color: #666;
	margin-top: 4rpx;
}

.order-time.time-warning {
	color: #ff6b6b;
	font-weight: 500;
}

/* 数量控制样式 */
.quantity-input-wrapper {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.quantity-label {
	font-size: 28rpx;
	color: #333;
	margin-right: 20rpx;
	min-width: 140rpx;
}

.quantity-controls {
	display: flex;
	align-items: center;
	flex: 1;
}

.quantity-btn {
	width: 60rpx;
	height: 60rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 8rpx;
	font-size: 32rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

.quantity-btn:active {
	background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.quantity-input {
	flex: 1;
	height: 60rpx;
	border: 2rpx solid #ddd;
	border-radius: 8rpx;
	text-align: center;
	font-size: 28rpx;
	margin: 0 10rpx;
	background: white;
}

/* 库存显示样式 */
.item-stock {
	font-size: 24rpx;
	color: #666;
	margin-left: 10rpx;
}

/* 总价显示样式 */
.total-price-info {
	display: flex;
	align-items: center;
	margin-top: 20rpx;
	padding: 15rpx;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 8rpx;
	border-left: 4rpx solid #667eea;
}

.total-label {
	font-size: 28rpx;
	color: #333;
	margin-right: 10rpx;
}

.total-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #667eea;
}
</style>