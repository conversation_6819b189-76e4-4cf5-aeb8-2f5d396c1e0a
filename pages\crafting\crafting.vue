<template>
	<view class="container">
		<!-- 头部信息 -->
		<view class="header">
			<view class="crafting-level">
				<text class="level-label">打造等级:</text>
				<text class="level-value">{{ craftingLevel }}</text>
			</view>
			<view class="stamina-info">
				<text class="stamina-label">体力:</text>
				<text class="stamina-value">{{ player.energy || 0 }}/{{ player.max_energy || 100 }}</text>
			</view>
		</view>

		<!-- 材料统计 已移除 -->

		<!-- 可合成物品列表 -->
		<view class="recipes-section">
			<view class="section-header">
				<text class="section-title">可合成物品 ({{ craftableItems.length }})</text>
				<button class="refresh-btn" @click="loadCraftableItems" :disabled="isLoading">
					{{ isLoading ? '加载中...' : '刷新' }}
				</button>
			</view>
			
			<!-- 加载状态 -->
			<view v-if="isLoading" class="loading-container">
				<text class="loading-text">正在加载可合成物品...</text>
			</view>
			
			<!-- 空状态 -->
			<view v-else-if="craftableItems.length === 0" class="empty-container">
				<text class="empty-icon">🔧</text>
				<text class="empty-text">暂无可合成物品</text>
				<text class="empty-desc">请收集更多材料来解锁合成配方</text>
			</view>
			
			<!-- 物品列表 -->
			<view v-else class="recipes-grid">
				<view 
					v-for="recipe in craftableItems" 
					:key="recipe.id" 
					class="recipe-item"
					:class="{ 'can-craft': recipe.can_craft, 'cannot-craft': !recipe.can_craft }"
					@click="showRecipeDetail(recipe)"
				>
					<text class="recipe-icon">{{ recipe.icon || '📦' }}</text>
					<text class="recipe-name">{{ recipe.name }}</text>
					<text class="recipe-quality">{{ getQualityName(recipe.quality) }}</text>
					<view class="recipe-type">{{ getTypeName(recipe.type) }}</view>
					<view class="craft-status">
						<text v-if="recipe.can_craft" class="can-craft-text">可合成</text>
						<text v-else class="cannot-craft-text">材料不足</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 合成详情弹窗 -->
		<view v-if="showDetail" class="detail-modal" @click="closeDetail">
			<view class="detail-content" @click.stop>
				<view class="detail-header">
					<text class="detail-title">{{ selectedRecipe.name }}</text>
					<text class="detail-quality">{{ getQualityName(selectedRecipe.quality) }}</text>
				</view>
				
				<view class="detail-info">
					<text class="detail-desc">{{ selectedRecipe.description }}</text>
					<text class="detail-stamina">消耗体力: 10点</text>
					
					<view class="materials-section">
						<text class="materials-title">所需材料:</text>
						<view class="materials-list">
							<view 
								v-for="(quantity, material) in parseRecipe(selectedRecipe.craft_recipe)" 
								:key="material"
								class="material-item"
								:class="{ 'sufficient': getMaterialCount(material) >= quantity, 'insufficient': getMaterialCount(material) < quantity }"
							>
								<text class="material-name">{{ material }}</text>
								<text class="material-quantity">{{ getMaterialCount(material) }}/{{ quantity }}</text>
							</view>
						</view>
						
						<!-- 缺少材料提示 -->
						<view v-if="getMissingMaterials(selectedRecipe).length > 0" class="missing-materials">
							<text class="missing-title">缺少材料:</text>
							<view class="missing-list">
								<text v-for="material in getMissingMaterials(selectedRecipe)" :key="material" class="missing-item">
									{{ material }}
								</text>
							</view>
						</view>
					</view>
				</view>

				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeDetail">关闭</button>
					<button 
						class="modal-btn confirm-btn" 
						@click="craftItem(selectedRecipe)"
						:disabled="!canCraft(selectedRecipe)"
					>
						合成
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import wsManager from '../../utils/websocket.js'
import gameState from '../../utils/gameState.js'
import { gameUtils } from '../../utils/gameData.js'

export default {
	data() {
		return {
			player: {},
			craftingLevel: 1,
			craftableItems: [],
			showDetail: false,
			selectedRecipe: null,
			isLoading: false
		}
	},
	
	computed: {
		// 移除材料统计 - 只显示合成配方中需要的材料
	},
	
	onLoad() {
		this.updateData()
		this.loadCraftableItems()
	},
	
	onShow() {
		this.updateData()
		this.loadCraftableItems()
	},
	
	onUnload() {
		// No specific cleanup needed here as gameUtils handles its own listeners
	},
	
	methods: {
		updateData() {
			this.player = { ...gameState.player }
			this.isAuthed = gameState.isAuthed || false
			// 固定打造等级为1
			this.craftingLevel = 1
		},
		
		async loadCraftableItems() {
			try {
				this.isLoading = true
				// 优先尝试 crafting_action
				let response = await gameUtils.sendMessage({
					type: 'crafting_action',
					data: { action: 'get_craftable_items' }
				});
				// 兼容后端直接推送 get_craftable_success
				if (response.type !== 'get_craftable_success' && response.data && response.data.craftable_items) {
					response = { type: 'get_craftable_success', data: response.data };
				}
				this.isLoading = false;
				if (response.type === 'get_craftable_success') {
					this.craftableItems = response.data.craftable_items || [];
					this.player.energy = response.data.energy || 0;
				} else if (response.type === 'get_craftable_failed') {
					uni.showToast({
						title: response.data.message || '获取可合成物品失败',
						icon: 'none'
					});
				} else if (response.type === 'error') {
					uni.showToast({
						title: response.data.message || '获取可合成物品失败',
						icon: 'none'
					});
				}
			} catch (error) {
				this.isLoading = false
				uni.showToast({
					title: '加载失败: ' + (error.message || '未知错误'),
					icon: 'none'
				})
			}
		},
		
		parseRecipe(recipe) {
			const materials = {};
			if (!recipe) return materials;

			// 如果是对象，直接返回
			if (typeof recipe === 'object') {
				return recipe;
			}

			// 如果是字符串，尝试解析JSON或单引号对象字符串
			if (typeof recipe === 'string') {
				if (recipe.trim().startsWith('{')) {
					try {
						// 尝试标准JSON
						return JSON.parse(recipe);
					} catch (e) {
						// 尝试单引号对象字符串
						try {
							const fixed = recipe.replace(/'/g, '"');
							return JSON.parse(fixed);
						} catch (e2) {}
					}
				}
			}

			// 否则按字符串分割
			const parts = recipe.split(',');
			for (const part of parts) {
				if (part.includes(':')) {
					const [material, quantity] = part.split(':');
					materials[material.trim()] = parseInt(quantity.trim());
				}
			}
			return materials;
		},
		
		getMaterialCount(materialName) {
			// 从gameState的背包中统计材料数量，支持name和id双重匹配
			let count = 0
			gameState.inventory.forEach(item => {
				// 直接匹配name
				if (item.name === materialName) {
					count += item.quantity || 1
				}
				// 如果name没匹配到，尝试通过id匹配
				else if (item.id && item.id.includes(materialName.replace('残页', '_canyie'))) {
					count += item.quantity || 1
				}
			})
			return count
		},
		
		getMissingMaterials(recipe) {
			if (!recipe || !recipe.craft_recipe) return []
			
			const materials = this.parseRecipe(recipe.craft_recipe)
			const missing = []
			
			for (const [material, quantity] of Object.entries(materials)) {
				if (this.getMaterialCount(material) < quantity) {
					missing.push(`${material}(需要${quantity}，现有${this.getMaterialCount(material)})`)
				}
			}
			
			return missing
		},
		
		getQualityColor(quality) {
			return gameUtils.getQualityColor(quality)
		},
		
		getQualityName(quality) {
			const qualities = {
				'common': '普通',
				'fine': '精良',
				'rare': '稀有',
				'epic': '传说',
				'legendary': '神品'
			}
			return qualities[quality] || '普通'
		},
		
		getTypeName(type) {
			const types = {
				'book': '秘籍',
				'consumable': '消耗品',
				'weapon': '武器',
				'armor': '防具',
				'necklace': '项链',
				'helmet': '头盔',
				'offhand': '副手',
				'accessory': '饰品',
				'medal': '勋章'
			}
			return types[type] || type
		},
		
		canCraft(recipe) {
			if (!recipe || !recipe.craft_recipe) return false
			
			// 优先使用后端返回的can_craft状态
			if (recipe.can_craft !== undefined) {
				// 但还要检查体力是否足够
				if (this.player.energy < 10) {
					return false
				}
				return recipe.can_craft
			}
			
			// 如果后端没有返回can_craft状态，则前端自己计算
			const materials = this.parseRecipe(recipe.craft_recipe)
			for (const [material, quantity] of Object.entries(materials)) {
				if (this.getMaterialCount(material) < quantity) {
					return false
				}
			}
			
			// 检查体力是否足够
			if (this.player.energy < 10) {
				return false
			}
			
			return true
		},
		
		showRecipeDetail(recipe) {
			this.selectedRecipe = recipe
			this.showDetail = true
		},
		
		closeDetail() {
			this.showDetail = false
			this.selectedRecipe = null
		},
		
		craftItem(recipe) {
			if (!gameState.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' })
				return
			}
			if (!this.canCraft(recipe)) {
				uni.showToast({
					title: '材料或体力不足',
					icon: 'none'
				})
				return
			}
			
			uni.showModal({
				title: '确认合成',
				content: `确定要合成 ${recipe.name} 吗？\n消耗体力: 10点`,
				success: (res) => {
					if (res.confirm) {
						this.performCraft(recipe)
					}
				}
			})
		},
		
		async performCraft(recipe) {
			try {
				this.isLoading = true
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'crafting_action',
					data: {
						action: 'craft_item',
						item_id: recipe.id
					}
				});
				
				this.isLoading = false;
				
				if (response.type === 'craft_success') {
					uni.showToast({
						title: response.data.message || '合成成功',
						icon: 'success'
					});
					
					// 更新本地数据
					if (response.data.inventory) {
						gameState.inventory = response.data.inventory;
					}
					if (response.data.energy !== undefined) {
						this.player.energy = response.data.energy;
						gameState.player.energy = response.data.energy;
					}
					
					this.closeDetail();
					this.loadCraftableItems(); // 重新加载可合成物品列表
				} else if (response.type === 'craft_failed') {
					uni.showToast({
						title: response.data.message || '合成失败',
						icon: 'none'
					});
				} else if (response.type === 'error') {
					uni.showToast({
						title: response.data.message || '合成失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('合成失败:', error)
				this.isLoading = false
				uni.showToast({
					title: '合成失败: ' + (error.message || '未知错误'),
					icon: 'none'
				})
			}
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	background: rgba(255, 255, 255, 0.9);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.crafting-level,
.stamina-info {
	display: flex;
	align-items: center;
}

.level-label,
.stamina-label {
	font-size: 28rpx;
	color: #666;
	margin-right: 10rpx;
}

.level-value,
.stamina-value {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 材料统计 */
.materials-summary {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.summary-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.no-materials {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 10rpx;
	border: 1rpx dashed #ccc;
}

.no-materials-text {
	font-size: 26rpx;
	color: #999;
}

.materials-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(150rpx, 1fr));
	gap: 10rpx;
}

.material-summary-item {
	background: #f8f9fa;
	border-radius: 10rpx;
	padding: 10rpx;
	text-align: center;
	border: 1rpx solid #e0e0e0;
}

.material-summary-name {
	font-size: 24rpx;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}

.material-summary-count {
	font-size: 28rpx;
	font-weight: bold;
	color: #667eea;
}

.recipes-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.refresh-btn {
	background: #667eea;
	color: white;
	border: none;
	border-radius: 10rpx;
	padding: 10rpx 20rpx;
	font-size: 24rpx;
	cursor: pointer;
	transition: all 0.3s ease;
}

.refresh-btn:hover {
	background: #5a6fd8;
}

.refresh-btn[disabled] {
	background: #bdc3c7;
	cursor: not-allowed;
}

/* 加载状态 */
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 60rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #666;
}

/* 空状态 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 80rpx 20rpx;
	text-align: center;
}

.empty-icon {
	font-size: 80rpx;
	margin-bottom: 20rpx;
}

.empty-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.empty-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.recipes-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
	gap: 15rpx;
}

.recipe-item {
	background: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx;
	text-align: center;
	border: 2rpx solid #e0e0e0;
	transition: all 0.3s ease;
	cursor: pointer;
	position: relative;
	overflow: hidden;
}

.recipe-item:hover {
	transform: translateY(-5rpx);
	box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
}

.recipe-item.can-craft {
	border-color: #27ae60;
	background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
}

.recipe-item.cannot-craft {
	border-color: #e74c3c;
	background: linear-gradient(135deg, #fdecea 0%, #ffcdd2 100%);
}

.recipe-icon {
	font-size: 48rpx;
	margin-bottom: 15rpx;
	color: #667eea;
	display: block;
}

.recipe-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 8rpx;
	display: block;
	line-height: 1.2;
}

.recipe-quality {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 8rpx;
	display: block;
}

.recipe-type {
	font-size: 22rpx;
	color: #999;
	margin-bottom: 15rpx;
	display: block;
	background: rgba(102, 126, 234, 0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.craft-status {
	margin-top: 10rpx;
}

.can-craft-text {
	color: #27ae60;
	font-size: 26rpx;
	font-weight: bold;
	background: rgba(39, 174, 96, 0.1);
	padding: 6rpx 12rpx;
	border-radius: 20rpx;
	display: inline-block;
}

.cannot-craft-text {
	color: #e74c3c;
	font-size: 26rpx;
	font-weight: bold;
	background: rgba(231, 76, 60, 0.1);
	padding: 6rpx 12rpx;
	border-radius: 20rpx;
	display: inline-block;
}

.detail-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	animation: fadeIn 0.3s ease;
}

.detail-content {
	background: white;
	border-radius: 20rpx;
	width: 90%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	animation: slideUp 0.3s ease;
}

.detail-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.detail-title {
	font-size: 36rpx;
	font-weight: bold;
	color: white;
}

.detail-quality {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	background: rgba(255, 255, 255, 0.2);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
}

.detail-info {
	padding: 30rpx;
	max-height: 400rpx;
	overflow-y: auto;
}

.detail-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	margin-bottom: 20rpx;
	display: block;
}

.detail-stamina {
	font-size: 28rpx;
	color: #e74c3c;
	font-weight: bold;
	margin-bottom: 20rpx;
	display: block;
	background: rgba(231, 76, 60, 0.1);
	padding: 10rpx 15rpx;
	border-radius: 10rpx;
}

.materials-section {
	margin-top: 20rpx;
}

.materials-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.materials-list {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.material-item {
	background: #f8f9fa;
	border-radius: 10rpx;
	padding: 15rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 26rpx;
	color: #333;
	border: 1rpx solid #e0e0e0;
	transition: all 0.3s ease;
}

.material-item.sufficient {
	color: #27ae60;
	font-weight: bold;
	background: #e8f5e9;
	border-color: #27ae60;
}

.material-item.insufficient {
	color: #e74c3c;
	font-weight: bold;
	background: #fdecea;
	border-color: #e74c3c;
}

.material-name {
	font-size: 28rpx;
	font-weight: 500;
}

.material-quantity {
	font-size: 28rpx;
	font-weight: bold;
}

/* 缺少材料提示 */
.missing-materials {
	margin-top: 20rpx;
	padding: 15rpx;
	background: #fdecea;
	border-radius: 10rpx;
	border: 1rpx solid #e74c3c;
}

.missing-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #e74c3c;
	margin-bottom: 10rpx;
	display: block;
}

.missing-list {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.missing-item {
	font-size: 24rpx;
	color: #e74c3c;
	background: rgba(231, 76, 60, 0.1);
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
}

.modal-footer {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
	border-top: 1rpx solid #f0f0f0;
	background: #f8f9fa;
}

.modal-btn {
	flex: 1;
	padding: 20rpx;
	border-radius: 15rpx;
	font-size: 30rpx;
	font-weight: bold;
	border: none;
	cursor: pointer;
	transition: all 0.3s ease;
}

.cancel-btn {
	background: #95a5a6;
	color: white;
}

.cancel-btn:hover {
	background: #7f8c8d;
}

.confirm-btn {
	background: #27ae60;
	color: white;
}

.confirm-btn:hover {
	background: #229954;
}

.confirm-btn[disabled] {
	background: #bdc3c7;
	color: #7f8c8d;
	cursor: not-allowed;
}

@keyframes fadeIn {
	from {
		opacity: 0;
	}
	to {
		opacity: 1;
	}
}

@keyframes slideUp {
	from {
		transform: translateY(50rpx);
		opacity: 0;
	}
	to {
		transform: translateY(0);
		opacity: 1;
	}
}

/* 响应式设计 */
@media screen and (max-width: 375px) {
	.recipes-grid {
		grid-template-columns: repeat(2, 1fr);
		gap: 10rpx;
	}
	
	.materials-grid {
		grid-template-columns: repeat(2, 1fr);
	}
	
	.recipe-item {
		padding: 15rpx;
	}
	
	.recipe-icon {
		font-size: 36rpx;
	}
	
	.recipe-name {
		font-size: 24rpx;
	}
}

@media screen and (min-width: 376px) and (max-width: 750px) {
	.recipes-grid {
		grid-template-columns: repeat(3, 1fr);
	}
	
	.materials-grid {
		grid-template-columns: repeat(3, 1fr);
	}
}

@media screen and (min-width: 751px) {
	.recipes-grid {
		grid-template-columns: repeat(4, 1fr);
	}
	
	.materials-grid {
		grid-template-columns: repeat(4, 1fr);
	}
}
</style> 