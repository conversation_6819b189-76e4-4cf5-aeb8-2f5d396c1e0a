<template>
	<view class="backpack-container">
		<!-- 调试信息 -->
		<view style="background: red; color: white; padding: 10rpx; margin: 10rpx;">
			<text>调试信息：背包页面已加载</text>
			<text>背包数据长度：{{ inventoryData.length }}</text>
			<text>背包容量：{{ inventoryCapacity }}</text>
		</view>
		
		<!-- 背包头部 -->
		<view class="backpack-header">
			<text class="backpack-title">🎒 背包</text>
			<text class="backpack-info">{{ inventoryData.length }}/{{ inventoryCapacity }}</text>
		</view>

		<!-- 背包网格 -->
		<view class="backpack-grid">
			<view 
				v-for="(item, index) in inventoryData" 
				:key="index" 
				class="item-slot"
				:class="getItemQualityClass(item)"
				@click="handleItemClick(item, index)"
			>
				<text class="item-icon">{{ item.icon || '📦' }}</text>
				<text class="item-name">{{ item.name || item.名称 }}</text>
				<!-- 移除类型显示 -->
				<text v-if="item.数量 > 1 || item.quantity > 1" class="item-quantity">{{ item.数量 || item.quantity }}</text>
			</view>
			
			<!-- 空槽位 -->
			<view 
				v-for="i in Math.max(0, inventoryCapacity - inventoryData.length)" 
				:key="`empty-${i}`" 
				class="item-slot empty"
			>
				<text class="empty-text">空</text>
			</view>
		</view>

		<!-- 操作按钮 -->
		<view class="backpack-actions">
			<button class="action-btn expand-btn" @click="expandInventory">
				<text>📦 扩充背包</text>
			</button>
			<button class="action-btn sort-btn" @click="sortInventory">
				<text>🔄 整理背包</text>
			</button>
			<button 
				class="action-btn clear-btn" 
				@click="clearInventory"
				:disabled="inventoryData.length === 0 || isLoading"
			>
				<text>{{ isLoading ? '处理中...' : '🗑️ 清空背包' }}</text>
			</button>
		</view>
		
		<!-- 返回按钮 -->
		<view class="backpack-actions back-actions">
			<button class="action-btn back-btn" @click="goBack">
				<text>⬅️ 返回角色</text>
			</button>
		</view>

		<!-- 物品详情弹窗 -->
		<view v-if="showItemDetail" class="item-detail-modal" @click="closeItemDetail">
			<view class="item-detail-content" @click.stop>
				<view class="item-detail-header">
					<text class="item-detail-title">{{ selectedItem.name || selectedItem.名称 }}</text>
					<text class="item-detail-quality">{{ getQualityText(selectedItem.品质 || selectedItem.quality) }}</text>
				</view>
				
				<view class="item-detail-info">
					<text class="item-detail-type">类型：{{ getTypeText(selectedItem.类型 || selectedItem.type) }}</text>
					<text class="item-detail-quality-text">品质：{{ getQualityText(selectedItem.品质 || selectedItem.quality) }}</text>
					<text v-if="selectedItem.数量 > 1 || selectedItem.quantity > 1" class="item-detail-quantity">数量：{{ selectedItem.数量 || selectedItem.quantity }}</text>

					<!-- 装备属性显示 -->
					<view v-if="getItemEffects(selectedItem).length > 0" class="item-effects">
						<text class="effects-title">属性加成：</text>
						<view class="effects-list">
							<text
								v-for="effect in getItemEffects(selectedItem)"
								:key="effect.name"
								class="effect-item"
							>
								{{ effect.name }}：+{{ effect.value }}
							</text>
						</view>
					</view>

					<text v-if="selectedItem.description || selectedItem.描述" class="item-detail-desc">{{ selectedItem.description || selectedItem.描述 }}</text>
				</view>

				<view class="item-detail-actions">
					<button 
						v-if="canEquip(selectedItem)" 
						class="detail-action-btn equip-btn" 
						@click="equipItem"
					>
						<text>⚔️ 装备</text>
					</button>
					<button 
						v-if="canUse(selectedItem)" 
						class="detail-action-btn use-btn" 
						@click="useItem"
					>
						<text>💊 使用</text>
					</button>
					<button class="detail-action-btn destroy-btn" @click="destroyItem">
						<text>🗑️ 销毁</text>
					</button>
					<button class="detail-action-btn cancel-btn" @click="closeItemDetail">
						<text>❌ 取消</text>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import wsManager from '../../utils/websocket.js'
import gameState from '@/utils/gameState.js'
import { gameUtils } from '../../utils/gameData.js'

export default {
	data() {
		return {
			inventoryData: [],
			inventoryCapacity: 50,
			showItemDetail: false,
			selectedItem: null,
			selectedIndex: -1,
			isLoading: false,
			// 新增：物品配置
			itemsConfig: {},
		}
	},
	
	onLoad() {
		console.log('[背包页面] onLoad 被调用');
		this.loadInventoryData();
		this.loadItemsConfig(); // 新增
	},
	
	onShow() {
		console.log('[背包页面] onShow 被调用');
		this.loadInventoryData();
	},
	
	onUnload() {
		this.cleanupEventListeners();
	},
	
	methods: {
		async loadInventoryData() {
			try {
				console.log('[背包页面] 开始加载背包数据');
				this.isLoading = true;
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'get_inventory_data',
					data: {}
				});
				
				console.log('[背包页面] 收到响应:', response);
				this.isLoading = false;
				
				if (response.type === 'inventory_data') {
					console.log('收到背包数据:', response.data);
					this.inventoryData = response.data.inventory || [];
					this.inventoryCapacity = response.data.capacity || 50;
					console.log('[背包页面] 背包数据已更新:', this.inventoryData);
				} else if (response.type === 'error') {
					console.error('WebSocket错误:', response.data);
					
					// 检查是否是背包已满的错误
					if (response.data && response.data.message && response.data.message.includes('背包已满')) {
						uni.showToast({
							title: response.data.message,
							icon: 'none',
							duration: 3000
						});
					} else {
						uni.showToast({
							title: '网络错误，请重试',
							icon: 'none'
						});
					}
				}
			} catch (error) {
				console.error('加载背包数据失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '加载失败: ' + (error.message || '未知错误'),
					icon: 'none'
				});
			}
		},
		
		handleItemClick(item, index) {
			console.log('[背包] 选中物品:', item, '索引:', index);
			// 合并itemsConfig属性，强制用 config.type（英文）覆盖 item.type
			const config = this.itemsConfig[item.id] || {};
			const type = config.type || item.type || '';
			const sellable = (typeof config.sellable !== 'undefined' ? config.sellable : item.sellable) ? true : false;
			this.selectedItem = { ...item, ...config, type, sellable };
			this.selectedIndex = index;
			this.showItemDetail = true;
		},
		
		closeItemDetail() {
			this.showItemDetail = false;
			this.selectedItem = null;
			this.selectedIndex = -1;
		},
		
		getItemQualityClass(item) {
			if (!item || !item.品质) return 'quality-normal';
			switch (item.品质) {
				case 'common': return 'quality-common';
				case 'uncommon': return 'quality-uncommon';
				case 'rare': return 'quality-rare';
				case 'epic': return 'quality-epic';
				case 'legendary': return 'quality-legendary';
				default: return 'quality-normal';
			}
		},
		
		getQualityText(quality) {
			const qualityMap = {
				'common': '普通',
				'uncommon': '优秀',
				'rare': '稀有',
				'epic': '史诗',
				'legendary': '传说',
				'mythic': '神话'
			};
			return qualityMap[quality] || '普通';
		},
		
		getTypeText(type) {
			const typeMap = {
				'weapon': '武器',
				'helmet': '头盔',
				'necklace': '项链',
				'armor': '衣服',
				'cloak': '披风',
				'pants': '裤子',
				'shoes': '鞋子',
				'bracelet': '手镯',
				'ring': '戒指',
				'shield': '盾牌',
				'consumable': '消耗品',
				'medicine': '药品',
				'pill': '丹药',
				'material': '材料',
				'ore': '矿石',
				'wood': '木材',
				'herb': '草药',
				'fur': '兽皮',
				'tool': '工具',
				'pickaxe': '矿镐',
				'axe': '斧头',
				'sickle': '镰刀',
				'knife': '小刀',
				'special': '特殊',
				'quest': '任务',
				'currency': '货币'
			};
			return typeMap[type] || type;
		},
		
		canEquip(item) {
			// 只支持英文类型，且采集工具不显示装备按钮
			const types = [
				'weapon', 'helmet', 'necklace', 'armor', 'cloak', 'pants', 'shoes',
				'bracelet', 'ring', 'shield', 'medal', 'accessory'
			];
			return (item.equipable === 1 || item.equipable === true) && item.type !== 'gather_tool' || types.includes(item.type);
		},
		
		canUse(item) {
			// 支持 usable 字段
			return item.usable === 1 || item.usable === true || ['consumable', 'medicine', 'pill'].includes(item.type);
		},

		getItemEffects(item) {
			// 解析装备的属性加成，只显示有数值的属性
			const effects = [];

			// 属性映射表（英文 -> 中文）
			const attributeMap = {
				'attack': '攻击',
				'defense': '防御',
				'hp': '气血',
				'mp': '内力',
				'energy': '精力',
				'energy_regen': '精力回复',
				'crit': '暴击',
				'dodge': '闪避',
				'hit': '命中',
				'speed': '速度'
			};

			// 检查直接属性字段
			for (const [englishName, chineseName] of Object.entries(attributeMap)) {
				const value = item[englishName] || item[chineseName];
				if (value && value > 0) {
					effects.push({
						name: chineseName,
						value: value
					});
				}
			}

			// 解析effects字段（格式如："hp:5,attack:3"）
			const effectsString = item.effects;
			if (effectsString && typeof effectsString === 'string') {
				const effectPairs = effectsString.split(',');
				for (const pair of effectPairs) {
					const [attr, val] = pair.split(':');
					if (attr && val) {
						const attrName = attr.trim();
						const attrValue = parseInt(val.trim());
						const chineseName = attributeMap[attrName] || attrName;

						if (attrValue > 0) {
							// 检查是否已经添加过这个属性
							const existingEffect = effects.find(e => e.name === chineseName);
							if (existingEffect) {
								// 如果已存在，累加数值
								existingEffect.value += attrValue;
							} else {
								effects.push({
									name: chineseName,
									value: attrValue
								});
							}
						}
					}
				}
			}

			return effects;
		},
		
		async equipItem() {
			console.log('[装备] 当前selectedItem:', this.selectedItem, 'selectedIndex:', this.selectedIndex);
			if (!this.selectedItem || !this.selectedItem.unique_id) {
				console.warn('[装备] 缺少unique_id，无法发送装备请求');
				return;
			}
			try {
				this.isLoading = true;
				const slotType = this.getDefaultSlot(this.selectedItem.type);
				console.log('[装备] 发送equip_item:', {
					unique_id: this.selectedItem.unique_id,
					slot_type: slotType
				});
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'equip_item',
					data: {
						unique_id: this.selectedItem.unique_id,
						type: this.selectedItem.type,
						sellable: this.selectedItem.sellable,
						slot_type: slotType
					}
				});
				
				this.isLoading = false;
				
				if (response.type === 'equip_success') {
					uni.showToast({
						title: response.data.message || '装备成功',
						icon: 'success'
					});
					this.closeItemDetail();
					// 直接使用后端返回的更新后的背包数据
					if (response.data.inventory) {
						this.inventoryData = response.data.inventory;
					} else {
						// 如果没有返回背包数据，则重新请求
						this.loadInventoryData();
					}
				} else if (response.type === 'equip_failed') {
					uni.showToast({
						title: response.data.message || '装备失败',
						icon: 'none'
					});
					// 装备失败时也关闭弹窗
					this.closeItemDetail();
				} else if (response.type === 'error') {
					uni.showToast({
						title: response.data.message || '装备失败',
						icon: 'none'
					});
					this.closeItemDetail();
				}
			} catch (error) {
				console.error('装备失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '装备失败: ' + (error.message || '未知错误'),
					icon: 'none'
				});
				// 网络错误时也关闭弹窗
				this.closeItemDetail();
			}
		},
		
		getDefaultSlot(itemType) {
			// 支持所有新装备类型，戒指/手镯有两个槽位，优先第一个
			const slotMapping = {
				'weapon': 'main_hand',
				'helmet': 'helmet',
				'necklace': 'necklace',
				'armor': 'armor',
				'cloak': 'cloak',
				'pants': 'pants',
				'shoes': 'shoes',
				'bracelet': ['bracelet1', 'bracelet2'],
				'ring': ['ring1', 'ring2'],
				'shield': 'off_hand',        // 盾牌装备到副手
				'off_hand': 'off_hand',      // 副手装备装备到副手
				'medal': 'medal',
				'accessory': ['bracelet1', 'bracelet2'] // 饰品按手镯槽处理
			};
			const player = gameState.playerData || {};
			// 优先空槽，已满则替换第一个
			if (itemType === 'bracelet' || itemType === 'accessory') {
				if (player.equipment && !player.equipment.bracelet1) return 'bracelet1';
				if (player.equipment && !player.equipment.bracelet2) return 'bracelet2';
				return 'bracelet1';
			}
			if (itemType === 'ring') {
				if (player.equipment && !player.equipment.ring1) return 'ring1';
				if (player.equipment && !player.equipment.ring2) return 'ring2';
				return 'ring1';
			}
			const slot = slotMapping[itemType];
			if (Array.isArray(slot)) return slot[0];
			return slot || 'main_hand';
		},
		
		useItem() {
			if (!this.selectedItem || this.selectedIndex === -1) return;
			
			uni.showModal({
				title: '使用物品',
				content: `确定要使用 ${this.selectedItem.name || this.selectedItem.名称} 吗？`,
				showCancel: true,
				cancelText: '取消',
				confirmText: '使用',
				success: (res) => {
					if (res.confirm) {
						this.sendUseItem();
					}
				}
			});
		},
		
		async sendUseItem() {
			try {
				this.isLoading = true;
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'use_item',
					data: {
						unique_id: this.selectedItem.unique_id,
						type: this.selectedItem.type,
						sellable: this.selectedItem.sellable
					}
				});
				
				this.isLoading = false;
				
				if (response.type === 'use_item_success') {
					uni.showToast({
						title: response.data.message || '使用成功',
						icon: 'success'
					});
					this.closeItemDetail();
					// 直接使用后端返回的更新后的背包数据
					if (response.data.inventory) {
						this.inventoryData = response.data.inventory;
					} else {
						// 如果没有返回背包数据，则重新请求
						this.loadInventoryData();
					}
				} else if (response.type === 'use_item_failed') {
					uni.showToast({
						title: response.data.message || '使用失败',
						icon: 'none'
					});
					// 使用失败时也关闭弹窗
					this.closeItemDetail();
				} else if (response.type === 'error') {
					uni.showToast({
						title: response.data.message || '使用失败',
						icon: 'none'
					});
					this.closeItemDetail();
				}
			} catch (error) {
				console.error('使用物品失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '使用失败: ' + (error.message || '未知错误'),
					icon: 'none'
				});
				// 网络错误时也关闭弹窗
				this.closeItemDetail();
			}
		},
		
		destroyItem() {
			if (!this.selectedItem || this.selectedIndex === -1) return;
			uni.showModal({
				title: '销毁物品',
				content: `确定要销毁 ${this.selectedItem.name || this.selectedItem.名称} 吗？此操作不可撤销！`,
				showCancel: true,
				cancelText: '取消',
				confirmText: '销毁',
				success: (res) => {
					if (res.confirm) {
						this.sendDestroyItem();
					}
				}
			});
		},
		
		async sendDestroyItem() {
			try {
				this.isLoading = true;
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'destroy_item',
					data: {
						unique_id: this.selectedItem.unique_id,
						type: this.selectedItem.type,
						sellable: this.selectedItem.sellable
					}
				});
				
				this.isLoading = false;
				
				if (response.type === 'destroy_item_success') {
					uni.showToast({
						title: response.data.message || '销毁成功',
						icon: 'success'
					});
					// 强制关闭物品详情窗口
					this.showItemDetail = false;
					this.selectedItem = null;
					this.selectedIndex = -1;
					// 直接使用后端返回的更新后的背包数据
					if (response.data.inventory) {
						this.inventoryData = response.data.inventory;
					} else {
						// 如果没有返回背包数据，则重新请求
						this.loadInventoryData();
					}
				} else if (response.type === 'destroy_item_failed') {
					uni.showToast({
						title: response.data.message || '销毁失败',
						icon: 'none'
					});
					// 销毁失败时也关闭弹窗
					this.closeItemDetail();
				} else if (response.type === 'error') {
					uni.showToast({
						title: response.data.message || '销毁失败',
						icon: 'none'
					});
					this.closeItemDetail();
				}
			} catch (error) {
				console.error('销毁物品失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '销毁失败: ' + (error.message || '未知错误'),
					icon: 'none'
				});
				// 网络错误时也关闭弹窗
				this.closeItemDetail();
			}
		},
		
		async expandInventory() {
			try {
				this.isLoading = true;
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'expand_inventory',
					data: {}
				});
				
				this.isLoading = false;
				
				if (response.type === 'expand_success') {
					uni.showToast({
						title: response.data.message || '扩充背包成功',
						icon: 'success'
					});
					// 直接使用后端返回的更新后的背包数据
					if (response.data.inventory) {
						this.inventoryData = response.data.inventory;
					}
					if (response.data.capacity) {
						this.inventoryCapacity = response.data.capacity;
					}
					// 如果没有返回背包数据，则重新请求
					if (!response.data.inventory) {
						this.loadInventoryData();
					}
				} else if (response.type === 'expand_failed') {
					uni.showToast({
						title: response.data.message || '扩充背包失败',
						icon: 'none'
					});
				} else if (response.type === 'error') {
					uni.showToast({
						title: response.data.message || '扩充背包失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('扩充背包失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '扩充失败: ' + (error.message || '未知错误'),
					icon: 'none'
				});
			}
		},
		
		sortInventory() {
			// 按类型和品质排序
			this.inventoryData.sort((a, b) => {
				// 先按类型排序
				const typeOrder = ['weapon', 'shield', 'helmet', 'necklace', 'armor', 'cloak', 'pants', 'shoes', 'bracelet', 'ring', 'consumable', 'medicine', 'pill', 'material', 'ore', 'wood', 'herb', 'fur', 'tool', 'pickaxe', 'axe', 'sickle', 'knife', 'special', 'quest', 'currency'];
				const aTypeIndex = typeOrder.indexOf(a.type) || 999;
				const bTypeIndex = typeOrder.indexOf(b.type) || 999;
				if (aTypeIndex !== bTypeIndex) {
					return aTypeIndex - bTypeIndex;
				}
				// 再按品质排序
				const qualityOrder = ['mythic', 'legendary', 'epic', 'rare', 'uncommon', 'common'];
				const aQualityIndex = qualityOrder.indexOf(a.品质 || a.quality) || 999;
				const bQualityIndex = qualityOrder.indexOf(b.品质 || b.quality) || 999;
				return aQualityIndex - bQualityIndex;
			});
			
			uni.showToast({
				title: '背包已整理',
				icon: 'success'
			});
		},
		
		clearInventory() {
			// 确认清空背包
			uni.showModal({
				title: '确认清空背包',
				content: `确定要清空背包中的所有物品吗？\n此操作不可撤销！\n当前背包中有 ${this.inventoryData.length} 个物品。`,
				confirmText: '确认清空',
				confirmColor: '#F44336',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.performClearInventory();
					}
				}
			});
		},
		
		async performClearInventory() {
			try {
				this.isLoading = true;
				
				// 显示开始清空的提示
				uni.showToast({
					title: '开始清空背包...',
					icon: 'none',
					duration: 1000
				});
				
				// 逐个销毁所有物品
				const itemsToDestroy = [...this.inventoryData];
				let destroyedCount = 0;
				
				for (let i = 0; i < itemsToDestroy.length; i++) {
					const item = itemsToDestroy[i];
					try {
						// 使用gameUtils.sendMessage发送请求
						await gameUtils.sendMessage({
							type: 'destroy_item',
							data: {
								unique_id: item.unique_id || item.id,
								slot: i
							}
						});
						destroyedCount++;
						
						// 每销毁5个物品显示一次进度
						if (destroyedCount % 5 === 0 || destroyedCount === itemsToDestroy.length) {
							uni.showToast({
								title: `已清空 ${destroyedCount}/${itemsToDestroy.length} 个物品`,
								icon: 'none',
								duration: 1000
							});
						}
						
						// 添加小延迟避免请求过于频繁
						await new Promise(resolve => setTimeout(resolve, 100));
						
					} catch (error) {
						console.error('销毁物品失败:', error);
					}
				}
				
				// 等待一段时间后刷新背包数据
				setTimeout(() => {
					this.loadInventoryData();
					uni.showToast({
						title: `背包清空完成！共清空 ${destroyedCount} 个物品`,
						icon: 'success',
						duration: 2000
					});
					this.isLoading = false;
				}, 1500);
				
			} catch (error) {
				console.error('清空背包失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '清空失败: ' + (error.message || '未知错误'),
					icon: 'none'
				});
			}
		},
		
		async loadItemsConfig() {
			this.itemsConfig = await gameState.getItemsConfig();
		},
		
		goBack() {
			uni.navigateBack({
				delta: 1
			});
		}
	}
}
</script>

<style scoped>
.backpack-container {
	padding: 16rpx;
	background: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%);
	min-height: 100vh;
}

.backpack-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
	background: rgba(255,255,255,0.9);
	border-radius: 16rpx;
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.backpack-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.backpack-info {
	font-size: 26rpx;
	color: #666;
	background: rgba(0,0,0,0.05);
	padding: 8rpx 12rpx;
	border-radius: 8rpx;
}

.backpack-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));
	gap: 8rpx;
	margin-bottom: 16rpx;
	max-height: 60vh;
	overflow-y: auto;
	padding-right: 8rpx;
}

.item-slot {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8rpx 6rpx;
	background: rgba(255,255,255,0.9);
	border-radius: 8rpx;
	border: 2rpx solid #dee2e6;
	transition: all 0.3s ease;
	min-height: 80rpx;
	justify-content: center;
	position: relative;
	width: 100%;
	box-sizing: border-box;
}

.item-slot:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
	border-color: #667eea;
}

.item-slot.empty {
	background: rgba(255,255,255,0.5);
	border-style: dashed;
	border-color: #ccc;
}

.item-icon {
	font-size: 32rpx;
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	border: 2rpx solid #ddd;
	background: #fff;
	box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.item-name {
	font-size: 18rpx;
	color: #333;
	margin-top: 4rpx;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 100%;
	font-weight: 500;
	line-height: 1.2;
	word-break: break-all;
}

.item-quantity {
	position: absolute;
	top: 2rpx;
	right: 2rpx;
	background: rgba(0,0,0,0.7);
	color: white;
	font-size: 18rpx;
	padding: 1rpx 4rpx;
	border-radius: 6rpx;
	font-weight: bold;
}

.empty-text {
	font-size: 18rpx;
	color: #ccc;
	font-style: italic;
}

/* 品质样式 */
.quality-common {
	border-color: #9e9e9e;
}

.quality-uncommon {
	border-color: #4caf50;
}

.quality-rare {
	border-color: #2196f3;
}

.quality-epic {
	border-color: #9c27b0;
}

.quality-legendary {
	border-color: #ff9800;
}

.quality-mythic {
	border-color: #f44336;
}

.backpack-actions {
	display: flex;
	gap: 16rpx;
	margin-bottom: 20rpx;
}

.back-actions {
	margin-top: 20rpx;
	margin-bottom: 0;
}

.action-btn {
	flex: 1;
	height: 56rpx;
	border: none;
	border-radius: 28rpx;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);
}

.expand-btn {
	background: linear-gradient(135deg, #4CAF50, #388E3C);
}

.sort-btn {
	background: linear-gradient(135deg, #2196F3, #1976D2);
}

.clear-btn {
	background: linear-gradient(135deg, #F44336, #D32F2F);
}

.clear-btn[disabled] {
	background: linear-gradient(135deg, #BDBDBD, #9E9E9E);
	color: #757575;
	cursor: not-allowed;
	opacity: 0.6;
}

.back-btn {
	background: linear-gradient(135deg, #9E9E9E, #757575);
}

.action-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

/* 物品详情弹窗 */
.item-detail-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.item-detail-content {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin: 32rpx;
	max-width: 600rpx;
	width: 100%;
	box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.3);
}

.item-detail-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
	padding-bottom: 12rpx;
	border-bottom: 2rpx solid #eee;
}

.item-detail-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.item-detail-quality {
	font-size: 24rpx;
	color: #666;
	background: rgba(0,0,0,0.05);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.item-detail-quality-text {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.item-detail-info {
	margin-bottom: 24rpx;
}

.item-detail-info text {
	display: block;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}

.item-detail-desc {
	font-style: italic;
	color: #888;
	margin-top: 12rpx;
	padding: 12rpx;
	background: rgba(0,0,0,0.05);
	border-radius: 8rpx;
}

/* 装备属性样式 */
.item-effects {
	margin: 16rpx 0;
	padding: 12rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12rpx;
	border: 1rpx solid #dee2e6;
}

.effects-title {
	font-size: 26rpx;
	font-weight: bold;
	color: #495057;
	margin-bottom: 8rpx;
	display: block;
}

.effects-list {
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.effect-item {
	font-size: 24rpx;
	color: #28a745;
	font-weight: 500;
	padding: 4rpx 8rpx;
	background: rgba(40, 167, 69, 0.1);
	border-radius: 6rpx;
	border-left: 3rpx solid #28a745;
}

.item-detail-actions {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 12rpx;
}

.detail-action-btn {
	height: 48rpx;
	border: none;
	border-radius: 24rpx;
	color: white;
	font-size: 24rpx;
	font-weight: bold;
	transition: all 0.3s ease;
}

.equip-btn {
	background: linear-gradient(135deg, #4CAF50, #388E3C);
}

.use-btn {
	background: linear-gradient(135deg, #2196F3, #1976D2);
}

.destroy-btn {
	background: linear-gradient(135deg, #F44336, #D32F2F);
}

.cancel-btn {
	background: linear-gradient(135deg, #9E9E9E, #757575);
}

.detail-action-btn:active {
	transform: scale(0.95);
}

/* 媒体查询 - 小屏幕设备 */
@media screen and (max-width: 375px) {
	.backpack-grid {
		grid-template-columns: repeat(4, 1fr);
		gap: 6rpx;
	}
	
	.item-slot {
		min-height: 70rpx;
		padding: 6rpx 4rpx;
	}
	
	.item-icon {
		font-size: 28rpx;
		width: 40rpx;
		height: 40rpx;
	}
	
	.item-name {
		font-size: 16rpx;
	}
}

/* 媒体查询 - 中等屏幕设备 */
@media screen and (min-width: 376px) and (max-width: 750px) {
	.backpack-grid {
		grid-template-columns: repeat(5, 1fr);
	}
}

/* 媒体查询 - 大屏幕设备 */
@media screen and (min-width: 751px) {
	.backpack-grid {
		grid-template-columns: repeat(6, 1fr);
		max-height: 70vh;
	}
}
</style> 