<template>
	<view class="character-compact">
		<!-- 加载状态 -->
		<view v-if="isLoading" class="loading-overlay">
			<view class="loading-content">
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>
		<!-- 头部信息 - 更紧凑 -->
		<view class="header-section">
			<view class="avatar-section">
				<image class="avatar" src="/static/logo.png" />
			</view>
			<view class="info-section">
				<view class="name-row">
					<text class="name">{{ playerData.character_name || playerData.name || '未知' }}</text>
					<text class="gender">{{ playerData.gender || '男' }}</text>
					<text class="experience">📈 历练 {{ formatExperience(playerData.experience || 0) }}</text>
					<text class="fortune">💎 富源 {{ playerData.fortune || 1 }}</text>
				</view>
				<view class="stats-row">
					<text class="money">💰 {{ formatMoney(playerData.money || 0) }}</text>
					<text class="skill-points">🎯 武学 {{ playerData.skill_points || 0 }}</text>
					<text class="realm">⚔️ {{ playerData.realm_info?.current_realm || '初出茅庐' }}</text>
				</view>
			</view>
		</view>

		<!-- 属性网格 - 更紧凑 -->
		<view class="attributes-grid">
			<text class="section-title">📊 基础属性</text>
			<view class="attr-item">
				<text class="attr-label">气血</text>
				<text class="attr-value">{{ Math.floor(playerData.max_hp || 0) }}</text>
			</view>
			<view class="attr-item">
				<text class="attr-label">内力</text>
				<text class="attr-value">{{ Math.floor(playerData.max_mp || 0) }}</text>
			</view>
			<view class="attr-item">
				<text class="attr-label">体力</text>
				<text class="attr-value">{{ Math.floor(playerData.max_energy || 0) }}</text>
			</view>
			<view class="attr-item">
				<text class="attr-label">精力</text>
				<text class="attr-value">{{ Math.floor(playerData.max_spirit || playerData.max_energy || 0) }}</text>
			</view>
			<view class="attr-item">
				<text class="attr-label">攻击</text>
				<text class="attr-value">{{ Math.floor(playerData.attack || 0) }}</text>
			</view>
			<view class="attr-item">
				<text class="attr-label">防御</text>
				<text class="attr-value">{{ Math.floor(playerData.defense || 0) }}</text>
			</view>
			<view class="attr-item">
				<text class="attr-label">闪避</text>
				<text class="attr-value">{{ (playerData.dodge || 0).toFixed(1) }}%</text>
			</view>
			<view class="attr-item">
				<text class="attr-label">暴击</text>
				<text class="attr-value">{{ (playerData.crit || 0).toFixed(1) }}%</text>
			</view>
		</view>
		
		<!-- 增益摘要展示 -->
		<view v-if="bonusSummary.total" style="background:#e8f4ff;padding:10rpx;margin:10rpx 0;font-size:22rpx;color:#333;">
			<text>装备加成：攻击{{ Math.floor(bonusSummary.equipment.attack || 0) }}，防御{{ Math.floor(bonusSummary.equipment.defense || 0) }}，气血{{ Math.floor(bonusSummary.equipment.hp || 0) }}</text>
			<br/>
			<text>武功加成：攻击{{ Math.floor(bonusSummary.martial.attack || 0) }}，防御{{ Math.floor(bonusSummary.martial.defense || 0) }}，气血{{ Math.floor(bonusSummary.martial.hp || 0) }}</text>
			<br/>
			<text>总加成：攻击{{ Math.floor(bonusSummary.total.attack || 0) }}，防御{{ Math.floor(bonusSummary.total.defense || 0) }}，气血{{ Math.floor(bonusSummary.total.hp || 0) }}</text>
		</view>

		<!-- 天赋属性 - 紧凑展示 -->
		<view class="talent-section">
			<text class="section-title">天赋属性</text>
			<view class="talent-list" style="display: flex; flex-direction: row; justify-content: space-between; align-items: center;">
				<view class="attr-item" style="flex: 1 1 0; text-align: center;" v-for="item in talentArrNoFortune" :key="item.label" @click="showTalentDetail(item)">
					<text class="attr-label">{{ item.label }}</text>
					<text class="attr-value">{{ Math.floor(item.value || 0) }}</text>
				</view>
			</view>
		</view>

		<!-- 天赋详情弹窗 -->
		<view v-if="showTalentModal" class="talent-modal-mask" @click="closeTalentModal">
			<view class="talent-modal-content" @click.stop>
				<view class="talent-modal-header">
					<text class="talent-modal-title">{{ selectedTalent.label }}详情</text>
					<text class="talent-modal-close" @click="closeTalentModal">×</text>
				</view>
				<view class="talent-modal-body">
					<view class="talent-current">
						<text class="talent-current-label">当前{{ selectedTalent.label }}：</text>
						<text class="talent-current-value">{{ Math.floor(selectedTalent.value || 0) }}</text>
					</view>
					<view class="talent-effects" v-if="selectedTalent.label === '根骨'">
						<text class="talent-effects-title">根骨增益效果：</text>
						<view class="talent-effect-item">
							<text class="effect-label">气血加成：</text>
							<text class="effect-value">+{{ (getConstitutionHpBonus() || 0).toFixed(1) }}%</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">实际提升：</text>
							<text class="effect-value">+{{ (getConstitutionHpBonusValue() || 0).toFixed(1) }}点</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">体力恢复加成：</text>
							<text class="effect-value">+{{ Math.floor(getConstitutionBonus() || 0) }}%</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">基础根骨值：</text>
							<text class="effect-value">15点</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">每点根骨增加：</text>
							<text class="effect-value">0.5%气血，2%恢复速度</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">最大加成：</text>
							<text class="effect-value">100%</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">当前恢复速度：</text>
							<text class="effect-value">{{ Number(getCurrentEnergyRegen() || 0).toFixed(1) }}/10秒</text>
						</view>
					</view>
					<view class="talent-effects" v-else-if="selectedTalent.label === '力量'">
						<text class="talent-effects-title">力量增益效果：</text>
						<view class="talent-effect-item">
							<text class="effect-label">攻击力加成：</text>
							<text class="effect-value">+{{ (getStrengthBonus() || 0).toFixed(1) }}%</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">基础力量值：</text>
							<text class="effect-value">15点</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">每点力量增加：</text>
							<text class="effect-value">0.3%攻击力</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">实际提升：</text>
							<text class="effect-value">+{{ (getStrengthAttackBonus() || 0).toFixed(1) }}点</text>
						</view>
					</view>
					<view class="talent-effects" v-else-if="selectedTalent.label === '悟性'">
						<text class="talent-effects-title">悟性增益效果：</text>
						<view class="talent-effect-item">
							<text class="effect-label">经验获取加成：</text>
							<text class="effect-value">+{{ (getIntelligenceBonus() || 0).toFixed(1) }}%</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">基础悟性值：</text>
							<text class="effect-value">15点</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">每点悟性增加：</text>
							<text class="effect-value">0.5%经验获取</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">经验倍率：</text>
							<text class="effect-value">{{ (getIntelligenceMultiplier() || 1.0).toFixed(2) }}x</text>
						</view>
					</view>
					<view class="talent-effects" v-else-if="selectedTalent.label === '身法'">
						<text class="talent-effects-title">身法增益效果：</text>
						<view class="talent-effect-item">
							<text class="effect-label">防御力加成：</text>
							<text class="effect-value">+{{ (getAgilityDefenseBonus() || 0).toFixed(1) }}%</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">实际提升：</text>
							<text class="effect-value">+{{ (getAgilityDefenseBonusValue() || 0).toFixed(1) }}点</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">闪避加成：</text>
							<text class="effect-value">+{{ (getAgilityDodgeBonus() || 0).toFixed(1) }}%</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">基础身法值：</text>
							<text class="effect-value">15点</text>
						</view>
						<view class="talent-effect-item">
							<text class="effect-label">每点身法增加：</text>
							<text class="effect-value">0.5%防御力，0.5%闪避</text>
						</view>
					</view>
				</view>
				<view class="talent-modal-footer">
					<button class="talent-modal-btn" @click="closeTalentModal">关闭</button>
				</view>
			</view>
		</view>

		<!-- 境界进度 - 紧凑 -->
		<view class="realm-section">
			<view class="realm-header">
				<text class="realm-title">下一境界：{{ playerData.realm_info?.next_realm || '不堪一击' }}</text>
				<text class="realm-progress">
					{{ formatExperience(playerData.experience || 0) }} / {{ formatExperience(playerData.realm_info?.current_max || 0) }}
				</text>
			</view>
			<view class="progress-bar">
				<view class="progress-fill" :style="{ width: getRealmProgressPercent() + '%' }"></view>
			</view>
			<text class="realm-bonus">{{ playerData.realm_info?.next_bonus?.description || '无增益' }}</text>
		</view>

		<!-- 装备展示 - 紧凑网格 -->
		<view class="equipment-section">
			<text class="section-title">装备</text>
			<view class="equipment-grid">
				<view v-for="slot in ['main_hand', 'off_hand', 'helmet', 'armor', 'necklace', 'ring1', 'ring2', 'medal']" :key="slot" class="equip-slot" @click="handleEquipmentClick(slot)">
					<template v-if="mainEquipments[slot] && mainEquipments[slot].id">
						<text class="equip-icon">{{ mainEquipments[slot].icon || '📦' }}</text>
						<text class="equip-name">{{ mainEquipments[slot].name }}</text>
					</template>
					<template v-else>
						<text class="empty-text">{{ getSlotLabel(slot) }}</text>
					</template>
				</view>
			</view>
		</view>

		<!-- 装备详情弹窗 -->
		<view v-if="showEquipmentModal" class="equipment-modal-mask" @click="closeEquipmentModal">
			<view class="equipment-modal-content" @click.stop>
				<view class="equipment-modal-header">
					<text class="equipment-modal-title">{{ selectedEquipment.name }}</text>
					<text class="equipment-modal-close" @click="closeEquipmentModal">×</text>
				</view>
				<view class="equipment-modal-body">
					<view class="equipment-info">
						<view class="equipment-basic">
							<text class="equipment-icon">{{ selectedEquipment.icon || '📦' }}</text>
							<view class="equipment-details">
								<text class="equipment-type">类型：{{ getTypeText(selectedEquipment.type) }}</text>
								<text class="equipment-quality">品质：{{ getQualityText(selectedEquipment.quality || selectedEquipment.品质) }}</text>
								<text class="equipment-slot">槽位：{{ getSlotLabel(selectedSlot) }}</text>
							</view>
						</view>
						
						<view class="equipment-stats">
							<text class="stats-title">战斗属性</text>
							<view v-if="getEquipmentEffects(selectedEquipment).length > 0" class="equipment-effects">
								<view
									v-for="effect in getEquipmentEffects(selectedEquipment)"
									:key="effect.name"
									class="stat-item"
								>
									<text class="stat-label">{{ effect.name }}：</text>
									<text class="stat-value">+{{ effect.value }}</text>
								</view>
							</view>
							<view v-else class="no-effects">
								<text class="no-effects-text">无属性加成</text>
							</view>
						</view>
						
						<view class="equipment-description" v-if="selectedEquipment.description || selectedEquipment.描述">
							<text class="description-title">描述</text>
							<text class="description-text">{{ selectedEquipment.description || selectedEquipment.描述 }}</text>
						</view>
					</view>
				</view>
				<view class="equipment-modal-actions">
					<button class="equipment-action-btn unequip-btn" @click="unequipSelectedEquipment">
						<text>卸下装备</text>
					</button>
					<button class="equipment-action-btn close-btn" @click="closeEquipmentModal">
						<text>关闭</text>
					</button>
				</view>
			</view>
		</view>

		<!-- 操作按钮 - 紧凑 -->
		<view class="action-section">
			<button class="action-btn healing-btn" @click="handleHealing">
				<text>🏥 疗伤</text>
			</button>
			<button class="action-btn crafting-btn" @click="handleCrafting">
				<text>⚒️ 打造</text>
			</button>
		</view>
		<view class="action-section">
			<button class="action-btn breakthrough-btn" @click="handleBreakthrough" :disabled="playerData.experience < (playerData.realm_info?.current_max || 0)">
				<text>🌟 境界突破</text>
			</button>
			<button class="action-btn backpack-btn" @click="handleBackpack">
				<text>🎒 背包</text>
			</button>
		</view>
	</view>
</template>

<script>
import wsManager from '../../utils/websocket.js'
import gameState from '../../utils/gameState.js'
import { gameUtils } from '@/utils/gameData.js'

export default {
	data() {
		return {
			isLoading: true,
			loadingText: '正在加载角色数据...',
			playerData: {
				character_name: '',
				name: '',
				gender: '男',
				level: 1,
				hp: 0,
				max_hp: 0,
				mp: 0,
				max_mp: 0,
				energy: 0,
				max_energy: 0,
				spirit: 0,
				max_spirit: 0,
				experience: 0,
				money: 0,
				talent: {},
				dodge: 1,
				crit: 1,
				fortune: 1,
				equipment: {},
				inventory_capacity: 50,
				realm_info: {
					current_realm: '初出茅庐',
					next_realm: '不堪一击',
					current_min: 0,
					current_max: 5,
					progress: 0,
					experience: 0
				},
				skill_points: 0,
				current_map: null, // 新增：当前地图
			},
			inventoryData: [],
			activeTab: 'attributes',
			isExpanding: false,
			bonusSummary: {}, // 新增：增益摘要
			// 新增：物品配置
			itemsConfig: {},
			mapsConfig: {}, // 新增：地图配置
			// 新增：天赋详情弹窗
			showTalentModal: false,
			selectedTalent: {},
			// 新增：装备详情弹窗
			showEquipmentModal: false,
			selectedEquipment: {},
			selectedSlot: '',
		}
	},
	
	computed: {
		mainEquipments() {
			const eq = this.playerData.equipment || {};
			// 深拷贝，去除 Proxy 响应式影响
			try {
				return JSON.parse(JSON.stringify({
					main_hand: eq.main_hand || null,
					off_hand: eq.off_hand || null,
					armor: eq.armor || null,
					helmet: eq.helmet || null,
					necklace: eq.necklace || null,
					ring1: eq.ring1 || null,
					ring2: eq.ring2 || null,
					medal: eq.medal || null
				}));
			} catch (error) {
				console.error('装备数据解析失败:', error);
				return {
					main_hand: null,
					off_hand: null,
					armor: null,
					helmet: null,
					necklace: null,
					ring1: null,
					ring2: null,
					medal: null
				};
			}
		},
		talentArr() {
			return [
				{ label: '力量', value: this.playerData.talent?.力量 ?? 0 },
				{ label: '悟性', value: this.playerData.talent?.悟性 ?? 0 },
				{ label: '身法', value: this.playerData.talent?.身法 ?? 0 },
				{ label: '根骨', value: this.playerData.talent?.根骨 ?? 0 },
				{ label: '富源', value: this.playerData.fortune ?? 1 }
			];
		},
		talentArrNoFortune() {
			return [
				{ label: '力量', value: this.playerData.talent?.力量 ?? 0 },
				{ label: '悟性', value: this.playerData.talent?.悟性 ?? 0 },
				{ label: '身法', value: this.playerData.talent?.身法 ?? 0 },
				{ label: '根骨', value: this.playerData.talent?.根骨 ?? 0 }
			];
		},
		currentMapName() {
			if (!this.mapsConfig || !gameState.player.current_map) return '未知';
			const map = this.mapsConfig[gameState.player.current_map];
			return map ? map.name : '未知';
		},
		energyRegenDetails() {
			// 使用后端推送的体力恢复详情数据
			return this.playerData.energy_regen_details || null;
		}
	},
	
	onLoad() {
		this.setupEventListeners();
		// 延迟初始化，确保页面完全加载
		setTimeout(async () => {
			await this.loadMapsConfigSafe(); // 先加载地图配置
			this.initGameState();
		}, 100);
		this.loadItemsConfig(); // 新增
	},
	
	onShow() {
		// 检查 WebSocket 连接
		if (!wsManager.isConnected) {
			wsManager.connect().then(() => {
				// 连接成功后自动认证
				if (!wsManager.isAuthed && wsManager.autoAuthenticate) {
					wsManager.autoAuthenticate();
				}
				// 认证成功后刷新数据
				setTimeout(() => {
					if (gameState.requestAllData) gameState.requestAllData();
				}, 500);
			});
		} else if (!wsManager.isAuthed && wsManager.autoAuthenticate) {
			wsManager.autoAuthenticate();
			setTimeout(() => {
				if (gameState.requestAllData) gameState.requestAllData();
			}, 500);
		}
		// 原有 onShow 逻辑
		this.updatePlayerData();
		this.fetchBonusSummary();
		if (!gameState.getPlayer()) {
			if (wsManager.isConnected && gameState.isAuthed) {
				gameState.requestAllData();
			} else {
				this.initGameState();
			}
		}
	},
	
	onUnload() {
		this.cleanupEventListeners();
		// 移除gameState更新回调
		gameState.offUpdate(this.handleStateUpdate);
	},
	
	methods: {
			async initGameState() {
		try {
			this.isLoading = true;
			this.loadingText = '正在初始化游戏状态...';

			// 注册状态更新回调
			gameState.onUpdate(this.handleStateUpdate);

			// 初始化游戏状态
			await gameState.init();

			// 检查认证状态
			if (gameState.isAuthed) {
				// 请求所有数据
				gameState.requestAllData();
			}

			// 设置超时机制
			setTimeout(() => {
				if (this.isLoading) {
					this.isLoading = false;
					uni.showToast({
						title: '数据加载超时，请重试',
						icon: 'none'
					});
				}
			}, 10000); // 10秒超时
		} catch (error) {
			this.isLoading = false;
			console.error('初始化游戏状态失败:', error);
			uni.showToast({
				title: '初始化失败: ' + error.message,
				icon: 'none'
			});
		}
	},
		
		updatePlayerData() {
			// 从gameState获取最新数据
			const player = gameState.getPlayer();
			if (player) {
				this.playerData = { ...player };
			} else {
				// 如果没有数据，尝试重新请求
				if (gameState.isAuthed) {
					gameState.requestAllData();
				}
			}

			const inventory = gameState.getInventory();
			if (inventory && Array.isArray(inventory)) {
				this.inventoryData = [...inventory];
			} else {
				this.inventoryData = [];
			}
		},
		
		handleStateUpdate(type, gameStateInstance) {
			switch (type) {
				case 'player':
					if (gameStateInstance.player) {
						this.playerData = { ...gameStateInstance.player };
						this.isLoading = false; // 停止加载状态
						if (!this.playerData.max_mp && this.playerData.max_mp !== 0) {
							console.warn('max_mp 为空或undefined');
						}
						if (!this.playerData.max_energy && this.playerData.max_energy !== 0) {
							console.warn('max_energy 为空或undefined');
						}
						if (!this.playerData.attack && this.playerData.attack !== 0) {
							console.warn('attack 为空或undefined');
						}
						if (!this.playerData.defense && this.playerData.defense !== 0) {
							console.warn('defense 为空或undefined');
						}
						
						// 检查数据是否完整
						this.checkDataCompleteness();
					}
					break;
				case 'inventory':
					if (gameStateInstance.inventory && Array.isArray(gameStateInstance.inventory)) {
						this.inventoryData = [...gameStateInstance.inventory];
					} else {
						this.inventoryData = [];
					}
					break;
			}
		},
		
		checkDataCompleteness() {
			const requiredFields = ['max_hp', 'max_mp', 'max_energy', 'max_spirit', 'attack', 'defense', 'dodge', 'crit'];
			const missingFields = requiredFields.filter(field => {
				const value = this.playerData[field];
				return value === undefined || value === null || value === '';
			});
			
			if (missingFields.length > 0) {
				// 如果缺少数据，尝试重新请求
				setTimeout(() => {
					gameState.requestAllData();
				}, 1000);
			}
		},
		
		formatMoney(money) {
			if (money >= 1000000000000) {
				return (money / 1000000000000).toFixed(1) + '万亿';
			} else if (money >= 100000000) {
				return (money / 100000000).toFixed(1) + '亿';
			} else if (money >= 10000) {
				return (money / 10000).toFixed(1) + '万';
			} else if (money >= 1000) {
				return money.toLocaleString();
			} else {
				return money.toString();
			}
		},
		
		formatExperience(exp) {
			if (exp >= 1000000000000) {
				return (exp / 1000000000000).toFixed(1) + '万亿';
			} else if (exp >= 100000000) {
				return (exp / 100000000).toFixed(1) + '亿';
			} else if (exp >= 10000) {
				return (exp / 10000).toFixed(1) + '万';
			} else if (exp >= 1000) {
				return exp.toLocaleString();
			} else {
				return exp.toString();
			}
		},
		
		setupEventListeners() {
			this.cleanupEventListeners();
			// 用箭头函数缓存，保证this指向组件
			this._errorHandler = (err) => this.handleError(err);
			this._authSuccessHandler = (data) => this.handleAuthSuccess(data);
			this._unequipSuccessHandler = (data) => this.handleUnequipSuccess(data);
			this._unequipFailedHandler = (data) => this.handleUnequipFailed(data);
			this._equipSuccessHandler = (data) => this.handleEquipSuccess(data);
			this._equipFailedHandler = (data) => this.handleEquipFailed(data);
			this._healingSuccessHandler = (data) => this.handleHealingSuccess(data);
			this._healingFailedHandler = (data) => this.handleHealingFailed(data);
			this._expandSuccessHandler = (data) => this.handleExpandSuccess(data);
			this._expandFailedHandler = (data) => this.handleExpandFailed(data);
			this._breakthroughSuccessHandler = (data) => this.handleBreakthroughSuccess(data);
			this._breakthroughFailedHandler = (data) => this.handleBreakthroughFailed(data);
			
			// 移除直接使用wsManager.on的方式，改为在各方法中使用gameUtils.sendMessage
		},
		
		cleanupEventListeners() {
			// 不再需要移除监听器，因为我们不再使用wsManager.on
		},
		
		async fetchBonusSummary() {
			try {
				const res = await gameUtils.sendMessage({
					type: 'get_bonus_summary',
					data: {}
				});
				if (res.type === 'bonus_summary') {
					this.bonusSummary = res.data;
				}
			} catch (e) {
				console.warn('获取增益摘要失败', e);
			}
		},
		
		handleAuthSuccess(data) {
			// 认证成功后请求数据
			if (gameState.isAuthed) {
				gameState.requestAllData();
			}
		},
		
		handleError(error) {
			this.isLoading = false;
			console.error('角色页面收到错误:', error);
			// 处理后端返回的错误格式
			const errorMessage = error.message || '网络错误，请重试';
			uni.showToast({
				title: errorMessage,
				icon: 'none'
			});
		},
		
		// 处理装备槽点击
		handleEquipmentClick(slot) {
			const equipment = this.mainEquipments[slot];
			if (equipment && equipment.id) {
				// 有装备，显示详情弹窗
				this.selectedEquipment = { ...equipment };
				this.selectedSlot = slot;
				this.showEquipmentModal = true;
			} else {
				// 空槽位，显示提示
				uni.showToast({
					title: `${this.getSlotLabel(slot)}为空`,
					icon: 'none'
				});
			}
		},
		
		// 关闭装备详情弹窗
		closeEquipmentModal() {
			this.showEquipmentModal = false;
			this.selectedEquipment = {};
			this.selectedSlot = '';
		},
		
		// 卸下选中的装备
		unequipSelectedEquipment() {
			if (this.selectedSlot) {
				const slot = this.selectedSlot; // 保存槽位信息
				this.closeEquipmentModal();
				this.unequipItem(slot);
			}
		},
		
		// 获取装备类型文本
		getTypeText(type) {
			const typeMap = {
				'weapon': '武器',
				'helmet': '头盔',
				'necklace': '项链',
				'armor': '衣服',
				'cloak': '披风',
				'pants': '裤子',
				'shoes': '鞋子',
				'bracelet': '手镯',
				'ring': '戒指',
				'shield': '盾牌',
				'medal': '勋章',
				'accessory': '饰品'
			};
			return typeMap[type] || type;
		},
		
		// 获取装备品质文本
		getQualityText(quality) {
			const qualityMap = {
				'common': '普通',
				'uncommon': '优秀',
				'rare': '稀有',
				'epic': '史诗',
				'legendary': '传说',
				'mythic': '神话'
			};
			return qualityMap[quality] || '普通';
		},

		// 获取装备属性效果（与背包页面保持一致）
		getEquipmentEffects(equipment) {
			if (!equipment) return [];

			const effects = [];

			// 属性映射表（英文 -> 中文）
			const attributeMap = {
				'attack': '攻击',
				'defense': '防御',
				'hp': '气血',
				'mp': '内力',
				'energy': '精力',
				'energy_regen': '精力回复',
				'crit': '暴击',
				'dodge': '闪避',
				'hit': '命中',
				'speed': '速度'
			};

			// 检查直接属性字段
			for (const [englishName, chineseName] of Object.entries(attributeMap)) {
				const value = equipment[englishName] || equipment[chineseName];
				if (value && value > 0) {
					effects.push({
						name: chineseName,
						value: value
					});
				}
			}

			// 解析effects字段（格式如："hp:5,attack:3"）
			const effectsString = equipment.effects;
			if (effectsString && typeof effectsString === 'string') {
				const effectPairs = effectsString.split(',');
				for (const pair of effectPairs) {
					const [attr, val] = pair.split(':');
					if (attr && val) {
						const attrName = attr.trim();
						const attrValue = parseInt(val.trim());
						const chineseName = attributeMap[attrName] || attrName;

						if (attrValue > 0) {
							// 检查是否已经添加过这个属性
							const existingEffect = effects.find(e => e.name === chineseName);
							if (existingEffect) {
								// 如果已存在，累加数值
								existingEffect.value += attrValue;
							} else {
								effects.push({
									name: chineseName,
									value: attrValue
								});
							}
						}
					}
				}
			}

			return effects;
		},
		
		selectEquipment(slot) {
			const item = this.equipment[slot];
			if (item) {
				uni.showModal({
					title: item.name,
					content: `类型：${item.type}\n品质：${item.quality}\n攻击：${item.attack || 0}\n防御：${item.defense || 0}`,
					showCancel: true,
					cancelText: '取消',
					confirmText: '卸下',
					success: (res) => {
						if (res.confirm) {
							this.unequipItem(slot);
						}
					}
				});
			} else {
				uni.showToast({
					title: '该槽位为空',
					icon: 'none'
				});
			}
		},
		
		selectInventoryItem(index) {
			const item = this.inventoryData[index];
			if (item) {
				// 检查是否为装备类型
				const isEquipment = ['weapon', 'helmet', 'necklace', 'armor', 'cloak', 'pants', 'shoes', 'bracelet', 'ring'].includes(item.type);
				
				if (isEquipment) {
					// 装备类型，显示装备选项
					uni.showActionSheet({
						itemList: ['装备', '使用', '取消'],
						success: (res) => {
							if (res.tapIndex === 0) {
								this.equipItem(index);
							} else if (res.tapIndex === 1) {
								this.useItem(index);
							}
						}
					});
				} else {
					// 非装备类型，直接使用
					uni.showModal({
						title: item.name,
						content: `类型：${item.type}\n品质：${item.quality}\n数量：${item.quantity}`,
						showCancel: true,
						cancelText: '取消',
						confirmText: '使用',
						success: (res) => {
							if (res.confirm) {
								this.useItem(index);
							}
						}
					});
				}
			}
		},
		
		async unequipItem(slot) {
			try {
				this.isLoading = true;
				this.loadingText = '正在卸下装备...';
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'unequip',
					data: { slot }
				});
				
				this.isLoading = false;
				
				if (response.type === 'unequip_success') {
					uni.showToast({
						title: response.data.message || '卸下装备成功',
						icon: 'success'
					});
					// 更新玩家数据
					this.updatePlayerData();
				} else if (response.type === 'unequip_failed') {
					console.error('卸下装备失败:', response.data);
					uni.showToast({
						title: response.data.message || '卸下装备失败',
						icon: 'none'
					});
				} else if (response.type === 'error') {
					this.handleError(response.data);
				}
				
			} catch (error) {
				console.error('卸下装备失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '卸下装备失败: ' + (error.message || '未知错误'),
					icon: 'none'
				});
			}
		},
		
		async equipItem(index) {
			const item = this.inventoryData[index];
			if (!item) return;
			
			// 根据物品类型确定装备槽
			const slotMapping = {
				'weapon': ['main_hand', 'off_hand'],
				'helmet': ['helmet'],
				'necklace': ['necklace'],
				'armor': ['armor'],
				'cloak': ['cloak'],
				'pants': ['pants'],
				'shoes': ['shoes'],
				'ring': ['ring1', 'ring2'],
				'off_hand': ['off_hand'],  // 副手装备
				'shield': ['off_hand'],    // 盾牌
				'medal': ['medal']         // 勋章
			};
			
			const possibleSlots = slotMapping[item.type] || [];
			if (possibleSlots.length === 0) {
				uni.showToast({
					title: '无法装备此物品',
					icon: 'none'
				});
				return;
			}
			
			// 如果有多个可能的槽位，让用户选择
			if (possibleSlots.length > 1) {
				const slotNames = {
					'main_hand': '主手',
					'off_hand': '副手',
					'ring1': '戒指1',
					'ring2': '戒指2'
				};
				
				const slotOptions = possibleSlots.map(slot => slotNames[slot] || slot);
				uni.showActionSheet({
					itemList: slotOptions,
					success: (res) => {
						const selectedSlot = possibleSlots[res.tapIndex];
						this.doEquipItem(index, selectedSlot);
					}
				});
			} else {
				this.doEquipItem(index, possibleSlots[0]);
			}
		},
		
		async doEquipItem(index, slot) {
			try {
				this.isLoading = true;
				this.loadingText = '正在装备...';
				
				// 发送装备请求
				wsManager.sendMessage('equip_item', {
					item_index: index,
					slot_type: slot
				});
				
			} catch (error) {
				console.error('装备失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '装备失败: ' + error.message,
					icon: 'none'
				});
			}
		},
		
		useItem(index) {
			// 使用物品的逻辑
			// TODO: 发送使用物品的消息到后端
		},
		
		async handleHealing() {
			try {
				this.isLoading = true;
				this.loadingText = '正在疗伤...';
				
				// 发送疗伤请求
				wsManager.sendMessage('healing', {});
				
			} catch (error) {
				console.error('疗伤失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '疗伤失败: ' + error.message,
					icon: 'none'
				});
			}
		},
		
		handleHealingSuccess(data) {
			this.isLoading = false;
			uni.showToast({
				title: '疗伤成功',
				icon: 'success'
			});
			// 更新玩家数据
			this.updatePlayerData();
		},
		
		handleHealingFailed(data) {
			this.isLoading = false;
			console.error('疗伤失败:', data);
			uni.showToast({
				title: data.message || '疗伤失败',
				icon: 'none'
			});
		},
		
		handleUnequipSuccess(data) {
			this.isLoading = false;
			uni.showToast({
				title: data.message || '卸下装备成功',
				icon: 'success'
			});
			// 更新玩家数据
			this.updatePlayerData();
		},
		
		handleUnequipFailed(data) {
			this.isLoading = false;
			console.error('卸下装备失败:', data);
			uni.showToast({
				title: data.message || '卸下装备失败',
				icon: 'none'
			});
		},
		
		handleEquipSuccess(data) {
			this.isLoading = false;
			uni.showToast({
				title: data.message || '装备成功',
				icon: 'success'
			});
			// 更新玩家数据
			this.updatePlayerData();
		},
		
		handleEquipFailed(data) {
			this.isLoading = false;
			console.error('装备失败:', data);
			uni.showToast({
				title: data.message || '装备失败',
				icon: 'none'
			});
		},
		
		async expandInventory() {
			try {
				this.isLoading = true;
				this.loadingText = '正在扩充背包...';
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'expand_inventory',
					data: {}
				});
				
				this.isLoading = false;
				
				if (response.type === 'expand_success') {
					uni.showToast({
						title: response.data.message || '扩充背包成功',
						icon: 'success'
					});
					// 更新背包容量
					if (response.data.capacity) {
						this.inventoryCapacity = response.data.capacity;
					}
					// 更新玩家数据
					this.updatePlayerData();
				} else if (response.type === 'expand_failed') {
					console.error('扩充背包失败:', response.data);
					uni.showToast({
						title: response.data.message || '扩充背包失败',
						icon: 'none'
					});
				} else if (response.type === 'error') {
					this.handleError(response.data);
				}
				
			} catch (error) {
				console.error('扩充背包失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '扩充背包失败: ' + (error.message || '未知错误'),
					icon: 'none'
				});
			}
		},
		
		handleExpandSuccess(data) {
			this.isLoading = false;
			uni.showToast({
				title: data.message || '扩充背包成功',
				icon: 'success'
			});
			// 更新背包容量
			if (data.capacity) {
				this.inventoryCapacity = data.capacity;
			}
			// 更新玩家数据
			this.updatePlayerData();
		},
		
		handleExpandFailed(data) {
			this.isLoading = false;
			console.error('扩充背包失败:', data);
			uni.showToast({
				title: data.message || '扩充背包失败',
				icon: 'none'
			});
		},
		
		handleBreakthroughSuccess(data) {
			this.isLoading = false;
			uni.showToast({
				title: data.message || '境界突破成功！',
				icon: 'success'
			});
			// 更新玩家数据
			this.updatePlayerData();
		},
		
		handleBreakthroughFailed(data) {
			this.isLoading = false;
			console.error('境界突破失败:', data);
			uni.showToast({
				title: data.message || '境界突破失败',
				icon: 'none'
			});
		},
		
		handleCrafting() {
			// 跳转到打造页面
			uni.navigateTo({
				url: '/pages/crafting/crafting'
			});
		},
		
		handleBreakthrough() {
			// 境界突破功能
			uni.showModal({
				title: '境界突破',
				content: '是否尝试突破当前境界？需要消耗大量历练值。',
				showCancel: true,
				cancelText: '取消',
				confirmText: '突破',
				success: (res) => {
					if (res.confirm) {
						this.attemptBreakthrough();
					}
				}
			});
		},
		
		async attemptBreakthrough() {
			try {
				this.isLoading = true;
				this.loadingText = '正在突破境界...';
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'realm_breakthrough',
					data: {}
				});
				
				this.isLoading = false;
				
				if (response.type === 'breakthrough_success') {
					uni.showToast({
						title: response.data.message || '境界突破成功！',
						icon: 'success'
					});
					// 更新玩家数据
					this.updatePlayerData();
				} else if (response.type === 'breakthrough_failed') {
					console.error('境界突破失败:', response.data);
					uni.showToast({
						title: response.data.message || '境界突破失败',
						icon: 'none'
					});
				} else if (response.type === 'error') {
					this.handleError(response.data);
				}
				
			} catch (error) {
				console.error('境界突破失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '境界突破失败: ' + (error.message || '未知错误'),
					icon: 'none'
				});
			}
		},
		
		handleBackpack() {
			// 跳转到背包页面
			uni.navigateTo({
				url: '/pages/character/backpack'
			});
		},
		
		getItemQualityClass(item) {
			if (!item || !item.quality) return 'quality-normal';
			switch (item.quality) {
				case 'common': return 'quality-common';
				case 'uncommon': return 'quality-uncommon';
				case 'rare': return 'quality-rare';
				case 'epic': return 'quality-epic';
				case 'legendary': return 'quality-legendary';
				default: return 'quality-normal';
			}
		},
		
		switchTab(tab) {
			this.activeTab = tab;
		},
		
		handleItemClick(item, index) {
			// 处理物品点击的逻辑
		},
		
		getTalentLabel(key) {
			const labels = {
				'力量': '力量',
				'悟性': '悟性',
				'身法': '身法',
				'根骨': '根骨'
			};
			return labels[key] || key;
		},
		
		getSlotLabel(slot) {
			const slotMap = {
				main_hand: '主手',
				off_hand: '副手',
				armor: '衣服',
				helmet: '头盔',
				necklace: '项链',
				ring1: '戒指1',
				ring2: '戒指2',
				medal: '勋章'
			};
			return slotMap[slot] || slot;
		},
		
		getRealmProgressPercent() {
			const realmInfo = this.playerData.realm_info;
			if (!realmInfo || !realmInfo.next_min) return 0;
			
			const progress = realmInfo.progress || 0;
			const nextMin = realmInfo.next_min || 0;
			const currentMax = realmInfo.current_max || 0;
			
			// 计算当前境界内的进度
			const currentRange = nextMin - currentMax;
			if (currentRange <= 0) return 100;
			
			const currentProgress = progress - currentMax;
			const percent = Math.min(100, Math.max(0, (currentProgress / currentRange) * 100));
			return Math.round(percent);
		},
		
		async loadItemsConfig() {
			this.itemsConfig = await gameState.getItemsConfig();
		},
		// 可在需要时通过 this.itemsConfig[itemId] 获取物品详情
		
		async loadMapsConfigSafe() {
			try {
				this.mapsConfig = await gameState.getMapsConfig();
			} catch (e) {
				console.error('地图信息加载失败', e);
				uni.showToast({ title: '地图信息错误', icon: 'none' });
			}
		},
		
		// 天赋详情相关方法
		showTalentDetail(talent) {
			this.selectedTalent = talent;
			this.showTalentModal = true;
		},
		
		closeTalentModal() {
			this.showTalentModal = false;
			this.selectedTalent = {};
		},
		
		getConstitutionBonus() {
			// 根骨百分比加成依然由后端体力恢复详情返回，或为0
			return 0;
		},
		
		getConstitutionHpBonus() {
			return this.playerData?.talent_bonuses?.constitution?.hp_bonus_percentage || 0;
		},
		
		getConstitutionHpBonusValue() {
			return this.playerData?.talent_bonuses?.constitution?.hp_bonus || 0;
		},
		
		getCurrentEnergyRegen() {
			if (this.energyRegenDetails) {
				return this.energyRegenDetails.final_energy_regen.toFixed(2);
			}
			// 如果没有详情数据，手动计算
			const baseRegen = this.playerData.energy_regen_rate || 0.1;
			const constitution = this.playerData.talent?.根骨 || 15;
			const bonus = Math.min(1.0, (constitution - 15) * 0.02);
			const multiplier = 1.0 + bonus * Math.log(constitution / 15 + 1) / Math.log(2);
			return (baseRegen * multiplier).toFixed(2);
		},
		
		// 力量增益计算方法
		getStrengthBonus() {
			return this.playerData?.talent_bonuses?.strength?.bonus_percentage || 0;
		},
		
		getStrengthAttackBonus() {
			return this.playerData?.talent_bonuses?.strength?.attack_bonus || 0;
		},
		
		// 悟性增益计算方法
		getIntelligenceBonus() {
			return this.playerData?.talent_bonuses?.intelligence?.bonus_percentage || 0;
		},
		
		getIntelligenceMultiplier() {
			return this.playerData?.talent_bonuses?.intelligence?.exp_multiplier || 1.0;
		},
		
		// 身法增益计算方法
		getAgilityDefenseBonus() {
			return this.playerData?.talent_bonuses?.agility?.defense_bonus_percentage || 0;
		},
		
		getAgilityDodgeBonus() {
			return this.playerData?.talent_bonuses?.agility?.dodge_bonus_percentage || 0;
		},
		
		getAgilityDefenseBonusValue() {
			return this.playerData?.talent_bonuses?.agility?.defense_bonus || 0;
		},
		
		getAgilityDodgeBonusValue() {
			return this.playerData?.talent_bonuses?.agility?.dodge_bonus || 0;
		}
	}
}
</script>

<style scoped>
.character-compact { 
	padding: 16rpx; 
	background: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%); 
	min-height: 100vh; 
	font-size: 26rpx;
}

/* 头部区域 */
.header-section { 
	display: flex; 
	align-items: center; 
	gap: 16rpx; 
	margin-bottom: 16rpx; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.avatar-section { 
	position: relative; 
}

.avatar { 
	width: 80rpx; 
	height: 80rpx; 
	border-radius: 50%; 
	border: 3rpx solid #667eea; 
	box-shadow: 0 4rpx 8rpx rgba(102,126,234,0.3);
}

.info-section { 
	flex: 1; 
}

.name-row { 
	display: flex; 
	align-items: center;
	gap: 12rpx; 
	margin-bottom: 8rpx; 
	flex-wrap: wrap;
}

.name { 
	font-weight: bold; 
	font-size: 32rpx; 
	color: #333; 
}

.gender { 
	font-size: 22rpx; 
	color: #888; 
	background: linear-gradient(135deg, #f0f0f0, #e0e0e0); 
	padding: 4rpx 8rpx; 
	border-radius: 8rpx;
	border: 1rpx solid #ddd;
}

.experience { 
	color: #4CAF50; 
	font-weight: bold;
	font-size: 24rpx;
	background: rgba(76,175,80,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.fortune { 
	color: #9C27B0; 
	font-weight: bold;
	background: rgba(156,39,176,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.stats-row { 
	display: flex; 
	gap: 12rpx; 
	font-size: 24rpx; 
	flex-wrap: wrap;
}

.money { 
	color: #ff9800; 
	font-weight: bold;
	background: rgba(255,152,0,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.skill-points { 
	color: #9C27B0; 
	font-weight: bold;
	background: rgba(156,39,176,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.realm { 
	color: #2196F3; 
	font-weight: bold;
	background: rgba(33,150,243,0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

/* 属性网格 */
.attributes-grid { 
	display: grid; 
	grid-template-columns: repeat(4, 1fr); 
	gap: 12rpx; 
	margin: 16rpx 0; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.attributes-grid .section-title {
	grid-column: 1 / -1;
	font-size: 32rpx; 
	font-weight: bold; 
	color: #333; 
	margin-bottom: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	padding: 8rpx 12rpx;
	border-radius: 8rpx;
	text-align: center;
	justify-content: center;
}

.attr-item { 
	display: flex; 
	flex-direction: column; 
	align-items: center; 
	padding: 12rpx 8rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 12rpx;
	border: 1rpx solid #dee2e6;
	transition: all 0.3s ease;
}

.attr-item:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.attr-label { 
	font-size: 22rpx; 
	color: #666; 
	margin-bottom: 6rpx;
	font-weight: 500;
}

.attr-value { 
	font-size: 26rpx; 
	font-weight: bold; 
	color: #333;
}

/* 天赋区域 */
.talent-section { 
	margin: 16rpx 0; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title { 
	font-size: 32rpx; 
	font-weight: bold; 
	color: #333; 
	margin-bottom: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.section-title::before {
	content: "⭐";
	font-size: 32rpx;
}

.talent-list {
	display: flex;
	flex-direction: row;
	flex-wrap: nowrap;
	gap: 16rpx;
	overflow-x: auto;
}

.talent-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8rpx 20rpx;
	background: #fffbe6;
	border-radius: 16rpx;
	border: 1rpx solid #ffeaa7;
	transition: all 0.3s ease;
}

.talent-item:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.talent-label { 
	font-size: 22rpx; 
	color: #856404; 
	margin-bottom: 6rpx;
	font-weight: 500;
}

.talent-value { 
	font-size: 26rpx; 
	font-weight: bold; 
	color: #333;
}

/* 境界区域 */
.realm-section { 
	margin: 16rpx 0; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.realm-header { 
	display: flex; 
	justify-content: space-between; 
	align-items: center; 
	margin-bottom: 12rpx;
}

.realm-title { 
	font-size: 28rpx; 
	font-weight: bold; 
	color: #333;
}

.realm-progress { 
	font-size: 24rpx; 
	color: #666;
	background: rgba(0,0,0,0.05);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.progress-bar { 
	width: 100%; 
	height: 12rpx; 
	background: #e9ecef; 
	border-radius: 6rpx; 
	margin: 8rpx 0; 
	overflow: hidden;
	box-shadow: inset 0 2rpx 4rpx rgba(0,0,0,0.1);
}

.progress-fill { 
	height: 100%; 
	background: linear-gradient(90deg, #FFD700, #FFA500); 
	border-radius: 6rpx; 
	transition: width 0.3s ease;
	box-shadow: 0 2rpx 4rpx rgba(255,215,0,0.3);
}

.realm-bonus { 
	font-size: 24rpx; 
	color: #ff9800; 
	font-style: italic;
	background: rgba(255,152,0,0.1);
	padding: 8rpx;
	border-radius: 8rpx;
	border-left: 4rpx solid #ff9800;
}

/* 装备区域 */
.equipment-section { 
	margin: 16rpx 0; 
	background: rgba(255,255,255,0.9); 
	border-radius: 16rpx; 
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}

.section-title { 
	font-size: 32rpx; 
	font-weight: bold; 
	color: #333; 
	margin-bottom: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.section-title::before {
	content: "⚔️";
	font-size: 32rpx;
}

.equipment-grid { 
	display: grid; 
	grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));
	gap: 12rpx;
}

.equip-slot { 
	min-width: 0;
	box-sizing: border-box;
	display: flex; 
	flex-direction: column; 
	align-items: center; 
	padding: 12rpx 8rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 12rpx;
	border: 2rpx solid #dee2e6;
	transition: all 0.3s ease;
	min-height: 80rpx;
	justify-content: center;
}

.equip-slot:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
	border-color: #667eea;
}

.equip-icon { 
	font-size: 32rpx; 
	color: #333; 
	margin-bottom: 6rpx;
}

.equip-name { 
	font-size: 22rpx; 
	color: #333; 
	margin-top: 6rpx; 
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 100%;
	font-weight: 500;
	word-break: break-all;
}

.empty-text { 
	font-size: 22rpx; 
	color: #ccc; 
	margin-top: 6rpx;
	font-style: italic;
}

/* 操作按钮 */
.action-section { 
	display: flex; 
	gap: 16rpx; 
	margin-top: 16rpx;
}

.action-btn { 
	flex: 1; 
	height: 56rpx; 
	border: none; 
	border-radius: 28rpx; 
	color: white; 
	font-size: 28rpx; 
	font-weight: bold; 
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.healing-btn { 
	background: linear-gradient(135deg, #FF5722, #F44336);
}

.crafting-btn { 
	background: linear-gradient(135deg, #2196F3, #1976D2);
}

.breakthrough-btn { 
	background: linear-gradient(135deg, #9C27B0, #673AB7);
}

.backpack-btn { 
	background: linear-gradient(135deg, #4CAF50, #388E3C);
}

.action-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.action-btn:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 6rpx 12rpx rgba(0,0,0,0.3);
}

/* 天赋详情弹窗样式 */
.talent-modal-mask {
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.talent-modal-content {
	background: white;
	border-radius: 20rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.talent-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
}

.talent-modal-title {
	font-size: 32rpx;
	font-weight: bold;
}

.talent-modal-close {
	font-size: 40rpx;
	cursor: pointer;
	padding: 8rpx;
	border-radius: 50%;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.2);
}

.talent-modal-body {
	padding: 24rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.talent-current {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 24rpx;
	padding: 16rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 12rpx;
	border-left: 6rpx solid #667eea;
}

.talent-current-label {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
}

.talent-current-value {
	font-size: 32rpx;
	color: #667eea;
	font-weight: bold;
}

.talent-effects {
	margin-top: 16rpx;
}

.talent-effects-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.talent-effect-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
	padding: 12rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	border-left: 4rpx solid #28a745;
}

.effect-label {
	font-size: 26rpx;
	color: #666;
}

.effect-value {
	font-size: 26rpx;
	color: #28a745;
	font-weight: bold;
}

.talent-modal-footer {
	padding: 24rpx;
	border-top: 2rpx solid #eee;
	display: flex;
	justify-content: center;
}

.talent-modal-btn {
	background: linear-gradient(135deg, #667eea, #764ba2);
	color: white;
	border: none;
	border-radius: 25rpx;
	padding: 16rpx 48rpx;
	font-size: 28rpx;
	font-weight: bold;
	box-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
}

.talent-modal-btn:active {
	transform: scale(0.95);
}

/* 天赋属性点击效果 */
.attr-item {
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 8rpx;
	padding: 8rpx;
}

.attr-item:hover {
	background: rgba(102, 126, 234, 0.1);
	transform: translateY(-2rpx);
}

.attr-item:active {
	transform: scale(0.95);
}

/* 装备详情弹窗样式 */
.equipment-modal-mask {
	position: fixed;
	left: 0;
	top: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 9999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.equipment-modal-content {
	background: white;
	border-radius: 20rpx;
	width: 85%;
	max-width: 650rpx;
	max-height: 85vh;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.3);
}

.equipment-modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 24rpx;
	background: linear-gradient(135deg, #4CAF50, #388E3C);
	color: white;
}

.equipment-modal-title {
	font-size: 32rpx;
	font-weight: bold;
}

.equipment-modal-close {
	font-size: 40rpx;
	cursor: pointer;
	padding: 8rpx;
	border-radius: 50%;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: rgba(255, 255, 255, 0.2);
}

.equipment-modal-body {
	padding: 24rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.equipment-info {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.equipment-basic {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 16rpx;
	background: linear-gradient(135deg, #f8f9fa, #e9ecef);
	border-radius: 12rpx;
	border-left: 6rpx solid #4CAF50;
}

.equipment-icon {
	font-size: 48rpx;
	width: 80rpx;
	height: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: white;
	border-radius: 12rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.equipment-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.equipment-type,
.equipment-quality,
.equipment-slot {
	font-size: 26rpx;
	color: #666;
}

.equipment-stats {
	margin-top: 16rpx;
}

.stats-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 16rpx;
	display: block;
}

.stat-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12rpx;
	padding: 12rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
	border-left: 4rpx solid #2196F3;
}

.stat-label {
	font-size: 26rpx;
	color: #666;
}

.stat-value {
	font-size: 26rpx;
	color: #2196F3;
	font-weight: bold;
}

/* 装备属性效果样式 */
.equipment-effects {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.no-effects {
	padding: 16rpx;
	text-align: center;
}

.no-effects-text {
	font-size: 24rpx;
	color: #999;
	font-style: italic;
}

.equipment-description {
	margin-top: 16rpx;
}

.description-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 12rpx;
	display: block;
}

.description-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	padding: 12rpx;
	background: #f8f9fa;
	border-radius: 8rpx;
}

.equipment-modal-actions {
	padding: 24rpx;
	border-top: 2rpx solid #eee;
	display: flex;
	gap: 16rpx;
}

.equipment-action-btn {
	flex: 1;
	height: 56rpx;
	border: none;
	border-radius: 28rpx;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
	box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.2);
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
}

.unequip-btn {
	background: linear-gradient(135deg, #FF5722, #F44336);
}

.close-btn {
	background: linear-gradient(135deg, #9E9E9E, #757575);
}

.equipment-action-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 装备槽点击效果 */
.equip-slot {
	cursor: pointer;
	transition: all 0.3s ease;
	border-radius: 8rpx;
}

.equip-slot:hover {
	background: rgba(76, 175, 80, 0.1);
	transform: translateY(-2rpx);
}

.equip-slot:active {
	transform: scale(0.95);
}
</style> 