<template>
	<view class="character-container">
		<!-- 上半部分：装备和属性 -->
		<view class="top-section">
			<!-- 装备区域 -->
			<view class="equipment-section">
				<text class="section-title">装备</text>
				<view class="equipment-grid">
					<!-- 主手武器 -->
					<view class="equipment-slot" @click="selectEquipment('main_hand')">
						<view class="slot-icon" :class="{ 'has-item': equipment.main_hand }">
							<text class="slot-text">{{ equipment.main_hand ? equipment.main_hand.name : '主手' }}</text>
						</view>
					</view>
					
					<!-- 副手武器 -->
					<view class="equipment-slot" @click="selectEquipment('off_hand')">
						<view class="slot-icon" :class="{ 'has-item': equipment.off_hand }">
							<text class="slot-text">{{ equipment.off_hand ? equipment.off_hand.name : '副手' }}</text>
						</view>
					</view>
					
					<!-- 头盔 -->
					<view class="equipment-slot" @click="selectEquipment('helmet')">
						<view class="slot-icon" :class="{ 'has-item': equipment.helmet }">
							<text class="slot-text">{{ equipment.helmet ? equipment.helmet.name : '头盔' }}</text>
						</view>
					</view>
					
					<!-- 项链 -->
					<view class="equipment-slot" @click="selectEquipment('necklace')">
						<view class="slot-icon" :class="{ 'has-item': equipment.necklace }">
							<text class="slot-text">{{ equipment.necklace ? equipment.necklace.name : '项链' }}</text>
						</view>
					</view>
					
					<!-- 衣服 -->
					<view class="equipment-slot" @click="selectEquipment('armor')">
						<view class="slot-icon" :class="{ 'has-item': equipment.armor }">
							<text class="slot-text">{{ equipment.armor ? equipment.armor.name : '衣服' }}</text>
						</view>
					</view>
					
					<!-- 披风 -->
					<view class="equipment-slot" @click="selectEquipment('cloak')">
						<view class="slot-icon" :class="{ 'has-item': equipment.cloak }">
							<text class="slot-text">{{ equipment.cloak ? equipment.cloak.name : '披风' }}</text>
						</view>
					</view>
					
					<!-- 裤子 -->
					<view class="equipment-slot" @click="selectEquipment('pants')">
						<view class="slot-icon" :class="{ 'has-item': equipment.pants }">
							<text class="slot-text">{{ equipment.pants ? equipment.pants.name : '裤子' }}</text>
						</view>
					</view>
					
					<!-- 鞋子 -->
					<view class="equipment-slot" @click="selectEquipment('shoes')">
						<view class="slot-icon" :class="{ 'has-item': equipment.shoes }">
							<text class="slot-text">{{ equipment.shoes ? equipment.shoes.name : '鞋子' }}</text>
						</view>
					</view>
					
					<!-- 手镯1 -->
					<view class="equipment-slot" @click="selectEquipment('bracelet1')">
						<view class="slot-icon" :class="{ 'has-item': equipment.bracelet1 }">
							<text class="slot-text">{{ equipment.bracelet1 ? equipment.bracelet1.name : '手镯1' }}</text>
						</view>
					</view>
					
					<!-- 手镯2 -->
					<view class="equipment-slot" @click="selectEquipment('bracelet2')">
						<view class="slot-icon" :class="{ 'has-item': equipment.bracelet2 }">
							<text class="slot-text">{{ equipment.bracelet2 ? equipment.bracelet2.name : '手镯2' }}</text>
						</view>
					</view>
					
					<!-- 戒指1 -->
					<view class="equipment-slot" @click="selectEquipment('ring1')">
						<view class="slot-icon" :class="{ 'has-item': equipment.ring1 }">
							<text class="slot-text">{{ equipment.ring1 ? equipment.ring1.name : '戒指1' }}</text>
						</view>
					</view>
					
					<!-- 戒指2 -->
					<view class="equipment-slot" @click="selectEquipment('ring2')">
						<view class="slot-icon" :class="{ 'has-item': equipment.ring2 }">
							<text class="slot-text">{{ equipment.ring2 ? equipment.ring2.name : '戒指2' }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 属性区域 -->
			<view class="attributes-section">
				<text class="section-title">属性</text>
				<view class="attributes-grid">
					<!-- 基础属性 -->
					<view class="attribute-group">
						<text class="group-title">基础属性</text>
						<view class="attribute-item">
							<text class="attr-label">气血</text>
							<text class="attr-value">{{ playerData.hp || 100 }}/{{ playerData.max_hp || 100 }}</text>
						</view>
						<view class="attribute-item">
							<text class="attr-label">内力</text>
							<text class="attr-value">{{ playerData.mp || 50 }}/{{ playerData.max_mp || 50 }}</text>
						</view>
						<view class="attribute-item">
							<text class="attr-label">攻击</text>
							<text class="attr-value">{{ playerData.attack || 10 }}</text>
						</view>
						<view class="attribute-item">
							<text class="attr-label">防御</text>
							<text class="attr-value">{{ playerData.defense || 5 }}</text>
						</view>
					</view>
					
					<!-- 天赋属性 -->
					<view class="attribute-group">
						<text class="group-title">天赋属性</text>
						<view class="attribute-item">
							<text class="attr-label">力量</text>
							<text class="attr-value">{{ playerData.talent ? playerData.talent['力量'] : 25 }}</text>
						</view>
						<view class="attribute-item">
							<text class="attr-label">悟性</text>
							<text class="attr-value">{{ playerData.talent ? playerData.talent['悟性'] : 25 }}</text>
						</view>
						<view class="attribute-item">
							<text class="attr-label">身法</text>
							<text class="attr-value">{{ playerData.talent ? playerData.talent['身法'] : 25 }}</text>
						</view>
						<view class="attribute-item">
							<text class="attr-label">根骨</text>
							<text class="attr-value">{{ playerData.talent ? playerData.talent['根骨'] : 25 }}</text>
						</view>
						<view class="attribute-item">
							<text class="attr-label">富源</text>
							<text class="attr-value">{{ playerData.fortune || 1 }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 中间部分：功能按钮 -->
		<view class="middle-section">
			<view class="function-buttons">
				<button class="function-btn healing-btn" @click="handleHealing">
					<text class="btn-text">疗伤</text>
				</button>
				<button class="function-btn crafting-btn" @click="handleCrafting">
					<text class="btn-text">打造</text>
				</button>
			</view>
		</view>
		
		<!-- 下半部分：背包 -->
		<view class="bottom-section">
			<view class="inventory-section">
				<view class="inventory-header">
					<text class="section-title">背包</text>
					<view class="inventory-info">
						<text class="inventory-count">{{ inventory.length }}/{{ inventoryCapacity }}</text>
						<button class="expand-btn" @click="expandInventory" v-if="canExpand">
							<text class="expand-text">扩充</text>
						</button>
					</view>
				</view>
				<view class="inventory-grid">
					<view 
						class="inventory-slot" 
						v-for="(item, index) in inventory" 
						:key="index"
						@click="selectInventoryItem(index)"
					>
						<view class="item-icon" :class="getItemQualityClass(item)">
							<text class="item-name">{{ item.name }}</text>
							<text class="item-count" v-if="item.quantity > 1">{{ item.quantity }}</text>
						</view>
					</view>
					<!-- 空槽位 -->
					<view 
						class="inventory-slot empty-slot" 
						v-for="index in (inventoryCapacity - inventory.length)" 
						:key="'empty-' + index"
					>
						<view class="empty-icon">
							<text class="empty-text">空</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="isLoading">
			<view class="loading-content">
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>
	</view>
</template>

<script>
import wsManager from '../../utils/websocket.js'

export default {
	data() {
		return {
			isLoading: false,
			loadingText: '正在加载...',
			playerData: {},
			equipment: {
				main_hand: null,
				off_hand: null,
				helmet: null,
				necklace: null,
				armor: null,
				cloak: null,
				pants: null,
				shoes: null,
				bracelet1: null,
				bracelet2: null,
				ring1: null,
				ring2: null
			},
			inventory: [],
			inventoryCapacity: 50
		}
	},
	
	computed: {
		canExpand() {
			// 可以扩充的条件：背包容量小于100，且有足够的资源
			return this.inventoryCapacity < 100;
		}
	},
	
	onLoad() {
		this.loadPlayerData();
	},
	
	onShow() {
		this.loadPlayerData();
	},
	
	methods: {
		async loadPlayerData() {
			try {
				this.isLoading = true;
				this.loadingText = '正在加载角色数据...';
				
				// 连接WebSocket
				await wsManager.connect();
				
				// 注册事件监听器
				wsManager.on('player_data', (data) => {
					this.handlePlayerData(data);
				});
				
				wsManager.on('inventory_data', (data) => {
					this.handleInventoryData(data);
				});
				
				// 获取玩家数据
				wsManager.sendMessage('get_player_data', {});
				wsManager.sendMessage('get_inventory_data', {});
				
			} catch (error) {
				console.error('加载角色数据失败:', error);
				uni.showToast({
					title: '加载失败: ' + error.message,
					icon: 'none'
				});
			} finally {
				this.isLoading = false;
			}
		},
		
		handlePlayerData(data) {
			console.log('收到玩家数据:', data);
			this.playerData = data;
			
			// 更新装备数据
			if (data.equipment) {
				this.equipment = {
					main_hand: data.equipment.main_hand || null,
					off_hand: data.equipment.off_hand || null,
					helmet: data.equipment.helmet || null,
					necklace: data.equipment.necklace || null,
					armor: data.equipment.armor || null,
					cloak: data.equipment.cloak || null,
					pants: data.equipment.pants || null,
					shoes: data.equipment.shoes || null,
					bracelet1: data.equipment.bracelet1 || null,
					bracelet2: data.equipment.bracelet2 || null,
					ring1: data.equipment.ring1 || null,
					ring2: data.equipment.ring2 || null
				};
			}
		},
		
		handleInventoryData(data) {
			console.log('收到背包数据:', data);
			this.inventory = data.inventory || [];
			this.inventoryCapacity = data.capacity || 50;
		},
		
		selectEquipment(slot) {
			const item = this.equipment[slot];
			if (item) {
				uni.showModal({
					title: item.name,
					content: `类型：${item.type}\n品质：${item.quality}\n攻击：${item.attack || 0}\n防御：${item.defense || 0}`,
					showCancel: true,
					cancelText: '取消',
					confirmText: '卸下',
					success: (res) => {
						if (res.confirm) {
							this.unequipItem(slot);
						}
					}
				});
			} else {
				uni.showToast({
					title: '该槽位为空',
					icon: 'none'
				});
			}
		},
		
		selectInventoryItem(index) {
			const item = this.inventory[index];
			if (item) {
				// 检查是否为装备类型
				const isEquipment = ['weapon', 'helmet', 'necklace', 'armor', 'cloak', 'pants', 'shoes', 'bracelet', 'ring'].includes(item.type);
				
				if (isEquipment) {
					// 装备类型，显示装备选项
					uni.showActionSheet({
						itemList: ['装备', '使用', '取消'],
						success: (res) => {
							if (res.tapIndex === 0) {
								this.equipItem(index);
							} else if (res.tapIndex === 1) {
								this.useItem(index);
							}
						}
					});
				} else {
					// 非装备类型，直接使用
					uni.showModal({
						title: item.name,
						content: `类型：${item.type}\n品质：${item.quality}\n数量：${item.quantity}`,
						showCancel: true,
						cancelText: '取消',
						confirmText: '使用',
						success: (res) => {
							if (res.confirm) {
								this.useItem(index);
							}
						}
					});
				}
			}
		},
		
		async unequipItem(slot) {
			try {
				this.isLoading = true;
				this.loadingText = '正在卸下装备...';
				
				// 注册事件监听器
				wsManager.on('unequip_success', (data) => {
					this.handleUnequipSuccess(data);
				});
				
				wsManager.on('unequip_failed', (data) => {
					this.handleUnequipFailed(data);
				});
				
				// 发送卸下装备请求
				wsManager.sendMessage('unequip_item', {
					slot_type: slot
				});
				
			} catch (error) {
				console.error('卸下装备失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '卸下装备失败: ' + error.message,
					icon: 'none'
				});
			}
		},
		
		async equipItem(index) {
			const item = this.inventory[index];
			if (!item) return;
			
			// 根据物品类型确定装备槽
			const slotMapping = {
				'weapon': ['main_hand', 'off_hand'],
				'helmet': ['helmet'],
				'necklace': ['necklace'],
				'armor': ['armor'],
				'cloak': ['cloak'],
				'pants': ['pants'],
				'shoes': ['shoes'],
				'bracelet': ['bracelet1', 'bracelet2'],
				'ring': ['ring1', 'ring2']
			};
			
			const possibleSlots = slotMapping[item.type] || [];
			if (possibleSlots.length === 0) {
				uni.showToast({
					title: '无法装备此物品',
					icon: 'none'
				});
				return;
			}
			
			// 如果有多个可能的槽位，让用户选择
			if (possibleSlots.length > 1) {
				const slotNames = {
					'main_hand': '主手',
					'off_hand': '副手',
					'bracelet1': '手镯1',
					'bracelet2': '手镯2',
					'ring1': '戒指1',
					'ring2': '戒指2'
				};
				
				const slotOptions = possibleSlots.map(slot => slotNames[slot] || slot);
				uni.showActionSheet({
					itemList: slotOptions,
					success: (res) => {
						const selectedSlot = possibleSlots[res.tapIndex];
						this.doEquipItem(index, selectedSlot);
					}
				});
			} else {
				this.doEquipItem(index, possibleSlots[0]);
			}
		},
		
		async doEquipItem(index, slot) {
			try {
				this.isLoading = true;
				this.loadingText = '正在装备...';
				
				// 注册事件监听器
				wsManager.on('equip_success', (data) => {
					this.handleEquipSuccess(data);
				});
				
				wsManager.on('equip_failed', (data) => {
					this.handleEquipFailed(data);
				});
				
				// 发送装备请求
				wsManager.sendMessage('equip_item', {
					item_index: index,
					slot_type: slot
				});
				
			} catch (error) {
				console.error('装备失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '装备失败: ' + error.message,
					icon: 'none'
				});
			}
		},
		
		useItem(index) {
			// 使用物品的逻辑
			console.log('使用物品:', index);
			// TODO: 发送使用物品的消息到后端
		},
		
		async handleHealing() {
			try {
				this.isLoading = true;
				this.loadingText = '正在疗伤...';
				
				// 注册事件监听器
				wsManager.on('healing_success', (data) => {
					this.handleHealingSuccess(data);
				});
				
				wsManager.on('healing_failed', (data) => {
					this.handleHealingFailed(data);
				});
				
				// 发送疗伤请求
				wsManager.sendMessage('healing', {});
				
			} catch (error) {
				console.error('疗伤失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '疗伤失败: ' + error.message,
					icon: 'none'
				});
			}
		},
		
		handleHealingSuccess(data) {
			this.isLoading = false;
			console.log('疗伤成功:', data);
			uni.showToast({
				title: '疗伤成功',
				icon: 'success'
			});
			// 重新加载玩家数据
			this.loadPlayerData();
		},
		
		handleHealingFailed(data) {
			this.isLoading = false;
			console.error('疗伤失败:', data);
			uni.showToast({
				title: data.message || '疗伤失败',
				icon: 'none'
			});
		},
		
		handleUnequipSuccess(data) {
			this.isLoading = false;
			console.log('卸下装备成功:', data);
			uni.showToast({
				title: data.message || '卸下装备成功',
				icon: 'success'
			});
			// 更新装备数据
			if (data.equipment) {
				this.equipment = data.equipment;
			}
			// 重新加载数据
			this.loadPlayerData();
		},
		
		handleUnequipFailed(data) {
			this.isLoading = false;
			console.error('卸下装备失败:', data);
			uni.showToast({
				title: data.message || '卸下装备失败',
				icon: 'none'
			});
		},
		
		handleEquipSuccess(data) {
			this.isLoading = false;
			console.log('装备成功:', data);
			uni.showToast({
				title: data.message || '装备成功',
				icon: 'success'
			});
			// 更新装备数据
			if (data.equipment) {
				this.equipment = data.equipment;
			}
			// 重新加载数据
			this.loadPlayerData();
		},
		
		handleEquipFailed(data) {
			this.isLoading = false;
			console.error('装备失败:', data);
			uni.showToast({
				title: data.message || '装备失败',
				icon: 'none'
			});
		},
		
		handleExpandSuccess(data) {
			this.isLoading = false;
			console.log('扩充背包成功:', data);
			uni.showToast({
				title: data.message || '背包扩充成功',
				icon: 'success'
			});
			// 更新背包容量
			if (data.new_capacity) {
				this.inventoryCapacity = data.new_capacity;
			}
			// 重新加载数据
			this.loadPlayerData();
		},
		
		handleExpandFailed(data) {
			this.isLoading = false;
			console.error('扩充背包失败:', data);
			uni.showToast({
				title: data.message || '扩充背包失败',
				icon: 'none'
			});
		},
		
		handleCrafting() {
			// 跳转到打造页面
			uni.navigateTo({
				url: '/pages/crafting/crafting'
			});
		},
		
		expandInventory() {
			uni.showModal({
				title: '扩充背包',
				content: '花费1000银两扩充10格背包空间？',
				showCancel: true,
				cancelText: '取消',
				confirmText: '扩充',
				success: (res) => {
					if (res.confirm) {
						this.doExpandInventory();
					}
				}
			});
		},
		
		async doExpandInventory() {
			try {
				this.isLoading = true;
				this.loadingText = '正在扩充背包...';
				
				// 注册事件监听器
				wsManager.on('expand_success', (data) => {
					this.handleExpandSuccess(data);
				});
				
				wsManager.on('expand_failed', (data) => {
					this.handleExpandFailed(data);
				});
				
				// 发送扩充背包请求
				wsManager.sendMessage('expand_inventory', {});
				
			} catch (error) {
				console.error('扩充背包失败:', error);
				this.isLoading = false;
				uni.showToast({
					title: '扩充失败: ' + error.message,
					icon: 'none'
				});
			}
		},
		
		getItemQualityClass(item) {
			const quality = item.quality || 'common';
			return `quality-${quality}`;
		}
	}
}
</script>

<style scoped>
.character-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 20rpx;
	display: flex;
	flex-direction: column;
}

/* 上半部分 */
.top-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

/* 装备区域 */
.equipment-section {
	margin-bottom: 40rpx;
}

.equipment-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 15rpx;
}

.equipment-slot {
	aspect-ratio: 1;
}

.slot-icon {
	width: 100%;
	height: 100%;
	border: 2rpx solid #e0e0e0;
	border-radius: 15rpx;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.slot-icon.has-item {
	border-color: #667eea;
	background: #667eea;
}

.slot-text {
	font-size: 20rpx;
	color: #666;
	text-align: center;
}

.slot-icon.has-item .slot-text {
	color: white;
}

/* 属性区域 */
.attributes-section {
	
}

.attributes-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30rpx;
}

.attribute-group {
	
}

.group-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.attribute-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 10rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.attr-label {
	font-size: 26rpx;
	color: #666;
}

.attr-value {
	font-size: 26rpx;
	font-weight: bold;
	color: #333;
}

/* 中间部分 */
.middle-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.function-buttons {
	display: flex;
	gap: 20rpx;
}

.function-btn {
	flex: 1;
	height: 80rpx;
	border: none;
	border-radius: 40rpx;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
}

.healing-btn {
	background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.crafting-btn {
	background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
}

.btn-text {
	color: white;
}

/* 下半部分 */
.bottom-section {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 20rpx;
	padding: 30rpx;
	flex: 1;
}

.inventory-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.inventory-info {
	display: flex;
	align-items: center;
	gap: 15rpx;
}

.inventory-count {
	font-size: 26rpx;
	color: #666;
}

.expand-btn {
	background: #667eea;
	color: white;
	border: none;
	border-radius: 20rpx;
	padding: 10rpx 20rpx;
	font-size: 24rpx;
}

.expand-text {
	color: white;
}

.inventory-grid {
	display: grid;
	grid-template-columns: repeat(5, 1fr);
	gap: 10rpx;
}

.inventory-slot {
	aspect-ratio: 1;
}

.item-icon {
	width: 100%;
	height: 100%;
	border: 2rpx solid #e0e0e0;
	border-radius: 10rpx;
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	position: relative;
	transition: all 0.3s ease;
}

.item-icon.quality-common {
	border-color: #9e9e9e;
}

.item-icon.quality-uncommon {
	border-color: #4CAF50;
}

.item-icon.quality-rare {
	border-color: #2196F3;
}

.item-icon.quality-epic {
	border-color: #9C27B0;
}

.item-icon.quality-legendary {
	border-color: #FF9800;
}

.item-name {
	font-size: 20rpx;
	color: #333;
	text-align: center;
	line-height: 1.2;
}

.item-count {
	position: absolute;
	top: 5rpx;
	right: 5rpx;
	background: #ff6b6b;
	color: white;
	border-radius: 10rpx;
	padding: 2rpx 6rpx;
	font-size: 18rpx;
}

.empty-slot {
	
}

.empty-icon {
	width: 100%;
	height: 100%;
	border: 2rpx dashed #e0e0e0;
	border-radius: 10rpx;
	background: #f8f9fa;
	display: flex;
	align-items: center;
	justify-content: center;
}

.empty-text {
	font-size: 20rpx;
	color: #ccc;
}

/* 加载状态 */
.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.loading-content {
	background: white;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	text-align: center;
}

.loading-text {
	font-size: 32rpx;
	color: #333;
}
</style> 