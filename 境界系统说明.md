# 境界系统说明

## 概述
境界系统是仗剑江湖行的核心成长机制，玩家通过获得历练值来提升境界，每个境界都会带来不同的增益效果。

## 境界划分

| 境界名称 | 历练值范围 | 增益效果 |
|---------|-----------|---------|
| 初出茅庐 | 0-100万 | 无增益 |
| 不堪一击 | 100万-500万 | 武学点获取+1 |
| 初窥门径 | 500万-5500万 | 历练值获取+1 |
| 豁然贯通 | 5500万-1亿 | 生命、精力、内力分别+10% |
| 出类拔萃 | 1亿-1.5亿 | 攻击+5% |
| 无可匹敌 | 1.5亿-2亿 | 防御+5% |
| 技冠群雄 | 2亿-2.5亿 | 闪避+5% |
| 神乎其技 | 2.5亿-3亿 | 暴击+5% |
| 出神入化 | 3亿-4亿 | 力量、悟性、身份、根骨+5 |
| 一代宗师 | 4亿-6亿 | 武学点获取+1 |
| 神功盖世 | 6亿-8亿 | 历练值获取+1 |
| 物我两忘 | 8亿-10亿 | 富源+5 |
| 至尊无敌 | 10亿-15亿 | 生命、精力、内力分别+10% |
| 惊世骇俗 | 15亿-20亿 | 力量、悟性、身份、根骨+5 |
| 神鬼莫测 | 20亿-30亿 | 暴击+5% |
| 陆地神仙 | 30亿-50亿 | 生命、精力、内力分别+20% |
| 大罗金仙 | 50亿+ | 攻击、防御、闪避、暴击分别+30% |

## 增益效果详解

### 获取类增益
- **武学点获取+1**: 每次获得武学点时额外+1点
- **历练值获取+1**: 每次获得历练值时额外+1点

### 属性百分比增益
- **生命+10%/20%**: 最大生命值提升
- **精力+10%/20%**: 最大精力值提升  
- **内力+10%/20%**: 最大内力值提升
- **攻击+5%/30%**: 攻击力提升
- **防御+5%/30%**: 防御力提升
- **闪避+5%/30%**: 闪避率提升
- **暴击+5%/30%**: 暴击率提升

### 固定数值增益
- **富源+5**: 富源属性直接+5
- **力量、悟性、身份、根骨+5**: 四项天赋属性各+5

## 界面显示

### 角色页面
- 境界信息显示在银币右侧
- 显示当前境界名称
- 显示下一境界的增益效果描述

### 境界突破
- 突破境界时弹出提示框
- 显示突破前后的境界名称
- 自动奖励武学点（基础5点+境界增益）

## 技术实现

### 后端模块
- `backend/realm_system.py`: 境界系统核心模块
- 包含境界配置、增益效果、计算方法
- 支持境界增益应用到玩家数据

### 前端显示
- 角色页面集成境界信息显示
- 境界突破提示处理
- 增益效果实时更新

## 维护说明

### 修改境界配置
编辑 `backend/realm_system.py` 中的 `REALM_CONFIGS` 和 `REALM_BONUSES`：

```python
# 修改境界范围
REALM_CONFIGS = [
    (0, 1000000, "初出茅庐"),
    # 添加或修改境界配置
]

# 修改增益效果
REALM_BONUSES = {
    "境界名称": {
        "description": "增益描述",
        "effects": {
            "effect_type": value
        }
    }
}
```

### 支持的增益类型
- `skill_points_gain`: 武学点获取增益
- `experience_gain`: 历练值获取增益
- `hp_bonus`: 生命值百分比增益
- `energy_bonus`: 精力值百分比增益
- `mp_bonus`: 内力值百分比增益
- `attack_bonus`: 攻击力百分比增益
- `defense_bonus`: 防御力百分比增益
- `dodge_bonus`: 闪避百分比增益
- `crit_bonus`: 暴击百分比增益
- `talent_bonus`: 天赋属性固定增益
- `fortune_bonus`: 富源固定增益

## 测试验证

运行测试脚本验证系统功能：
```bash
python test_realm_system.py    # 基础境界系统测试
python test_realm_bonus.py     # 境界增益系统测试
``` 