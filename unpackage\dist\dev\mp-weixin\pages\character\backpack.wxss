
.backpack-container.data-v-5c7554f9 {
	padding: 16rpx;
	background: linear-gradient(135deg, #e0e7ff 0%, #f8fafc 100%);
	min-height: 100vh;
}
.backpack-header.data-v-5c7554f9 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
	background: rgba(255,255,255,0.9);
	border-radius: 16rpx;
	padding: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);
}
.backpack-title.data-v-5c7554f9 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.backpack-info.data-v-5c7554f9 {
	font-size: 26rpx;
	color: #666;
	background: rgba(0,0,0,0.05);
	padding: 8rpx 12rpx;
	border-radius: 8rpx;
}
.backpack-grid.data-v-5c7554f9 {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(120rpx, 1fr));
	gap: 8rpx;
	margin-bottom: 16rpx;
	max-height: 60vh;
	overflow-y: auto;
	padding-right: 8rpx;
}
.item-slot.data-v-5c7554f9 {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 8rpx 6rpx;
	background: rgba(255,255,255,0.9);
	border-radius: 8rpx;
	border: 2rpx solid #dee2e6;
	transition: all 0.3s ease;
	min-height: 80rpx;
	justify-content: center;
	position: relative;
	width: 100%;
	box-sizing: border-box;
}
.item-slot.data-v-5c7554f9:hover {
	transform: translateY(-2rpx);
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
	border-color: #667eea;
}
.item-slot.empty.data-v-5c7554f9 {
	background: rgba(255,255,255,0.5);
	border-style: dashed;
	border-color: #ccc;
}
.item-icon.data-v-5c7554f9 {
	font-size: 32rpx;
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 8rpx;
	border: 2rpx solid #ddd;
	background: #fff;
	box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.1);
}
.item-name.data-v-5c7554f9 {
	font-size: 18rpx;
	color: #333;
	margin-top: 4rpx;
	text-align: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	max-width: 100%;
	font-weight: 500;
	line-height: 1.2;
	word-break: break-all;
}
.item-quantity.data-v-5c7554f9 {
	position: absolute;
	top: 2rpx;
	right: 2rpx;
	background: rgba(0,0,0,0.7);
	color: white;
	font-size: 18rpx;
	padding: 1rpx 4rpx;
	border-radius: 6rpx;
	font-weight: bold;
}
.empty-text.data-v-5c7554f9 {
	font-size: 18rpx;
	color: #ccc;
	font-style: italic;
}

/* 品质样式 */
.quality-common.data-v-5c7554f9 {
	border-color: #9e9e9e;
}
.quality-uncommon.data-v-5c7554f9 {
	border-color: #4caf50;
}
.quality-rare.data-v-5c7554f9 {
	border-color: #2196f3;
}
.quality-epic.data-v-5c7554f9 {
	border-color: #9c27b0;
}
.quality-legendary.data-v-5c7554f9 {
	border-color: #ff9800;
}
.quality-mythic.data-v-5c7554f9 {
	border-color: #f44336;
}
.backpack-actions.data-v-5c7554f9 {
	display: flex;
	gap: 16rpx;
	margin-bottom: 20rpx;
}
.back-actions.data-v-5c7554f9 {
	margin-top: 20rpx;
	margin-bottom: 0;
}
.action-btn.data-v-5c7554f9 {
	flex: 1;
	height: 56rpx;
	border: none;
	border-radius: 28rpx;
	color: white;
	font-size: 28rpx;
	font-weight: bold;
	transition: all 0.3s ease;
	box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.2);
}
.expand-btn.data-v-5c7554f9 {
	background: linear-gradient(135deg, #4CAF50, #388E3C);
}
.sort-btn.data-v-5c7554f9 {
	background: linear-gradient(135deg, #2196F3, #1976D2);
}
.clear-btn.data-v-5c7554f9 {
	background: linear-gradient(135deg, #F44336, #D32F2F);
}
.clear-btn[disabled].data-v-5c7554f9 {
	background: linear-gradient(135deg, #BDBDBD, #9E9E9E);
	color: #757575;
	cursor: not-allowed;
	opacity: 0.6;
}
.back-btn.data-v-5c7554f9 {
	background: linear-gradient(135deg, #9E9E9E, #757575);
}
.action-btn.data-v-5c7554f9:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

/* 物品详情弹窗 */
.item-detail-modal.data-v-5c7554f9 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}
.item-detail-content.data-v-5c7554f9 {
	background: white;
	border-radius: 16rpx;
	padding: 24rpx;
	margin: 32rpx;
	max-width: 600rpx;
	width: 100%;
	box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.3);
}
.item-detail-header.data-v-5c7554f9 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
	padding-bottom: 12rpx;
	border-bottom: 2rpx solid #eee;
}
.item-detail-title.data-v-5c7554f9 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.item-detail-quality.data-v-5c7554f9 {
	font-size: 24rpx;
	color: #666;
	background: rgba(0,0,0,0.05);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}
.item-detail-quality-text.data-v-5c7554f9 {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}
.item-detail-info.data-v-5c7554f9 {
	margin-bottom: 24rpx;
}
.item-detail-info text.data-v-5c7554f9 {
	display: block;
	font-size: 26rpx;
	color: #666;
	margin-bottom: 8rpx;
}
.item-detail-desc.data-v-5c7554f9 {
	font-style: italic;
	color: #888;
	margin-top: 12rpx;
	padding: 12rpx;
	background: rgba(0,0,0,0.05);
	border-radius: 8rpx;
}

/* 装备属性样式 */
.item-effects.data-v-5c7554f9 {
	margin: 16rpx 0;
	padding: 12rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12rpx;
	border: 1rpx solid #dee2e6;
}
.effects-title.data-v-5c7554f9 {
	font-size: 26rpx;
	font-weight: bold;
	color: #495057;
	margin-bottom: 8rpx;
	display: block;
}
.effects-list.data-v-5c7554f9 {
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}
.effect-item.data-v-5c7554f9 {
	font-size: 24rpx;
	color: #28a745;
	font-weight: 500;
	padding: 4rpx 8rpx;
	background: rgba(40, 167, 69, 0.1);
	border-radius: 6rpx;
	border-left: 3rpx solid #28a745;
}
.item-detail-actions.data-v-5c7554f9 {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 12rpx;
}
.detail-action-btn.data-v-5c7554f9 {
	height: 48rpx;
	border: none;
	border-radius: 24rpx;
	color: white;
	font-size: 24rpx;
	font-weight: bold;
	transition: all 0.3s ease;
}
.equip-btn.data-v-5c7554f9 {
	background: linear-gradient(135deg, #4CAF50, #388E3C);
}
.use-btn.data-v-5c7554f9 {
	background: linear-gradient(135deg, #2196F3, #1976D2);
}
.destroy-btn.data-v-5c7554f9 {
	background: linear-gradient(135deg, #F44336, #D32F2F);
}
.cancel-btn.data-v-5c7554f9 {
	background: linear-gradient(135deg, #9E9E9E, #757575);
}
.detail-action-btn.data-v-5c7554f9:active {
	transform: scale(0.95);
}

/* 媒体查询 - 小屏幕设备 */
@media screen and (max-width: 375px) {
.backpack-grid.data-v-5c7554f9 {
		grid-template-columns: repeat(4, 1fr);
		gap: 6rpx;
}
.item-slot.data-v-5c7554f9 {
		min-height: 70rpx;
		padding: 6rpx 4rpx;
}
.item-icon.data-v-5c7554f9 {
		font-size: 28rpx;
		width: 40rpx;
		height: 40rpx;
}
.item-name.data-v-5c7554f9 {
		font-size: 16rpx;
}
}

/* 媒体查询 - 中等屏幕设备 */
@media screen and (min-width: 376px) and (max-width: 750px) {
.backpack-grid.data-v-5c7554f9 {
		grid-template-columns: repeat(5, 1fr);
}
}

/* 媒体查询 - 大屏幕设备 */
@media screen and (min-width: 751px) {
.backpack-grid.data-v-5c7554f9 {
		grid-template-columns: repeat(6, 1fr);
		max-height: 70vh;
}
}
