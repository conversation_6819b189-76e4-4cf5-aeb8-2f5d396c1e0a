<template>
  <view v-if="visible" class="battle-popup-mask">
    <view class="battle-popup-content">
      <view class="battle-title">战斗中</view>
      <view class="battle-roles">
        <view class="role player">
          <image class="role-avatar" :src="player.avatar || defaultPlayerAvatar" mode="aspectFill" />
          <view class="role-name">{{ player.name || '玩家' }}</view>
          <view class="role-hp-bar">
            <text>气血</text>
            <progress :percent="playerHpPercent" stroke-width="8" activeColor="#e74c3c" />
            <text>{{ Math.floor(player.hp) }}/{{ Math.floor(player.max_hp) }}</text>
          </view>
          <view class="role-mp-bar">
            <text>内力</text>
            <progress :percent="playerMpPercent" stroke-width="8" activeColor="#3498db" />
            <text>{{ player.mp }}/{{ player.max_mp }}</text>
          </view>
        </view>
        <view class="role monster">
          <image class="role-avatar" :src="monster.avatar || defaultMonsterAvatar" mode="aspectFill" />
          <view class="role-name">{{ monster.name || '怪物' }}</view>
          <view class="role-hp-bar">
            <text>气血</text>
            <progress :percent="monsterHpPercent" stroke-width="8" activeColor="#e67e22" />
            <text>{{ Math.floor(monster.hp) }}/{{ Math.floor(monster.max_hp) }}</text>
          </view>
        </view>
      </view>
      <scroll-view 
        class="battle-log" 
        scroll-y="true" 
        scroll-with-animation="true" 
        show-scrollbar="true"
        :scroll-top="scrollTop"
        :scroll-into-view="scrollIntoView"
        @scroll="onScroll"
      >
        <view v-if="battleLog && battleLog.length > 0" class="battle-log-content">
          <view v-for="(round, idx) in processedBattleLog" :key="`round-${idx}`" class="battle-round">
            <view class="round-header">
              <text class="round-number">第{{ round.round || (idx + 1) }}回合</text>
              <text class="round-time">{{ round.timestamp || round.defaultTime }}</text>
            </view>
            <view class="round-desc" v-html="round.formattedDesc"></view>
            <view v-if="round.formattedEffectDesc" class="effect-desc" v-html="round.formattedEffectDesc"></view>
          </view>
          <!-- 添加一个底部占位元素，用于滚动到底部 -->
          <view id="battle-log-bottom" class="battle-log-bottom"></view>
        </view>
        <view v-else class="battle-log-placeholder">
          <text>等待战斗开始...</text>
        </view>
      </scroll-view>
      <view class="battle-popup-buttons">
        <!-- 遇怪阶段：被动怪物 -->
        <template v-if="battleStage==='encounter' && attackMode==='passive'">
          <button class="main-btn" @click.stop="$emit('attack')">攻击</button>
          <button class="sub-btn" @click.stop="$emit('escape')">逃跑</button>
        </template>
        <!-- 遇怪阶段：主动怪物 -->
        <template v-else-if="battleStage==='encounter' && attackMode==='active'">
          <button class="sub-btn" @click.stop="$emit('escape')">逃离</button>
        </template>
        <!-- 战斗阶段：始终显示逃跑 -->
        <template v-else-if="battleStage==='battle'">
          <button class="main-btn" @click.stop="$emit('escape')">逃跑</button>
        </template>
        <!-- 战斗结束 -->
        <template v-else-if="battleStage==='end'">
          <button class="main-btn" @click.stop="$emit('close')">关闭</button>
        </template>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BattlePopup',
  props: {
    visible: Boolean,
    battleLog: Array, // [{round, desc, effect_desc, ...}]
    player: Object,
    monster: Object,
    attackMode: {
      type: String, // 'active' or 'passive'
      default: 'active'
    },
    battleStage: {
      type: String, // 'encounter' | 'battle' | 'end'
      default: 'battle'
    }
  },
  data() {
    return {
      defaultPlayerAvatar: '/static/npc/default.png',
      defaultMonsterAvatar: '/static/npc/default.png',
      debug: false, // 关闭调试模式
      scrollTop: 0, // 滚动位置
      scrollIntoView: '', // 滚动到指定元素
    }
  },
  computed: {
    playerHpPercent() {
      if (!this.player) return 100;
      return this.player.hp && this.player.max_hp ? (this.player.hp / this.player.max_hp) * 100 : 100;
    },
    playerMpPercent() {
      if (!this.player) return 100;
      return this.player.mp && this.player.max_mp ? (this.player.mp / this.player.max_mp) * 100 : 100;
    },
    monsterHpPercent() {
      if (!this.monster) return 100;
      return this.monster.hp && this.monster.max_hp ? (this.monster.hp / this.monster.max_hp) * 100 : 100;
    },

    processedBattleLog() {
      if (!this.battleLog || !Array.isArray(this.battleLog)) {
        return [];
      }

      return this.battleLog.map((round, idx) => {
        const defaultTime = this.getCurrentTime();
        const formattedDesc = this.formatBattleDesc(round.desc);
        const formattedEffectDesc = round.effect_desc && round.effect_desc !== ''
          ? this.formatBattleDesc(round.effect_desc)
          : null;

        return {
          ...round,
          defaultTime,
          formattedDesc,
          formattedEffectDesc
        };
      });
    }

  },
  watch: {
    battleLog: {
      handler(newVal) {
        // 立即滚动到底部
        this.scrollToBottom();
      },
      deep: true
    }
  },
  mounted() {
    // 组件挂载完成
  },
  methods: {
    onClose() {
      this.$emit('close')
    },
    onNext() {
      this.$emit('next')
    },
    onAttack() {
      this.$emit('attack')
    },
    onEscape() {
      this.$emit('escape')
    },
    formatBattleDesc(desc) {
      if (!desc || desc === '') {
        return '';
      }

      // 使用内联样式来实现高亮，避免scoped样式问题
      // 高亮招式名称（【招式名】格式）
      let formatted = desc.replace(/【([^】]+)】/g, '<span style="color: #e74c3c; font-weight: bold; text-shadow: 0 0 2px rgba(231, 76, 60, 0.3);">【$1】</span>');

      // 高亮伤害数字（只高亮数字部分，不包括"点伤害"文字）
      formatted = formatted.replace(/(\d+)(?=点伤害)/g, '<span style="color: #f39c12; font-weight: bold; text-shadow: 0 0 2px rgba(243, 156, 18, 0.3);">$1</span>');

      // 高亮其他数字（如攻击力、防御力等，只高亮数字部分）
      formatted = formatted.replace(/(\d+)(?=点)/g, '<span style="color: #f39c12; font-weight: bold; text-shadow: 0 0 2px rgba(243, 156, 18, 0.3);">$1</span>');

      // 高亮特殊效果（只保留真正的特殊效果，移除"难以招架"）
      formatted = formatted.replace(/(眩晕|中毒|流血)/g, '<span style="color: #9b59b6; font-weight: bold; text-shadow: 0 0 2px rgba(155, 89, 182, 0.3);">$1</span>');

      return formatted;
    },
    scrollToBottom() {
      // 使用scroll-into-view滚动到底部
      this.$nextTick(() => {
        this.scrollIntoView = 'battle-log-bottom';
        // 延迟重置，避免重复滚动
        setTimeout(() => {
          this.scrollIntoView = '';
        }, 100);
      });
    },
    onScroll(e) {
      // 记录滚动位置
      this.scrollTop = e.detail.scrollTop;
    },

    getCurrentTime() {
      const now = new Date();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      return `${hours}:${minutes}:${seconds}`;
    }
  }
}
</script>

<style scoped>
.battle-popup-mask {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 99999;
  background: rgba(0,0,0,0.32);
  display: flex;
  align-items: center;
  justify-content: center;
}
.battle-popup-content {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);
  padding: 32rpx 24rpx 24rpx 24rpx;
  min-width: 600rpx;
  max-width: 96vw;
  max-height: 92vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.battle-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #c0392b;
  margin-bottom: 12rpx; /* 减少底部间距 */
}
.battle-roles {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 32rpx;
  margin-bottom: 12rpx; /* 减少底部间距 */
}
.role {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 180rpx;
}
.role-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #eee;
  margin-bottom: 8rpx;
}
.role-name {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}
.role-hp-bar, .role-mp-bar {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  margin-bottom: 2rpx;
}
.vs-text {
  font-size: 32rpx;
  color: #888;
  font-weight: bold;
  margin: 0 12rpx;
}
.debug-info {
  background: #f0f0f0;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
  font-size: 20rpx;
  color: #666;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}
.battle-log {
  width: 100%;
  height: 800rpx; /* 继续增加高度，占满更多空间 */
  background: #f8f8f8;
  border-radius: 12rpx;
  margin: 18rpx 0 12rpx 0;
  padding: 8rpx 12rpx;
  border: 2px solid #e0e0e0;
  display: block;
}
.battle-log-bottom {
  height: 20rpx;
  width: 100%;
}
.battle-log-content {
  /* 确保内容有足够的高度 */
  padding: 4rpx;
  min-height: 100%;
}
.battle-round {
  margin-bottom: 4rpx;
  padding: 4rpx 8rpx;
  background: transparent;
  border-radius: 4rpx;
  border: none;
  word-wrap: break-word;
  word-break: break-all;
}
.round-title {
  font-size: 22rpx;
  color: #4b3fa7;
  font-weight: bold;
}
.round-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rpx;
}
.round-number {
  font-size: 20rpx;
  color: #4b3fa7;
  font-weight: bold;
}
.round-time {
  font-size: 18rpx;
  color: #888;
  font-weight: normal;
}
.round-desc {
  font-size: 24rpx;
  color: #222;
  margin: 1rpx 0 1rpx 0;
  line-height: 1.3;
  word-wrap: break-word;
  word-break: break-all;
}

.effect-desc {
  font-size: 20rpx;
  color: #e67e22;
  margin: 1rpx 0 1rpx 0;
  line-height: 1.2;
}
.battle-log-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: #999;
  font-size: 24rpx;
}
.battle-popup-buttons {
  display: flex;
  gap: 24rpx;
  margin-top: 12rpx; /* 减少顶部间距 */
}
.main-btn {
  background: linear-gradient(90deg, #e67e22, #f9d423);
  color: #fff;
  font-size: 26rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.sub-btn {
  background: linear-gradient(90deg, #888, #bbb);
  color: #fff;
  font-size: 24rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.next-btn {
  background: linear-gradient(90deg, #e67e22, #f9d423);
  color: #fff;
  font-size: 26rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.close-btn {
  background: #eee;
  color: #888;
  font-size: 24rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.next-btn[disabled], .main-btn[disabled] {
  opacity: 0.5;
}
</style> 
