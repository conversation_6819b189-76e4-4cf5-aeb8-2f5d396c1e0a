#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试管理工具修复
"""

import asyncio
from admin_tool import GameAdminTool

async def quick_test():
    """快速测试管理工具"""
    print("🧪 快速测试管理工具修复...")

    admin = GameAdminTool()

    try:
        # 测试获取玩家列表
        players = await admin.get_player_list()
        print(f"✅ 成功获取玩家列表: {len(players)} 个玩家")

        if players:
            # 测试获取第一个玩家的详细信息
            first_player = players[0]
            user_id = first_player['user_id']

            player_data = await admin.get_player_data(user_id)
            if player_data:
                print(f"✅ 成功获取玩家 {player_data['character_name']} 的详细信息")
                print(f"   等级: {player_data['level']}")
                print(f"   金钱: {player_data['money']}")

                # 测试添加物品
                print(f"\n🧪 测试添加物品...")
                success = await admin.add_item_to_player(user_id, 'sickle_1', 1)
                if success:
                    print("✅ 添加物品测试成功")
                else:
                    print("❌ 添加物品测试失败")

            else:
                print("❌ 获取玩家详细信息失败")
        else:
            print("ℹ️  数据库中没有玩家数据")

        print("✅ 管理工具修复成功！")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    asyncio.run(quick_test())
