[{"id": "sickle_1", "name": "简易镰刀", "type": "sickle", "quality": "common", "description": "最基础的镰刀，可采集1级草药，每次可采集1次。", "icon": "", "price": 200, "sell_price": 8, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 1, "equip_require": {}, "gather_type": "herbalism", "gather_times": 1}, {"id": "sickle_2", "name": "铜镰刀", "type": "sickle", "quality": "uncommon", "description": "铜制镰刀，可采集2级草药，每次可采集2次。", "icon": "", "price": 60, "sell_price": 24, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 2, "equip_require": {}, "gather_type": "herbalism", "gather_times": 2}, {"id": "sickle_3", "name": "铁镰刀", "type": "sickle", "quality": "uncommon", "description": "铁制镰刀，可采集3级草药，每次可采集3次。", "icon": "", "price": 120, "sell_price": 48, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 3, "equip_require": {}, "gather_type": "herbalism", "gather_times": 3}, {"id": "sickle_4", "name": "银镰刀", "type": "sickle", "quality": "rare", "description": "银制镰刀，可采集4级草药，每次可采集4次。", "icon": "", "price": 250, "sell_price": 100, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 4, "equip_require": {}, "gather_type": "herbalism", "gather_times": 4}, {"id": "sickle_5", "name": "金镰刀", "type": "sickle", "quality": "rare", "description": "金制镰刀，可采集5级草药，每次可采集5次。", "icon": "", "price": 500, "sell_price": 200, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 5, "equip_require": {}, "gather_type": "herbalism", "gather_times": 5}, {"id": "axe_1", "name": "简易斧头", "type": "axe", "quality": "common", "description": "最基础的斧头，可采集1级木材，每次可采集1次。", "icon": "", "price": 200, "sell_price": 12, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 1, "equip_require": {}, "gather_type": "logging", "gather_times": 1}, {"id": "axe_2", "name": "铜斧头", "type": "axe", "quality": "uncommon", "description": "铜制斧头，可采集2级木材，每次可采集2次。", "icon": "", "price": 80, "sell_price": 32, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 2, "equip_require": {}, "gather_type": "logging", "gather_times": 2}, {"id": "axe_3", "name": "铁斧头", "type": "axe", "quality": "uncommon", "description": "铁制斧头，可采集3级木材，每次可采集3次。", "icon": "", "price": 150, "sell_price": 60, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 3, "equip_require": {}, "gather_type": "logging", "gather_times": 3}, {"id": "pickaxe_1", "name": "简易矿镐", "type": "pickaxe", "quality": "common", "description": "最基础的矿镐，可采集1级矿石，每次可采集1次。", "icon": "", "price": 200, "sell_price": 16, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 1, "equip_require": {}, "gather_type": "mining", "gather_times": 1}, {"id": "pickaxe_2", "name": "铜矿镐", "type": "pickaxe", "quality": "uncommon", "description": "铜制矿镐，可采集2级矿石，每次可采集2次。", "icon": "", "price": 100, "sell_price": 40, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 2, "equip_require": {}, "gather_type": "mining", "gather_times": 2}, {"id": "knife_1", "name": "简易小刀", "type": "knife", "quality": "common", "description": "最基础的小刀，可采集1级兽皮，每次可采集1次。", "icon": "", "price": 200, "sell_price": 10, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 0, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 1, "equip_require": {}, "gather_type": "skinning", "gather_times": 1}]