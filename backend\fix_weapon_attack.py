#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复武器攻击力数据
为武器添加合理的攻击力值
"""

import json
import os

def fix_weapon_attack():
    """修复武器攻击力数据"""
    
    # 武器文件路径
    weapon_file = 'items_weapon.json'
    
    # 读取武器数据
    with open(weapon_file, 'r', encoding='utf-8') as f:
        weapons = json.load(f)
    
    print(f"开始修复 {len(weapons)} 个武器的攻击力...")
    
    # 武器类型基础攻击力配置
    weapon_type_base_attack = {
        "剑": 8,      # 剑类武器基础攻击力
        "刀": 10,     # 刀类武器基础攻击力
        "枪": 12,     # 枪类武器基础攻击力
        "棍": 6,      # 棍类武器基础攻击力
        "拳": 4,      # 拳类武器基础攻击力
        "暗器": 5,    # 暗器基础攻击力
    }
    
    # 品质加成系数
    quality_multiplier = {
        "common": 1.0,      # 普通
        "uncommon": 1.2,    # 精良
        "rare": 1.5,        # 稀有
        "epic": 2.0,        # 传说
        "legendary": 3.0    # 神品
    }
    
    fixed_count = 0
    
    for weapon in weapons:
        weapon_type = weapon.get('weapon_type', '剑')
        level = weapon.get('level', 1)
        quality = weapon.get('quality', 'common')
        
        # 计算基础攻击力
        base_attack = weapon_type_base_attack.get(weapon_type, 8)
        
        # 等级加成：每级增加20%基础攻击力
        level_bonus = 1.0 + (level - 1) * 0.2
        
        # 品质加成
        quality_bonus = quality_multiplier.get(quality, 1.0)
        
        # 最终攻击力
        final_attack = int(base_attack * level_bonus * quality_bonus)
        
        # 更新武器攻击力
        if weapon['attack'] == 0:  # 只修复攻击力为0的武器
            weapon['attack'] = final_attack
            fixed_count += 1
            
            # 同时更新effects字段
            if weapon.get('effects') == "":
                weapon['effects'] = f"attack:{final_attack}"
            elif "attack:" not in weapon.get('effects', ""):
                weapon['effects'] = weapon.get('effects', "") + f",attack:{final_attack}"
        
        print(f"武器: {weapon['name']} (等级{level}, 品质{quality}, 类型{weapon_type}) -> 攻击力: {weapon['attack']}")
    
    # 保存修复后的数据
    with open(weapon_file, 'w', encoding='utf-8') as f:
        json.dump(weapons, f, ensure_ascii=False, indent=2)
    
    print(f"修复完成！共修复了 {fixed_count} 个武器的攻击力")
    print(f"数据已保存到 {weapon_file}")

if __name__ == "__main__":
    fix_weapon_attack() 