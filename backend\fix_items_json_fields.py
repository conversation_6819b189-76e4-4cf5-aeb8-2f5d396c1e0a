import os
import glob
import json
import re

# 中英文 type/quality 映射表
TYPE_MAP = {
    '防具': 'armor', '头盔': 'helmet', '项链': 'necklace', '衣服': 'armor', '披风': 'cloak', '裤子': 'pants', '鞋子': 'shoes',
    '手镯': 'bracelet', '戒指': 'ring', '武器': 'weapon', '盾牌': 'shield',
    '消耗品': 'consumable', '药品': 'medicine', '丹药': 'pill',
    '材料': 'material', '矿石': 'ore', '木材': 'wood', '草药': 'herb', '兽皮': 'fur',
    '工具': 'tool', '矿镐': 'pickaxe', '斧头': 'axe', '镰刀': 'sickle', '小刀': 'knife',
    '特殊': 'special', '任务': 'quest', '货币': 'currency',
    'armor': 'armor', 'helmet': 'helmet', 'necklace': 'necklace', 'cloak': 'cloak', 'pants': 'pants', 'shoes': 'shoes',
    'bracelet': 'bracelet', 'ring': 'ring', 'weapon': 'weapon', 'shield': 'shield',
    'consumable': 'consumable', 'medicine': 'medicine', 'pill': 'pill',
    'material': 'material', 'ore': 'ore', 'wood': 'wood', 'herb': 'herb', 'fur': 'fur',
    'tool': 'tool', 'pickaxe': 'pickaxe', 'axe': 'axe', 'sickle': 'sickle', 'knife': 'knife',
    'special': 'special', 'quest': 'quest', 'currency': 'currency',
}
QUALITY_MAP = {
    '普通': 'common', '精品': 'uncommon', '稀有': 'rare', '史诗': 'epic', '传说': 'legendary', '仙品': 'mythic', '绝世': 'legendary',
    'common': 'common', 'uncommon': 'uncommon', 'rare': 'rare', 'epic': 'epic', 'legendary': 'legendary', 'mythic': 'mythic'
}

# book专用：正则提取标准字段名
BOOK_FIELD_PATTERN = re.compile(r'^(.*?)(_book_)?(name|desc|type|quality|price|sell_price|craftable|craft_recipe|sellable|tradable|usable|equipable|stackable|max_stack|effects|icon|attack|defense|hp|mp|energy|energy_regen|level|equip_require)$')

STANDARD_FIELDS = [
    'id', 'name', 'type', 'quality', 'description', 'icon', 'price', 'sell_price', 'craftable', 'craft_recipe',
    'sellable', 'tradable', 'usable', 'equipable', 'stackable', 'max_stack', 'effects',
    'attack', 'defense', 'hp', 'mp', 'energy', 'energy_regen', 'level', 'equip_require'
]

DEFAULTS = {
    'id': '',
    'name': '', 'type': 'special', 'quality': 'common', 'description': '', 'icon': '', 'price': 0, 'sell_price': 0,
    'craftable': 0, 'craft_recipe': '', 'sellable': 1, 'tradable': 1, 'usable': 0, 'equipable': 0, 'stackable': 1, 'max_stack': 999,
    'effects': '', 'attack': 0, 'defense': 0, 'hp': 0, 'mp': 0, 'energy': 0, 'energy_regen': 0.0, 'level': 0, 'equip_require': {}
}

def fix_fields(item):
    # 1. 普通字段映射
    if 'desc' in item and 'description' not in item:
        item['description'] = item.pop('desc')
    # 2. 自动补全所有标准字段
    for f in STANDARD_FIELDS:
        if f not in item:
            item[f] = DEFAULTS[f]
    # 3. type/quality 字段自动转英文
    t = item.get('type')
    q = item.get('quality')
    if t in TYPE_MAP:
        item['type'] = TYPE_MAP[t]
    if q in QUALITY_MAP:
        item['quality'] = QUALITY_MAP[q]
    # 4. 字段顺序统一
    result = {f: item.get(f, DEFAULTS[f]) for f in STANDARD_FIELDS}
    # 5. 保留其它字段
    for k, v in item.items():
        if k not in result:
            result[k] = v
    return result

def fix_book_fields(item):
    # 1. 统一所有 xxx_book_xxx 字段为标准字段
    new_item = {}
    for k, v in item.items():
        if k in STANDARD_FIELDS:
            new_item[k] = v
            continue
        m = BOOK_FIELD_PATTERN.match(k)
        if m:
            std = m.group(3)
            if std == 'desc':
                std = 'description'
            new_item[std] = v
        else:
            new_item[k] = v
    # 2. 自动补全所有标准字段
    for f in STANDARD_FIELDS:
        if f not in new_item:
            new_item[f] = DEFAULTS[f]
    # 3. type/quality 字段自动转英文
    t = new_item.get('type')
    q = new_item.get('quality')
    if t in TYPE_MAP:
        new_item['type'] = TYPE_MAP[t]
    if q in QUALITY_MAP:
        new_item['quality'] = QUALITY_MAP[q]
    # 4. 字段顺序统一
    result = {f: new_item.get(f, DEFAULTS[f]) for f in STANDARD_FIELDS}
    # 5. 保留其它字段
    for k, v in new_item.items():
        if k not in result:
            result[k] = v
    return result

def main():
    base_dir = os.path.dirname(__file__)
    items_files = glob.glob(os.path.join(base_dir, 'items_*.json'))
    for file_path in items_files:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        changed = False
        if isinstance(data, list):
            for i, item in enumerate(data):
                before = json.dumps(item, ensure_ascii=False, sort_keys=True)
                if os.path.basename(file_path) == 'items_book.json':
                    fixed = fix_book_fields(item)
                    if json.dumps(fixed, ensure_ascii=False, sort_keys=True) != before:
                        data[i] = fixed
                        changed = True
                else:
                    fixed = fix_fields(item)
                    if json.dumps(fixed, ensure_ascii=False, sort_keys=True) != before:
                        data[i] = fixed
                        changed = True
        elif isinstance(data, dict):
            for k, item in data.items():
                before = json.dumps(item, ensure_ascii=False, sort_keys=True)
                fixed = fix_fields(item)
                if json.dumps(fixed, ensure_ascii=False, sort_keys=True) != before:
                    data[k] = fixed
                    changed = True
        if changed:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f'已修正字段: {file_path}')
        else:
            print(f'无需修正: {file_path}')

if __name__ == '__main__':
    main() 