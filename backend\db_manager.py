import aiosqlite
import asyncio
import json
import sqlite3
import sys
import os

DB_PATH = os.path.join(os.path.dirname(__file__), 'game.db')

# 账号初始状态模板（可根据实际需要调整）
INIT_PLAYER_TEMPLATE = {
    'name': '',
    'money': 1000,
    'inventory': [],
    'equipment': {},
    'skills': {},
    'gather_skills': {},
    'level': 1,
    'experience': 0,
    'hp': 100,
    'mp': 50,
    'stamina': 100,
    'reputation': 0,
    'current_map': 'changan',
    # 可根据实际字段补充
}

async def fix_all_players_current_map():
    async with aiosqlite.connect(DB_PATH) as db:
        async with db.execute("SELECT id, data FROM players") as cursor:
            rows = await cursor.fetchall()
            for row in rows:
                player_id, data_json = row
                try:
                    data = json.loads(data_json)
                    data['current_map'] = 'changan'
                    new_data_json = json.dumps(data, ensure_ascii=False)
                    await db.execute("UPDATE players SET data = ? WHERE id = ?", (new_data_json, player_id))
                    print(f"玩家ID {player_id} 已修正 current_map 为 'changan'")
                except Exception as e:
                    print(f"玩家ID {player_id} 修正失败: {e}")
        await db.commit()
    print('所有玩家 current_map 字段已批量修正为 changan')

async def clear_all_players_event_log():
    async with aiosqlite.connect(DB_PATH) as db:
        async with db.execute("SELECT id, data FROM players") as cursor:
            rows = await cursor.fetchall()
            for row in rows:
                player_id, data_json = row
                try:
                    data = json.loads(data_json)
                    if 'event_log' in data:
                        data['event_log'] = []
                        new_data_json = json.dumps(data, ensure_ascii=False)
                        await db.execute("UPDATE players SET data = ? WHERE id = ?", (new_data_json, player_id))
                        print(f"玩家ID {player_id} 的 event_log 已清空")
                except Exception as e:
                    print(f"玩家ID {player_id} 清空 event_log 失败: {e}")
        await db.commit()
    print('所有玩家的 event_log 字段已清空')

async def fix_single_user_current_map(username, map_id):
    async with aiosqlite.connect(DB_PATH) as db:
        async with db.execute("SELECT id FROM users WHERE username = ?", (username,)) as cursor:
            row = await cursor.fetchone()
            if not row:
                print(f'未找到用户 {username}')
                return
            user_id = row[0]
        async with db.execute("SELECT id, data FROM players WHERE user_id = ?", (user_id,)) as cursor:
            row = await cursor.fetchone()
            if not row:
                print(f'未找到 {username} 玩家数据')
                return
            player_id, data_json = row
            data = json.loads(data_json)
            data['current_map'] = map_id
            new_data_json = json.dumps(data, ensure_ascii=False)
            await db.execute("UPDATE players SET data = ? WHERE id = ?", (new_data_json, player_id))
            await db.commit()
            print(f'{username} 玩家 current_map 已修正为 {map_id}')

async def clear_player_level_and_exp():
    """清理所有玩家的等级和经验字段"""
    async with aiosqlite.connect(DB_PATH) as db:
        async with db.execute("SELECT id, data FROM players") as cursor:
            rows = await cursor.fetchall()
            for row in rows:
                player_id, data_json = row
                try:
                    data = json.loads(data_json)
                    # 移除level和exp字段
                    if 'level' in data:
                        del data['level']
                    if 'exp' in data:
                        del data['exp']
                    new_data_json = json.dumps(data, ensure_ascii=False)
                    await db.execute("UPDATE players SET data = ? WHERE id = ?", (new_data_json, player_id))
                    print(f"玩家ID {player_id} 的等级和经验字段已清理")
                except Exception as e:
                    print(f"玩家ID {player_id} 清理失败: {e}")
        await db.commit()
    print('所有玩家的等级和经验字段已清理')

def list_accounts():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT id, username, character_name FROM users")
    accounts = cursor.fetchall()
    print("所有账号：")
    for acc in accounts:
        print(f"id: {acc[0]}, username: {acc[1]}, character_name: {acc[2]}")
    conn.close()

def reset_account(user_id):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    # 查询角色名
    cursor.execute("SELECT character_name FROM users WHERE id=?", (user_id,))
    row = cursor.fetchone()
    if not row:
        print(f"未找到id={user_id}的账号！")
        conn.close()
        return
    INIT_PLAYER_TEMPLATE['name'] = row[0]
    # 更新players表
    cursor.execute("UPDATE players SET data=? WHERE user_id=?", (json.dumps(INIT_PLAYER_TEMPLATE, ensure_ascii=False), user_id))
    conn.commit()
    print(f"账号 {user_id} 已恢复为初始状态！")
    conn.close()

def fix_all_status():
    import json
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute("SELECT id, data FROM players")
    rows = cursor.fetchall()
    fixed = 0
    for row in rows:
        pid, data = row
        try:
            pdata = json.loads(data)
            if 'status' not in pdata:
                pdata['status'] = 'normal'
                cursor.execute("UPDATE players SET data=? WHERE id=?", (json.dumps(pdata, ensure_ascii=False), pid))
                fixed += 1
        except Exception as e:
            print(f"修复玩家{pid}数据异常: {e}")
    conn.commit()
    conn.close()
    print(f"已修复{fixed}个账号的status字段缺失问题")

def main():
    if len(sys.argv) == 1:
        print("用法：python db_manager.py list | reset <user_id> | fix_status")
        return
    if sys.argv[1] == 'list':
        list_accounts()
    elif sys.argv[1] == 'reset' and len(sys.argv) == 3:
        reset_account(sys.argv[2])
    elif sys.argv[1] == 'fix_status':
        fix_all_status()
        return
    elif sys.argv[1] == 'fix_attack':
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        cursor.execute('SELECT id, data FROM players')
        players = cursor.fetchall()
        fixed = 0
        for player_id, data in players:
            pdata = json.loads(data)
            if 'attack' not in pdata:
                pdata['attack'] = 10
                cursor.execute('UPDATE players SET data = ? WHERE id = ?', (json.dumps(pdata, ensure_ascii=False), player_id))
                fixed += 1
        conn.commit()
        conn.close()
        print(f"已补全 attack 字段的账号数量: {fixed}")
    else:
        print("用法：python db_manager.py list | reset <user_id> | fix_status")

if __name__ == '__main__':
    main() 