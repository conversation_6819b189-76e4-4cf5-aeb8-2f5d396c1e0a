# WebSocket连接问题解决指南

## 问题描述
微信小程序WebSocket连接超时或连接数超限

## 解决方案

### 1. 关闭微信开发者工具日志回显功能

**问题原因：**
微信开发者工具的日志回显功能会使用Socket连接，与WebSocket连接冲突，导致连接超时。

**解决步骤：**
1. 打开微信开发者工具
2. 点击右上角"详情"按钮
3. 在"本地设置"中，找到"调试基础库"
4. 取消勾选"开启调试模式"或"开启日志回显"
5. 重新编译小程序

### 2. 检查网络设置

确保微信开发者工具允许本地服务：
1. 点击右上角"详情"
2. 在"本地设置"中，确保勾选"不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书"

### 3. 使用局域网IP地址

如果127.0.0.1连接失败，尝试使用局域网IP：
1. 在命令行运行 `ipconfig` 查看本机IP
2. 将WebSocket地址改为 `ws://你的IP:8080`

### 4. 检查后端服务器

确保后端服务器正确启动：
```bash
cd backend
python server.py
```

应该看到类似输出：
```
INFO - WebSocket服务器启动在 ws://0.0.0.0:8080
```

### 5. 测试连接

1. 重新编译小程序
2. 尝试登录（使用测试账号：test/123456）
3. 观察后端日志是否收到连接和消息

## 常见错误及解决方法

### 连接超时
- 关闭日志回显功能
- 检查后端是否正常运行
- 尝试使用局域网IP

### 连接数超限
- 重启微信开发者工具
- 清除小程序缓存
- 检查是否有重复连接

### 消息发送失败
- 确保连接已建立
- 检查消息格式是否正确
- 查看后端日志确认消息接收

## 调试技巧

1. **查看前端日志**：在微信开发者工具控制台查看WebSocket连接日志
2. **查看后端日志**：观察Python服务器的输出信息
3. **使用测试按钮**：在主页面使用"测试连接"按钮手动测试
4. **网络抓包**：使用开发者工具的网络面板查看WebSocket通信

## 成功标志

连接成功后，你应该看到：
- 前端：`WebSocket连接已打开`
- 后端：`新的客户端连接: 127.0.0.1:端口号`
- 登录时后端显示：`收到消息: {'type': 'login', ...}` 