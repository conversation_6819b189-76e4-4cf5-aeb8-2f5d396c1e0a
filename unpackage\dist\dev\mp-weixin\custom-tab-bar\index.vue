<template>
  <view class="custom-tab-bar">
    <view 
      v-for="(item, index) in tabList" 
      :key="index"
      class="tab-item"
      :class="{ 
        'active': selected === index,
        'adventure-tab': index === 2 
      }"
      @click="switchTab(index, item.pagePath)"
    >
      <view class="tab-content">
        <view class="tab-icon">
          <text v-if="index === 2" class="adventure-text">江湖</text>
          <text v-else class="normal-text">{{ item.text }}</text>
        </view>
        <text class="tab-text">{{ item.text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'CustomTabBar',
  data() {
    return {
      tabList: [
        { pagePath: 'pages/backpack/backpack', text: '背包' },
        { pagePath: 'pages/skills/skills', text: '武功' },
        { pagePath: 'pages/index/index', text: '江湖' },
        { pagePath: 'pages/shop/shop', text: '市场' },
        { pagePath: 'pages/guild/guild', text: '门派' }
      ]
    }
  },
  computed: {
    selected() {
      // 自动根据当前页面路径判断高亮
      const pages = getCurrentPages();
      const route = pages[pages.length - 1].route;
      return this.tabList.findIndex(tab => tab.pagePath === route);
    }
  },
  methods: {
    switchTab(index, pagePath) {
      if (this.selected === index) return;
      uni.switchTab({
        url: '/' + pagePath
      })
    }
  }
}
</script>

<style scoped>
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding: 0 20rpx;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 999;
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s ease;
}

.tab-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tab-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8rpx;
  transition: all 0.3s ease;
}

.normal-text {
  font-size: 24rpx;
  color: #fff;
  font-weight: bold;
}

.tab-text {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

/* 普通tab激活状态 */
.tab-item.active .tab-icon {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.tab-item.active .tab-text {
  color: #fff;
  font-weight: bold;
}

/* "闯"按钮特殊样式 */
.adventure-tab .tab-icon {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);
  border: 3rpx solid rgba(255, 255, 255, 0.3);
}

.adventure-tab.active .tab-icon {
  background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
  transform: scale(1.15);
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.6);
}

.adventure-tab .tab-text {
  font-size: 22rpx;
  font-weight: bold;
  color: rgba(255, 255, 255, 0.9);
}

.adventure-tab.active .tab-text {
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.adventure-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 添加底部安全区域 */
.custom-tab-bar {
  padding-bottom: env(safe-area-inset-bottom);
}
</style> 