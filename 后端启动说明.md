# 🐍 Python后端启动说明

## 系统要求

- Python 3.7+
- pip包管理器

## 安装依赖

```bash
cd backend
pip install -r requirements.txt
```

## 启动服务器

```bash
python server.py
```

服务器启动后会在 `ws://localhost:8080/ws` 监听WebSocket连接。

## 功能说明

### 已实现功能

1. **WebSocket通信**
   - 客户端连接管理
   - 消息处理
   - 自动重连机制

2. **玩家数据管理**
   - 新玩家创建
   - 数据持久化（内存存储）
   - 状态同步

3. **闯江湖系统**
   - 7种随机事件类型
   - 事件权重系统
   - 奖励发放

4. **基础游戏功能**
   - 打坐恢复
   - 疗伤系统
   - 经验获取

### 待实现功能

1. **数据库集成**
   - 玩家数据持久化
   - 物品数据库
   - 武功数据库

2. **完整游戏系统**
   - 装备系统
   - 武功系统
   - 商店系统
   - 市场系统
   - 门派系统
   - 打造系统

3. **高级功能**
   - 用户认证
   - 数据验证
   - 反作弊系统
   - 日志记录

## 消息协议

### 客户端发送消息格式

```json
{
  "type": "消息类型",
  "data": {
    "参数1": "值1",
    "参数2": "值2"
  },
  "timestamp": 1234567890
}
```

### 服务器响应消息格式

```json
{
  "type": "响应类型",
  "data": {
    "结果数据"
  }
}
```

### 主要消息类型

| 类型 | 描述 | 客户端→服务器 | 服务器→客户端 |
|------|------|---------------|---------------|
| auth | 认证 | ✓ | ✓ |
| get_player_data | 获取玩家数据 | ✓ | ✓ |
| get_inventory_data | 获取背包数据 | ✓ | ✓ |
| get_skills_data | 获取武功数据 | ✓ | ✓ |
| adventure | 闯江湖 | ✓ | ✓ |
| meditation | 打坐 | ✓ | ✓ |
| healing | 疗伤 | ✓ | ✓ |
| game_event | 游戏事件 | - | ✓ |
| error | 错误消息 | - | ✓ |
| success | 成功消息 | - | ✓ |

## 开发建议

1. **数据库选择**
   - 推荐使用SQLite或PostgreSQL
   - 考虑使用ORM框架（如SQLAlchemy）

2. **扩展性**
   - 使用配置文件管理游戏参数
   - 实现插件系统支持自定义事件

3. **性能优化**
   - 使用连接池
   - 实现数据缓存
   - 异步数据库操作

4. **安全性**
   - 实现JWT认证
   - 数据验证和清理
   - 防止SQL注入和XSS攻击

## 测试

可以使用以下工具测试WebSocket连接：

1. **浏览器控制台**
```javascript
const ws = new WebSocket('ws://localhost:8080/ws');
ws.onopen = () => console.log('连接成功');
ws.onmessage = (event) => console.log('收到消息:', JSON.parse(event.data));
ws.send(JSON.stringify({
  type: 'auth',
  data: { token: 'test_token' },
  timestamp: Date.now()
}));
```

2. **Python客户端**
```python
import asyncio
import websockets
import json

async def test_client():
    uri = "ws://localhost:8080/ws"
    async with websockets.connect(uri) as websocket:
        # 发送认证消息
        auth_message = {
            "type": "auth",
            "data": {"token": "test_token"},
            "timestamp": 1234567890
        }
        await websocket.send(json.dumps(auth_message))
        
        # 接收响应
        response = await websocket.recv()
        print(f"收到响应: {response}")

asyncio.run(test_client())
```

## 部署

1. **开发环境**
   - 直接运行 `python server.py`

2. **生产环境**
   - 使用Gunicorn + uvicorn
   - 配置Nginx反向代理
   - 使用SSL证书

```bash
# 使用Gunicorn启动
gunicorn -w 4 -k uvicorn.workers.UvicornWorker server:app
``` 