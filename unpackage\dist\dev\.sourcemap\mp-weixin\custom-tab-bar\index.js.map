{"version": 3, "file": "index.js", "sources": ["custom-tab-bar/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovempqaHgv5LuX5YmR5rGf5rmW6KGML2N1c3RvbS10YWItYmFyL2luZGV4LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"custom-tab-bar\">\r\n    <view \r\n      v-for=\"(item, index) in tabList\" \r\n      :key=\"index\"\r\n      class=\"tab-item\"\r\n      :class=\"{ \r\n        'active': selected === index,\r\n        'adventure-tab': index === 2 \r\n      }\"\r\n      @click=\"switchTab(index, item.pagePath)\"\r\n    >\r\n      <view class=\"tab-content\">\r\n        <view class=\"tab-icon\">\r\n          <text v-if=\"index === 2\" class=\"adventure-text\">江湖</text>\r\n          <text v-else class=\"normal-text\">{{ item.text }}</text>\r\n        </view>\r\n        <text class=\"tab-text\">{{ item.text }}</text>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'CustomTabBar',\r\n  data() {\r\n    return {\r\n      tabList: [\r\n        { pagePath: 'pages/backpack/backpack', text: '背包' },\r\n        { pagePath: 'pages/skills/skills', text: '武功' },\r\n        { pagePath: 'pages/index/index', text: '江湖' },\r\n        { pagePath: 'pages/shop/shop', text: '市场' },\r\n        { pagePath: 'pages/guild/guild', text: '门派' }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    selected() {\r\n      // 自动根据当前页面路径判断高亮\r\n      const pages = getCurrentPages();\r\n      const route = pages[pages.length - 1].route;\r\n      return this.tabList.findIndex(tab => tab.pagePath === route);\r\n    }\r\n  },\r\n  methods: {\r\n    switchTab(index, pagePath) {\r\n      if (this.selected === index) return;\r\n      uni.switchTab({\r\n        url: '/' + pagePath\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.custom-tab-bar {\r\n  position: fixed;\r\n  bottom: 0;\r\n  left: 0;\r\n  right: 0;\r\n  height: 120rpx;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-around;\r\n  padding: 0 20rpx;\r\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 999;\r\n}\r\n\r\n.tab-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: relative;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.tab-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.tab-icon {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  background: rgba(255, 255, 255, 0.2);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-bottom: 8rpx;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.normal-text {\r\n  font-size: 24rpx;\r\n  color: #fff;\r\n  font-weight: bold;\r\n}\r\n\r\n.tab-text {\r\n  font-size: 20rpx;\r\n  color: rgba(255, 255, 255, 0.8);\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n/* 普通tab激活状态 */\r\n.tab-item.active .tab-icon {\r\n  background: rgba(255, 255, 255, 0.3);\r\n  transform: scale(1.1);\r\n}\r\n\r\n.tab-item.active .tab-text {\r\n  color: #fff;\r\n  font-weight: bold;\r\n}\r\n\r\n/* \"闯\"按钮特殊样式 */\r\n.adventure-tab .tab-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\r\n  box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);\r\n  border: 3rpx solid rgba(255, 255, 255, 0.3);\r\n}\r\n\r\n.adventure-tab.active .tab-icon {\r\n  background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);\r\n  transform: scale(1.15);\r\n  box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.6);\r\n}\r\n\r\n.adventure-tab .tab-text {\r\n  font-size: 22rpx;\r\n  font-weight: bold;\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.adventure-tab.active .tab-text {\r\n  color: #fff;\r\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n.adventure-text {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #fff;\r\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\r\n}\r\n\r\n/* 添加底部安全区域 */\r\n.custom-tab-bar {\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n</style> ", "import Component from 'D:/zjjhx/仗剑江湖行/custom-tab-bar/index.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAwBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AACL,WAAO;AAAA,MACL,SAAS;AAAA,QACP,EAAE,UAAU,2BAA2B,MAAM,KAAM;AAAA,QACnD,EAAE,UAAU,uBAAuB,MAAM,KAAM;AAAA,QAC/C,EAAE,UAAU,qBAAqB,MAAM,KAAM;AAAA,QAC7C,EAAE,UAAU,mBAAmB,MAAM,KAAM;AAAA,QAC3C,EAAE,UAAU,qBAAqB,MAAM,KAAK;AAAA,MAC9C;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,WAAW;AAET,YAAM,QAAQ;AACd,YAAM,QAAQ,MAAM,MAAM,SAAS,CAAC,EAAE;AACtC,aAAO,KAAK,QAAQ,UAAU,SAAO,IAAI,aAAa,KAAK;AAAA,IAC7D;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,UAAU,OAAO,UAAU;AACzB,UAAI,KAAK,aAAa;AAAO;AAC7BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,KAAK,MAAM;AAAA,OACZ;AAAA,IACH;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;ACpDA,GAAG,gBAAgB,SAAS;"}