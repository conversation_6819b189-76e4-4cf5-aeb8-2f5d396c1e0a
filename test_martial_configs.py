#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试武功配置API
"""

import asyncio
import websockets
import json

async def test_martial_configs():
    uri = "ws://localhost:8080"
    
    try:
        async with websockets.connect(uri) as websocket:
            print("连接到服务器成功")
            
            # 发送获取武功配置请求
            request = {
                "type": "get_martial_configs",
                "data": {}
            }
            
            await websocket.send(json.dumps(request))
            print("已发送武功配置请求")
            
            # 接收响应
            response = await websocket.recv()
            data = json.loads(response)
            
            print(f"响应类型: {data.get('type')}")
            
            if data.get('type') == 'martial_configs':
                configs = data.get('data', {}).get('configs', [])
                print(f"获取到 {len(configs)} 个武功配置")
                
                # 显示前3个武功的详细信息
                for i, config in enumerate(configs[:3]):
                    print(f"\n武功 {i+1}:")
                    print(f"  名称: {config.get('武功名')}")
                    print(f"  类型: {config.get('类型')}")
                    print(f"  品质: {config.get('品质')}")
                    print(f"  特效: {config.get('武功特效')}")
                    
                    moves = config.get('招式列表', [])
                    print(f"  招式数量: {len(moves)}")
                    for move in moves:
                        print(f"    - {move.get('名称')} (解锁等级: {move.get('解锁等级')}, 攻击: {move.get('攻击', 'N/A')})")
                        
            elif data.get('type') == 'error':
                print(f"错误: {data.get('data', {}).get('message')}")
            else:
                print(f"未知响应类型: {data.get('type')}")
                
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_martial_configs())
