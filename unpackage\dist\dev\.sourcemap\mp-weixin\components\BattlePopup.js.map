{"version": 3, "file": "BattlePopup.js", "sources": ["components/BattlePopup.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovempqaHgv5LuX5YmR5rGf5rmW6KGML2NvbXBvbmVudHMvQmF0dGxlUG9wdXAudnVl"], "sourcesContent": ["<template>\r\n  <view v-if=\"visible\" class=\"battle-popup-mask\">\r\n    <view class=\"battle-popup-content\">\r\n      <view class=\"battle-title\">战斗中</view>\r\n      <view class=\"battle-roles\">\r\n        <view class=\"role player\">\r\n          <image class=\"role-avatar\" :src=\"player.avatar || defaultPlayerAvatar\" mode=\"aspectFill\" />\r\n          <view class=\"role-name\">{{ player.name || '玩家' }}</view>\r\n          <view class=\"role-hp-bar\">\r\n            <text>气血</text>\r\n            <progress :percent=\"playerHpPercent\" stroke-width=\"8\" activeColor=\"#e74c3c\" />\r\n            <text>{{ Math.floor(player.hp) }}/{{ Math.floor(player.max_hp) }}</text>\r\n          </view>\r\n          <view class=\"role-mp-bar\">\r\n            <text>内力</text>\r\n            <progress :percent=\"playerMpPercent\" stroke-width=\"8\" activeColor=\"#3498db\" />\r\n            <text>{{ player.mp }}/{{ player.max_mp }}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"role monster\">\r\n          <image class=\"role-avatar\" :src=\"monster.avatar || defaultMonsterAvatar\" mode=\"aspectFill\" />\r\n          <view class=\"role-name\">{{ monster.name || '怪物' }}</view>\r\n          <view class=\"role-hp-bar\">\r\n            <text>气血</text>\r\n            <progress :percent=\"monsterHpPercent\" stroke-width=\"8\" activeColor=\"#e67e22\" />\r\n            <text>{{ Math.floor(monster.hp) }}/{{ Math.floor(monster.max_hp) }}</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <scroll-view \r\n        class=\"battle-log\" \r\n        scroll-y=\"true\" \r\n        scroll-with-animation=\"true\" \r\n        show-scrollbar=\"true\"\r\n        :scroll-top=\"scrollTop\"\r\n        :scroll-into-view=\"scrollIntoView\"\r\n        @scroll=\"onScroll\"\r\n      >\r\n        <view v-if=\"battleLog && battleLog.length > 0\" class=\"battle-log-content\">\r\n          <view v-for=\"(round, idx) in battleLog\" :key=\"`round-${idx}`\" class=\"battle-round\">\r\n            <view class=\"round-header\">\r\n              <text class=\"round-time\">{{ round.timestamp || getCurrentTime() }}</text>\r\n            </view>\r\n            <view class=\"round-desc\" v-html=\"formatBattleDesc(round.desc)\"></view>\r\n            <view v-if=\"round.effect_desc && round.effect_desc !== ''\" class=\"effect-desc\" v-html=\"formatBattleDesc(round.effect_desc)\"></view>\r\n          </view>\r\n          <!-- 添加一个底部占位元素，用于滚动到底部 -->\r\n          <view id=\"battle-log-bottom\" class=\"battle-log-bottom\"></view>\r\n        </view>\r\n        <view v-else class=\"battle-log-placeholder\">\r\n          <text>等待战斗开始...</text>\r\n          <text v-if=\"debug\">调试: battleLog = {{ JSON.stringify(battleLog) }}</text>\r\n          <text v-if=\"debug\">调试: battleLog.length = {{ battleLog ? battleLog.length : 'undefined' }}</text>\r\n          <text v-if=\"debug\">调试: battleLog类型 = {{ typeof battleLog }}</text>\r\n          <text v-if=\"debug\">调试: 条件判断 = {{ battleLog && battleLog.length > 0 }}</text>\r\n          <text v-if=\"debug && battleLog && battleLog.length > 0\">调试: 第一条日志desc = {{ battleLog[0].desc }}</text>\r\n        </view>\r\n      </scroll-view>\r\n      <view class=\"battle-popup-buttons\">\r\n        <!-- 遇怪阶段：被动怪物 -->\r\n        <template v-if=\"battleStage==='encounter' && attackMode==='passive'\">\r\n          <button class=\"main-btn\" @click.stop=\"$emit('attack')\">攻击</button>\r\n          <button class=\"sub-btn\" @click.stop=\"$emit('escape')\">逃跑</button>\r\n        </template>\r\n        <!-- 遇怪阶段：主动怪物 -->\r\n        <template v-else-if=\"battleStage==='encounter' && attackMode==='active'\">\r\n          <button class=\"sub-btn\" @click.stop=\"$emit('escape')\">逃离</button>\r\n        </template>\r\n        <!-- 战斗阶段：始终显示逃跑 -->\r\n        <template v-else-if=\"battleStage==='battle'\">\r\n          <button class=\"main-btn\" @click.stop=\"$emit('escape')\">逃跑</button>\r\n        </template>\r\n        <!-- 战斗结束 -->\r\n        <template v-else-if=\"battleStage==='end'\">\r\n          <button class=\"main-btn\" @click.stop=\"$emit('close')\">关闭</button>\r\n        </template>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'BattlePopup',\r\n  props: {\r\n    visible: Boolean,\r\n    battleLog: Array, // [{round, desc, effect_desc, ...}]\r\n    player: Object,\r\n    monster: Object,\r\n    attackMode: {\r\n      type: String, // 'active' or 'passive'\r\n      default: 'active'\r\n    },\r\n    battleStage: {\r\n      type: String, // 'encounter' | 'battle' | 'end'\r\n      default: 'battle'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      defaultPlayerAvatar: '/static/npc/default.png',\r\n      defaultMonsterAvatar: '/static/npc/default.png',\r\n      debug: true, // 开启调试模式\r\n      forceUpdate: 0, // 强制更新计数器\r\n      scrollTop: 0, // 滚动位置\r\n      scrollIntoView: '', // 滚动到指定元素\r\n    }\r\n  },\r\n  computed: {\r\n    playerHpPercent() {\r\n      if (!this.player) return 100;\r\n      return this.player.hp && this.player.max_hp ? (this.player.hp / this.player.max_hp) * 100 : 100;\r\n    },\r\n    playerMpPercent() {\r\n      if (!this.player) return 100;\r\n      return this.player.mp && this.player.max_mp ? (this.player.mp / this.player.max_mp) * 100 : 100;\r\n    },\r\n    monsterHpPercent() {\r\n      if (!this.monster) return 100;\r\n      return this.monster.hp && this.monster.max_hp ? (this.monster.hp / this.monster.max_hp) * 100 : 100;\r\n    }\r\n  },\r\n  watch: {\r\n    battleLog: {\r\n      handler(newVal) {\r\n        console.log('[BattlePopup] 战斗日志更新:', newVal);\r\n        console.log('[BattlePopup] 日志数量:', newVal ? newVal.length : 0);\r\n        console.log('[BattlePopup] 日志类型:', typeof newVal);\r\n        console.log('[BattlePopup] 是否为数组:', Array.isArray(newVal));\r\n        if (newVal && newVal.length > 0) {\r\n          console.log('[BattlePopup] 第一条日志:', newVal[0]);\r\n          console.log('[BattlePopup] 第一条日志desc:', newVal[0].desc);\r\n        }\r\n        // 立即滚动到底部，不阻塞战斗流程\r\n        console.log('[BattlePopup] 准备滚动到底部');\r\n        this.scrollToBottom();\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  mounted() {\r\n    console.log('[BattlePopup] 组件已挂载');\r\n    console.log('[BattlePopup] 初始battleLog:', this.battleLog);\r\n    \r\n    // 测试格式化函数\r\n    const testDesc = \"地痞使出无名拳法中的【无名初现】，造成295点伤害！\";\r\n    const formatted = this.formatBattleDesc(testDesc);\r\n    console.log('[BattlePopup] 测试格式化:', testDesc);\r\n    console.log('[BattlePopup] 格式化结果:', formatted);\r\n  },\r\n  methods: {\r\n    onClose() {\r\n      this.$emit('close')\r\n    },\r\n    onNext() {\r\n      this.$emit('next')\r\n    },\r\n    onAttack() {\r\n      this.$emit('attack')\r\n    },\r\n    onEscape() {\r\n      this.$emit('escape')\r\n    },\r\n    formatBattleDesc(desc) {\r\n      if (!desc || desc === '') {\r\n        console.log('[BattlePopup] 格式化战斗描述: 空描述');\r\n        return '';\r\n      }\r\n      console.log('[BattlePopup] 格式化战斗描述:', desc);\r\n      console.log('[BattlePopup] 描述类型:', typeof desc);\r\n      console.log('[BattlePopup] 描述长度:', desc.length);\r\n      \r\n      // 使用内联样式来实现高亮，避免scoped样式问题\r\n      // 高亮招式名称（【招式名】格式）\r\n      let formatted = desc.replace(/【([^】]+)】/g, '<span style=\"color: #e74c3c; font-weight: bold; text-shadow: 0 0 2px rgba(231, 76, 60, 0.3);\">【$1】</span>');\r\n      \r\n      // 高亮伤害数字（只高亮数字部分，不包括\"点伤害\"文字）\r\n      formatted = formatted.replace(/(\\d+)(?=点伤害)/g, '<span style=\"color: #f39c12; font-weight: bold; text-shadow: 0 0 2px rgba(243, 156, 18, 0.3);\">$1</span>');\r\n      \r\n      // 高亮其他数字（如攻击力、防御力等，只高亮数字部分）\r\n      formatted = formatted.replace(/(\\d+)(?=点)/g, '<span style=\"color: #f39c12; font-weight: bold; text-shadow: 0 0 2px rgba(243, 156, 18, 0.3);\">$1</span>');\r\n      \r\n      // 高亮特殊效果（只保留真正的特殊效果，移除\"难以招架\"）\r\n      formatted = formatted.replace(/(眩晕|中毒|流血)/g, '<span style=\"color: #9b59b6; font-weight: bold; text-shadow: 0 0 2px rgba(155, 89, 182, 0.3);\">$1</span>');\r\n      \r\n      console.log('[BattlePopup] 格式化后:', formatted);\r\n      return formatted;\r\n    },\r\n    scrollToBottom() {\r\n      // 使用scroll-into-view滚动到底部\r\n      console.log('[BattlePopup] scrollToBottom被调用');\r\n      this.$nextTick(() => {\r\n        console.log('[BattlePopup] 设置scrollIntoView为battle-log-bottom');\r\n        this.scrollIntoView = 'battle-log-bottom';\r\n        // 延迟重置，避免重复滚动\r\n        setTimeout(() => {\r\n          this.scrollIntoView = '';\r\n        }, 100);\r\n      });\r\n    },\r\n    onScroll(e) {\r\n      // 记录滚动位置\r\n      this.scrollTop = e.detail.scrollTop;\r\n    },\r\n\r\n    getCurrentTime() {\r\n      const now = new Date();\r\n      const hours = now.getHours().toString().padStart(2, '0');\r\n      const minutes = now.getMinutes().toString().padStart(2, '0');\r\n      const seconds = now.getSeconds().toString().padStart(2, '0');\r\n      return `${hours}:${minutes}:${seconds}`;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.battle-popup-mask {\r\n  position: fixed;\r\n  left: 0;\r\n  right: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  z-index: 99999;\r\n  background: rgba(0,0,0,0.32);\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.battle-popup-content {\r\n  background: #fff;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);\r\n  padding: 32rpx 24rpx 24rpx 24rpx;\r\n  min-width: 600rpx;\r\n  max-width: 96vw;\r\n  max-height: 92vh;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n.battle-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #c0392b;\r\n  margin-bottom: 12rpx; /* 减少底部间距 */\r\n}\r\n.battle-roles {\r\n  display: flex;\r\n  align-items: flex-end;\r\n  justify-content: center;\r\n  gap: 32rpx;\r\n  margin-bottom: 12rpx; /* 减少底部间距 */\r\n}\r\n.role {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  min-width: 180rpx;\r\n}\r\n.role-avatar {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  background: #eee;\r\n  margin-bottom: 8rpx;\r\n}\r\n.role-name {\r\n  font-size: 26rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 6rpx;\r\n}\r\n.role-hp-bar, .role-mp-bar {\r\n  width: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8rpx;\r\n  font-size: 22rpx;\r\n  margin-bottom: 2rpx;\r\n}\r\n.vs-text {\r\n  font-size: 32rpx;\r\n  color: #888;\r\n  font-weight: bold;\r\n  margin: 0 12rpx;\r\n}\r\n.debug-info {\r\n  background: #f0f0f0;\r\n  padding: 8rpx 12rpx;\r\n  border-radius: 8rpx;\r\n  margin-bottom: 12rpx;\r\n  font-size: 20rpx;\r\n  color: #666;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 4rpx;\r\n}\r\n.battle-log {\r\n  width: 100%;\r\n  height: 800rpx; /* 继续增加高度，占满更多空间 */\r\n  background: #f8f8f8;\r\n  border-radius: 12rpx;\r\n  margin: 18rpx 0 12rpx 0;\r\n  padding: 8rpx 12rpx;\r\n  border: 2px solid #e0e0e0;\r\n  display: block;\r\n}\r\n.battle-log-bottom {\r\n  height: 20rpx;\r\n  width: 100%;\r\n}\r\n.battle-log-content {\r\n  /* 确保内容有足够的高度 */\r\n  padding: 4rpx;\r\n  min-height: 100%;\r\n}\r\n.battle-round {\r\n  margin-bottom: 4rpx;\r\n  padding: 4rpx 8rpx;\r\n  background: transparent;\r\n  border-radius: 4rpx;\r\n  border: none;\r\n  word-wrap: break-word;\r\n  word-break: break-all;\r\n}\r\n.round-title {\r\n  font-size: 22rpx;\r\n  color: #4b3fa7;\r\n  font-weight: bold;\r\n}\r\n.round-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 2rpx;\r\n}\r\n.round-time {\r\n  font-size: 18rpx;\r\n  color: #888;\r\n  font-weight: normal;\r\n}\r\n.round-desc {\r\n  font-size: 24rpx;\r\n  color: #222;\r\n  margin: 1rpx 0 1rpx 0;\r\n  line-height: 1.3;\r\n  word-wrap: break-word;\r\n  word-break: break-all;\r\n}\r\n\r\n.effect-desc {\r\n  font-size: 20rpx;\r\n  color: #e67e22;\r\n  margin: 1rpx 0 1rpx 0;\r\n  line-height: 1.2;\r\n}\r\n.battle-log-placeholder {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  flex: 1;\r\n  color: #999;\r\n  font-size: 24rpx;\r\n}\r\n.battle-popup-buttons {\r\n  display: flex;\r\n  gap: 24rpx;\r\n  margin-top: 12rpx; /* 减少顶部间距 */\r\n}\r\n.main-btn {\r\n  background: linear-gradient(90deg, #e67e22, #f9d423);\r\n  color: #fff;\r\n  font-size: 26rpx;\r\n  border-radius: 24rpx;\r\n  padding: 12rpx 36rpx;\r\n  border: none;\r\n}\r\n.sub-btn {\r\n  background: linear-gradient(90deg, #888, #bbb);\r\n  color: #fff;\r\n  font-size: 24rpx;\r\n  border-radius: 24rpx;\r\n  padding: 12rpx 36rpx;\r\n  border: none;\r\n}\r\n.next-btn {\r\n  background: linear-gradient(90deg, #e67e22, #f9d423);\r\n  color: #fff;\r\n  font-size: 26rpx;\r\n  border-radius: 24rpx;\r\n  padding: 12rpx 36rpx;\r\n  border: none;\r\n}\r\n.close-btn {\r\n  background: #eee;\r\n  color: #888;\r\n  font-size: 24rpx;\r\n  border-radius: 24rpx;\r\n  padding: 12rpx 36rpx;\r\n  border: none;\r\n}\r\n.next-btn[disabled], .main-btn[disabled] {\r\n  opacity: 0.5;\r\n}\r\n</style> ", "import Component from 'D:/zjjhx/仗剑江湖行/components/BattlePopup.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAkFA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,SAAS;AAAA,IACT,WAAW;AAAA;AAAA,IACX,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,YAAY;AAAA,MACV,MAAM;AAAA;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,aAAa;AAAA,MACX,MAAM;AAAA;AAAA,MACN,SAAS;AAAA,IACX;AAAA,EACD;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,qBAAqB;AAAA,MACrB,sBAAsB;AAAA,MACtB,OAAO;AAAA;AAAA,MACP,aAAa;AAAA;AAAA,MACb,WAAW;AAAA;AAAA,MACX,gBAAgB;AAAA;AAAA,IAClB;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,kBAAkB;AAChB,UAAI,CAAC,KAAK;AAAQ,eAAO;AACzB,aAAO,KAAK,OAAO,MAAM,KAAK,OAAO,SAAU,KAAK,OAAO,KAAK,KAAK,OAAO,SAAU,MAAM;AAAA,IAC7F;AAAA,IACD,kBAAkB;AAChB,UAAI,CAAC,KAAK;AAAQ,eAAO;AACzB,aAAO,KAAK,OAAO,MAAM,KAAK,OAAO,SAAU,KAAK,OAAO,KAAK,KAAK,OAAO,SAAU,MAAM;AAAA,IAC7F;AAAA,IACD,mBAAmB;AACjB,UAAI,CAAC,KAAK;AAAS,eAAO;AAC1B,aAAO,KAAK,QAAQ,MAAM,KAAK,QAAQ,SAAU,KAAK,QAAQ,KAAK,KAAK,QAAQ,SAAU,MAAM;AAAA,IAClG;AAAA,EACD;AAAA,EACD,OAAO;AAAA,IACL,WAAW;AAAA,MACT,QAAQ,QAAQ;AACdA,sBAAY,MAAA,MAAA,OAAA,qCAAA,yBAAyB,MAAM;AAC3CA,4BAAY,MAAA,OAAA,qCAAA,uBAAuB,SAAS,OAAO,SAAS,CAAC;AAC7DA,8EAAY,uBAAuB,OAAO,MAAM;AAChDA,4BAAA,MAAA,OAAA,qCAAY,wBAAwB,MAAM,QAAQ,MAAM,CAAC;AACzD,YAAI,UAAU,OAAO,SAAS,GAAG;AAC/BA,wBAAA,MAAA,MAAA,OAAA,qCAAY,wBAAwB,OAAO,CAAC,CAAC;AAC7CA,8BAAA,MAAA,OAAA,qCAAY,4BAA4B,OAAO,CAAC,EAAE,IAAI;AAAA,QACxD;AAEAA,sBAAAA,MAAY,MAAA,OAAA,qCAAA,uBAAuB;AACnC,aAAK,eAAc;AAAA,MACpB;AAAA,MACD,MAAM;AAAA,IACR;AAAA,EACD;AAAA,EACD,UAAU;AACRA,kBAAAA,MAAY,MAAA,OAAA,qCAAA,qBAAqB;AACjCA,kBAAA,MAAA,MAAA,OAAA,qCAAY,8BAA8B,KAAK,SAAS;AAGxD,UAAM,WAAW;AACjB,UAAM,YAAY,KAAK,iBAAiB,QAAQ;AAChDA,kBAAY,MAAA,MAAA,OAAA,qCAAA,wBAAwB,QAAQ;AAC5CA,kBAAY,MAAA,MAAA,OAAA,qCAAA,wBAAwB,SAAS;AAAA,EAC9C;AAAA,EACD,SAAS;AAAA,IACP,UAAU;AACR,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,IACD,SAAS;AACP,WAAK,MAAM,MAAM;AAAA,IAClB;AAAA,IACD,WAAW;AACT,WAAK,MAAM,QAAQ;AAAA,IACpB;AAAA,IACD,WAAW;AACT,WAAK,MAAM,QAAQ;AAAA,IACpB;AAAA,IACD,iBAAiB,MAAM;AACrB,UAAI,CAAC,QAAQ,SAAS,IAAI;AACxBA,sBAAAA,MAAA,MAAA,OAAA,qCAAY,4BAA4B;AACxC,eAAO;AAAA,MACT;AACAA,4EAAY,0BAA0B,IAAI;AAC1CA,oBAAA,MAAA,MAAA,OAAA,qCAAY,uBAAuB,OAAO,IAAI;AAC9CA,oBAAA,MAAA,MAAA,OAAA,qCAAY,uBAAuB,KAAK,MAAM;AAI9C,UAAI,YAAY,KAAK,QAAQ,cAAc,2GAA2G;AAGtJ,kBAAY,UAAU,QAAQ,iBAAiB,0GAA0G;AAGzJ,kBAAY,UAAU,QAAQ,eAAe,0GAA0G;AAGvJ,kBAAY,UAAU,QAAQ,eAAe,0GAA0G;AAEvJA,oBAAA,MAAA,MAAA,OAAA,qCAAY,uBAAuB,SAAS;AAC5C,aAAO;AAAA,IACR;AAAA,IACD,iBAAiB;AAEfA,oBAAAA,MAAA,MAAA,OAAA,qCAAY,iCAAiC;AAC7C,WAAK,UAAU,MAAM;AACnBA,sBAAAA,MAAA,MAAA,OAAA,qCAAY,kDAAkD;AAC9D,aAAK,iBAAiB;AAEtB,mBAAW,MAAM;AACf,eAAK,iBAAiB;AAAA,QACvB,GAAE,GAAG;AAAA,MACR,CAAC;AAAA,IACF;AAAA,IACD,SAAS,GAAG;AAEV,WAAK,YAAY,EAAE,OAAO;AAAA,IAC3B;AAAA,IAED,iBAAiB;AACf,YAAM,MAAM,oBAAI;AAChB,YAAM,QAAQ,IAAI,SAAU,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AACvD,YAAM,UAAU,IAAI,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC3D,YAAM,UAAU,IAAI,WAAY,EAAC,SAAQ,EAAG,SAAS,GAAG,GAAG;AAC3D,aAAO,GAAG,KAAK,IAAI,OAAO,IAAI,OAAO;AAAA,IACvC;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpNA,GAAG,gBAAgB,SAAS;"}