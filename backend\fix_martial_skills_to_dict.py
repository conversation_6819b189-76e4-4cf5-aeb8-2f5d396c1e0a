import aiosqlite
import asyncio
import json
import os

DB_PATH = os.path.join(os.path.dirname(__file__), 'game.db')

async def fix_martial_skills():
    async with aiosqlite.connect(DB_PATH) as db:
        async with db.execute("SELECT id, data FROM players") as cursor:
            rows = await cursor.fetchall()
            for row in rows:
                player_id, data_json = row
                try:
                    data = json.loads(data_json)
                    ms = data.get('martial_skills')
                    if isinstance(ms, list):
                        # 转为 dict
                        new_ms = {entry['name']: entry for entry in ms if 'name' in entry}
                        data['martial_skills'] = new_ms
                        new_data_json = json.dumps(data, ensure_ascii=False)
                        await db.execute("UPDATE players SET data = ? WHERE id = ?", (new_data_json, player_id))
                        print(f"玩家ID {player_id} martial_skills 已修正为 dict")
                except Exception as e:
                    print(f"玩家ID {player_id} 修正失败: {e}")
        await db.commit()
    print('所有玩家 martial_skills 字段已批量修正为 dict')

if __name__ == '__main__':
    asyncio.run(fix_martial_skills()) 