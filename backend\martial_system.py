# -*- coding: utf-8 -*-
"""
武学系统模块
定义武学分级、基础与入门武学对应关系、武学数据结构
"""

import os
import json

def load_wugong_config():
    """
    从backend/wugong_enhanced.json加载所有武功配置，返回字典结构
    """
    wugong_path = os.path.join(os.path.dirname(__file__), 'wugong_enhanced.json')
    martials = {}
    if not os.path.exists(wugong_path):
        return martials
    with open(wugong_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    for item in data:
        name = item.get('武功名') or item.get('name')
        if name:
            martials[name] = item
    return martials 