[{"id": "basic_jianfa_book", "name": "基本剑法秘籍", "type": "book", "quality": "common", "description": "记载基本剑法的武学书籍，使用后可学习或提升基本剑法。", "icon": "", "price": 100, "sell_price": 40, "craftable": 1, "craft_recipe": "基本剑法残页:10", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:基本剑法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_daofa_book", "name": "基本刀法秘籍", "type": "book", "quality": "common", "description": "记载基本刀法的武学书籍，使用后可学习或提升基本刀法。", "icon": "", "price": 100, "sell_price": 40, "craftable": 1, "craft_recipe": "基本刀法残页:10", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:基本刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_quanfa_book", "name": "基本拳法秘籍", "type": "book", "quality": "common", "description": "记载基本拳法的武学书籍，使用后可学习或提升基本拳法。", "icon": "", "price": 100, "sell_price": 40, "craftable": 1, "craft_recipe": "基本拳法残页:10", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:基本拳法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_zhaojia_book", "name": "基本招架秘籍", "type": "book", "quality": "common", "description": "记载基本招架的武学书籍，使用后可学习或提升基本招架。", "icon": "", "price": 100, "sell_price": 40, "craftable": 1, "craft_recipe": "基本招架残页:10", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:基本招架", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_qinggong_book", "name": "基本轻功秘籍", "type": "book", "quality": "common", "description": "记载基本轻功的武学书籍，使用后可学习或提升基本轻功。", "icon": "", "price": 100, "sell_price": 40, "craftable": 1, "craft_recipe": "基本轻功残页:10", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:基本轻功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_neigong_book", "name": "基本内功秘籍", "type": "book", "quality": "common", "description": "记载基本内功的武学书籍，使用后可学习或提升基本内功。", "icon": "", "price": 100, "sell_price": 40, "craftable": 1, "craft_recipe": "基本内功残页:10", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:基本内功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_anqi_book", "name": "基本暗器法秘籍", "type": "book", "quality": "common", "description": "记载基本暗器法的武学书籍，使用后可学习或提升基本暗器法。", "icon": "", "price": 100, "sell_price": 40, "craftable": 1, "craft_recipe": "基本暗器法残页:10", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:基本暗器法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_dushu_book", "name": "读书写字秘籍", "type": "book", "quality": "common", "description": "记载读书写字的书籍，使用后可提升悟性。", "icon": "", "price": 100, "sell_price": 40, "craftable": 1, "craft_recipe": "读书写字残页:10", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:读书写字", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "taijiquan_book", "name": "太极拳秘籍", "type": "book", "quality": "rare", "description": "武当派绝学，以柔克刚。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "太极拳残页:100", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:太极拳", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_book", "name": "独孤九剑秘籍", "type": "book", "quality": "legendary", "description": "华山派绝学，破尽天下武功。", "icon": "", "price": 5000, "sell_price": 2000, "craftable": 1, "craft_recipe": "独孤九剑残页:100", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:独孤九剑", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "xianglongshibazhang_book", "name": "降龙十八掌秘籍", "type": "book", "quality": "legendary", "description": "丐帮绝学，掌力雄浑。", "icon": "", "price": 5000, "sell_price": 2000, "craftable": 1, "craft_recipe": "降龙十八掌残页:100", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:降龙十八掌", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "dagoubangfa_book", "name": "打狗棒法秘籍", "type": "book", "quality": "legendary", "description": "丐帮绝学，棒法精妙。", "icon": "", "price": 5000, "sell_price": 2000, "craftable": 1, "craft_recipe": "打狗棒法残页:100", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:打狗棒法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "lingboweibu_book", "name": "凌波微步秘籍", "type": "book", "quality": "legendary", "description": "逍遥派绝学，身法如鬼魅。", "icon": "", "price": 5000, "sell_price": 2000, "craftable": 1, "craft_recipe": "凌波微步残页:100", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:凌波微步", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "shenxingbaibian_book", "name": "神行百变秘籍", "type": "book", "quality": "legendary", "description": "逍遥派绝学，身法变化多端。", "icon": "", "price": 5000, "sell_price": 2000, "craftable": 1, "craft_recipe": "神行百变残页:100", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:神行百变", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "zixiagong_book", "name": "紫霞功秘籍", "type": "book", "quality": "rare", "description": "华山派内功，提升内力修为。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "紫霞功残页:100", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:紫霞功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "huntianqigong_book", "name": "混天气功秘籍", "type": "book", "quality": "rare", "description": "丐帮内功，提升内力修为。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "混天气功残页:100", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:混天气功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "yunvxinjing_book", "name": "玉女心经秘籍", "type": "book", "quality": "rare", "description": "古墓派内功，提升内力修为。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "玉女心经残页:100", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:玉女心经", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "xiaoyaoxinfabook", "name": "逍遥心法秘籍", "type": "book", "quality": "rare", "description": "灵鹫宫内功，提升内力修为。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "逍遥心法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:逍遥心法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "biboxinfabook", "name": "碧波心法秘籍", "type": "book", "quality": "rare", "description": "桃花岛内功，提升内力修为。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "碧波心法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:碧波心法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "yijinjing_book", "name": "易筋经秘籍", "type": "book", "quality": "rare", "description": "少林派内功，提升内力修为。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "易筋经残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:易筋经", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "shenlongxinfabook", "name": "神龙心法秘籍", "type": "book", "quality": "rare", "description": "神龙教内功，提升内力修为。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "神龙心法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:神龙心法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "murongxinfabook", "name": "慕容心法秘籍", "type": "book", "quality": "rare", "description": "姑苏慕容内功，提升内力修为。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "慕容心法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:慕容心法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "t<PERSON><PERSON>_liu<PERSON>_zhang_book", "name": "天山六阳掌秘籍", "type": "book", "quality": "rare", "description": "天山六阳掌的武学秘籍，记载了天山六阳掌的修炼方法。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "天山六阳掌残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:天山六阳掌", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "tianyu_qijian_book", "name": "天羽奇剑秘籍", "type": "book", "quality": "rare", "description": "天羽奇剑的武学秘籍，记载了天羽奇剑的修炼方法。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "天羽奇剑残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:天羽奇剑", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "wanhua_jianfa_book", "name": "万花剑法秘籍", "type": "book", "quality": "rare", "description": "万花剑法的武学秘籍，记载了万花剑法的修炼方法。", "icon": "", "price": 2000, "sell_price": 800, "craftable": 1, "craft_recipe": "万花剑法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:万花剑法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_duofa_book", "name": "基本毒法秘籍", "type": "book", "quality": "common", "description": "记载基本毒法的武学书籍，使用后可学习或提升基本毒法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "基本毒法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:基本毒法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_anqi_book", "name": "基本暗器秘籍", "type": "book", "quality": "common", "description": "记载基本暗器的武学书籍，使用后可学习或提升基本暗器。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "基本暗器残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:基本暗器", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_liuyun_book", "name": "流云剑法秘籍", "type": "book", "quality": "common", "description": "记载流云剑法的武学书籍，使用后可学习或提升流云剑法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "流云剑法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:流云剑法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_jinshe_book", "name": "金蛇剑法秘籍", "type": "book", "quality": "common", "description": "记载金蛇剑法的武学书籍，使用后可学习或提升金蛇剑法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "金蛇剑法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:金蛇剑法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_qingfeng_book", "name": "青锋剑诀秘籍", "type": "book", "quality": "common", "description": "记载青锋剑诀的武学书籍，使用后可学习或提升青锋剑诀。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "青锋剑诀残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:青锋剑诀", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_zidi_book", "name": "紫电剑法秘籍", "type": "book", "quality": "common", "description": "记载紫电剑法的武学书籍，使用后可学习或提升紫电剑法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "紫电剑法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:紫电剑法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_han_book", "name": "寒光剑法秘籍", "type": "book", "quality": "common", "description": "记载寒光剑法的武学书籍，使用后可学习或提升寒光剑法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "寒光剑法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:寒光剑法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_tianwai_book", "name": "天外飞仙秘籍", "type": "book", "quality": "common", "description": "记载天外飞仙的武学书籍，使用后可学习或提升天外飞仙。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "天外飞仙残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:天外飞仙", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_bihai_book", "name": "碧海潮生曲秘籍", "type": "book", "quality": "common", "description": "记载碧海潮生曲的武学书籍，使用后可学习或提升碧海潮生曲。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "碧海潮生曲残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:碧海潮生曲", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_luoying_book", "name": "落英神剑秘籍", "type": "book", "quality": "common", "description": "记载落英神剑的武学书籍，使用后可学习或提升落英神剑。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "落英神剑残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:落英神剑", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_tianshan_book", "name": "天山剑法秘籍", "type": "book", "quality": "common", "description": "记载天山剑法的武学书籍，使用后可学习或提升天山剑法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "天山剑法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:天山剑法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_wuming_book", "name": "无名剑法秘籍", "type": "book", "quality": "common", "description": "记载无名剑法的武学书籍，使用后可学习或提升无名剑法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "无名剑法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:无名剑法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_kuangfeng_book", "name": "狂风刀法秘籍", "type": "book", "quality": "common", "description": "记载狂风刀法的武学书籍，使用后可学习或提升狂风刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "狂风刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:狂风刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_xueying_book", "name": "血影刀法秘籍", "type": "book", "quality": "common", "description": "记载血影刀法的武学书籍，使用后可学习或提升血影刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "血影刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:血影刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_duanshui_book", "name": "断水刀法秘籍", "type": "book", "quality": "common", "description": "记载断水刀法的武学书籍，使用后可学习或提升断水刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "断水刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:断水刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_lieyan_book", "name": "烈焰刀法秘籍", "type": "book", "quality": "common", "description": "记载烈焰刀法的武学书籍，使用后可学习或提升烈焰刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "烈焰刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:烈焰刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_hanbing_book", "name": "寒冰刀法秘籍", "type": "book", "quality": "common", "description": "记载寒冰刀法的武学书籍，使用后可学习或提升寒冰刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "寒冰刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:寒冰刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_fenglei_book", "name": "风雷刀法秘籍", "type": "book", "quality": "common", "description": "记载风雷刀法的武学书籍，使用后可学习或提升风雷刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "风雷刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:风雷刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_wuying_book", "name": "无影刀法秘籍", "type": "book", "quality": "common", "description": "记载无影刀法的武学书籍，使用后可学习或提升无影刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "无影刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:无影刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_lieyang_book", "name": "烈阳刀法秘籍", "type": "book", "quality": "common", "description": "记载烈阳刀法的武学书籍，使用后可学习或提升烈阳刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "烈阳刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:烈阳刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_duanyue_book", "name": "断岳刀法秘籍", "type": "book", "quality": "common", "description": "记载断岳刀法的武学书籍，使用后可学习或提升断岳刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "断岳刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:断岳刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_wuming_book", "name": "无名刀法秘籍", "type": "book", "quality": "common", "description": "记载无名刀法的武学书籍，使用后可学习或提升无名刀法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "无名刀法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:无名刀法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_luohan_book", "name": "罗汉拳秘籍", "type": "book", "quality": "common", "description": "记载罗汉拳的武学书籍，使用后可学习或提升罗汉拳。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "罗汉拳残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:罗汉拳", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_qishang_book", "name": "七伤拳秘籍", "type": "book", "quality": "common", "description": "记载七伤拳的武学书籍，使用后可学习或提升七伤拳。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "七伤拳残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:七伤拳", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_dali_book", "name": "大力金刚拳秘籍", "type": "book", "quality": "common", "description": "记载大力金刚拳的武学书籍，使用后可学习或提升大力金刚拳。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "大力金刚拳残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:大力金刚拳", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_wuying_book", "name": "无影拳秘籍", "type": "book", "quality": "common", "description": "记载无影拳的武学书籍，使用后可学习或提升无影拳。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "无影拳残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:无影拳", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_jingong_book", "name": "金刚拳秘籍", "type": "book", "quality": "common", "description": "记载金刚拳的武学书籍，使用后可学习或提升金刚拳。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "金刚拳残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:金刚拳", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_lieyang_book", "name": "烈阳拳秘籍", "type": "book", "quality": "common", "description": "记载烈阳拳的武学书籍，使用后可学习或提升烈阳拳。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "烈阳拳残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:烈阳拳", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_duanyue_book", "name": "断岳拳秘籍", "type": "book", "quality": "common", "description": "记载断岳拳的武学书籍，使用后可学习或提升断岳拳。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "断岳拳残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:断岳拳", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_wuming_book", "name": "无名拳法秘籍", "type": "book", "quality": "common", "description": "记载无名拳法的武学书籍，使用后可学习或提升无名拳法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "无名拳法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:无名拳法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_tuixue_book", "name": "踏雪无痕秘籍", "type": "book", "quality": "common", "description": "记载踏雪无痕的武学书籍，使用后可学习或提升踏雪无痕。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "踏雪无痕残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:踏雪无痕", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_fengxing_book", "name": "风行步秘籍", "type": "book", "quality": "common", "description": "记载风行步的武学书籍，使用后可学习或提升风行步。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "风行步残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:风行步", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_yunzong_book", "name": "云踪幻影秘籍", "type": "book", "quality": "common", "description": "记载云踪幻影的武学书籍，使用后可学习或提升云踪幻影。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "云踪幻影残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:云踪幻影", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_longxing_book", "name": "龙行虎步秘籍", "type": "book", "quality": "common", "description": "记载龙行虎步的武学书籍，使用后可学习或提升龙行虎步。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "龙行虎步残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:龙行虎步", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_yingbu_book", "name": "影步秘籍", "type": "book", "quality": "common", "description": "记载影步的武学书籍，使用后可学习或提升影步。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "影步残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:影步", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_youlong_book", "name": "游龙步秘籍", "type": "book", "quality": "common", "description": "记载游龙步的武学书籍，使用后可学习或提升游龙步。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "游龙步残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:游龙步", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_tayun_book", "name": "踏云步秘籍", "type": "book", "quality": "common", "description": "记载踏云步的武学书籍，使用后可学习或提升踏云步。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "踏云步残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:踏云步", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_feiyanchun_book", "name": "飞燕穿云秘籍", "type": "book", "quality": "common", "description": "记载飞燕穿云的武学书籍，使用后可学习或提升飞燕穿云。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "飞燕穿云残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:飞燕穿云", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_tianyu_book", "name": "天羽身法秘籍", "type": "book", "quality": "common", "description": "记载天羽身法的武学书籍，使用后可学习或提升天羽身法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "天羽身法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:天羽身法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_hunyuan_book", "name": "混元功秘籍", "type": "book", "quality": "common", "description": "记载混元功的武学书籍，使用后可学习或提升混元功。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "混元功残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:混元功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_chunyang_book", "name": "纯阳无极功秘籍", "type": "book", "quality": "common", "description": "记载纯阳无极功的武学书籍，使用后可学习或提升纯阳无极功。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "纯阳无极功残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:纯阳无极功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_xiaowuxiang_book", "name": "小无相功秘籍", "type": "book", "quality": "common", "description": "记载小无相功的武学书籍，使用后可学习或提升小无相功。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "小无相功残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:小无相功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_jiuyin_book", "name": "九阴真经秘籍", "type": "book", "quality": "common", "description": "记载九阴真经的武学书籍，使用后可学习或提升九阴真经。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "九阴真经残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:九阴真经", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_zixia_book", "name": "紫霞神功秘籍", "type": "book", "quality": "common", "description": "记载紫霞神功的武学书籍，使用后可学习或提升紫霞神功。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "紫霞神功残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:紫霞神功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_tiangan_book", "name": "天罡神功秘籍", "type": "book", "quality": "common", "description": "记载天罡神功的武学书籍，使用后可学习或提升天罡神功。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "天罡神功残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:天罡神功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_beiming_book", "name": "北冥神功秘籍", "type": "book", "quality": "common", "description": "记载北冥神功的武学书籍，使用后可学习或提升北冥神功。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "北冥神功残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:北冥神功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_jinkong_book", "name": "金刚不坏功秘籍", "type": "book", "quality": "common", "description": "记载金刚不坏功的武学书籍，使用后可学习或提升金刚不坏功。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "金刚不坏功残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:金刚不坏功", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_wuming_book", "name": "无名心法秘籍", "type": "book", "quality": "common", "description": "记载无名心法的武学书籍，使用后可学习或提升无名心法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "无名心法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:无名心法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_feihuazhishou_book", "name": "飞花摘叶手秘籍", "type": "book", "quality": "common", "description": "记载飞花摘叶手的武学书籍，使用后可学习或提升飞花摘叶手。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "飞花摘叶手残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:飞花摘叶手", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_xiu_book", "name": "袖箭秘籍", "type": "book", "quality": "common", "description": "记载袖箭的武学书籍，使用后可学习或提升袖箭。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "袖箭残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:袖箭", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_kongque_book", "name": "孔雀翎秘籍", "type": "book", "quality": "common", "description": "记载孔雀翎的武学书籍，使用后可学习或提升孔雀翎。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "孔雀翎残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:孔雀翎", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_liuxing_book", "name": "流星飞镖秘籍", "type": "book", "quality": "common", "description": "记载流星飞镖的武学书籍，使用后可学习或提升流星飞镖。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "流星飞镖残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:流星飞镖", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_duoshaji_book", "name": "毒砂掌秘籍", "type": "book", "quality": "common", "description": "记载毒砂掌的武学书籍，使用后可学习或提升毒砂掌。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "毒砂掌残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:毒砂掌", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_meihua_book", "name": "梅花针秘籍", "type": "book", "quality": "common", "description": "记载梅花针的武学书籍，使用后可学习或提升梅花针。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "梅花针残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:梅花针", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_xiuli_book", "name": "袖里乾坤秘籍", "type": "book", "quality": "common", "description": "记载袖里乾坤的武学书籍，使用后可学习或提升袖里乾坤。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "袖里乾坤残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:袖里乾坤", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_wuying_book", "name": "无影神针秘籍", "type": "book", "quality": "common", "description": "记载无影神针的武学书籍，使用后可学习或提升无影神针。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "无影神针残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:无影神针", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_duolong_book", "name": "毒龙钻秘籍", "type": "book", "quality": "common", "description": "记载毒龙钻的武学书籍，使用后可学习或提升毒龙钻。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "毒龙钻残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:毒龙钻", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "basic_wuming_book", "name": "无名暗器法秘籍", "type": "book", "quality": "common", "description": "记载无名暗器法的武学书籍，使用后可学习或提升无名暗器法。", "icon": "", "price": 1000, "sell_price": 400, "craftable": 1, "craft_recipe": "无名暗器法残页:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "learn:无名暗器法", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}]