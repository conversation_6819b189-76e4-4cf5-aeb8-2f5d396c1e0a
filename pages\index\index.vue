<template>
	<view class="container">
		<!-- 顶部地图信息 -->
		<view class="map-bar">
			<view class="map-bar-left">
				<text class="user-name">{{ player && player.name ? player.name : '' }}</text>
				<text class="user-label">｜</text>
				<text class="map-label">当前地图：</text>
				<text class="map-name">{{ currentMapName || '' }}</text>
			</view>
			<view class="map-bar-right">
				<text class="connection-status" :class="connectionStatusClass">{{ connectionStatus }}</text>
				<button class="map-btn" @click="showMapPopup = true">切换地图</button>
			</view>
		</view>
		
		<!-- 系统公告栏 -->
		<view class="announcement-bar">
			<view class="announcement-icon">📢</view>
			<view class="announcement-content">
				<view class="announcement-scroll">
					<text class="announcement-text">{{ announcementText }}</text>
					<text class="announcement-text">{{ announcementText }}</text>
				</view>
			</view>
		</view>

		<!-- 角色信息卡片（去除角色名和等级，仅保留属性条） -->
		<view class="character-card">
			<view class="progress-bar">
				<text class="progress-label">气血</text>
				<view class="progress-bg">
					<view class="progress-fill hp-fill" :style="{ width: hpPercent + '%' }"></view>
				</view>
				<text class="progress-text">{{ Math.floor(player.hp !== undefined ? player.hp : 100) }}/{{ Math.floor(player.max_hp || player.base_max_hp || 100) }}</text>
			</view>
			<view class="progress-bar">
				<text class="progress-label">内力</text>
				<view class="progress-bg">
					<view class="progress-fill mp-fill" :style="{ width: mpPercent + '%' }"></view>
				</view>
				<text class="progress-text">{{ Math.floor(player.mp || 50) }}/{{ Math.floor(player.max_mp || player.base_max_mp || 50) }}</text>
			</view>
			<view class="progress-bar">
				<text class="progress-label">体力</text>
				<view class="progress-bg">
					<view class="progress-fill stamina-fill" :style="{ width: staminaPercent + '%' }"></view>
				</view>
				<text class="progress-text">{{ Math.floor(player.energy || 100) }}/{{ Math.floor(player.max_energy || player.base_max_energy || 100) }}</text>
			</view>
			<view class="progress-bar">
				<text class="progress-label">精力</text>
				<view class="progress-bg">
					<view class="progress-fill energy-fill" :style="{ width: energyPercent + '%' }"></view>
				</view>
				<text class="progress-text">{{ Math.floor(player.spirit || 100) }}/{{ Math.floor(player.max_spirit || 100) }}</text>
			</view>
		</view>

		<!-- 天赋增益信息 -->
		<view class="talent-bonus-card" v-if="hasTalentBonuses">
			<view class="talent-bonus-title">天赋增益</view>
			<view class="talent-bonus-grid">
				<view class="talent-bonus-item" v-if="getStrengthBonus() > 0">
					<text class="talent-bonus-label">力量</text>
					<text class="talent-bonus-value">+{{ Math.floor(getStrengthBonus() || 0) }}%攻击</text>
				</view>
				<view class="talent-bonus-item" v-if="getIntelligenceBonus() > 0">
					<text class="talent-bonus-label">悟性</text>
					<text class="talent-bonus-value">+{{ Math.floor(getIntelligenceBonus() || 0) }}%经验</text>
				</view>
				<view class="talent-bonus-item" v-if="getAgilityDefenseBonus() > 0">
					<text class="talent-bonus-label">身法</text>
					<text class="talent-bonus-value">+{{ Math.floor(getAgilityDefenseBonus() || 0) }}%防御</text>
				</view>
				<view class="talent-bonus-item" v-if="getConstitutionHpBonus() > 0">
					<text class="talent-bonus-label">根骨</text>
					<text class="talent-bonus-value">+{{ Math.floor(getConstitutionHpBonus() || 0) }}%气血</text>
				</view>
			</view>
		</view>

		<!-- 江湖操作按钮区（美观优化版） -->
		<view class="jianghu-section-flex-opt">
			<button class="main-btn" @click="triggerAdventure" :disabled="!isAuthed || status !== 'normal'">
				<text class="main-btn-text">闯</text>
			</button>
		</view>

		<!-- NPC功能菜单弹窗 -->
		<view class="npc-menu-modal" v-if="showNpcMenuModal" @click="closeNpcMenu">
			<view class="npc-menu-content" @click.stop>
				<view class="npc-menu-header">
					<image class="npc-menu-avatar" :src="selectedNpc.avatar" mode="aspectFill" />
					<text class="npc-menu-name">{{ selectedNpc && selectedNpc.name ? selectedNpc.name : '' }}</text>
				</view>
				<text class="npc-menu-desc">{{ selectedNpc.desc }}</text>
				<view class="npc-menu-list">
					<button v-for="func in selectedNpc.functions" :key="func.key" class="npc-menu-btn" @click="onNpcFunction(func, selectedNpc)">{{ func.label }}</button>
				</view>
				<button class="npc-menu-close" @click="closeNpcMenu">关闭</button>
			</view>
		</view>

		<!-- NPC购买弹窗 -->
		<view class="buy-modal-mask" v-if="showBuyModal" @click="closeBuyModal">
			<view class="buy-modal" @click.stop>
				<view class="buy-modal-header">
					<text class="buy-modal-title">购买物品</text>
					<button class="buy-modal-close" @click="closeBuyModal">×</button>
				</view>
				<view class="buy-modal-content">
					<view class="item-info">
						<view class="item-name">{{ buyItem.name || buyItem.item_id }}</view>
						<view class="item-details">
							<text class="item-price">单价: {{ buyItem.price || 0 }} 银两</text>
							<text class="item-stock">库存: {{ buyItem.stock || 0 }}</text>
						</view>
					</view>
					<view class="quantity-section">
						<text class="quantity-label">购买数量:</text>
						<view class="quantity-input-group">
							<button class="quantity-btn" @click="decreaseQuantity">-</button>
							<input class="quantity-input" type="number" v-model="buyQuantity" @input="onQuantityInput" />
							<button class="quantity-btn" @click="increaseQuantity">+</button>
						</view>
					</view>
					<view class="total-section">
						<text class="total-label">总价: {{ (buyItem.price || 0) * buyQuantity }} 银两</text>
						<text class="money-info">当前银两: {{ money }}</text>
					</view>
				</view>
				<view class="buy-modal-actions">
					<button class="buy-btn-cancel" @click="closeBuyModal">取消</button>
					<button class="buy-btn-confirm" @click="confirmBuy" :disabled="!canBuy">确认购买</button>
				</view>
			</view>
		</view>

		<!-- 状态显示 -->
		<view class="status-display" v-if="status !== 'normal'">
			<text class="status-text" :class="'status-' + status">{{ getStatusText() }}</text>
		</view>

		<!-- 事件日志 -->
		<view class="event-log">
			<view class="log-header">
				<text class="log-title">江湖日志</text>
				<text class="log-clear" @click="clearLog">清空</text>
			</view>
			<scroll-view class="log-content" scroll-y="true">
				<view class="log-item" v-for="(event, index) in eventLog" :key="index">
					<view class="log-header-line">
						<text class="log-time">{{ event.timestamp }}</text>
						<text class="log-event">{{ event && event.name ? event.name : '' }}</text>
					</view>
					<view class="log-content-line">
						<view class="log-desc" v-html="event.displayText"></view>
					</view>
				</view>
				<view class="log-empty" v-if="eventLog.length === 0">
					<text>暂无江湖记录</text>
				</view>
			</scroll-view>
		</view>

		<gathering-popup
			v-if="showGatheringPopup"
			:visible="showGatheringPopup"
			:event="gatheringEvent"
			:times="gatheringTimes"
			:result="gatheringResult"
			:inventory="currentInventory"
			@close="closeGatheringPopup"
			@do-gather="doGather"
		/>

		<!-- 地图切换弹窗 -->
		<view v-if="showMapPopup" class="map-popup-mask" @click="showMapPopup = false">
			<view class="map-popup" @click.stop>
				<view class="map-popup-title">切换地图</view>
				<view class="map-list">
					<view v-for="map in mapList" :key="map.id" class="map-item" :class="{ active: currentMap && currentMap.id === map.id, locked: !canEnterMap(map) }" @click="selectMap(map)">
						<view class="map-item-title">{{ map.名称 || map.name || '' }}</view>
						<view class="map-item-desc">{{ map.描述 || map.desc }}</view>
						<view class="map-item-npc" v-if="getMapNpcs(map).length">NPC：{{ getMapNpcs(map).join('、') }}</view>
						<view class="map-item-monster" v-if="getMapMonsters(map).length">怪物：{{ getMapMonsters(map).join('、') }}</view>
						<view class="map-item-gather" v-if="getMapGatherItems(map).length">采集物品：{{ getMapGatherItems(map).join('、') }}</view>
						<view class="map-item-req" v-if="getMapRequirements(map)">进入条件：{{ getMapRequirements(map) }}</view>
					</view>
				</view>
				<button class="close-btn" @click="showMapPopup = false">关闭</button>
			</view>
		</view>


		<BattlePopup
			v-if="showBattlePopup"
			:visible="showBattlePopup"
			:battleLog="battleLog"
			:player="battlePlayer"
			:monster="battleMonster"
			:battleStage="battleStage"
			:attackMode="battleAttackMode"
			@attack="handleBattleAttack"
			@escape="handleBattleEscape"
			@close="handleBattleClose"
		/>

		<!-- 疗伤/打坐弹窗描述区（美化+居中+转圈） -->
		<view v-if="healingMeditationMessages.length > 0" class="healing-meditation-popup-centered">
			<view v-if="healingMeditationLoading" class="healing-meditation-loading">
				<view class="spinner"></view>
			</view>
			<view class="healing-meditation-msg-list" style="max-height:180px;overflow-y:auto;">
				<view v-for="(msg, idx) in healingMeditationMessages" :key="idx" class="healing-meditation-msg-centered">{{ msg }}</view>
			</view>
		</view>

		<!-- 聊天弹窗 -->
		<view v-if="showChatPopup" class="chat-popup-mask" @click="showChatPopup = false">
			<view class="chat-popup" @click.stop>
				<view class="chat-popup-header">
					<text class="chat-popup-title">全服聊天</text>
					<button class="chat-popup-close" @click="showChatPopup = false">×</button>
				</view>
				<view class="chat-messages" ref="chatMessages">
					<view v-for="(msg, index) in chatMessages" :key="index"
						  :class="['chat-message', msg.isOwn ? 'chat-message-own' : '', 'chat-message-' + (msg.chat_type || 'world')]">
						<view class="chat-message-header">
							<view class="chat-message-info">
								<text class="chat-time">{{ formatChatTimeWithSeconds(msg.time) }}</text>
								<text class="chat-sender" :class="[msg.isOwn ? 'chat-sender-own' : '', 'chat-sender-' + (msg.chat_type || 'world')]">{{ msg.sender }}</text>
							</view>
							<view v-if="msg.chat_type && msg.chat_type !== 'world'" class="chat-type-badge" :class="'chat-type-' + msg.chat_type">
								{{ getChatTypeName(msg.chat_type) }}
							</view>
						</view>
						<view class="chat-content">{{ msg.content }}</view>
					</view>
					<view v-if="chatMessages.length === 0" class="no-messages">
						<text>暂无聊天消息</text>
					</view>
				</view>
				<view class="chat-input-area">
					<view class="chat-type-selector">
						<picker
							:value="chatTypeIndex"
							:range="chatTypeNames"
							@change="onChatTypeChange"
							class="chat-type-picker"
						>
							<view class="chat-type-display">
								<text>{{ getCurrentChatTypeName() }}</text>
								<text class="picker-arrow">▼</text>
							</view>
						</picker>
					</view>
					<view class="chat-input-row">
						<input
							v-if="currentChatType === 'private'"
							class="chat-target-input"
							v-model="chatTargetName"
							placeholder="目标玩家名"
							maxlength="20"
						/>
						<input
							class="chat-input"
							v-model="chatInputText"
							:placeholder="getChatPlaceholder()"
							@confirm="sendChatMessage"
							maxlength="100"
						/>
						<button class="chat-send-btn" @click="sendChatMessage" :disabled="!chatInputText.trim() || (currentChatType === 'private' && !chatTargetName.trim())">发送</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 玩家交互弹窗 -->
		<view v-if="showPlayerMenuModal" class="player-menu-modal-mask" @click="showPlayerMenuModal = false">
			<view class="player-menu-modal" @click.stop>
				<view class="player-menu-header">
					<text class="player-menu-title">{{ selectedPlayer.name || selectedPlayer.character_name }}</text>
					<button class="player-menu-close" @click="showPlayerMenuModal = false">×</button>
				</view>
				<view class="player-menu-info">
					<text class="player-menu-level">等级: {{ selectedPlayer.level || '未知' }}</text>
					<text class="player-menu-status">状态: {{ getPlayerStatusText(selectedPlayer.status) }}</text>
				</view>
				<view class="player-menu-actions">
					<button class="player-action-btn attack" @click="playerAction('sneak_attack')">偷袭</button>
					<button class="player-action-btn give" @click="playerAction('give')">给与</button>
					<button class="player-action-btn steal" @click="playerAction('steal')">偷窃</button>
					<button class="player-action-btn view" @click="playerAction('view')">查看</button>
				</view>
			</view>
		</view>

		<!-- 右侧NPC按钮 -->
		<view
			v-if="displayMapNpcs.length > 0"
			class="npc-sidebar-btn"
			:style="{ top: npcBtnTop }"
			@touchstart="onNpcBtnTouchStart"
			@touchmove="onNpcBtnTouchMove"
			@touchend="onNpcBtnTouchEnd"
			@longpress="onNpcBtnLongPress"
			@click="npcSidebarVisible = true"
		>
			<text>NPC</text>
		</view>

		<!-- 右侧侠客按钮 -->
		<view
			class="player-sidebar-btn"
			:style="{ top: playerBtnTop }"
			@touchstart="onPlayerBtnTouchStart"
			@touchmove="onPlayerBtnTouchMove"
			@touchend="onPlayerBtnTouchEnd"
			@longpress="onPlayerBtnLongPress"
			@click="showPlayerSidebar"
		>
			<text>侠客</text>
		</view>
		<!-- 侧边栏遮罩 -->
		<view v-if="npcSidebarVisible" class="npc-sidebar-mask" @click="npcSidebarVisible = false"></view>
		<!-- 侧边栏内容 -->
		<view class="npc-sidebar" :class="{ 'npc-sidebar-show': npcSidebarVisible }">
			<view class="npc-sidebar-header">
				<text>功能NPC</text>
				<button class="npc-sidebar-close" @click="npcSidebarVisible = false">×</button>
			</view>
			<view class="npc-sidebar-list">
				<view class="npc-item" v-for="npc in displayMapNpcs" :key="npc.id" @click="showNpcMenu(npc)">
					<image class="npc-avatar" :src="npc.avatar" mode="aspectFill" />
					<text class="npc-name">{{ npc && npc.name ? npc.name : '' }}</text>
					<text class="npc-desc" v-if="npc.desc">{{ npc.desc }}</text>
				</view>
			</view>
		</view>

		<!-- 侠客侧边栏遮罩 -->
		<view v-if="playerSidebarVisible" class="player-sidebar-mask" @click="playerSidebarVisible = false"></view>
		<!-- 侠客侧边栏内容 -->
		<view class="player-sidebar" :class="{ 'player-sidebar-show': playerSidebarVisible }">
			<view class="player-sidebar-header">
				<text>在线侠客 ({{ mapPlayers.length }})</text>
				<button class="player-sidebar-close" @click="playerSidebarVisible = false">×</button>
			</view>
			<view class="player-sidebar-list">
				<view
					class="player-item"
					:class="{ 'player-item-self': player.isSelf }"
					v-for="player in mapPlayers"
					:key="player.id"
					@click="player.isSelf ? null : showPlayerMenu(player)"
				>
					<image class="player-avatar" :src="player.avatar || '/static/npc/default.png'" mode="aspectFill" />
					<view class="player-info">
						<text class="player-name" :class="{ 'player-name-self': player.isSelf }">
							{{ player.name || player.character_name }}
							<text v-if="player.isSelf" class="self-tag">（我）</text>
						</text>
						<text class="player-level">等级: {{ player.level || '未知' }}</text>
						<text class="player-status" :class="player.status">{{ getPlayerStatusText(player.status) }}</text>
					</view>
				</view>
				<view v-if="mapPlayers.length === 0" class="no-players">
					<text>当前地图暂无侠客</text>
				</view>
			</view>
		</view>

		<!-- 左侧按钮 -->
		<view
			class="left-menu-btn"
			:style="{ top: leftBtnTop }"
			@touchstart="onLeftBtnTouchStart"
			@touchmove="onLeftBtnTouchMove"
			@touchend="onLeftBtnTouchEnd"
			@longpress="onLeftBtnLongPress"
			@click="toggleLeftMenu"
		>
			<text>快捷</text>
		</view>

		<!-- 左侧聊天按钮 -->
		<view
			class="left-chat-btn"
			:style="{ top: chatBtnTop }"
			@touchstart="onChatBtnTouchStart"
			@touchmove="onChatBtnTouchMove"
			@touchend="onChatBtnTouchEnd"
			@longpress="onChatBtnLongPress"
			@click="openChat"
		>
			<text>💬</text>
		</view>
		<!-- 左侧横向菜单条 -->
		<view
			v-if="leftMenuVisible"
			class="left-menu-bar"
			:style="{ top: leftBtnTop }"
			@click.stop
		>
			<button class="left-menu-btn-item" @click="onMenuClick('healing')">
				<text class="left-menu-btn-icon">🩺</text>
				<text class="left-menu-btn-text">疗伤</text>
			</button>
			<button class="left-menu-btn-item" @click="onMenuClick('meditation')">
				<text class="left-menu-btn-icon">🧘</text>
				<text class="left-menu-btn-text">打坐</text>
			</button>
			<button class="left-menu-btn-item" @click="onMenuClick('ranking')">
				<text class="left-menu-btn-icon">🏆</text>
				<text class="left-menu-btn-text">排行榜</text>
			</button>
			<button class="left-menu-btn-item" @click="onMenuClick('redeem')">
				<text class="left-menu-btn-icon">🎁</text>
				<text class="left-menu-btn-text">兑换码</text>
			</button>
		</view>
		<!-- 菜单外部遮罩，点击收起 -->
		<view v-if="leftMenuVisible" class="left-menu-mask" @click="leftMenuVisible = false"></view>
	</view>

	<!-- 排行榜弹窗 -->
	<view v-if="showRankingModal" class="modal-overlay" @click="showRankingModal = false">
		<view class="ranking-modal" @click.stop>
			<view class="modal-header">
				<text class="modal-title">🏆 排行榜</text>
				<text class="modal-close" @click="showRankingModal = false">×</text>
			</view>

			<!-- 排行榜类型切换 -->
			<view class="ranking-tabs">
				<view
					class="ranking-tab"
					:class="{ active: rankingCurrentTab === 'wealth' }"
					@click="switchRankingTab('wealth')"
				>
					<text>💰 富豪榜</text>
				</view>
				<view
					class="ranking-tab"
					:class="{ active: rankingCurrentTab === 'experience' }"
					@click="switchRankingTab('experience')"
				>
					<text>⭐ 经验榜</text>
				</view>
				<view
					class="ranking-tab"
					:class="{ active: rankingCurrentTab === 'adventure' }"
					@click="switchRankingTab('adventure')"
				>
					<text>🗡️ 肝帝榜</text>
				</view>
			</view>

			<!-- 排行榜内容 -->
			<view class="ranking-content">
				<view v-if="rankingLoading" class="loading-text">加载中...</view>
				<view v-else-if="rankingList.length === 0" class="empty-text">暂无排行数据</view>
				<view v-else class="ranking-list">
					<view
						v-for="(item, index) in rankingList"
						:key="item.id || index"
						class="ranking-item"
						:class="{
							'current-player': item.isCurrentPlayer,
							'top-rank': index < 3,
							'first-place': index === 0,
							'second-place': index === 1,
							'third-place': index === 2
						}"
					>
						<view class="rank-number" :class="{ 'top-three': index < 3 }">
							<text v-if="index === 0" class="rank-icon gold">🥇</text>
							<text v-else-if="index === 1" class="rank-icon silver">🥈</text>
							<text v-else-if="index === 2" class="rank-icon bronze">🥉</text>
							<text v-else class="rank-text">{{ index + 1 }}</text>
						</view>
						<view class="player-info">
							<text class="player-name">{{ item.name }}</text>
						</view>
						<view class="ranking-value">
							<text v-if="rankingCurrentTab === 'wealth'">{{ formatMoney(item.money) }}</text>
							<text v-else-if="rankingCurrentTab === 'experience'">{{ formatNumber(item.experience) }}</text>
							<text v-else-if="rankingCurrentTab === 'adventure'">{{ formatNumber(item.adventureCount) }}次</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 我的排名 -->
			<view v-if="myRanking" class="my-ranking">
				<text class="my-ranking-title">我的排名：第{{ myRanking.rank }}名</text>
				<text class="my-ranking-value">
					<text v-if="rankingCurrentTab === 'wealth'">{{ formatMoney(myRanking.money) }}</text>
					<text v-else-if="rankingCurrentTab === 'experience'">{{ formatNumber(myRanking.experience) }}</text>
					<text v-else-if="rankingCurrentTab === 'adventure'">{{ formatNumber(myRanking.adventureCount) }}次</text>
				</text>
			</view>
		</view>
	</view>

	<!-- 兑换码弹窗 -->
	<view v-if="showRedeemModal" class="modal-overlay" @click="showRedeemModal = false">
		<view class="redeem-modal" @click.stop>
			<view class="modal-header">
				<text class="modal-title">🎁 兑换码</text>
				<text class="modal-close" @click="showRedeemModal = false">×</text>
			</view>

			<!-- 兑换码输入 -->
			<view class="redeem-input-section">
				<input
					class="redeem-input"
					v-model="redeemCode"
					placeholder="请输入兑换码"
					:disabled="redeemLoading"
				/>
				<button
					class="redeem-btn"
					:class="{ disabled: !redeemCode.trim() || redeemLoading }"
					@click="submitRedeemCode"
					:disabled="!redeemCode.trim() || redeemLoading"
				>
					<text v-if="redeemLoading">兑换中...</text>
					<text v-else>兑换</text>
				</button>
			</view>

			<!-- 兑换提示 -->
			<view class="redeem-tips">
				<text class="tips-item">• 每个兑换码只能使用一次</text>
				<text class="tips-item">• 兑换码有有效期限制</text>
				<text class="tips-item">• 请确保输入正确的兑换码</text>
			</view>


		</view>
	</view>
</template>

<script>
import gameState from '../../utils/gameState.js'
import { gameUtils } from '../../utils/gameData.js'
import wsManager from '../../utils/websocket.js'
import GatheringPopup from '../../components/GatheringPopup.vue'
import BattlePopup from '../../components/BattlePopup.vue'

	export default {
		components: { GatheringPopup, BattlePopup },
		data() {
			return {
			player: {},
			money: 0,
			gold: 0,
			status: 'normal',
			eventLog: [],
			requiredExp: 0,
			connectionStatus: '未连接',
			isAuthed: false,
			showGatheringPopup: false,
			gatheringEvent: null,
			gatheringTimes: 0,
			gatheringResult: '',
			lastGatheringResultTime: 0, // 防止重复处理采集结果
			currentMap: null,
			showMapPopup: false,
			announcementText: '欢迎来到仗剑江湖行！系统运行正常，祝您游戏愉快！新版本已上线，新增多种武功秘籍和装备道具，快来体验吧！',
			mapNpcs: [],
			showNpcMenuModal: false,
			selectedNpc: {},
			mapsConfig: {},

			// 购买弹窗相关
			showBuyModal: false,
			buyItem: {},
			buyQuantity: 1,
			buyNpcName: '',
			showBattlePopup: false,
			battleLog: [],

			// 排行榜弹窗
			showRankingModal: false,
			rankingCurrentTab: 'wealth',
			rankingLoading: false,
			rankingList: [],
			myRanking: null,

			// 兑换码弹窗
			showRedeemModal: false,
			redeemCode: '',
			redeemLoading: false,

			// 排行榜弹窗
			showRankingModal: false,
			rankingCurrentTab: 'wealth',
			rankingLoading: false,
			rankingList: [],
			myRanking: null,
			battlePlayer: {},
			battleMonster: {},
			battleStage: '',
			battleAttackMode: '',
			healingMeditationMessages: [],
			healingMeditationLoading: false, // 新增：疗伤/打坐loading
			npcSidebarVisible: false, // 新增：NPC侧边栏显示状态
			npcBtnTop: '70%', // NPC按钮初始top，调整为屏幕下半部分
			npcBtnDragging: false,
			npcBtnStartY: 0,
			npcBtnStartTop: 0,

			// 侠客相关
			playerSidebarVisible: false, // 侠客侧边栏显示状态
			playerBtnTop: '60%', // 侠客按钮初始位置
			playerBtnDragging: false,
			playerBtnStartY: 0,
			playerBtnStartTop: 0,
			mapPlayers: [], // 当前地图的玩家列表
			showPlayerMenuModal: false,
			selectedPlayer: {},

			// 聊天相关
			showChatPopup: false,
			chatMessages: [],
			chatInputText: '',
			chatTargetName: '',
			currentChatType: 'world',
			chatTypeIndex: 0,
			chatTypes: [
				{ value: 'world', name: '世界' },
				{ value: 'private', name: '私聊' },
				{ value: 'rumor', name: '谣言' }
			],
			leftMenuVisible: false,
			leftBtnTop: '70%', // 左侧按钮初始top
			leftBtnDragging: false,
			leftBtnStartY: 0,
			leftBtnStartTop: 0,

			// 防重复请求标志
			fetchingBonusSummary: false,
			fetchingMapNpcs: false,
			fetchingMapPlayers: false,
			isInitializing: false,

			// 聊天按钮相关
			chatBtnTop: '55%', // 聊天按钮初始位置
			chatBtnDragging: false,
			chatBtnStartY: 0,
			chatBtnStartTop: 0,
			lastBattleHpUpdate: 0, // 记录最后一次战斗血量更新时间
		}
	},
	
	computed: {
		// 获取当前背包数据
		currentInventory() {
			const inventory = gameState.inventory || [];
			console.log('当前背包数据:', inventory);
			return inventory;
		},

		// 聊天类型名称数组
		chatTypeNames() {
			return this.chatTypes.map(type => type.name);
		},

		hpPercent() {
			const hp = this.player.hp || 0;
			const maxHp = this.player.max_hp || this.player.base_max_hp || 100;
			const percent = (hp / maxHp) * 100;
			return percent;
		},
		mpPercent() {
			return this.player.mp && (this.player.max_mp || this.player.base_max_mp) ? (this.player.mp / (this.player.max_mp || this.player.base_max_mp)) * 100 : 100
		},
		staminaPercent() {
			return this.player.energy && (this.player.max_energy || this.player.base_max_energy) ? (this.player.energy / (this.player.max_energy || this.player.base_max_energy)) * 100 : 100
		},
		energyPercent() {
			return this.player.spirit && this.player.max_spirit ? (this.player.spirit / this.player.max_spirit) * 100 : 100
		},
		expPercent() {
			return this.player.exp && this.requiredExp ? (this.player.exp / this.requiredExp) * 100 : 0
		},
		connectionStatusClass() {
			const status = this.connectionStatus;
			if (status === '已连接') return 'connected';
			if (status === '未连接') return 'disconnected';
			if (status === '连接失败') return 'disconnected';
			return 'disconnected';
		},
		currentMapName() {
			const mapId = this.player && this.player.current_map;
			const mapObj = this.mapsConfig && mapId ? this.mapsConfig[mapId] : null;
			return mapObj ? (mapObj.名称 || mapObj.name) : '未知';
		},
		mapList() {
			if (!this.mapsConfig) return [];
			return Object.values(this.mapsConfig).map(m => {
				const map = { ...m };
				// NPC 字段兼容
				if (!Array.isArray(map.NPC)) {
					if (typeof map.NPC === 'string') {
						map.NPC = map.NPC.split(',').map(n => ({ 名称: n.trim() }));
					} else {
						map.NPC = [];
					}
				}
				// 怪物字段兼容
				if (!Array.isArray(map.怪物)) {
					if (typeof map.怪物 === 'string') {
						map.怪物 = map.怪物.split(',').map(n => ({ 名称: n.trim() }));
					} else {
						map.怪物 = [];
					}
				}
				if (!Array.isArray(map.monsters)) {
					if (typeof map.monsters === 'string') {
						map.monsters = map.monsters.split(',').map(n => ({ name: n.trim() }));
					} else {
						map.monsters = [];
					}
				}
				// 采集物品字段兼容
				if (!Array.isArray(map.采集物品)) {
					if (typeof map.采集物品 === 'string') {
						map.采集物品 = map.采集物品.split(',').map(item => {
							const [name, prob] = item.split(':');
							return { 物品: name.trim(), 概率: prob ? parseFloat(prob) : undefined };
						});
					} else {
						map.采集物品 = [];
					}
				}
				if (!Array.isArray(map.gather_items)) {
					if (typeof map.gather_items === 'string') {
						map.gather_items = map.gather_items.split(',').map(item => {
							const [name, prob] = item.split(':');
							return { item: name.trim(), prob: prob ? parseFloat(prob) : undefined };
						});
					} else {
						map.gather_items = [];
					}
				}
				return map;
			});
		},
		currentMapNpcsLocal() {
			const mapId = this.player && this.player.current_map;
			const map = this.mapsConfig && this.mapsConfig[mapId];
			if (!map || !Array.isArray(map.NPC)) return [];
			return map.NPC.map((name, idx) => ({
				id: `npc_${idx}`,
				name,
				avatar: 'static/npc/default.png',
				desc: `${name}：一位神秘的江湖人物。`,
				functions: [
					{ key: 'talk', label: '对话' },
					{ key: 'shop', label: '交易' }
				]
			}));
		},
		displayMapNpcs() {
			return this.mapNpcs && this.mapNpcs.length > 0 ? this.mapNpcs : this.currentMapNpcsLocal;
		},
		getStrengthBonus() {
			return this.player?.talent_bonuses?.strength?.bonus_percentage || 0;
		},
		getIntelligenceBonus() {
			return this.player?.talent_bonuses?.intelligence?.bonus_percentage || 0;
		},
		getAgilityDefenseBonus() {
			return this.player?.talent_bonuses?.agility?.defense_bonus_percentage || 0;
		},
		getConstitutionBonus() {
			// 根骨百分比加成依然由后端体力恢复详情返回，或为0
			return 0;
		},
		getConstitutionHpBonus() {
			return this.player?.talent_bonuses?.constitution?.hp_bonus_percentage || 0;
		},
		get hasTalentBonuses() {
			return (this.player?.talent_bonuses?.strength?.bonus_percentage || 0) > 0 ||
				   (this.player?.talent_bonuses?.intelligence?.bonus_percentage || 0) > 0 ||
				   (this.player?.talent_bonuses?.agility?.defense_bonus_percentage || 0) > 0 ||
				   (this.player?.talent_bonuses?.constitution?.hp_bonus_percentage || 0) > 0;
		},

		// 是否可以购买
		canBuy() {
			const totalPrice = (this.buyItem.price || 0) * this.buyQuantity;
			const hasEnoughMoney = this.money >= totalPrice;
			const hasStock = (this.buyItem.stock || 0) >= this.buyQuantity;
			const validQuantity = this.buyQuantity > 0;
			return hasEnoughMoney && hasStock && validQuantity;
		}
	},
	
	async onLoad() {
		await this.loadMapsConfig(); // 确保加载地图配置
		// 检查登录状态
		this.checkLoginAndInit();
		// 注册状态更新回调
		if (typeof gameState.onUpdate === 'function') {
			gameState.onUpdate(this.handleStateUpdate);
		}
		// 主动拉取一次数据
		this.updateData();
		// 注册全服公告监听
		wsManager.on('announcement', (data) => {
			if (data && data.content) {
				this.announcementText = data.content;
			}
		});
		
		// 添加对game_event事件的监听
		wsManager.on('game_event', this.handleGameEvent);
		
		// 获取天赋加成信息
		this.fetchBonusSummary();
		
		// 新增：onLoad时也主动拉取一次地图列表
		// this.fetchMapList(); // 移除对 fetchMapList 的依赖
		// 强制2秒后拉取一次NPC，便于调试
		setTimeout(() => {
			this.fetchMapNpcs();
		}, 2000);
		wsManager.on('encounter_monster', this.handleEncounterMonster);
		wsManager.on('battle_round', this.handleBattleRound);
		wsManager.on('battle_result', this.handleBattleResult);
		wsManager.on('player_data', this.handleBattlePlayerData);
		wsManager.on('escape_battle_result', this.handleEscapeBattleResult);
		// 只通过 Promise 处理采集结果，不使用事件监听器，避免重复调用
		console.log('📡 采集结果将通过 Promise 处理，避免重复调用');
	},
	
	onReady() {
		// 页面渲染完成
	},
	
	onShow() {
		// 防止重复初始化
		if (this.isInitializing) {
			return;
		}
		this.isInitializing = true;

		// 检查 WebSocket 连接
		if (!wsManager.isConnected) {
			wsManager.connect().then(() => {
				if (!wsManager.isAuthed && wsManager.autoAuthenticate) {
					wsManager.autoAuthenticate();
				}
				setTimeout(() => {
					if (gameState.requestAllData) gameState.requestAllData();
					this.isInitializing = false;
				}, 500);
			});
		} else if (!wsManager.isAuthed && wsManager.autoAuthenticate) {
			wsManager.autoAuthenticate();
			setTimeout(() => {
				if (gameState.requestAllData) gameState.requestAllData();
				this.isInitializing = false;
			}, 500);
		} else {
			// 已连接且已认证，只更新连接状态
			this.updateConnectionStatus && this.updateConnectionStatus();
			this.isInitializing = false;
		}

		// 获取当前地图玩家列表（只在有地图信息时）
		if (this.player && this.player.current_map && !this.fetchingMapPlayers) {
			this.fetchMapPlayers();
		}
	},
	
	onTabItemTap(item) {
		// 移除自动触发闯江湖的逻辑，让用户主动点击页面内的按钮才触发
		if (item.index === 2) { // 闯按钮的索引是2
			// 检测到闯按钮被点击，但不自动触发闯江湖，等待用户主动操作
		}
	},
	
	onUnload() {
		// 页面卸载时移除回调
		gameState.offUpdate(this.handleStateUpdate)
		// 新增：移除 game_event 监听，防止重复注册
		wsManager.off('game_event', this.handleGameEvent)
		// gathering_result 通过 Promise 处理，无需注销事件监听器
		// wsManager.off('map_list', this.onMapList) // 移除对 onMapList 的依赖
		wsManager.off('select_map_success', this.onSelectMapSuccess)
		wsManager.off('error', this.onMapError)
	},
	
	created() {
		// 不再直接使用wsManager.on，改为在各方法中使用gameUtils.sendMessage
	},
	
	watch: {
		'player.current_map': {
			handler(newVal, oldVal) {
				// 只有当值真正改变时才触发
				if (newVal && newVal !== oldVal && this.mapList && this.mapList.length) {
					this.fetchMapNpcs();
				}
			},
			immediate: false
		},
		mapList: {
			handler(newVal, oldVal) {
				// 只有当值真正改变且有地图数据时才触发
				if (gameState.player && gameState.player.current_map && newVal && newVal.length && newVal !== oldVal) {
					this.fetchMapNpcs();
				}
			},
			immediate: false
		}
	},
	
	methods: {
		checkLoginAndInit() {
			// 检查是否有登录信息
			const token = uni.getStorageSync('token');
			const userInfo = uni.getStorageSync('userInfo');
			
			if (!token || !userInfo) {
				uni.reLaunch({
					url: '/pages/login/login'
				});
				return;
			}
			
			this.initGame();
		},
		
		async initGame() {
			try {
				// 注册状态更新回调
				gameState.onUpdate(this.handleStateUpdate);
				
				// 初始化游戏状态
				await gameState.init();
				this.updateConnectionStatus();
				this.updateData();
			} catch (error) {
				this.connectionStatus = '连接失败';
				uni.showToast({
					title: '游戏初始化失败: ' + error.message,
					icon: 'none',
					duration: 3000
				});
			}
		},
		
		updateConnectionStatus() {
			this.connectionStatus = wsManager.isConnected ? '已连接' : '未连接';
			// 断开时自动取消认证
			if (!wsManager.isConnected) {
				this.isAuthed = false;
			}
		},
		
		handleStateUpdate(type, gameStateInstance) {
			// 根据更新类型更新对应数据
			switch (type) {
				case 'player':
					if (gameStateInstance.player) {
						// 检查是否在战斗中，如果是则保留当前的气血值
						const currentHp = this.player.hp;
						const currentMaxHp = this.player.max_hp;
						const isInBattle = this.showBattlePopup && this.battleStage === 'battle';

						this.player = { ...gameStateInstance.player };

						// 如果在战斗中，保留当前实时血量数据，避免被覆盖
						if (isInBattle) {
							if (typeof currentHp !== 'undefined' && currentHp !== null) {
								this.player.hp = currentHp;
							}
							if (typeof currentMaxHp !== 'undefined' && currentMaxHp !== null) {
								this.player.max_hp = currentMaxHp;
							}
						}
					}
					break;
				case 'currency':
					this.money = gameStateInstance.money;
					this.gold = gameStateInstance.gold;
					break;
				case 'status':
					this.status = gameStateInstance.status;
					break;
				case 'eventLog':
					this.eventLog = gameStateInstance.eventLog.map((log, idx) => {
						if (idx === 0) {
							return { ...log, displayText: '' };
						} else {
							return { ...log, displayText: log.description };
						}
					});
					if (this.eventLog.length > 0) {
						this.typeWriterEffect(this.eventLog[0], 0);
					}
					break;
				case 'auth':
					this.isAuthed = gameStateInstance.isAuthed || false;
					// 新增：认证成功后自动拉取地图列表
					if (this.isAuthed) {
						// this.fetchMapList(); // 移除对 fetchMapList 的依赖
					}
					break;
				default:
					this.updateData();
			}
			
			// 确保认证状态始终同步（优先使用WebSocket管理器的状态）
			if (type !== 'auth') {
				this.isAuthed = wsManager.isAuthed || gameStateInstance.isAuthed || false;
			}
		},
		
		updateData() {
			// 始终用最新的 gameState.player
			this.player = gameState.getPlayerData ? gameState.getPlayerData() : gameState.player;
			if (gameState.player) {
				this.player = { ...gameState.player };
				if (this.mapList && this.mapList.length && gameState.player.current_map) {
					this.currentMap = this.mapList.find(m => m.id === gameState.player.current_map) || null;
				}
			}
			// 优先从player对象获取银两和金币
			this.money = this.player.money || gameState.money || 0;
			this.gold = this.player.gold || gameState.gold || 0;
			this.status = gameState.status || 'normal';
			this.eventLog = [...(gameState.eventLog || [])];
			
			// 获取天赋加成信息
			this.fetchBonusSummary();
			
			// 获取地图NPC信息
			this.fetchMapNpcs();
		},
		
		formatNumber(num) {
			return gameUtils.formatNumber(num)
		},
		
		getStatusText() {
			const statusTexts = {
				'injured': '重伤',
				'internal_injury': '内伤',
				'poisoned': '中毒',
				'tired': '疲劳'
			}
			return statusTexts[this.status] || '正常'
		},
		
		triggerAdventure() {
			if (!this.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' });
				return;
			}
			if (!wsManager.isConnected) {
				uni.showToast({ title: '网络连接失败', icon: 'none' });
				return;
			}
			gameState.triggerAdventure();
		},
		
		clearLog() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空江湖日志吗？',
				success: (res) => {
					if (res.confirm) {
						gameState.eventLog = []
						this.eventLog = []
						gameState.save()
					}
				}
			})
		},
		
		navigateTo(path) {
			uni.navigateTo({
				url: path
			})
		},
		
		testConnection() {
			// 检查本地存储
			const token = uni.getStorageSync('token');
			const userInfo = uni.getStorageSync('userInfo');
			
			// 显示当前状态
			const statusInfo = `WebSocket连接: ${wsManager.isConnected ? '已连接' : '未连接'}
服务器地址: ${wsManager.serverUrl}
WebSocket认证: ${wsManager.isAuthed ? '已认证' : '未认证'}
页面认证: ${this.isAuthed ? '已认证' : '未认证'}
GameState认证: ${gameState.isAuthed ? '已认证' : '未认证'}
本地token: ${token ? '存在' : '不存在'}
本地userInfo: ${userInfo ? '存在' : '不存在'}
玩家数据: ${gameState.player ? '存在' : '不存在'}
连接状态: ${this.connectionStatus}`;
			
			uni.showModal({
				title: '连接状态详情',
				content: statusInfo,
				showCancel: true,
				cancelText: '手动认证',
				confirmText: '确定',
				success: (res) => {
					if (res.cancel) {
						// 手动触发认证
						wsManager.autoAuthenticate();
					}
				}
			});
			
			// 如果未连接，尝试重新连接
			if (!wsManager.isConnected) {
				wsManager.connect().then(() => {
					this.updateConnectionStatus();
				}).catch(error => {
				});
			}
		},
		
		// 新增：根据装备动态计算可采集次数
		getGatherToolInfo(requiredTool, requiredLevel = 1) {
			console.log('查找工具:', requiredTool, '需要等级:', requiredLevel);

			// 工具类型映射
			const toolTypeMap = {
				'镰刀': 'sickle',
				'斧头': 'axe',
				'矿镐': 'pickaxe',
				'小刀': 'knife',
				'sickle': 'sickle',
				'axe': 'axe',
				'pickaxe': 'pickaxe',
				'knife': 'knife'
			};

			// 查找所有匹配的工具
			const findAllTools = (items) => {
				if (!Array.isArray(items)) return [];

				return items.filter(item => {
					if (!item) return false;

					// 直接名称匹配
					if (item.name === requiredTool || item.id === requiredTool) {
						console.log('找到直接匹配工具:', item);
						return true;
					}

					// 类型匹配
					const requiredType = toolTypeMap[requiredTool];
					if (requiredType && item.type === requiredType) {
						console.log('找到类型匹配工具:', item, '需要类型:', requiredType);
						return true;
					}

					// 名称包含匹配
					if (item.name && item.name.includes(requiredTool)) {
						console.log('找到名称包含匹配工具:', item);
						return true;
					}

					return false;
				});
			};

			// 收集所有可用工具
			let allTools = [];

			// 先查装备栏
			if (gameState.equipment) {
				allTools = allTools.concat(findAllTools(Object.values(gameState.equipment)));
			}

			// 再查背包
			if (gameState.inventory) {
				allTools = allTools.concat(findAllTools(gameState.inventory));
			}

			if (allTools.length === 0) {
				console.log('未找到匹配工具');
				return null;
			}

			// 过滤出等级足够的工具
			const validTools = allTools.filter(tool => {
				const toolLevel = parseInt(tool.level || 1);
				const isValid = toolLevel >= requiredLevel;
				console.log(`工具 ${tool.name} 等级${toolLevel} ${isValid ? '✅' : '❌'} 需要等级${requiredLevel}`);
				return isValid;
			});

			if (validTools.length === 0) {
				console.log('没有等级足够的工具');
				return null;
			}

			// 选择等级最高的工具
			const bestTool = validTools.reduce((best, current) => {
				const bestLevel = parseInt(best.level || 1);
				const currentLevel = parseInt(current.level || 1);
				return currentLevel > bestLevel ? current : best;
			});

			console.log('选择最高级工具:', bestTool, '等级:', bestTool.level);
			return bestTool;
		},

		// 工具等级与可采集次数映射
		getGatherTimesByTool(tool) {
			if (!tool) return 0;
			// 与后端一致，直接读取工具的 gather_times 属性
			return tool.gather_times || 1;
		},

		handleGameEvent(data) {
			console.log('处理游戏事件:', data);

			const toolField = data.requiredTool || data.toolType || data.tool || data.gatherTool || '';
			const gatherType = data.gatherType || data.type || '';
			const hasToolField = !!(data.requiredTool || data.toolType || data.tool || data.gatherTool);
			const isGatherPoint =
				data.eventType === 'gathering' ||
				data.type === 'gathering' ||
				data.type === 3 ||
				gatherType === 'gathering' ||
				gatherType === 'gather' ||
				/(采集点|可以采集|发现资源|发现矿脉|发现草药|资源地.*可以采集|可采集)/.test(data.content) ||
				hasToolField;
			const isGatherResult = /采集到|获得了|挖到了|收获了/.test(data.content);

			console.log('🔍 事件判断:', { isGatherPoint, isGatherResult, toolField, gatherType });

			if (isGatherPoint && !isGatherResult) {
				console.log('✅ 确认为采集点事件，开始处理');
				// 采集点事件：先添加到江湖日志，然后显示采集弹窗
				const gatherEventLog = {
					timestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
					name: '采集事件',
					description: data.content || '你发现了可采集的资源。',
					displayText: data.content || '你发现了可采集的资源。'
				};

				// 添加到江湖日志
				gameState.eventLog.unshift(gatherEventLog);
				if (gameState.eventLog.length > 50) gameState.eventLog = gameState.eventLog.slice(0, 50);
				gameState.notifyUpdate('eventLog');

				// 准备采集事件数据
				console.log('🔧 准备采集数据，toolField:', toolField);
				console.log('🔧 事件数据:', data);
				console.log('🔧 资源名称:', data.resource, data.fixedResource);
				const resourceLevel = data.resourceLevel || 1;
				console.log('🔧 采集品等级要求:', resourceLevel);
				const tool = this.getGatherToolInfo(toolField || 'hoe', resourceLevel);
				console.log('🔧 找到的工具:', tool);
				const maxTimes = this.getGatherTimesByTool(tool);
				console.log('🔧 计算的采集次数:', maxTimes);
				const toolName = tool ? tool.name : (toolField || '采集工具');
				const requiredToolDesc = data.requiredToolDesc || `${toolField}(${resourceLevel}级以上)`;
				this.gatheringEvent = {
					content: data.content || '你发现了可采集的资源。',
					gatherType,
					requiredTool: toolField || 'hoe',
					requiredToolDesc: requiredToolDesc,
					resourceLevel: resourceLevel,
					gatherTimes: maxTimes,
					toolName,
					resource: data.resource || data.fixedResource  // 添加资源名称
				};
				this.gatheringTimes = maxTimes;
				console.log('🔧 设置采集次数:', this.gatheringTimes);
				this.gatheringResult = '';

				// 显示采集弹窗前刷新背包数据
				wsManager.sendMessage('get_inventory_data');

				// 日志动画结束后再弹出采集窗口（类似战斗）
				this.typeWriterEffect(gatherEventLog, 0, () => {
					setTimeout(() => {
						this.showGatheringPopup = true;
					}, 500);
				});

			} else if (isGatherResult) {
				// 采集结果：现在由 handleGatheringResult 专门处理
				console.log('采集结果事件，应该由 handleGatheringResult 处理');

			} else {
				// 其他事件：正常处理
				if (gameState && typeof gameState.handleGameEvent === 'function') {
					gameState.handleGameEvent({
						type: data.type || data.eventType,
						content: data.content,
						rewards: data.rewards || {}
					});
				}
			}
		},

		doGather() {
			console.log('🎯 doGather 方法被调用');
			console.log('开始采集，检查工具...');
			console.log('当前采集事件:', this.gatheringEvent);

			// 检查采集事件是否存在
			if (!this.gatheringEvent) {
				console.error('采集事件不存在');
				this.gatheringResult = '采集事件异常，请重新触发！';
				return;
			}

			// 先刷新背包数据
			// wsManager.sendMessage('get_inventory_data'); // 暂时注释掉，看看是否影响

			// 保存当前上下文
			const gatheringEvent = this.gatheringEvent;
			const vm = this;

			// 等待一下让数据更新
			setTimeout(() => {
				const requiredTool = gatheringEvent.requiredTool;
				const resourceLevel = gatheringEvent.resourceLevel || 1;
				const tool = vm.getGatherToolInfo(requiredTool, resourceLevel);
				console.log('采集工具检查:', { requiredTool, resourceLevel, tool, inventory: gameState.inventory });
				console.log('当前采集次数:', vm.gatheringTimes);

				if (!tool) {
					console.log('❌ 没有找到合适等级的工具，停止采集');
					const requiredToolDesc = gatheringEvent.requiredToolDesc || `${requiredTool}(${resourceLevel}级以上)`;
					vm.gatheringResult = `需要${requiredToolDesc}才能采集！`;
					return;
				}
				if (vm.gatheringTimes <= 0) {
					console.log('❌ 采集次数用完，停止采集');
					vm.gatheringResult = '本次采集已完成！';
					return;
				}
				console.log('✅ 工具检查通过，准备发送采集请求');
				// 使用gameUtils.sendMessage发送采集请求
				const gatherMessage = {
					type: 'gather_action',
					data: {
						gatherType: gatheringEvent.gatherType,
						fixedResource: gatheringEvent.resource  // 传递事件指定的资源
					}
				};
				console.log('🚀 发送采集请求:', gatherMessage);
				console.log('🚀 gatheringEvent.resource:', gatheringEvent.resource);

				// 移除临时监听器，使用正式的 handleGatheringResult 方法

				// 发送采集请求并等待结果
				gameUtils.sendMessage(gatherMessage).then(response => {
					console.log('🎯 收到采集响应:', response);

					// 处理采集结果
					if (response && response.type === 'gather_action_success' && response.data) {
						console.log('🎯 直接处理采集结果');
						console.log('🎯 vm对象:', vm);
						console.log('🎯 vm.handleGatheringResult方法:', vm.handleGatheringResult);

						try {
							vm.handleGatheringResult(response);
							console.log('✅ handleGatheringResult 调用完成');
						} catch (error) {
							console.error('❌ 调用 handleGatheringResult 时出错:', error);
						}
					} else {
						console.log('❌ 响应格式不正确:', response);
					}
				}).catch(error => {
					console.error('❌ 采集请求失败:', error);
					vm.gatheringResult = '采集失败，请重试！';
				});

				// 发送请求后立即减少采集次数，防止重复点击
				vm.gatheringTimes--;
				console.log('✅ 采集请求已发送，剩余次数:', vm.gatheringTimes);

				// 如果次数用完，显示提示信息
				if (vm.gatheringTimes <= 0) {
					vm.gatheringResult = '采集中，请等待结果...';
				}
			}, 100);
		},
		
		handleGatheringResult(data) {
			try {
				// 防止重复处理同一个采集结果
				if (this.lastGatheringResultTime && Date.now() - this.lastGatheringResultTime < 1000) {
					console.log('🚫 防止重复处理采集结果，忽略此次调用');
					return;
				}
				this.lastGatheringResultTime = Date.now();

				console.log('🎯 handleGatheringResult 被调用，采用战斗结果相同的处理方式');
				console.log('🎯 收到的数据:', data);

			// 处理不同的数据格式
			let content = data.content;
			if (!content && data.data && data.data.content) {
				// 如果是 gather_action_success 格式，数据在 data.data 中
				content = data.data.content;
				console.log('🎯 使用 data.data.content:', content);
			}

			if (!content) {
				console.error('❌ 没有找到采集结果内容');
				return;
			}

			// 更新采集弹窗内容
			this.gatheringResult = content.replace(/\n/g, '\n');
			console.log('✅ 采集弹窗内容已更新:', this.gatheringResult);

			// 添加采集结果到江湖日志（和 handleBattleResult 相同的方式）
			const gatherResultLog = {
				timestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
				name: '采集结果',
				description: content,
				displayText: content
			};
			console.log('🎯 准备添加到江湖日志:', gatherResultLog);
			console.log('🎯 当前 gameState.eventLog:', gameState.eventLog);

			gameState.eventLog.unshift(gatherResultLog);
			console.log('🎯 添加后的 gameState.eventLog:', gameState.eventLog);

			if (gameState.eventLog.length > 50) gameState.eventLog = gameState.eventLog.slice(0, 50);

			console.log('🎯 调用 gameState.notifyUpdate("eventLog")');
			gameState.notifyUpdate('eventLog');
			console.log('✅ 江湖日志更新完成');

			// 更新玩家数据
			let playerData = data.player_data;
			if (!playerData && data.data && data.data.player_data) {
				playerData = data.data.player_data;
			}

			if (playerData) {
				if (playerData.inventory) {
					gameState.inventory = playerData.inventory;
					gameState.notifyUpdate('inventory');
				}
				if (playerData.gather_skills) {
					this.player.gather_skills = playerData.gather_skills;
				}
			}

			// 如果采集次数用完，延迟关闭弹窗
			if (this.gatheringTimes <= 0) {
				setTimeout(() => {
					this.closeGatheringPopup();
				}, 2000);
			}

			} catch (error) {
				console.error('❌ handleGatheringResult 执行出错:', error);
				console.error('❌ 错误堆栈:', error.stack);
			}
		},

		closeGatheringPopup() {
			console.log('🚪 closeGatheringPopup 被调用，当前次数:', this.gatheringTimes);
			// 如果还有剩余采集次数且没有采集结果，说明是放弃采集
			if (this.gatheringTimes > 0 && !this.gatheringResult && this.gatheringEvent) {
				const abandonLog = {
					timestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
					name: '放弃采集',
					description: '你放弃了这次采集机会，离开了采集点。',
					displayText: '你放弃了这次采集机会，离开了采集点。'
				};

				gameState.eventLog.unshift(abandonLog);
				if (gameState.eventLog.length > 50) gameState.eventLog = gameState.eventLog.slice(0, 50);
				gameState.notifyUpdate('eventLog');
			}

			this.showGatheringPopup = false;
			this.gatheringEvent = null;
			this.gatheringResult = '';
			this.gatheringTimes = 0;
		},
		
		// 天赋增益计算方法
		getStrengthBonus() {
			return this.player?.talent_bonuses?.strength?.bonus_percentage || 0;
		},
		
		getIntelligenceBonus() {
			return this.player?.talent_bonuses?.intelligence?.bonus_percentage || 0;
		},
		
		getAgilityDefenseBonus() {
			return this.player?.talent_bonuses?.agility?.defense_bonus_percentage || 0;
		},
		
		getConstitutionBonus() {
			// 根骨百分比加成依然由后端体力恢复详情返回，或为0
			return 0;
		},
		
		getConstitutionHpBonus() {
			return this.player?.talent_bonuses?.constitution?.hp_bonus_percentage || 0;
		},
		
		get hasTalentBonuses() {
			return (this.player?.talent_bonuses?.strength?.bonus_percentage || 0) > 0 || 
				   (this.player?.talent_bonuses?.intelligence?.bonus_percentage || 0) > 0 || 
				   (this.player?.talent_bonuses?.agility?.defense_bonus_percentage || 0) > 0 || 
				   (this.player?.talent_bonuses?.constitution?.hp_bonus_percentage || 0) > 0;
		},

		// 获取采集技能信息
		getGatherSkillInfo(gatherType) {
			const player = gameState.player || {};
			const gatherSkills = player.gather_skills || {};
			const skill = gatherSkills[gatherType] || { level: 1, exp: 0 };

			// 使用与武功系统一致的二次方升级公式：经验需求 = 系数 × (等级+1)²
			const coefficient = 60; // 采集技能系数
			const needExp = coefficient * Math.pow(skill.level + 1, 2);

			return {
				level: skill.level,
				exp: skill.exp,
				needExp: needExp,
				progress: needExp > 0 ? (skill.exp / needExp * 100).toFixed(1) : 0
			};
		},

		// 获取所有采集技能信息
		getAllGatherSkills() {
			const gatherTypes = ['mining', 'logging', 'herbalism', 'skinning'];
			const typeNames = {
				'mining': '挖矿',
				'logging': '伐木',
				'herbalism': '采药',
				'skinning': '剥皮'
			};

			return gatherTypes.map(type => ({
				type: type,
				name: typeNames[type],
				...this.getGatherSkillInfo(type)
			}));
		},

		// 获取天赋加成信息
		async fetchBonusSummary() {
			// 防止重复请求
			if (this.fetchingBonusSummary) {
				return;
			}
			this.fetchingBonusSummary = true;

			try {
				const response = await gameUtils.sendMessage({
					type: 'get_bonus_summary',
					data: {}
				});
				
				// 处理超时情况
				if (response.type === 'get_bonus_summary_timeout') {
					return;
				}
				
				// 处理成功情况
				if (response.type === 'get_bonus_summary_success' || response.type === 'bonus_summary') {
					const bonusData = response.data || {};
					
					// 更新玩家天赋加成数据
					if (!this.player.talent_bonuses) {
						this.player.talent_bonuses = {};
					}
					
					// 处理力量加成
					if (bonusData.strength) {
						this.player.talent_bonuses.strength = {
							bonus_percentage: bonusData.strength.attack_bonus || 0
						};
					}
					
					// 处理悟性加成
					if (bonusData.intelligence) {
						this.player.talent_bonuses.intelligence = {
							bonus_percentage: bonusData.intelligence.exp_bonus || 0
						};
					}
					
					// 处理身法加成
					if (bonusData.agility) {
						this.player.talent_bonuses.agility = {
							defense_bonus_percentage: bonusData.agility.defense_bonus || 0
						};
					}
					
					// 处理根骨加成
					if (bonusData.constitution) {
						this.player.talent_bonuses.constitution = {
							hp_bonus_percentage: bonusData.constitution.hp_bonus || 0
						};
					}
				}
			} catch (error) {
			} finally {
				this.fetchingBonusSummary = false;
			}
		},
		
		canEnterMap(map) {
			const player = gameState.player || {};
			// 兼容结构化进入要求
			const req = map.进入要求 || map.enter_requirements || {};
			if (req.item) {
				const inv = (player.inventory || []);
				if (!inv.some(i => i.name === req.item)) return false;
			}
			if (req.attack) {
				if ((player.attack || 0) < req.attack) return false;
			}
			// 其他条件可扩展
			return true;
		},
		async selectMap(map) {
			if (!map || !map.id) {
				uni.showToast({ title: '地图ID无效', icon: 'none' });
				return;
			}
			this.currentMap = map;
			if (!this.canEnterMap(map)) {
				uni.showToast({ title: '不满足进入条件', icon: 'none' });
				return;
			}
			
			try {
				// 显示加载提示
				uni.showLoading({
					title: '正在切换地图...',
					mask: true
				});
				
				// 使用gameUtils.sendMessage发送请求并处理响应
				const response = await gameUtils.sendMessage({
					type: 'select_map',
					data: { map_id: map.id }
				});
				
				// 隐藏加载提示
				uni.hideLoading();
				
				if (response.type === 'select_map_success') {
					this.currentMap = this.mapList.find(m => m.id === response.data.map_id);
					if (gameState.player) {
						gameState.player.current_map = response.data.map_id;
						gameState.notifyUpdate('player');
					}
					// 切换地图后主动刷新玩家数据
					this.refreshPlayerData();
					this.showMapPopup = false;
					uni.showToast({ title: '切换成功', icon: 'success' });
				} else if (response.type === 'error') {
					if (response.data && response.data.message) {
						uni.showToast({ title: response.data.message, icon: 'none' });
					}
				}
			} catch (error) {
				uni.hideLoading();
				uni.showToast({ 
					title: '切换地图失败: ' + (error.message || '未知错误'), 
					icon: 'none' 
				});
			}
		},
		
		async fetchMapNpcs() {
			// 防止重复请求
			if (this.fetchingMapNpcs) {
				return;
			}
			this.fetchingMapNpcs = true;

			try {
				const mapId = gameState.player?.current_map || (gameState.player && gameState.player.current_map);
				if (!mapId) {
					return;
				}
				
				const response = await gameUtils.sendMessage({ 
					type: 'get_map_npcs',
					data: { map_id: mapId }
				});
				
				// 处理超时情况
				if (response.type === 'get_map_npcs_timeout') {
					// 使用地图配置中的默认NPC数据
					if (this.mapsConfig && this.mapsConfig[mapId]) {
						const mapConfig = this.mapsConfig[mapId];
						if (mapConfig.NPC && Array.isArray(mapConfig.NPC)) {
							this.mapNpcs = mapConfig.NPC.map((npc, idx) => {
								// 如果NPC已经是对象格式，直接使用
								if (typeof npc === 'object') {
									return {
										id: npc.id || `npc_${idx}`,
										name: npc.名称 || npc.name || '未知NPC',
										avatar: 'static/npc/default.png',
										desc: npc.描述 || npc.desc || `${npc.名称 || npc.name || '未知NPC'}：一位神秘的江湖人物。`,
										functions: [
											{ key: 'talk', label: '对话' },
											{ key: 'shop', label: '交易' }
										]
									};
								}
								// 如果NPC是字符串格式，转换为对象
								return {
									id: `npc_${idx}`,
									name: npc,
									avatar: 'static/npc/default.png',
									desc: `${npc}：一位神秘的江湖人物。`,
									functions: [
										{ key: 'talk', label: '对话' },
										{ key: 'shop', label: '交易' }
									]
								};
							});
						}
					}
					return;
				}
				
				let npcs = [];
				if (Array.isArray(response.data)) {
					npcs = response.data;
				} else if (response.data && Array.isArray(response.data.npcs)) {
					npcs = response.data.npcs;
				} else if (response.data && response.data.data && Array.isArray(response.data.data)) {
					npcs = response.data.data;
				}
				
				if (npcs.length > 0) {
					this.mapNpcs = npcs;
				} else {
					// 使用默认NPC数据
					this.mapNpcs = [];
				}
			} catch (e) {
				if (typeof e === 'object') {
					for (const key in e) {
					}
				}
				// 出错时使用空数组
				this.mapNpcs = [];

				uni.showToast({
					title: '获取NPC数据失败',
					icon: 'none',
					duration: 2000
				});
			} finally {
				this.fetchingMapNpcs = false;
			}
		},
		showNpcMenu(npc) {
			this.selectedNpc = npc
			this.showNpcMenuModal = true
			// 自动收起NPC侧边栏
			this.npcSidebarVisible = false
		},
		closeNpcMenu() {
			this.showNpcMenuModal = false
			this.selectedNpc = {}
		},
		async onNpcFunction(func, npc) {
			// 处理NPC功能
			try {
				if (func.key === 'talk') {
					await this.handleNpcTalk(npc)
				} else if (func.key === 'shop') {
					await this.handleNpcShop(npc)
				} else if (func.key === 'sell') {
					await this.handleNpcSell(npc)
				} else if (func.key === 'transport') {
					await this.handleNpcTransport(npc)
				} else if (func.key === 'heal') {
					await this.handleNpcHeal(npc)
				} else if (func.key === 'info') {
					await this.handleNpcInfo(npc)
				} else if (func.key === 'learn') {
					await this.handleNpcLearn(npc)
				} else {
					uni.showToast({ title: `${func.label}功能暂未开放`, icon: 'none' })
				}
			} catch (e) {
				console.error('NPC功能处理错误:', e)
				uni.showToast({ title: '操作失败', icon: 'none' })
			}
			this.closeNpcMenu()
		},
		debugSendMapList() {
		},
		async loadMapsConfig() {
			this.mapsConfig = await gameState.getMapsConfig();
		},
		async refreshPlayerData() {
			// 假设有 gameState.requestAllData 方法
			if (gameState.requestAllData) {
				await gameState.requestAllData();
			} else {
				// 兼容直接请求 get_player_data
				await gameUtils.sendMessage({ type: 'get_player_data' });
			}
			this.updateData();
		},

		// NPC功能处理方法
		async handleNpcTalk(npc) {
			const response = await gameUtils.sendMessage({
				type: 'npc_function',
				npc_name: npc.name,
				function: 'talk'
			})

			if (response && response.type === 'npc_talk') {
				uni.showModal({
					title: response.data.npc_name,
					content: response.data.dialogue,
					showCancel: false
				})
			}
		},

		async handleNpcShop(npc) {
			// 直接在当前页面处理NPC商店功能
			try {
				console.log('发送NPC商店请求:', npc.name)
				const response = await gameUtils.sendMessage({
					type: 'npc_function',
					npc_name: npc.name,
					function: 'shop',
					data: { action: 'list' }
				})

				console.log('收到NPC商店响应:', response)

				// 检查响应类型和数据结构
				if (response && response.type === 'shop_items') {
					console.log('商店物品:', response.data.items)
					this.showNpcShopModal(response.data.items, npc.name)
				} else if (response && response.data && response.data.items && Array.isArray(response.data.items)) {
					// 兼容处理：如果响应类型不是shop_items但有items数组，也认为是商店数据
					console.log('兼容处理商店物品:', response.data.items)
					this.showNpcShopModal(response.data.items, npc.name)
				} else if (response && response.type === 'error') {
					console.error('NPC商店错误:', response.data.message)
					uni.showToast({
						title: response.data.message,
						icon: 'none'
					})
				} else {
					console.error('未知响应类型:', response)
					uni.showToast({
						title: `未知响应: ${response ? response.type : '无响应'}`,
						icon: 'none'
					})
				}
			} catch (error) {
				console.error('获取NPC商店失败:', error)
				uni.showToast({
					title: '获取商店信息失败',
					icon: 'none'
				})
			}
		},

		async handleNpcSell(npc) {
			// 直接处理出售功能
			uni.showModal({
				title: npc.name,
				content: '请在背包中选择要出售的物品',
				showCancel: true,
				cancelText: '取消',
				confirmText: '去背包',
				success: (res) => {
					if (res.confirm) {
						// 跳转到背包页面，传递出售模式
						uni.navigateTo({
							url: `/pages/character/backpack?mode=sell&npc=${encodeURIComponent(npc.name)}`
						})
					}
				}
			})
		},

		async handleNpcTransport(npc) {
			const response = await gameUtils.sendMessage({
				type: 'npc_function',
				npc_name: npc.name,
				function: 'transport'
			})

			if (response && response.type === 'transport_destinations') {
				this.showTransportModal(response.data.destinations)
			}
		},

		async handleNpcHeal(npc) {
			uni.showModal({
				title: '治疗服务',
				content: '是否花费100银两完全恢复生命值？',
				success: async (res) => {
					if (res.confirm) {
						const response = await gameUtils.sendMessage({
							type: 'npc_function',
							npc_name: npc.name,
							function: 'heal'
						})

						if (response && response.type === 'heal_success') {
							uni.showToast({
								title: '治疗成功',
								icon: 'success'
							})
							this.updateData()
						} else if (response && response.type === 'error') {
							uni.showToast({
								title: response.data.message,
								icon: 'none'
							})
						}
					}
				}
			})
		},

		async handleNpcInfo(npc) {
			const response = await gameUtils.sendMessage({
				type: 'npc_function',
				npc_name: npc.name,
				function: 'info'
			})

			if (response && response.type === 'info_services') {
				this.showInfoServicesModal(response.data.services)
			}
		},

		async handleNpcLearn(npc) {
			// 显示学习确认对话框
			uni.showModal({
				title: '学习读书写字',
				content: '学费：10银两\n\n学习读书写字可以增加1点经验，悟性越高获得的经验越多。如果你还没有掌握读书写字技能，第一次学习将自动掌握该技能。\n\n确定要学习吗？',
				success: async (res) => {
					if (res.confirm) {
						try {
							const response = await gameUtils.sendMessage({
								type: 'npc_function',
								npc_name: npc.name,
								function: 'learn'
							})

							if (response && response.type === 'learn_success') {
								uni.showModal({
									title: '学习成功',
									content: response.data.message,
									showCancel: false,
									success: () => {
										// 刷新玩家数据
										this.updateData()
									}
								})
							} else if (response && response.type === 'error') {
								uni.showToast({
									title: response.data.message,
									icon: 'none'
								})
							}
						} catch (error) {
							console.error('学习失败:', error)
							uni.showToast({
								title: '学习失败',
								icon: 'none'
							})
						}
					}
				}
			})
		},

		showTransportModal(destinations) {
			const items = destinations.map(dest => `${dest.map} (${dest.price}银两)`)
			uni.showActionSheet({
				itemList: items,
				success: async (res) => {
					const selectedDest = destinations[res.tapIndex]
					const response = await gameUtils.sendMessage({
						type: 'npc_function',
						npc_name: this.selectedNpc.name,
						function: 'transport',
						data: { destination: selectedDest.map }
					})

					if (response && response.type === 'transport_success') {
						uni.showToast({
							title: '传送成功',
							icon: 'success'
						})
						this.updateData()
					} else if (response && response.type === 'error') {
						uni.showToast({
							title: response.data.message,
							icon: 'none'
						})
					}
				}
			})
		},

		showInfoServicesModal(services) {
			const items = services.map(service => `${service.description} (${service.price}银两)`)
			uni.showActionSheet({
				itemList: items,
				success: async (res) => {
					const selectedService = services[res.tapIndex]
					const response = await gameUtils.sendMessage({
						type: 'npc_function',
						npc_name: this.selectedNpc.name,
						function: 'info',
						data: { info_type: selectedService.type }
					})

					if (response && response.type === 'info_success') {
						uni.showModal({
							title: '消息',
							content: response.data.message,
							showCancel: false
						})
						this.updateData()
					} else if (response && response.type === 'error') {
						uni.showToast({
							title: response.data.message,
							icon: 'none'
						})
					}
				}
			})
		},

		showNpcShopModal(items, npcName) {
			// 显示NPC商店物品列表
			console.log('显示NPC商店模态框:', npcName, items)

			if (!items || !Array.isArray(items) || items.length === 0) {
				console.error('商店物品数据无效:', items)
				uni.showToast({
					title: '该NPC没有商品出售',
					icon: 'none'
				})
				return
			}

			// 使用后端返回的物品名称和价格
			const itemList = items.map(item => {
				const itemName = item.name || item.item_id
				const price = item.price || 0
				return `${itemName} - ${price}银两 (库存:${item.stock})`
			})

			console.log('商店物品列表:', itemList)

			uni.showActionSheet({
				itemList: itemList.slice(0, 6), // 微信小程序最多显示6个选项
				success: async (res) => {
					console.log('选择了物品:', res.tapIndex, items[res.tapIndex])
					const selectedItem = items[res.tapIndex]
					await this.buyNpcItem(selectedItem, npcName)
				},
				fail: (err) => {
					console.log('用户取消选择:', err)
				}
			})
		},

		getItemName(itemId) {
			// 物品ID到中文名称的映射
			const itemNames = {
				// 药品类
				'heal_potion': '治疗药水',
				'mana_potion': '法力药水',
				'antidote': '解毒剂',
				'strength_pill': '力量丹',
				'agility_pill': '敏捷丹',
				'intelligence_pill': '智力丹',
				'great_heal_potion': '大治疗药水',
				'exp_pill': '经验丹',
				'breakthrough_pill': '突破丹',
				'immortal_pill': '仙丹',

				// 装备类
				'iron_sword': '铁剑',
				'steel_sword': '钢剑',
				'iron_armor': '铁甲',
				'steel_armor': '钢甲',
				'iron_helmet': '铁盔',
				'steel_helmet': '钢盔',

				// 日用品类
				'bread': '面包',
				'water': '清水',
				'rope': '绳索',
				'torch': '火把',
				'map_scroll': '地图卷轴',
				'teleport_scroll': '传送卷轴',

				// 药材类
				'common_herb': '普通草药',
				'rare_herb': '稀有草药',
				'ginseng': '人参',
				'lingzhi': '灵芝'
			}

			return itemNames[itemId] || itemId
		},

		async buyNpcItem(item, npcName) {
			// 设置购买弹窗数据
			this.buyItem = item;
			this.buyNpcName = npcName;
			this.buyQuantity = 1;
			this.showBuyModal = true;
		},

		// 关闭购买弹窗
		closeBuyModal() {
			this.showBuyModal = false;
			this.buyItem = {};
			this.buyNpcName = '';
			this.buyQuantity = 1;
		},

		// 减少购买数量
		decreaseQuantity() {
			if (this.buyQuantity > 1) {
				this.buyQuantity--;
			}
		},

		// 增加购买数量
		increaseQuantity() {
			const maxQuantity = this.buyItem.stock || 0;
			if (this.buyQuantity < maxQuantity) {
				this.buyQuantity++;
			}
		},

		// 数量输入处理
		onQuantityInput(e) {
			const value = parseInt(e.detail.value) || 1;
			const maxQuantity = this.buyItem.stock || 0;
			this.buyQuantity = Math.max(1, Math.min(value, maxQuantity));
		},

		// 确认购买
		async confirmBuy() {
			if (!this.canBuy) {
				uni.showToast({
					title: '无法购买',
					icon: 'none'
				});
				return;
			}

			try {
				const response = await gameUtils.sendMessage({
					type: 'npc_function',
					npc_name: this.buyNpcName,
					function: 'shop',
					data: {
						action: 'buy',
						item_id: this.buyItem.item_id,
						quantity: this.buyQuantity
					}
				});

				if (response && response.type === 'buy_success') {
					uni.showToast({
						title: '购买成功',
						icon: 'success'
					});
					// 刷新玩家数据
					this.updateData();
					this.closeBuyModal();
				} else if (response && response.type === 'error') {
					uni.showToast({
						title: response.data.message,
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('购买失败:', error);
				uni.showToast({
					title: '购买失败',
					icon: 'none'
				});
			}
		},
		// 可在需要时通过 this.mapsConfig[mapId] 获取地图详情


		// 获取地图NPC列表
		getMapNpcs(map) {
			if (!map) return [];

			// 处理多种可能的NPC数据格式
			let npcs = map.NPC || map.npcs || [];

			if (Array.isArray(npcs)) {
				return npcs.map(npc => {
					if (typeof npc === 'string') return npc;
					return npc.名称 || npc.name || npc.id || '';
				}).filter(name => name);
			}

			return [];
		},

		// 获取地图怪物列表
		getMapMonsters(map) {
			if (!map) return [];

			// 处理多种可能的怪物数据格式
			let monsters = map.怪物 || map.monsters || map.monster || [];

			if (Array.isArray(monsters)) {
				return monsters.map(monster => {
					if (typeof monster === 'string') return monster;
					return monster.名称 || monster.name || monster.id || '';
				}).filter(name => name);
			}

			return [];
		},

		// 获取地图采集物品列表
		getMapGatherItems(map) {
			if (!map) return [];

			// 处理多种可能的采集物品数据格式
			let items = map.采集物品 || map.gather_items || map.gatherItems || [];

			if (Array.isArray(items)) {
				return items.map(item => {
					if (typeof item === 'string') return item;

					const itemName = item.物品 || item.item || item.name || '';
					const prob = item.概率 || item.prob || item.probability || '';

					if (itemName && prob) {
						return `${itemName}:${prob}`;
					}
					return itemName;
				}).filter(name => name);
			}

			return [];
		},

		// 获取地图进入要求
		getMapRequirements(map) {
			if (!map) return '';

			const requirements = map.进入要求 || map.enter_requirements || map.requirements || {};

			if (typeof requirements === 'object' && requirements !== null) {
				return Object.entries(requirements)
					.map(([key, value]) => `${key}:${value}`)
					.join('、');
			}

			return '';
		},

		// 侠客按钮拖拽相关方法
		onPlayerBtnTouchStart(e) {
			this.playerBtnDragging = true;
			this.playerBtnStartY = e.touches[0].clientY;
			this.playerBtnStartTop = parseInt(this.playerBtnTop);
		},

		onPlayerBtnTouchMove(e) {
			if (!this.playerBtnDragging) return;

			const deltaY = e.touches[0].clientY - this.playerBtnStartY;
			let newTop = this.playerBtnStartTop + deltaY;

			// 限制在屏幕范围内
			const minTop = 10;
			const maxTop = 90;
			newTop = Math.max(minTop, Math.min(maxTop, newTop));

			this.playerBtnTop = newTop + '%';
		},

		onPlayerBtnTouchEnd() {
			this.playerBtnDragging = false;
		},

		onPlayerBtnLongPress() {
			// 长按可以重置位置
			this.playerBtnTop = '60%';
		},

		// 获取当前地图玩家列表
		async fetchMapPlayers() {
			// 防止重复请求
			if (this.fetchingMapPlayers) {
				return;
			}
			this.fetchingMapPlayers = true;

			try {
				console.log('fetchMapPlayers - this.player:', this.player);

				const response = await gameUtils.sendMessage({
					type: 'get_map_players',
					data: {
						map_id: this.player?.current_map || 'changan'
					}
				});

				console.log('fetchMapPlayers - response:', response);

				if (response.type === 'map_players_success') {
					const otherPlayers = response.data.players || [];

					// 构建自己的玩家信息，固定在第一位
					if (this.player) {
						const selfPlayer = {
							id: this.player.id || 'self',
							name: this.player.name || this.player.character_name || '我',
							character_name: this.player.character_name || this.player.name || '我',
							level: this.player.level || 1,
							status: this.player.status || 'online',
							avatar: '/static/npc/default.png',
							isSelf: true // 标记为自己
						};

						console.log('fetchMapPlayers - selfPlayer:', selfPlayer);
						// 将自己放在第一位，其他玩家按顺序排列
						this.mapPlayers = [selfPlayer, ...otherPlayers];
					} else {
						console.log('fetchMapPlayers - no player data, only showing other players');
						// 如果没有player数据，只显示其他玩家
						this.mapPlayers = otherPlayers;
					}

					console.log('fetchMapPlayers - final mapPlayers:', this.mapPlayers);
				}
			} catch (error) {
				console.error('获取地图玩家失败:', error);
				// 即使获取失败，也要显示自己（如果有player数据）
				if (this.player) {
					const selfPlayer = {
						id: this.player.id || 'self',
						name: this.player.name || this.player.character_name || '我',
						character_name: this.player.character_name || this.player.name || '我',
						level: this.player.level || 1,
						status: this.player.status || 'online',
						avatar: '/static/npc/default.png',
						isSelf: true // 标记为自己
					};
					this.mapPlayers = [selfPlayer];
				} else {
					this.mapPlayers = [];
				}
			} finally {
				this.fetchingMapPlayers = false;
			}
		},

		// 显示侠客侧边栏
		async showPlayerSidebar() {
			this.playerSidebarVisible = true;
			// 刷新玩家列表
			await this.fetchMapPlayers();
		},

		// 显示玩家菜单
		showPlayerMenu(player) {
			this.selectedPlayer = player;
			this.showPlayerMenuModal = true;
			this.playerSidebarVisible = false;
		},

		// 玩家状态文本
		getPlayerStatusText(status) {
			const statusMap = {
				'online': '在线',
				'busy': '忙碌',
				'battle': '战斗中',
				'meditation': '打坐中',
				'healing': '疗伤中',
				'offline': '离线'
			};
			return statusMap[status] || '未知';
		},

		// 玩家交互操作
		async playerAction(action) {
			const actionMap = {
				'sneak_attack': '偷袭',
				'give': '给与',
				'steal': '偷窃',
				'view': '查看'
			};

			try {
				const response = await gameUtils.sendMessage({
					type: 'player_action',
					data: {
						action: action,
						target_player_id: this.selectedPlayer.id,
						target_player_name: this.selectedPlayer.name || this.selectedPlayer.character_name
					}
				});

				if (response.type === 'player_action_success') {
					// 如果是偷袭且成功，触发战斗
					if (action === 'sneak_attack' && response.data.battle_started) {
						// 关闭玩家菜单
						this.showPlayerMenuModal = false;
						// 显示战斗弹窗
						this.showBattlePopup = true;
						this.battlePlayer = response.data.player;
						this.battleMonster = response.data.enemy;
						this.battleLog = response.data.battle_log || [];
						this.battleStage = response.data.stage || 'battle';
						return;
					}

					uni.showToast({
						title: response.data.message || `${actionMap[action]}成功`,
						icon: 'success'
					});
				} else if (response.type === 'error') {
					uni.showToast({
						title: response.data.message || `${actionMap[action]}失败`,
						icon: 'none'
					});
				}

				this.showPlayerMenuModal = false;
			} catch (error) {
				uni.showToast({
					title: `${actionMap[action]}失败: ${error.message}`,
					icon: 'none'
				});
			}
		},

		// 聊天相关方法
		async loadChatMessages() {
			try {
				const response = await gameUtils.sendMessage({
					type: 'get_chat_messages',
					data: {}
				});

				if (response.type === 'chat_messages_success') {
					this.chatMessages = response.data.messages || [];
					this.scrollChatToBottom();
				}
			} catch (error) {
				console.error('加载聊天消息失败:', error);
			}
		},

		// 发送聊天消息
		async sendChatMessage() {
			const content = this.chatInputText.trim();
			if (!content) return;

			// 私聊需要目标玩家名
			if (this.currentChatType === 'private' && !this.chatTargetName.trim()) {
				uni.showToast({
					title: '请输入目标玩家名',
					icon: 'none'
				});
				return;
			}

			// 简单的敏感词过滤
			const filteredContent = this.filterSensitiveWords(content);

			try {
				const messageData = {
					content: filteredContent,
					chat_type: this.currentChatType
				};

				// 如果是私聊，添加目标玩家名
				if (this.currentChatType === 'private') {
					messageData.target_name = this.chatTargetName.trim();
				}

				const response = await gameUtils.sendMessage({
					type: 'send_chat_message',
					data: messageData
				});

				if (response.type === 'chat_message_success') {
					this.chatInputText = '';
					// 私聊成功后不清空目标玩家名，方便连续私聊

					uni.showToast({
						title: '发送成功',
						icon: 'success'
					});
				} else if (response.type === 'error') {
					uni.showToast({
						title: response.data.message || '发送失败',
						icon: 'none'
					});
				}
			} catch (error) {
				uni.showToast({
					title: '发送失败: ' + error.message,
					icon: 'none'
				});
			}
		},

		// 敏感词过滤
		filterSensitiveWords(text) {
			// 基础敏感词列表
			const sensitiveWords = [
				'政治', '反动', '暴力', '色情', '赌博', '毒品', '诈骗', '外挂', '作弊',
				'习近平', '共产党', '法轮功', '台独', '藏独', '疆独', '六四', '天安门',
				'操你妈', '草你妈', '傻逼', '煞笔', '智障', '脑残', '去死', '死全家'
			];

			let filteredText = text;
			sensitiveWords.forEach(word => {
				const regex = new RegExp(word, 'gi');
				filteredText = filteredText.replace(regex, '*'.repeat(word.length));
			});

			return filteredText;
		},

		// 格式化聊天时间
		formatChatTime(timestamp) {
			const date = new Date(timestamp);
			const now = new Date();

			if (date.toDateString() === now.toDateString()) {
				// 今天的消息只显示时间
				return date.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit'
				});
			} else {
				// 其他日期显示月日时间
				return date.toLocaleString('zh-CN', {
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				});
			}
		},

		// 格式化聊天时间（包含秒）
		formatChatTimeWithSeconds(timestamp) {
			const date = new Date(timestamp);
			return date.toLocaleTimeString('zh-CN', {
				hour: '2-digit',
				minute: '2-digit',
				second: '2-digit'
			});
		},

		// 获取聊天类型名称
		getChatTypeName(chatType) {
			const typeMap = {
				'world': '世界',
				'private': '私聊',
				'rumor': '谣言',
				'system': '系统'
			};
			return typeMap[chatType] || '未知';
		},

		// 获取聊天输入框占位符
		getChatPlaceholder() {
			const placeholderMap = {
				'world': '输入世界聊天内容...',
				'private': '输入私聊内容...',
				'rumor': '输入谣言内容...'
			};
			return placeholderMap[this.currentChatType] || '输入聊天内容...';
		},

		// 获取当前聊天类型名称
		getCurrentChatTypeName() {
			const currentType = this.chatTypes.find(type => type.value === this.currentChatType);
			return currentType ? currentType.name : '世界';
		},

		// 聊天类型选择改变
		onChatTypeChange(e) {
			const index = e.detail.value;
			this.chatTypeIndex = index;
			this.currentChatType = this.chatTypes[index].value;
			// 切换类型时清空目标玩家名
			if (this.currentChatType !== 'private') {
				this.chatTargetName = '';
			}
		},

		// 滚动聊天到底部
		scrollChatToBottom() {
			this.$nextTick(() => {
				// 由于uniapp的限制，这里使用简单的方式
				// 在实际项目中可能需要使用scroll-view组件
			});
		},

		// 处理聊天消息推送
		handleChatMessage(data) {
			// 显示所有聊天消息，包括自己发送的
			this.chatMessages.push({
				sender: data.sender,
				content: data.content,
				time: data.time || Date.now(),
				chat_type: data.chat_type || 'world',
				target_name: data.target_name,
				isOwn: data.sender === (this.player.name || this.player.character_name) || data.sender === '你'
			});
			this.scrollChatToBottom();
		},

		// 处理地图玩家更新
		handleMapPlayersUpdate(data) {
			if (data.map_id === this.player.current_map) {
				this.mapPlayers = data.players || [];
			}
		},
		typeWriterEffect(log, idx, onFinish) {
			// 只对最新一条日志做打字机动画
			log.displayText = '';
			const fullText = log.description;
			
			// 将文本分割成普通字符和HTML标签
			const segments = [];
			let currentSegment = '';
			let inTag = false;
			
			for (let i = 0; i < fullText.length; i++) {
				const char = fullText[i];
				if (char === '<') {
					if (currentSegment) {
						segments.push({ type: 'text', content: currentSegment });
						currentSegment = '';
					}
					inTag = true;
					currentSegment = char;
				} else if (char === '>') {
					currentSegment += char;
					segments.push({ type: 'tag', content: currentSegment });
					currentSegment = '';
					inTag = false;
				} else {
					currentSegment += char;
				}
			}
			
			// 处理最后一段
			if (currentSegment) {
				segments.push({ type: inTag ? 'tag' : 'text', content: currentSegment });
			}
			
			let segmentIndex = 0;
			let charIndex = 0;
			
			const printNext = () => {
				if (segmentIndex < segments.length) {
					const segment = segments[segmentIndex];
					
					if (segment.type === 'tag') {
						// HTML标签一次性添加
						log.displayText += segment.content;
						segmentIndex++;
						charIndex = 0;
					} else {
						// 普通文本逐字符添加
						if (charIndex < segment.content.length) {
							log.displayText += segment.content[charIndex];
							charIndex++;
						} else {
							segmentIndex++;
							charIndex = 0;
						}
					}
					
					this.$set(this.eventLog, idx, { ...log });
					setTimeout(printNext, 30);
				} else {
					log.displayText = fullText;
					this.$set(this.eventLog, idx, { ...log });
					if (typeof onFinish === 'function') onFinish();
				}
			};
			
			printNext();
		},
		handleEncounterMonster(data) {
			// 怪物数据、attack_mode
			const monster = data.monster || {};
			// 先推送日志
			let encounterDescription = '';
			if (monster.attack_mode === 'active') {
				encounterDescription = `${monster.name || '未知怪物'}向你大喊一声扑了上来！`;
			} else {
				encounterDescription = `你遇到了${monster.name || '未知怪物'}，你要怎么办呢？`;
			}
			const encounterLog = {
				timestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
				name: '遭遇战斗',
				description: encounterDescription
			};
			gameState.eventLog.unshift(encounterLog);
			if (gameState.eventLog.length > 50) gameState.eventLog = gameState.eventLog.slice(0, 50);
			gameState.notifyUpdate('eventLog');
			// 日志动画结束后再弹出战斗窗口
			this.typeWriterEffect(encounterLog, 0, () => {
				setTimeout(() => {
				this.battleMonster = { ...monster };
				this.battlePlayer = { ...this.player };
				this.battleLog = [];
				this.battleStage = 'encounter';
				this.battleAttackMode = monster.attack_mode || 'passive';
				// 重置战斗处理标记
				this.battleResultProcessed = false;
				this.escapeProcessed = false;
				this.showBattlePopup = true;
				// 如果是主动攻击型怪物，自动开始战斗
				if (monster.attack_mode === 'active') {
					setTimeout(() => {
						gameUtils.sendMessage({ type: 'start_battle_from_encounter', data: { monster_id: this.battleMonster.id } });
						this.battleStage = 'battle';
					}, 1000);
				}
				}, 1000); // 延迟1秒后再弹出战斗窗口
			});
		},
			handleBattleAttack() {
		// 仅被动怪物遇怪阶段才需要发送攻击
		if (this.battleStage === 'encounter' && this.battleAttackMode === 'passive') {
			// 重置战斗处理标记
			this.battleResultProcessed = false;
			this.escapeProcessed = false;
			gameUtils.sendMessage({ type: 'start_battle_from_encounter', data: { monster_id: this.battleMonster.id } });
			this.battleStage = 'battle';
		}
	},
		handleBattleEscape() {
			// 防止重复发送逃跑请求
			if (this.escapeRequested) {
				return;
			}

			// 防止在已经逃跑成功后再次点击
			if (this.escapeProcessed) {
				return;
			}

			// 标记已发送逃跑请求
			this.escapeRequested = true;

			// 发送逃跑请求到后端
			gameUtils.sendMessage({ type: 'escape_battle', data: { monster_id: this.battleMonster.id } })
				.then(response => {
					// 不在这里重置标记，等收到结果后再重置
				}).catch(error => {
					this.escapeRequested = false;
					uni.showToast({
						title: '逃跑请求失败',
						icon: 'none'
					});
				});
		},
		handleBattleClose() {
			// 关闭弹窗
			this.showBattlePopup = false;

			// 清理战斗数据
			this.battleLog = [];
			this.battleStage = '';
			this.battleAttackMode = '';
			this.battlePlayer = {};
			this.battleMonster = {};

			// 重置所有处理标记
			this.escapeProcessed = false;
			this.battleResultProcessed = false;
			this.escapeRequested = false;

			// 强制更新玩家数据
			this.updateData();
		},
		handleBattleRound(data) {
			// 兼容无时间戳，补充武功、装备等详细描述
			const roundData = {
				...data,
				timestamp: data.timestamp || new Date().toLocaleTimeString('zh-CN', { hour12: false })
			};
			if (!Array.isArray(this.battleLog)) {
				this.battleLog = [];
			}
			// 只保留最近50条
			this.battleLog.push(roundData);
			if (this.battleLog.length > 50) {
				this.battleLog = this.battleLog.slice(-50);
			}
			// 新增：实时刷新头像区属性，添加时间戳确保数据新鲜度
			const updateTime = Date.now();
			if (typeof data.player_hp !== 'undefined') {
				this.player.hp = data.player_hp;
				this.battlePlayer.hp = data.player_hp;
				this.lastBattleHpUpdate = updateTime; // 记录最后更新时间
			}
			if (typeof data.player_max_hp !== 'undefined') {
				this.player.max_hp = data.player_max_hp;
				this.battlePlayer.max_hp = data.player_max_hp;
			}
			if (typeof data.player_mp !== 'undefined') this.player.mp = data.player_mp;
			if (typeof data.player_max_mp !== 'undefined') this.player.max_mp = data.player_max_mp;
			if (typeof data.enemy_hp !== 'undefined') this.battleMonster.hp = data.enemy_hp;
			if (typeof data.enemy_max_hp !== 'undefined') this.battleMonster.max_hp = data.enemy_max_hp;
		},
		handleBattleResult(data) {
			// 防止重复处理
			if (this.battleResultProcessed) {
				return;
			}

			// 检查是否已经逃离战斗
			if (this.escapeProcessed) {
				return;
			}

			// 标记已处理
			this.battleResultProcessed = true;
			
			// 设置战斗阶段为结束
			this.battleStage = 'end';
			
			// 添加战斗结果到江湖日志
			const battleResultLog = {
				timestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
				name: '战斗结束',
				description: data.win ? 
					`你击败了${this.battleMonster.name || '怪物'}，战斗胜利！` : 
					`你被${this.battleMonster.name || '怪物'}击败了，战斗失败！`
			};
			gameState.eventLog.unshift(battleResultLog);
			if (gameState.eventLog.length > 50) gameState.eventLog = gameState.eventLog.slice(0, 50);
			gameState.notifyUpdate('eventLog');
			
			// 处理战斗奖励和结果显示
			if (data.win && data.rewards) {
				this.showBattleRewards(data.rewards);
			} else if (!data.win) {
				this.showBattleFailure();
			}
		},
		// 显示战斗胜利奖励
		showBattleRewards(rewards) {
			let rewardText = '战斗胜利！获得奖励：';

			// 显示历练值奖励
			if (rewards['历练值']) {
				rewardText += `\n历练值 +${rewards['历练值']}`;
			}

			// 显示银两奖励
			if (rewards['银两']) {
				rewardText += `\n银两 +${rewards['银两']}`;
			}

			// 显示武学点奖励
			if (rewards['武学点']) {
				rewardText += `\n武学点 +${rewards['武学点']}`;
			}

			// 显示物品奖励
			if (rewards['物品'] && rewards['物品'].length > 0) {
				rewardText += '\n物品：';
				rewards['物品'].forEach(item => {
					rewardText += `\n  ${item.id} x${item.quantity}`;
				});
			}

			// 如果没有任何奖励，显示默认信息
			if (rewardText === '战斗胜利！获得奖励：') {
				rewardText = '战斗胜利！但是没有获得任何奖励。';
			}
			
			// 延迟显示奖励弹窗
			setTimeout(() => {
				uni.showModal({
					title: '战斗胜利',
					content: rewardText.trim(),
					showCancel: false,
					confirmText: '确定'
				});
			}, 1000);
		},
		// 显示战斗失败
		showBattleFailure() {
			setTimeout(() => {
				uni.showModal({
					title: '战斗失败',
					content: '你被击败了，受了重伤。',
					showCancel: false,
					confirmText: '确定'
				});
			}, 1000);
		},
		handleBattlePlayerData(data) {

			// 战斗中同步玩家属性，但不覆盖更新的实时血量
			if (this.showBattlePopup && this.battleStage === 'battle') {
				const currentTime = Date.now();
				const timeSinceLastBattleUpdate = currentTime - (this.lastBattleHpUpdate || 0);

				// 保存当前实时血量
				const currentBattleHp = this.battlePlayer.hp;
				const currentPlayerHp = this.player.hp;

				this.battlePlayer = { ...this.battlePlayer, ...data };
				this.player = { ...this.player, ...data };

				// 如果最近1秒内有战斗血量更新，优先使用战斗血量
				if (timeSinceLastBattleUpdate < 1000) {
					if (typeof currentBattleHp !== 'undefined' && currentBattleHp !== null) {
						this.battlePlayer.hp = currentBattleHp;
					}
					if (typeof currentPlayerHp !== 'undefined' && currentPlayerHp !== null) {
						this.player.hp = currentPlayerHp;
					}
				}
			} else {
				// 非战斗状态正常更新
				this.player = { ...this.player, ...data };
			}


		},
		handleEscapeBattleResult(data) {
			// 重置逃跑请求标记
			this.escapeRequested = false;

			// 防止重复处理
			if (this.escapeProcessed) {
				return;
			}

			if (data.success) {
				// 标记已处理
				this.escapeProcessed = true;
				
				// 逃跑成功，添加到战斗日志
				const escapeLog = {
					round: this.battleLog.length + 1,
					attacker: '系统',
					defender: '玩家',
					damage: 0,
					desc: data.message || '你成功逃离了战斗！',
					move: '逃跑',
					martial: '',
					player_hp: this.battlePlayer.hp,
					enemy_hp: this.battleMonster.hp,
					special_effect: null,
					effect_desc: '',
					timestamp: new Date().toLocaleTimeString('zh-CN', { 
						hour12: false, 
						hour: '2-digit', 
						minute: '2-digit', 
						second: '2-digit' 
					})
				};
				this.battleLog.push(escapeLog);
				
				// 添加逃跑成功到江湖日志
				const escapeResultLog = {
					timestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
					name: '战斗结束',
					description: `你成功从${this.battleMonster.name || '怪物'}的追击中逃脱，逃之夭夭！`
				};
				gameState.eventLog.unshift(escapeResultLog);
				// 最多保留50条
				if (gameState.eventLog.length > 50) gameState.eventLog = gameState.eventLog.slice(0, 50);
				gameState.notifyUpdate('eventLog');
				
				// 延迟关闭战斗弹窗
				setTimeout(() => {
					this.showBattlePopup = false;
					// 重置标记
					this.escapeProcessed = false;
				}, 2000);
			} else {
				// 逃跑失败，添加到战斗日志
				const escapeLog = {
					round: this.battleLog.length + 1,
					attacker: '系统',
					defender: '玩家',
					damage: 0,
					desc: data.message || '你试图逃跑，但被怪物拦住了！',
					move: '逃跑',
					martial: '',
					player_hp: this.battlePlayer.hp,
					enemy_hp: this.battleMonster.hp,
					special_effect: null,
					effect_desc: '',
					timestamp: new Date().toLocaleTimeString('zh-CN', { 
						hour12: false, 
						hour: '2-digit', 
						minute: '2-digit', 
						second: '2-digit' 
					})
				};
				this.battleLog.push(escapeLog);
			}
		},
		async handleHealing() {
			try {
				this.healingMeditationMessages = [];
				this.healingMeditationLoading = true;
				uni.showLoading({ title: '正在疗伤...' });
				wsManager.sendMessage('healing', {});
			} catch (error) {
				this.healingMeditationLoading = false;
				uni.hideLoading();
				uni.showToast({ title: '疗伤失败: ' + error.message, icon: 'none' });
			}
		},
		async handleMeditation() {
			try {
				this.healingMeditationMessages = [];
				this.healingMeditationLoading = true;
				uni.showLoading({ title: '正在打坐...' });
				wsManager.sendMessage('meditation', {});
			} catch (error) {
				this.healingMeditationLoading = false;
				uni.hideLoading();
				uni.showToast({ title: '打坐失败: ' + error.message, icon: 'none' });
			}
		},
		handleHealingLog(data) {
			if (data && data.message) {
				this.healingMeditationMessages.push(data.message);
				if (this.healingMeditationMessages.length > 10) {
					this.healingMeditationMessages = this.healingMeditationMessages.slice(-10);
				}
				this.healingMeditationLoading = !/疗伤结束|收功|耗尽/.test(data.message);
				if (/疗伤结束|收功|耗尽/.test(data.message)) {
					setTimeout(() => {
						this.healingMeditationMessages = [];
						this.healingMeditationLoading = false;
					}, 1200);
				}
			}
		},
		handleMeditationLog(data) {
			if (data && data.message) {
				this.healingMeditationMessages.push(data.message);
				if (this.healingMeditationMessages.length > 10) {
					this.healingMeditationMessages = this.healingMeditationMessages.slice(-10);
				}
				this.healingMeditationLoading = !/打坐结束|收功|耗尽/.test(data.message);
				if (/打坐结束|收功|耗尽/.test(data.message)) {
					setTimeout(() => {
						this.healingMeditationMessages = [];
						this.healingMeditationLoading = false;
						// 打坐结束后自动刷新玩家数据，确保疗伤按钮可用
						this.refreshPlayerData && this.refreshPlayerData();
					}, 1200);
				}
			}
		},

		// 删除重复的 handleGatheringResult 方法，使用前面完整版本的方法
		onNpcBtnLongPress(e) {
			this.npcBtnDragging = true;
			this.npcBtnStartY = e.touches[0].clientY;
			// 解析当前top为px
			let topPx = 0;
			if (typeof this.npcBtnTop === 'string' && this.npcBtnTop.includes('%')) {
				topPx = uni.getSystemInfoSync().windowHeight * parseFloat(this.npcBtnTop) / 100;
			} else {
				topPx = parseFloat(this.npcBtnTop);
			}
			this.npcBtnStartTop = topPx;
		},
		onNpcBtnTouchMove(e) {
			if (!this.npcBtnDragging) return;
			const moveY = e.touches[0].clientY;
			let delta = moveY - this.npcBtnStartY;
			let newTop = this.npcBtnStartTop + delta;
			// 限制范围
			const minTop = 40; // px
			const maxTop = uni.getSystemInfoSync().windowHeight - 80; // px
			if (newTop < minTop) newTop = minTop;
			if (newTop > maxTop) newTop = maxTop;
			this.npcBtnTop = newTop + 'px';
		},
		onNpcBtnTouchEnd() {
			this.npcBtnDragging = false;
		},
		onLeftBtnTouchStart(e) {
			this.leftBtnDragging = true;
			this.leftBtnStartY = e.touches[0].clientY;
			let topPx = 0;
			if (typeof this.leftBtnTop === 'string' && this.leftBtnTop.includes('%')) {
				topPx = uni.getSystemInfoSync().windowHeight * parseFloat(this.leftBtnTop) / 100;
			} else {
				topPx = parseFloat(this.leftBtnTop);
			}
			this.leftBtnStartTop = topPx;
		},

		onLeftBtnLongPress() {
			// 长按可以重置位置
			this.leftBtnTop = '70%';
		},
		onLeftBtnTouchMove(e) {
			if (!this.leftBtnDragging) return;
			const moveY = e.touches[0].clientY;
			let delta = moveY - this.leftBtnStartY;
			let newTop = this.leftBtnStartTop + delta;
			const minTop = 40;
			const maxTop = uni.getSystemInfoSync().windowHeight - 80;
			if (newTop < minTop) newTop = minTop;
			if (newTop > maxTop) newTop = maxTop;
			this.leftBtnTop = newTop + 'px';
		},
		onLeftBtnTouchEnd() {
			this.leftBtnDragging = false;
		},
		toggleLeftMenu() {
			this.leftMenuVisible = !this.leftMenuVisible;
		},
		onMenuClick(type) {
			this.leftMenuVisible = false;
			if (type === 'healing') {
				this.handleHealing();
			} else if (type === 'meditation') {
				this.handleMeditation();
			} else if (type === 'ranking') {
				this.openRanking();
			} else if (type === 'redeem') {
				this.openRedeemCode();
			}
		},

		// 打开聊天
		openChat() {
			this.showChatPopup = true;
			this.loadChatMessages();
		},

		// 聊天按钮拖拽相关方法
		onChatBtnTouchStart(e) {
			this.chatBtnDragging = true;
			this.chatBtnStartY = e.touches[0].clientY;
			let topPx = 0;
			if (typeof this.chatBtnTop === 'string' && this.chatBtnTop.includes('%')) {
				topPx = uni.getSystemInfoSync().windowHeight * parseFloat(this.chatBtnTop) / 100;
			} else {
				topPx = parseFloat(this.chatBtnTop);
			}
			this.chatBtnStartTop = topPx;
		},

		onChatBtnTouchMove(e) {
			if (!this.chatBtnDragging) return;

			const deltaY = e.touches[0].clientY - this.chatBtnStartY;
			let newTop = this.chatBtnStartTop + deltaY;

			// 限制在屏幕范围内
			const minTop = 10;
			const maxTop = uni.getSystemInfoSync().windowHeight - 80;
			if (newTop < minTop) newTop = minTop;
			if (newTop > maxTop) newTop = maxTop;
			this.chatBtnTop = newTop + 'px';
		},

		onChatBtnTouchEnd() {
			this.chatBtnDragging = false;
		},

		onChatBtnLongPress() {
			// 长按可以重置位置
			this.chatBtnTop = '55%';
		},

		// 打开排行榜弹窗
		openRanking() {
			this.showRankingModal = true;
			this.loadRanking();
		},

		// 打开兑换码弹窗
		openRedeemCode() {
			this.showRedeemModal = true;
		},

		// 排行榜相关方法
		switchRankingTab(tab) {
			if (this.rankingCurrentTab !== tab) {
				this.rankingCurrentTab = tab;
				this.loadRanking();
			}
		},

		async loadRanking() {
			this.rankingLoading = true;
			try {
				const resp = await gameUtils.sendMessage({
					type: 'get_ranking',
					data: {
						type: this.rankingCurrentTab,
						limit: 30
					}
				});

				if (resp && (resp.type === 'success' || resp.type === 'get_ranking_success') && resp.data) {
					this.rankingList = resp.data.list || [];
					this.myRanking = resp.data.myRanking || null;

					// 标记当前玩家
					const currentPlayerName = gameState.player?.name || gameState.name;
					this.rankingList.forEach(item => {
						item.isCurrentPlayer = item.name === currentPlayerName;
					});
				} else {
					uni.showToast({ title: '获取排行榜失败', icon: 'none' });
				}
			} catch (error) {
				console.error('获取排行榜失败:', error);
				uni.showToast({ title: '网络错误', icon: 'none' });
			} finally {
				this.rankingLoading = false;
			}
		},

		// 兑换码相关方法
		async submitRedeemCode() {
			if (!this.redeemCode.trim()) {
				uni.showToast({ title: '请输入兑换码', icon: 'none' });
				return;
			}

			this.redeemLoading = true;
			try {
				const resp = await gameUtils.sendMessage({
					type: 'redeem_code',
					data: {
						code: this.redeemCode.trim()
					}
				});

				if (resp && resp.type === 'success') {
					// 兑换成功
					const rewards = resp.data.rewards || [];
					const rewardText = rewards.map(r => `${r.name} x${r.quantity}`).join('、');
					uni.showToast({
						title: `兑换成功！获得：${rewardText}`,
						icon: 'success',
						duration: 3000
					});

					this.redeemCode = '';
					this.refreshPlayerData();

				} else {
					const message = resp?.data?.message || '兑换失败';
					uni.showToast({ title: message, icon: 'none' });
				}
			} catch (error) {
				console.error('兑换码兑换失败:', error);
				uni.showToast({ title: '网络错误，请重试', icon: 'none' });
			} finally {
				this.redeemLoading = false;
			}
		},



		// 格式化方法
		formatMoney(money) {
			if (money >= 10000) {
				return (money / 10000).toFixed(1) + '万';
			}
			return money + '两';
		},

		formatNumber(num) {
			if (num >= 10000) {
				return (num / 10000).toFixed(1) + '万';
			}
			return num.toString();
		},

		formatTime(timestamp) {
			const date = new Date(timestamp);
			const now = new Date();
			const diff = now - date;

			if (diff < 60000) { // 1分钟内
				return '刚刚';
			} else if (diff < 3600000) { // 1小时内
				return Math.floor(diff / 60000) + '分钟前';
			} else if (diff < 86400000) { // 1天内
				return Math.floor(diff / 3600000) + '小时前';
			} else {
				return date.toLocaleDateString();
			}
		},

		addMeditationMessage(msg) {
			this.healingMeditationMessages.push(msg);
			this.$nextTick(() => {
				const msgList = this.$el.querySelector('.healing-meditation-msg-list');
				if (msgList) msgList.scrollTop = msgList.scrollHeight;
			});
		},
		endMeditation() {
			this.healingMeditationLoading = false;
			// ...其他打坐结束逻辑...
		},
		handleReconnectCloseAllPopups() {
			this.showBattlePopup = false;
			this.showGatheringPopup = false;
			this.showMapPopup = false;
			this.showNpcMenuModal = false;
			this.npcSidebarVisible = false;
			this.leftMenuVisible = false;
			this.healingMeditationMessages = [];
			// 新增的弹窗状态
			this.playerSidebarVisible = false;
			this.showPlayerMenuModal = false;
			this.showChatPopup = false;
			this.showBuyModal = false;
			// 重置按钮拖拽状态
			this.chatBtnDragging = false;
			this.leftBtnDragging = false;
			this.npcBtnDragging = false;
			this.playerBtnDragging = false;
			// 其它需要关闭的弹窗/状态可在此补充
		},
	},
	mounted() {
		// 页面挂载后自动请求地图配置
		gameState.requestMapsConfig().then(config => {
			this.mapsConfig = config;
		});
		wsManager.on('escape_battle_result', this.handleEscapeBattleResult);
		wsManager.on('healing_log', this.handleHealingLog);
		wsManager.on('meditation_log', this.handleMeditationLog);
		// gathering_result 已在 onLoad 中注册，不要重复注册
		// 新增：监听聊天和玩家相关事件
		wsManager.on('chat_message', this.handleChatMessage);
		wsManager.on('map_players_update', this.handleMapPlayersUpdate);
		// 新增：监听重连事件，关闭所有弹窗
		uni.$on && uni.$on('ws_reconnected', this.handleReconnectCloseAllPopups);
	},
	beforeDestroy() {
		// 移除WebSocket事件监听
		wsManager.off('chat_message', this.handleChatMessage);
		wsManager.off('map_players_update', this.handleMapPlayersUpdate);
		// 移除全局事件监听
		uni.$off && uni.$off('ws_reconnected', this.handleReconnectCloseAllPopups);
	}
}
</script>

<style scoped>
.container {
	padding: 15rpx;
	background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
	min-height: 100vh;
	height: 100vh;
	display: flex;
	flex-direction: column;
	gap: 10rpx;
	overflow: hidden;
	box-sizing: border-box;
}

.top-info-bar {
	display: flex;
		align-items: center;
		justify-content: center;
	flex-wrap: wrap;
	padding: 8rpx 0 0 0;
	font-size: 26rpx;
	color: #333;
	background: transparent;
	margin-bottom: 8rpx;
	line-height: 1.7;
}
.player-name {
	font-weight: bold;
	font-size: 32rpx;
	color: #4b3fa7;
	margin-right: 4rpx;
}
.player-exp, .conn-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}
.info-sep {
	margin: 0 8rpx;
	color: #bbb;
	font-size: 22rpx;
}
.conn-dot {
	display: inline-block;
	width: 14rpx;
	height: 14rpx;
	border-radius: 50%;
	margin-right: 4rpx;
	background: #bbb;
}
.conn-dot.connected { background: #27ae60; }
.conn-dot.disconnected, .conn-dot.failed { background: #e74c3c; }
.player-attr {
	font-size: 22rpx;
	color: #666;
	margin: 0 2rpx;
}
.attr-sep {
	color: #ccc;
	font-size: 20rpx;
	margin: 0 2rpx;
}

.character-card {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 15rpx;
	margin-bottom: 10rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
}

.character-header {
	display: none;
}

.progress-bar {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.progress-label {
	width: 80rpx;
	font-size: 28rpx;
	color: #666;
}

.progress-bg {
	flex: 1;
	height: 20rpx;
	background: #f0f0f0;
	border-radius: 10rpx;
	margin: 0 20rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	border-radius: 10rpx;
	transition: width 0.3s ease;
}

.hp-fill {
	background: linear-gradient(90deg, #ff6b6b, #ee5a52);
}

.mp-fill {
	background: linear-gradient(90deg, #4ecdc4, #44a08d);
}

.stamina-fill {
	background: linear-gradient(90deg, #ffe66d, #f7b731);
}

.energy-fill {
	background: linear-gradient(90deg, #a8edea, #fed6e3);
}

.progress-text {
	width: 120rpx;
	font-size: 24rpx;
	color: #666;
	text-align: right;
}

.status-display {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 15rpx;
	margin-bottom: 10rpx;
	text-align: center;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
}

.status-text {
	font-size: 32rpx;
	font-weight: bold;
}

.status-injured {
	color: #e74c3c;
}

.status-internal_injury {
	color: #9b59b6;
}

.status-poisoned {
	color: #27ae60;
}

.status-tired {
	color: #95a5a6;
}

.event-log {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 20rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
	flex: 1;
	margin-bottom: 0;
	height: 0;
	min-height: 0;
	max-height: none;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.log-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.log-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #667eea;
}

.log-clear {
	font-size: 28rpx;
	color: #764ba2;
	font-weight: 500;
}

.log-content {
	height: calc(100% - 60rpx);
	flex: 1;
	overflow-y: auto;
	-webkit-overflow-scrolling: touch;
}

.log-item {
	padding: 15rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s ease;
}

.log-item:hover {
	background-color: rgba(102, 126, 234, 0.05);
}

.log-item:last-child {
	border-bottom: none;
}

.log-header-line {
	display: flex;
	align-items: center;
	margin-bottom: 8rpx;
}

.log-content-line {
	margin-left: 0;
}

.log-time {
	font-size: 24rpx;
	color: #999;
	margin-right: 15rpx;
}

.log-event {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
}

.log-desc {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

.log-empty {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

.map-bar {
	background: linear-gradient(135deg, #667eea, #764ba2);
	padding: 12px 15px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-radius: 0 0 15px 15px;
	box-shadow: 0 4px 15px rgba(0,0,0,0.2);
	margin-bottom: 5px;
	white-space: nowrap;
	overflow: hidden;
}

.map-bar-left {
	display: flex;
	align-items: center;
	flex: 1;
	min-width: 0;
	gap: 6px;
}

.map-bar-right {
	display: flex;
	align-items: center;
	gap: 8px;
	flex-shrink: 0;
}

.user-name {
	color: #ffffff;
	font-weight: bold;
	font-size: 14px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 150px;
	flex-shrink: 0;
}

.user-label {
	color: #e8e8e8;
	margin: 0 6px;
	font-size: 12px;
}

.map-label {
	color: #e8e8e8;
	font-size: 12px;
}

.map-name {
	color: #ffd700;
	font-weight: bold;
	margin: 0 6px;
	font-size: 14px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 80px;
	flex-shrink: 0;
}

.map-btn {
	font-size: 12px;
	padding: 4px 12px;
	border-radius: 15px;
	background: rgba(255, 255, 255, 0.2);
	color: #ffffff;
	border: 1px solid rgba(255, 255, 255, 0.3);
	backdrop-filter: blur(10px);
	white-space: nowrap;
}

.connection-status {
	font-size: 12px;
	font-weight: 500;
	padding: 3px 8px;
	border-radius: 12px;
	background: rgba(255, 255, 255, 0.1);
	white-space: nowrap;
}

.connection-status.connected {
	color: #4ade80;
	background: rgba(74, 222, 128, 0.1);
	border: 1px solid rgba(74, 222, 128, 0.3);
}

.connection-status.disconnected {
	color: #f87171;
	background: rgba(248, 113, 113, 0.1);
	border: 1px solid rgba(248, 113, 113, 0.3);
}

.map-popup-mask {
	position: fixed; left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.25); z-index: 9999; display: flex; align-items: center; justify-content: center;
}
.map-popup {
	background: #fff; border-radius: 18rpx; padding: 32rpx 24rpx; min-width: 540rpx; max-width: 90vw; box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18); display: flex; flex-direction: column; align-items: center;
}
.map-popup-title { font-size: 32rpx; font-weight: bold; color: #4b3fa7; margin-bottom: 18rpx; }
.map-list { max-height: 600rpx; min-width: 480rpx; overflow-y: auto; }
.map-item { background: #f7f7f7; border-radius: 12rpx; margin-bottom: 16rpx; padding: 18rpx 16rpx; cursor: pointer; transition: background 0.2s; }
.map-item.active { background: #e6f7ff; border: 2rpx solid #4b3fa7; }
.map-item.locked { opacity: 0.5; pointer-events: none; }
.map-item-title { font-size: 28rpx; font-weight: bold; color: #333; margin-bottom: 6rpx; }
.map-item-desc { font-size: 24rpx; color: #666; margin-bottom: 4rpx; }
.map-item-npc { font-size: 22rpx; color: #27ae60; margin-bottom: 2rpx; }
.map-item-monster { font-size: 22rpx; color: #e74c3c; margin-bottom: 2rpx; }
.map-item-gather { font-size: 22rpx; color: #2980b9; margin-bottom: 2rpx; }
.map-item-req { font-size: 20rpx; color: #888; }
.close-btn { background: #eee; color: #888; font-size: 24rpx; border-radius: 24rpx; padding: 12rpx 36rpx; border: none; margin-top: 18rpx; }

.announcement-bar {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 20rpx;
	padding: 15rpx 20rpx;
	margin-bottom: 10rpx;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
	display: flex;
	align-items: center;
	gap: 15rpx;
	height: 60rpx;
	overflow: hidden;
}

.announcement-icon {
	font-size: 28rpx;
	flex-shrink: 0;
	animation: announcement-bounce 1.5s ease-in-out infinite;
}

.announcement-content {
	flex: 1;
	overflow: hidden;
}

.announcement-scroll {
		display: flex;
	align-items: center;
	height: 100%;
	animation: announcement-scroll 25s linear infinite;
	white-space: nowrap;
	animation-play-state: running;
}

.announcement-scroll:hover {
	animation-play-state: paused;
}

@keyframes announcement-scroll {
	0% {
		transform: translateX(100%);
	}
	100% {
		transform: translateX(-100%);
	}
}

.announcement-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #333;
	white-space: nowrap;
	margin-right: 50rpx;
	}

.npc-menu-modal {
	position: fixed;
	left: 0; top: 0; right: 0; bottom: 0;
	background: rgba(0,0,0,0.3);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.npc-menu-content {
	background: #fff;
	border-radius: 12px;
	padding: 24px 20px 16px 20px;
	min-width: 220px;
	max-width: 80vw;
	box-shadow: 0 2px 12px rgba(0,0,0,0.12);
	display: flex;
	flex-direction: column;
	align-items: center;
}

.npc-menu-header {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-bottom: 8px;
}

.npc-menu-avatar {
	width: 60px;
	height: 60px;
	border-radius: 50%;
	margin-bottom: 6px;
}

.npc-menu-name {
	font-size: 18px;
	font-weight: bold;
	color: #222;
}

.npc-menu-desc {
	font-size: 14px;
	color: #666;
	margin-bottom: 12px;
	text-align: center;
}

.npc-menu-list {
	width: 100%;
	margin-bottom: 10px;
}

.npc-menu-btn {
	width: 100%;
	margin-bottom: 8px;
	background: #409eff;
	color: #fff;
	border: none;
	border-radius: 6px;
	padding: 8px 0;
	font-size: 15px;
}

.npc-menu-close {
	margin-top: 6px;
	background: #eee;
	color: #333;
	border: none;
	border-radius: 6px;
	padding: 7px 0;
	width: 100%;
	font-size: 15px;
}

/* 购买弹窗样式 */
.buy-modal-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1002;
}

.buy-modal {
	background: white;
	border-radius: 20rpx;
	width: 90vw;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.buy-modal-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 30rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.buy-modal-title {
	font-size: 36rpx;
	font-weight: bold;
}

.buy-modal-close {
	background: none;
	border: none;
	color: white;
	font-size: 40rpx;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.buy-modal-content {
	padding: 40rpx 30rpx;
}

.item-info {
	margin-bottom: 30rpx;
}

.item-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 15rpx;
}

.item-details {
	display: flex;
	justify-content: space-between;
	font-size: 28rpx;
	color: #666;
}

.quantity-section {
	margin-bottom: 30rpx;
}

.quantity-label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.quantity-input-group {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20rpx;
}

.quantity-btn {
	width: 60rpx;
	height: 60rpx;
	background: #f0f0f0;
	border: none;
	border-radius: 50%;
	font-size: 32rpx;
	color: #333;
	display: flex;
	align-items: center;
	justify-content: center;
}

.quantity-btn:active {
	background: #e0e0e0;
}

.quantity-input {
	width: 120rpx;
	height: 60rpx;
	text-align: center;
	border: 2rpx solid #e0e0e0;
	border-radius: 10rpx;
	font-size: 28rpx;
}

.total-section {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 15rpx;
	margin-bottom: 30rpx;
}

.total-label {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.money-info {
	font-size: 26rpx;
	color: #666;
}

.buy-modal-actions {
	display: flex;
	gap: 20rpx;
	padding: 0 30rpx 30rpx;
}

.buy-btn-cancel, .buy-btn-confirm {
	flex: 1;
	height: 80rpx;
	border: none;
	border-radius: 15rpx;
	font-size: 30rpx;
	font-weight: bold;
}

.buy-btn-cancel {
	background: #f0f0f0;
	color: #666;
}

.buy-btn-confirm {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.buy-btn-confirm:disabled {
	background: #ccc;
	color: #999;
}

/* 天赋增益卡片样式 */
.talent-bonus-card {
	background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
	border-radius: 16rpx;
	padding: 16rpx;
	margin-bottom: 10rpx;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.1);
	border: 1px solid rgba(102, 126, 234, 0.1);
}

.talent-bonus-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #4b3fa7;
	margin-bottom: 12rpx;
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.talent-bonus-title::before {
	content: "⭐";
	font-size: 28rpx;
}

.talent-bonus-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 8rpx;
}

.talent-bonus-item {
	background: linear-gradient(135deg, #e8f4ff, #f0f8ff);
	border-radius: 8rpx;
	padding: 8rpx 12rpx;
	border: 1px solid rgba(102, 126, 234, 0.2);
	display: flex;
	flex-direction: column;
	align-items: center;
	min-width: 80rpx;
}

.talent-bonus-label {
	font-size: 22rpx;
	font-weight: bold;
	color: #4b3fa7;
	margin-bottom: 4rpx;
}

.talent-bonus-value {
	font-size: 20rpx;
	color: #27ae60;
	font-weight: 500;
}

/* 江湖按钮样式 */
.jianghu-section {
	position: fixed;
	bottom: 20rpx;
	left: 20rpx;
	right: 20rpx;
	z-index: 100;
}

.jianghu-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50rpx;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}

.jianghu-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.jianghu-btn:disabled {
	background: linear-gradient(135deg, #ccc 0%, #999 100%);
	box-shadow: none;
	opacity: 0.6;
}

.jianghu-btn-text {
	font-size: 32rpx;
	font-weight: bold;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.jianghu-btn::before {
	content: '';
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
	transition: left 0.5s;
}

.jianghu-btn:not(:disabled):hover::before {
	left: 100%;
}

.jianghu-section-flex {
  position: fixed;
  bottom: 20rpx;
  left: 20rpx;
  right: 20rpx;
  z-index: 100;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 32rpx;
}
.healing-btn {
  height: 100rpx;
  min-width: 160rpx;
  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
  border-radius: 50rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(67, 206, 162, 0.2);
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
  margin-right: 0;
}
.healing-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(67, 206, 162, 0.3);
}
.healing-btn:disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: none;
  opacity: 0.6;
}
.healing-btn-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.meditation-btn {
  height: 100rpx;
  min-width: 160rpx;
  background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);
  border-radius: 50rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.15);
  font-size: 32rpx;
  color: #fff;
  font-weight: bold;
  margin-left: 0;
}
.meditation-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.25);
}
.meditation-btn:disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: none;
  opacity: 0.6;
}
.meditation-btn-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.jianghu-section-flex-opt {
  position: fixed;
  bottom: 20rpx;
  left: 0;
  right: 0;
  z-index: 100;
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  justify-content: center;
  gap: 48rpx;
}
.side-btn {
  width: 90rpx;
  height: 90rpx;
  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
  border-radius: 50%;
  border: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(67, 206, 162, 0.15);
  transition: box-shadow 0.2s, transform 0.2s;
}
.side-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 6rpx rgba(67, 206, 162, 0.25);
}
.side-btn:disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: none;
  opacity: 0.6;
}
.side-btn-icon {
  font-size: 36rpx;
  line-height: 1;
}
.side-btn-text {
  font-size: 20rpx;
  margin-top: 2rpx;
  line-height: 1;
}
.main-btn {
  width: 160rpx;
  height: 90rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 45rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);
  margin: 0 8rpx;
  transition: box-shadow 0.2s, transform 0.2s;
}
.main-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}
.main-btn:disabled {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: none;
  opacity: 0.6;
}
.main-btn-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.healing-meditation-popup-centered {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
  min-width: 520rpx;
  max-width: 90vw;
  min-height: 80rpx;
  max-height: 420rpx;
  background: rgba(255,255,255,0.85);
  backdrop-filter: blur(12px);
  border-radius: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);
  border: 2rpx solid #e0cda2;
  padding: 24rpx 28rpx 16rpx 28rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #3e2c13;
  text-align: center;
  pointer-events: auto;
  font-family: 'STKaiti', 'KaiTi', 'FZKai-Z03', '楷体', serif;
  letter-spacing: 1.5rpx;
  animation: fadeInScale 0.3s;
}
.healing-meditation-loading {
  margin-bottom: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.spinner {
  width: 64rpx;
  height: 64rpx;
  border: 8rpx solid #e0cda2;
  border-top: 8rpx solid #bfa76a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.healing-meditation-msg-centered {
  margin-bottom: 8rpx;
  line-height: 1.8;
  word-break: break-all;
  font-size: 24rpx;
}
@keyframes fadeInScale {
  0% { opacity: 0; transform: scale(0.8) translate(-50%, -50%); }
  100% { opacity: 1; transform: scale(1) translate(-50%, -50%); }
}

.npc-sidebar-btn {
  position: fixed;
  right: 0;
  z-index: 1001;
  background: #409eff;
  color: #fff;
  border-radius: 16rpx 0 0 16rpx;
  padding: 18rpx 12rpx;
  font-size: 28rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
  transition: top 0.15s;
  user-select: none;
}
.npc-sidebar-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.18);
  z-index: 1000;
}
.npc-sidebar {
  position: fixed;
  top: 0; right: -340rpx;
  width: 340rpx;
  height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 60%, #e0e7ef 100%);
  z-index: 1002;
  box-shadow: -8rpx 0 32rpx rgba(102,126,234,0.12);
  border-radius: 24rpx 0 0 24rpx;
  transition: right 0.35s cubic-bezier(.4,0,.2,1), opacity 0.2s;
  opacity: 0.98;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.npc-sidebar-show {
  right: 0 !important;
}
.npc-sidebar-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 32rpx 18rpx 18rpx 18rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #4b3fa7;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx 0 0 0;
  box-shadow: 0 2rpx 8rpx rgba(102,126,234,0.08);
}
.npc-sidebar-close {
  position: absolute;
  right: 18rpx;
  top: 32rpx;
  background: none;
  border: none;
  font-size: 36rpx;
  color: #888;
  cursor: pointer;
  z-index: 2;
}
.npc-sidebar-list {
  flex: 1;
  overflow-y: auto;
  padding: 18rpx 12rpx;
}
.npc-item {
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(102,126,234,0.06);
  margin-bottom: 18rpx;
  padding: 18rpx 0 12rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: box-shadow 0.2s, transform 0.2s;
}
.npc-item:active {
  box-shadow: 0 4rpx 16rpx rgba(102,126,234,0.18);
  transform: scale(0.97);
}
.npc-avatar {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  border: 3rpx solid #667eea;
  box-shadow: 0 2rpx 8rpx rgba(102,126,234,0.12);
  margin-bottom: 8rpx;
  background: #f0f0f0;
}
.npc-name {
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 4rpx;
}
.npc-desc {
  font-size: 22rpx;
  color: #888;
  text-align: center;
  margin-bottom: 6rpx;
}

.left-menu-btn {
  position: fixed;
  left: 0;
  z-index: 1001;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 0 16rpx 16rpx 0;
  padding: 18rpx 12rpx;
  font-size: 28rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
  border: none;
  transition: top 0.15s;
  user-select: none;
}
.left-menu-btn:active {
  transform: scale(0.95);
}
.left-menu-bar {
  position: fixed;
  left: 90rpx;
  z-index: 1002;
  display: flex;
  flex-direction: row;
  align-items: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  border-radius: 25rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  padding: 8rpx 16rpx;
  height: 80rpx;
  min-width: 280rpx;
  animation: leftMenuPop 0.3s cubic-bezier(.4,0,.2,1);
  transition: top 0.15s;
}
@keyframes leftMenuPop {
  0% { opacity: 0; transform: scale(0.92) translateX(-24rpx);}
  100% { opacity: 1; transform: scale(1) translateX(0);}
}
.left-menu-btn-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8rpx;
  font-size: 26rpx;
  color: #333;
  font-weight: bold;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: none;
  outline: none;
  transition: all 0.3s ease;
  height: 56rpx;
  min-width: 80rpx;
  box-shadow: 0 4rpx 8rpx rgba(102, 126, 234, 0.3);
  flex-shrink: 0;
}
.left-menu-btn-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(102, 126, 234, 0.4);
}
.left-menu-btn-icon {
  font-size: 28rpx;
  margin-right: 6rpx;
}
.left-menu-btn-text {
  font-size: 24rpx;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.left-menu-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  z-index: 1000;
  background: transparent;
}
.healing-meditation-msg-list {
  max-height: 180px;
  overflow-y: auto;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
}

.ranking-modal, .redeem-modal {
	background: #fff;
	border-radius: 20rpx;
	padding: 0;
	margin: 20rpx;
	width: calc(100vw - 40rpx);
	max-width: 90vw;
	max-height: 90vh;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.modal-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
}

.modal-close {
	font-size: 40rpx;
	font-weight: bold;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
}

/* 排行榜样式 */
.ranking-tabs {
	display: flex;
	background: #f8f9fa;
	padding: 10rpx;
}

.ranking-tab {
	flex: 1;
	text-align: center;
	padding: 20rpx 10rpx;
	border-radius: 10rpx;
	font-size: 26rpx;
	color: #666;
	transition: all 0.3s ease;
}

.ranking-tab.active {
	background: #667eea;
	color: #fff;
}

.ranking-content {
	flex: 1;
	padding: 20rpx;
	overflow-y: auto;
	max-height: 1200rpx;
	min-height: 800rpx;
}

.loading-text, .empty-text {
	text-align: center;
	color: #999;
	padding: 60rpx 0;
	font-size: 28rpx;
}

.ranking-list {
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.ranking-item {
	display: flex;
	align-items: center;
	padding: 20rpx;
	background: #f8f9fa;
	border-radius: 15rpx;
	border-left: 6rpx solid #e9ecef;
}

.ranking-item.current-player {
	background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
	border-left-color: #17a2b8;
}

.ranking-item.top-rank {
	border-left-width: 8rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.ranking-item.first-place {
	background: linear-gradient(135deg, #fff9c4 0%, #fff3a0 100%);
	border-left-color: #ffd700;
}

.ranking-item.second-place {
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-left-color: #c0c0c0;
}

.ranking-item.third-place {
	background: linear-gradient(135deg, #fdf2e9 0%, #fce4d6 100%);
	border-left-color: #cd7f32;
}

.rank-number {
	width: 80rpx;
	text-align: center;
	font-size: 28rpx;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
}

.rank-number.top-three {
	width: 90rpx;
}

.rank-icon {
	font-size: 32rpx;
}

.rank-icon.gold {
	filter: drop-shadow(0 0 8rpx rgba(255, 215, 0, 0.6));
}

.rank-icon.silver {
	filter: drop-shadow(0 0 8rpx rgba(192, 192, 192, 0.6));
}

.rank-icon.bronze {
	filter: drop-shadow(0 0 8rpx rgba(205, 127, 50, 0.6));
}

.rank-text {
	color: #666;
	font-size: 26rpx;
}

.player-info {
	flex: 1;
	margin-left: 20rpx;
}

.player-name {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 5rpx;
}



.ranking-value {
	font-size: 26rpx;
	font-weight: bold;
	color: #e74c3c;
}

.my-ranking {
	padding: 20rpx;
	background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
	color: #fff;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.my-ranking-title {
	font-size: 26rpx;
}

.my-ranking-value {
	font-size: 28rpx;
	font-weight: bold;
}

/* 兑换码样式 */
.redeem-input-section {
	display: flex;
	gap: 20rpx;
	padding: 30rpx;
}

.redeem-input {
	flex: 1;
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e9ecef;
	border-radius: 15rpx;
	font-size: 28rpx;
	background: #fff;
}

.redeem-input:focus {
	border-color: #667eea;
}

.redeem-btn {
	width: 160rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	border: none;
	border-radius: 15rpx;
	font-size: 26rpx;
	font-weight: bold;
}

.redeem-btn.disabled {
	background: #ccc;
	color: #999;
}

.redeem-tips {
	padding: 0 30rpx 20rpx;
	background: #f8f9fa;
}

.tips-item {
	font-size: 24rpx;
	color: #666;
	display: block;
	margin-bottom: 8rpx;
}



.reward-tag {
	font-size: 22rpx;
	color: #28a745;
	background: rgba(40, 167, 69, 0.1);
	padding: 5rpx 10rpx;
	border-radius: 8rpx;
}

/* 侠客按钮样式 - 与NPC按钮保持一致 */
.player-sidebar-btn {
	position: fixed;
	right: 0;
	z-index: 1001;
	background: #ff6b6b;
	color: #fff;
	border-radius: 16rpx 0 0 16rpx;
	padding: 18rpx 12rpx;
	font-size: 28rpx;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
	transition: top 0.15s;
	user-select: none;
}

.player-sidebar-btn:active {
	transform: scale(0.95);
}

/* 侠客侧边栏样式 */
.player-sidebar-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
}

.player-sidebar {
	position: fixed;
	top: 0;
	right: -600rpx;
	width: 600rpx;
	height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	transition: right 0.3s ease;
	z-index: 1001;
	display: flex;
	flex-direction: column;
}

.player-sidebar-show {
	right: 0;
}

.player-sidebar-header {
	padding: 40rpx 30rpx 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.player-sidebar-close {
	background: none;
	border: none;
	color: white;
	font-size: 40rpx;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.player-sidebar-list {
	flex: 1;
	padding: 20rpx;
	overflow-y: auto;
}

.player-item {
	background: white;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	display: flex;
	align-items: center;
}

.player-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 40rpx;
	margin-right: 20rpx;
}

.player-info {
	flex: 1;
}

.player-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.player-level {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 5rpx;
}

.player-status {
	font-size: 24rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	display: inline-block;
}

.player-status.online {
	background: #e8f5e8;
	color: #52c41a;
}

.player-status.busy {
	background: #fff7e6;
	color: #fa8c16;
}

.player-status.battle {
	background: #fff1f0;
	color: #f5222d;
}

/* 自己的特殊样式 */
.player-item-self {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.player-item-self .player-name,
.player-item-self .player-level {
	color: white;
}

.player-name-self {
	position: relative;
}

.self-tag {
	font-size: 24rpx;
	color: #ffd700;
	font-weight: normal;
	margin-left: 10rpx;
}

.no-players {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
}

/* 玩家交互弹窗样式 */
.player-menu-modal-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1002;
}

.player-menu-modal {
	background: white;
	border-radius: 20rpx;
	padding: 0;
	margin: 40rpx;
	width: calc(100vw - 80rpx);
	max-width: 600rpx;
}

.player-menu-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 30rpx;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.player-menu-title {
	font-size: 36rpx;
	font-weight: bold;
}

.player-menu-close {
	background: none;
	border: none;
	color: white;
	font-size: 40rpx;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
}

.player-menu-info {
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.player-menu-level, .player-menu-status {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.player-menu-actions {
	padding: 30rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.player-action-btn {
	flex: 1;
	min-width: 120rpx;
	height: 80rpx;
	border: none;
	border-radius: 12rpx;
	font-size: 28rpx;
	color: white;
	font-weight: bold;
}

.player-action-btn.attack {
	background: linear-gradient(135deg, #f5222d 0%, #cf1322 100%);
}

.player-action-btn.give {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
}

.player-action-btn.steal {
	background: linear-gradient(135deg, #fa8c16 0%, #d46b08 100%);
}

.player-action-btn.view {
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
}

/* 聊天弹窗样式 */
.chat-popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1003;
}

.chat-popup {
	background: white;
	border-radius: 20rpx;
	margin: 40rpx;
	width: calc(100vw - 80rpx);
	height: calc(100vh - 200rpx);
	max-width: 800rpx;
	display: flex;
	flex-direction: column;
}

.chat-popup-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	padding: 30rpx;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.chat-popup-title {
	font-size: 36rpx;
	font-weight: bold;
}

.chat-popup-close {
	background: none;
	border: none;
	color: white;
	font-size: 40rpx;
	padding: 0;
	width: 60rpx;
	height: 60rpx;
}

.chat-messages {
	flex: 1;
	padding: 20rpx;
	overflow-y: auto;
	background: #f8f9fa;
}

.chat-message {
	background: white;
	border-radius: 12rpx;
	padding: 20rpx;
	margin-bottom: 15rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.chat-message-own {
	background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
	border-left: 4rpx solid #667eea;
}

/* 不同聊天类型的样式 */
.chat-message-world {
	border-left: 4rpx solid #409eff;
}

.chat-message-private {
	background: linear-gradient(135deg, #fff3e0, #fce4ec);
	border-left: 4rpx solid #ff9800;
}

.chat-message-rumor {
	background: linear-gradient(135deg, #f3e5f5, #e8f5e8);
	border-left: 4rpx solid #9c27b0;
}

.chat-message-system {
	background: linear-gradient(135deg, #fff8e1, #f1f8e9);
	border-left: 4rpx solid #ffc107;
}

.chat-message-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.chat-message-info {
	display: flex;
	align-items: center;
	gap: 10rpx;
}

.chat-sender {
	font-size: 28rpx;
	font-weight: bold;
	color: #667eea;
}

.chat-sender-own {
	color: #ff6b6b;
}

/* 不同聊天类型的发送者颜色 */
.chat-sender-world {
	color: #409eff;
}

.chat-sender-private {
	color: #ff9800;
}

.chat-sender-rumor {
	color: #9c27b0;
}

.chat-sender-system {
	color: #ffc107;
}

.chat-type-badge {
	font-size: 20rpx;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	color: white;
	font-weight: bold;
}

.chat-type-private {
	background: #ff9800;
}

.chat-type-rumor {
	background: #9c27b0;
}

.chat-type-system {
	background: #ffc107;
}

.chat-time {
	font-size: 24rpx;
	color: #999;
}

.chat-content {
	font-size: 30rpx;
	color: #333;
	line-height: 1.5;
}

.no-messages {
	text-align: center;
	color: #999;
	padding: 60rpx 20rpx;
}

.chat-input-area {
	padding: 20rpx;
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	flex-direction: column;
	gap: 15rpx;
}

.chat-type-selector {
	display: flex;
	justify-content: center;
	margin-bottom: 10rpx;
}

.chat-type-picker {
	width: 200rpx;
}

.chat-type-display {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	border: 2rpx solid #667eea;
	background: white;
	color: #667eea;
	font-size: 24rpx;
}

.picker-arrow {
	font-size: 20rpx;
	color: #667eea;
}

.chat-input-row {
	display: flex;
	gap: 10rpx;
	align-items: center;
}

.chat-target-input {
	width: 150rpx;
	height: 80rpx;
	border: 2rpx solid #ff9800;
	border-radius: 12rpx;
	padding: 0 15rpx;
	font-size: 26rpx;
	color: #ff9800;
}

.chat-input {
	flex: 1;
	height: 80rpx;
	border: 2rpx solid #d9d9d9;
	border-radius: 12rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}

.chat-input:focus {
	border-color: #667eea;
}

.chat-send-btn {
	width: 120rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
	border: none;
	border-radius: 12rpx;
	font-size: 28rpx;
	font-weight: bold;
}

.chat-send-btn:disabled {
	background: #d9d9d9;
	color: #999;
}

/* 左侧聊天按钮样式 - 与NPC按钮大小一致 */
.left-chat-btn {
	position: fixed;
	left: 0;
	z-index: 1001;
	background: linear-gradient(135deg, #4facfe, #00f2fe);
	color: white;
	border-radius: 0 16rpx 16rpx 0;
	padding: 18rpx 12rpx;
	font-size: 28rpx;
	font-weight: bold;
	box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
	border: none;
	transition: top 0.15s;
	user-select: none;
}

.left-chat-btn:active {
	transform: scale(0.95);
}
</style>








