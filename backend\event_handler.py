#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件系统处理模块
负责各种游戏事件的业务逻辑处理
"""

import random
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class EventHandler:
    """事件系统处理器"""
    
    def __init__(self, game_data: dict, realm_handler, battle_handler):
        self.game_data = game_data
        self.realm_handler = realm_handler
        self.battle_handler = battle_handler
    
    def generate_random_event(self, map_id: str) -> dict:
        """生成随机事件"""
        maps = self.game_data['maps']
        map_data = next((m for m in maps.values() if m['id'] == map_id), None)
        
        if not map_data:
            # 默认事件
            return {
                'type': 4,
                'name': '普通事件',
                'description': '什么都没有发生。'
            }
        
        # 根据地图配置的事件权重生成事件
        events = map_data.get('events', [])
        if not events:
            return {
                'type': 4,
                'name': '普通事件',
                'description': '什么都没有发生。'
            }
        
        # 按权重随机选择事件
        total_weight = sum(event.get('weight', 1) for event in events)
        rand = random.uniform(0, total_weight)
        
        current_weight = 0
        for event in events:
            current_weight += event.get('weight', 1)
            if rand <= current_weight:
                return event
        
        # 兜底返回第一个事件
        return events[0]
    
    def process_event(self, event: dict, player: dict, map_level: int = 1) -> dict:
        """处理事件"""
        event_type = event['type']
        
        if event_type == 1:  # 好运事件
            return self.process_good_fortune(player)
        elif event_type == 2:  # 遭遇NPC
            return self.process_npc_encounter(player)
        elif event_type == 4:  # 普通事件
            return self.process_empty_event(player, map_level)
        elif event_type == 5:  # 奇遇事件
            return self.process_adventure(player)
        elif event_type == 6:  # 恩怨事件
            return self.process_enmity(player)
        elif event_type == 7:  # 组队事件
            return self.process_team_event(player)
        elif event_type == 8:  # 商队事件
            return self.process_caravan_event(player)
        elif event_type == 9:  # 江湖传闻
            return self.process_rumor_event(player)
        elif event_type == 10:  # 天气事件
            return self.process_weather_event(player)
        else:
            return self.process_empty_event(player, map_level)
    
    def process_good_fortune(self, player: dict) -> dict:
        """处理好运事件 - 已迁移到 event_manager.py"""
        # 这个方法已经不再使用，统一使用 event_manager.py 中的版本
        import asyncio
        from event_manager import EventManager

        # 由于这是同步方法，我们需要创建一个简化版本
        import random
        base_money = random.randint(100, 300)
        money_reward = self.realm_handler.calculate_experience_reward(base_money, player)
        player['money'] = player.get('money', 0) + money_reward

        return {
            'content': '你遇到了好运，获得了一些银两。',
            'rewards': {'银两': money_reward}
        }
    
    def process_npc_encounter(self, player: dict) -> dict:
        """处理NPC遭遇事件"""
        npc_events = [
            "你遇到了一位江湖侠客，与他切磋了一番。",
            "你遇到了一位武林前辈，他传授了你一些经验。",
            "你遇到了一位游方道士，他为你指点迷津。",
            "你遇到了一位行商，与他交流了一些见闻。"
        ]
        
        content = random.choice(npc_events)
        
        # 历练值奖励（应用境界增益）
        base_exp = random.randint(30, 100)
        exp_reward = self.realm_handler.calculate_experience_reward(base_exp, player)
        
        rewards = {'历练值': exp_reward}
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_empty_event(self, player: dict, map_level: int) -> dict:
        """处理普通事件"""
        empty_events = [
            "你在路上走了一段，什么都没有发生。",
            "你停下来休息了一会儿。",
            "你观察了一下周围的环境。",
            "你继续前行，一路平安。"
        ]
        
        content = random.choice(empty_events)
        
        # 小量历练值奖励
        base_exp = random.randint(5, 15)
        exp_reward = self.realm_handler.calculate_experience_reward(base_exp, player)
        
        rewards = {'历练值': exp_reward}
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_adventure(self, player: dict) -> dict:
        """处理奇遇事件"""
        adventure_events = [
            "你发现了一个神秘的洞穴，在其中找到了宝物！",
            "你遇到了一位隐世高人，他传授了你一些武学心得。",
            "你在古迹中发现了前人留下的秘籍残页。",
            "你救助了一位受伤的武者，他感激地给了你一些珍贵物品。"
        ]
        
        content = random.choice(adventure_events)
        
        # 丰厚奖励
        base_exp = random.randint(100, 300)
        exp_reward = self.realm_handler.calculate_experience_reward(base_exp, player)
        
        base_money = random.randint(100, 500)
        money_reward = self.realm_handler.calculate_experience_reward(base_money, player)
        
        rewards = {
            '历练值': exp_reward,
            '银两': money_reward
        }
        
        # 有概率获得武学点
        if random.random() < 0.3:
            skill_points = random.randint(1, 3)
            rewards['武学点'] = skill_points
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_enmity(self, player: dict) -> dict:
        """处理恩怨事件"""
        enmity_events = [
            "你遇到了一个寻仇的武者，不得不与他一战。",
            "你卷入了一场江湖恩怨，必须自卫。",
            "你遇到了山贼，他们想要劫财害命。",
            "你被一个神秘人挑战，无法避免战斗。"
        ]
        
        content = random.choice(enmity_events)
        
        # 这类事件通常会触发战斗，这里只返回描述
        return {
            'content': content,
            'rewards': {},
            'trigger_battle': True
        }
    
    def process_team_event(self, player: dict) -> dict:
        """处理组队事件"""
        team_events = [
            "你遇到了一群志同道合的武者，大家一起行动。",
            "你加入了一个临时的冒险小队。",
            "你与其他武者结伴而行，互相照应。"
        ]
        
        content = random.choice(team_events)
        
        # 组队事件给予额外经验奖励
        base_exp = random.randint(50, 150)
        exp_reward = self.realm_handler.calculate_experience_reward(base_exp, player)
        
        rewards = {'历练值': exp_reward}
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_caravan_event(self, player: dict) -> dict:
        """处理商队事件"""
        caravan_events = [
            "你遇到了一支商队，他们愿意以优惠价格出售物品。",
            "你帮助商队解决了一些麻烦，他们给了你报酬。",
            "你与商队的护卫交流了一些武学心得。"
        ]
        
        content = random.choice(caravan_events)
        
        # 银两奖励
        base_money = random.randint(80, 300)
        money_reward = self.realm_handler.calculate_experience_reward(base_money, player)
        
        rewards = {'银两': money_reward}
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_rumor_event(self, player: dict) -> dict:
        """处理江湖传闻事件"""
        rumor_events = [
            "你听到了一些江湖传闻，增长了见识。",
            "你从路人口中得知了一些有用的信息。",
            "你听说了某位高手的传奇故事。"
        ]
        
        content = random.choice(rumor_events)
        
        # 少量历练值奖励
        base_exp = random.randint(20, 60)
        exp_reward = self.realm_handler.calculate_experience_reward(base_exp, player)
        
        rewards = {'历练值': exp_reward}
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def process_weather_event(self, player: dict) -> dict:
        """处理天气事件"""
        weather_events = [
            "天气晴朗，你的心情很好，修炼效果提升。",
            "下起了小雨，你在雨中感悟了一些武学道理。",
            "起风了，你借助风势练习轻功。"
        ]
        
        content = random.choice(weather_events)
        
        # 历练值奖励
        base_exp = random.randint(30, 80)
        exp_reward = self.realm_handler.calculate_experience_reward(base_exp, player)
        
        rewards = {'历练值': exp_reward}
        
        return {
            'content': content,
            'rewards': rewards
        }
    
    def add_event_to_log(self, player: dict, event_type: str, content: str, rewards: dict):
        """添加事件到玩家日志"""
        if 'event_log' not in player:
            player['event_log'] = []
        
        log_entry = {
            'type': event_type,
            'name': '江湖事件',
            'content': content,
            'rewards': rewards,
            'timestamp': datetime.now().isoformat()
        }
        
        player['event_log'].insert(0, log_entry)
        
        # 最多保留50条
        if len(player['event_log']) > 50:
            player['event_log'] = player['event_log'][:50]
