
.battle-popup-mask.data-v-66bb1315 {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 99999;
  background: rgba(0,0,0,0.32);
  display: flex;
  align-items: center;
  justify-content: center;
}
.battle-popup-content.data-v-66bb1315 {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);
  padding: 32rpx 24rpx 24rpx 24rpx;
  min-width: 600rpx;
  max-width: 96vw;
  max-height: 92vh;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.battle-title.data-v-66bb1315 {
  font-size: 36rpx;
  font-weight: bold;
  color: #c0392b;
  margin-bottom: 12rpx; /* 减少底部间距 */
}
.battle-roles.data-v-66bb1315 {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 32rpx;
  margin-bottom: 12rpx; /* 减少底部间距 */
}
.role.data-v-66bb1315 {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 180rpx;
}
.role-avatar.data-v-66bb1315 {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: #eee;
  margin-bottom: 8rpx;
}
.role-name.data-v-66bb1315 {
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}
.role-hp-bar.data-v-66bb1315, .role-mp-bar.data-v-66bb1315 {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 22rpx;
  margin-bottom: 2rpx;
}
.vs-text.data-v-66bb1315 {
  font-size: 32rpx;
  color: #888;
  font-weight: bold;
  margin: 0 12rpx;
}
.debug-info.data-v-66bb1315 {
  background: #f0f0f0;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  margin-bottom: 12rpx;
  font-size: 20rpx;
  color: #666;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}
.battle-log.data-v-66bb1315 {
  width: 100%;
  height: 800rpx; /* 继续增加高度，占满更多空间 */
  background: #f8f8f8;
  border-radius: 12rpx;
  margin: 18rpx 0 12rpx 0;
  padding: 8rpx 12rpx;
  border: 2px solid #e0e0e0;
  display: block;
}
.battle-log-bottom.data-v-66bb1315 {
  height: 20rpx;
  width: 100%;
}
.battle-log-content.data-v-66bb1315 {
  /* 确保内容有足够的高度 */
  padding: 4rpx;
  min-height: 100%;
}
.battle-round.data-v-66bb1315 {
  margin-bottom: 4rpx;
  padding: 4rpx 8rpx;
  background: transparent;
  border-radius: 4rpx;
  border: none;
  word-wrap: break-word;
  word-break: break-all;
}
.round-title.data-v-66bb1315 {
  font-size: 22rpx;
  color: #4b3fa7;
  font-weight: bold;
}
.round-header.data-v-66bb1315 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rpx;
}
.round-number.data-v-66bb1315 {
  font-size: 20rpx;
  color: #4b3fa7;
  font-weight: bold;
}
.round-time.data-v-66bb1315 {
  font-size: 18rpx;
  color: #888;
  font-weight: normal;
}
.round-desc.data-v-66bb1315 {
  font-size: 24rpx;
  color: #222;
  margin: 1rpx 0 1rpx 0;
  line-height: 1.3;
  word-wrap: break-word;
  word-break: break-all;
}
.effect-desc.data-v-66bb1315 {
  font-size: 20rpx;
  color: #e67e22;
  margin: 1rpx 0 1rpx 0;
  line-height: 1.2;
}
.battle-log-placeholder.data-v-66bb1315 {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: #999;
  font-size: 24rpx;
}
.battle-popup-buttons.data-v-66bb1315 {
  display: flex;
  gap: 24rpx;
  margin-top: 12rpx; /* 减少顶部间距 */
}
.main-btn.data-v-66bb1315 {
  background: linear-gradient(90deg, #e67e22, #f9d423);
  color: #fff;
  font-size: 26rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.sub-btn.data-v-66bb1315 {
  background: linear-gradient(90deg, #888, #bbb);
  color: #fff;
  font-size: 24rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.next-btn.data-v-66bb1315 {
  background: linear-gradient(90deg, #e67e22, #f9d423);
  color: #fff;
  font-size: 26rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.close-btn.data-v-66bb1315 {
  background: #eee;
  color: #888;
  font-size: 24rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.next-btn[disabled].data-v-66bb1315, .main-btn[disabled].data-v-66bb1315 {
  opacity: 0.5;
}
