#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
事件管理器模块
负责所有事件相关的计算逻辑
"""

import random
import logging
import json
import os
from probability_config import EMPTY_EVENT_PROBABILITY, EVENT_PROBABILITIES, load_map_probability_config
import item_system
from item_system import load_items_config, ItemType

logger = logging.getLogger(__name__)

class EventManager:
    """事件管理器"""
    
    def __init__(self):
        self.empty_event_probability = EMPTY_EVENT_PROBABILITY
        self.event_probabilities = EVENT_PROBABILITIES.copy()
        self.map_probability_config = load_map_probability_config()
        logger.info(f"事件管理器初始化完成")
        logger.info(f"全局普通事件概率: {self.empty_event_probability:.1%}")
        logger.info(f"全局事件概率总和: {sum(self.event_probabilities.values()):.1%}")
        logger.info(f"地图专属概率配置: {list(self.map_probability_config.keys())}")
        
        # 事件基础信息
        self.events = {
            1: {'name': '好运事件', 'description': '你遇到了一个善良的商人，获得了一些奖励。'},
            2: {'name': '遭遇NPC', 'description': '你遇到了一个神秘的江湖人士。'},
            3: {'name': '采集事件', 'description': '你发现了一片资源丰富的区域。'},
            4: {'name': '普通事件', 'description': '你漫步在江湖中，感受着武侠世界的魅力。'},
            5: {'name': '奇遇事件', 'description': '你遇到了一个千载难逢的奇遇！'}
        }
    
    def get_probability_for_map(self, map_id: str):
        """
        获取指定地图的概率配置（没有则用全局默认）
        Returns: (empty_prob, event_probs)
        """
        if map_id and map_id in self.map_probability_config:
            conf = self.map_probability_config[map_id]
            empty_prob = conf.get('empty', self.empty_event_probability)
            event_probs = conf.get('events', self.event_probabilities)
            return empty_prob, event_probs
        return self.empty_event_probability, self.event_probabilities

    def generate_random_event(self, map_id: str = None) -> dict:
        """
        生成随机事件（两轮概率判定，支持地图专属概率）
        """
        logger.debug(f"开始生成随机事件，地图: {map_id}")
        empty_prob, event_probs = self.get_probability_for_map(map_id)
        # 第一轮概率判定：普通事件 vs 其他事件
        first_rand = random.random()
        logger.debug(f"第一轮随机数: {first_rand}, 普通事件概率: {empty_prob}")
        if first_rand < empty_prob:
            logger.debug("第一轮命中普通事件")
            return self._get_event_info(4)
        logger.debug("第一轮未命中普通事件，进行第二轮概率判定")
        # 只保留剩余事件类型的概率
        event_probs = {k: v for k, v in event_probs.items() if k in self.events}
        total_probability = sum(event_probs.values())
        second_rand = random.random()
        logger.debug(f"第二轮随机数: {second_rand}, 总概率: {total_probability}")
        if second_rand < total_probability:
            current_prob = 0
            for event_type, probability in event_probs.items():
                current_prob += probability
                if second_rand <= current_prob:
                    event = self._get_event_info(event_type)
                    logger.debug(f"第二轮命中事件: {event_type}. {event['name']}")
                    return event
            logger.warning("第二轮概率计算异常，返回空事件")
            return self._get_event_info(4)
        logger.debug("第二轮未命中任何事件，返回空事件")
        return self._get_event_info(4)
    
    def _get_event_info(self, event_type: int) -> dict:
        """
        获取事件信息
        
        Args:
            event_type: 事件类型
            
        Returns:
            dict: 事件信息
        """
        event_info = self.events.get(event_type, {
            'name': f'未知事件{event_type}',
            'description': '这是一个未知的事件。'
        })
        
        return {
            'type': event_type,
            'name': event_info['name'],
            'description': event_info['description']
        }
    
    def get_event_probabilities(self) -> dict:
        """
        获取事件概率配置
        
        Returns:
            dict: 事件概率配置
        """
        return self.event_probabilities.copy()
    
    def get_all_events(self) -> list:
        """
        获取所有事件列表
        
        Returns:
            list: 事件列表
        """
        return [self._get_event_info(event_type) for event_type in sorted(self.events.keys())]
    
    def calculate_empty_event_probability(self, map_id: str = None) -> float:
        """
        计算普通事件的实际概率（支持地图专属）
        """
        empty_prob, event_probs = self.get_probability_for_map(map_id)
        first_round_empty = empty_prob
        second_round_no_hit = (1.0 - empty_prob) * (1.0 - sum(event_probs.values()))
        return first_round_empty + second_round_no_hit
    
    def get_empty_event_probability(self) -> float:
        """
        获取第一轮普通事件概率

        Returns:
            float: 第一轮普通事件概率
        """
        return self.empty_event_probability
    
    def set_empty_event_probability(self, probability: float):
        """
        设置第一轮空事件概率
        
        Args:
            probability: 空事件概率 (0.0-1.0)
        """
        if 0.0 <= probability <= 1.0:
            self.empty_event_probability = probability
            logger.info(f"空事件概率已设置为: {probability}")
        else:
            logger.error(f"无效的概率值: {probability}，概率必须在0.0-1.0之间")
    
    def set_event_probability(self, event_type: int, probability: float):
        """
        设置特定事件的概率
        
        Args:
            event_type: 事件类型
            probability: 事件概率 (0.0-1.0)
        """
        if 0.0 <= probability <= 1.0:
            self.event_probabilities[event_type] = probability
            logger.info(f"事件{event_type}概率已设置为: {probability}")
        else:
            logger.error(f"无效的概率值: {probability}，概率必须在0.0-1.0之间")

    @staticmethod
    async def process_good_fortune(player: dict, server=None, user_id=None) -> dict:
        """
        处理好运事件 - 统一优化版本
        支持多样化奖励：银两、历练值、悟性、声望、物品等
        """
        import random
        import os
        import json
        from item_system import item_system, ItemType, ItemQuality
        from realm_system import RealmSystem

        # 加载好运事件配置
        events_path = os.path.join(os.path.dirname(__file__), 'good_fortune_events.json')
        try:
            with open(events_path, 'r', encoding='utf-8') as f:
                events_config = json.load(f)
        except Exception as e:
            logger.error(f"加载好运事件配置失败: {e}")
            # 兜底事件
            return {
                'content': '你遇到了一位慷慨的商人，他送给你一些银两。',
                'rewards': {'银两': random.randint(50, 200)}
            }

        # 按权重随机选择事件
        total_weight = sum(event.get('weight', 1) for event in events_config)
        rand = random.uniform(0, total_weight)
        current_weight = 0
        selected_event = None

        for event in events_config:
            current_weight += event.get('weight', 1)
            if rand <= current_weight:
                selected_event = event
                break

        if not selected_event:
            selected_event = events_config[0]  # 兜底选择第一个

        return await EventManager._process_good_fortune_event(selected_event, player, server, user_id)

    @staticmethod
    async def _process_good_fortune_event(event_config: dict, player: dict, server=None, user_id=None) -> dict:
        """
        处理具体的好运事件
        """
        import random
        from item_system import item_system, ItemType, ItemQuality
        from realm_system import RealmSystem

        content = event_config['content']
        base_reward = event_config.get('base_reward', {})
        special_reward = event_config.get('special_reward', {})

        # 处理基础奖励
        rewards = {}
        reward_text_parts = []

        for reward_type, value_range in base_reward.items():
            if isinstance(value_range, list) and len(value_range) == 2:
                base_amount = random.randint(value_range[0], value_range[1])
            else:
                base_amount = value_range

            # 应用境界增益
            if reward_type == '历练值':
                experience_gain_bonus = player.get('experience_gain_bonus', 0)
                final_amount = base_amount + experience_gain_bonus
            elif reward_type == '银两':
                money_gain_bonus = player.get('money_gain_bonus', 0)
                final_amount = base_amount + money_gain_bonus
            else:
                final_amount = base_amount

            rewards[reward_type] = final_amount

            # 更新玩家数据
            if reward_type == '银两':
                player['money'] = player.get('money', 0) + final_amount
            elif reward_type == '历练值':
                player['exp'] = player.get('exp', 0) + final_amount
            elif reward_type == '武学点':
                player['skill_points'] = player.get('skill_points', 0) + final_amount

            # 添加到奖励文本
            reward_text_parts.append(f'{reward_type}+{final_amount}')

        # 处理特殊奖励
        special_text = await EventManager._process_special_reward(special_reward, player, server, user_id, rewards)
        if special_text:
            reward_text_parts.append(special_text)

        # 生成最终内容
        if reward_text_parts:
            reward_highlight = ', '.join(reward_text_parts)
            final_content = f'{content} <span style="color:#e4393c;font-weight:bold;">{reward_highlight}</span>'
        else:
            final_content = content

        return {
            'content': final_content,
            'rewards': rewards
        }

    @staticmethod
    async def _process_special_reward(special_config: dict, player: dict, server=None, user_id=None, rewards=None) -> str:
        """
        处理特殊奖励（物品、武学点等）
        返回奖励文本描述
        """
        if not special_config or not server or not user_id:
            return ""

        import random
        from item_system import item_system, ItemType, ItemQuality

        reward_type = special_config.get('type')
        probability = special_config.get('probability', 0.5)

        if random.random() > probability:
            return ""  # 未触发特殊奖励

        if reward_type == 'skill_book':
            return await EventManager._give_skill_book(player, server, user_id, rewards)
        elif reward_type == 'equipment':
            return await EventManager._give_equipment(special_config, player, server, user_id, rewards)

        return ""



    @staticmethod
    async def _give_skill_book(player: dict, server, user_id: str, rewards: dict) -> str:
        """给予武功残页（好运事件专用）- 根据地图等级"""
        import random
        import os
        import json
        from item_system import item_system, ItemType

        # 获取当前地图等级
        current_map = player.get('current_map', 'changan')
        map_level = EventManager._get_map_level(current_map)

        # 加载秘籍残页配置
        pages_path = os.path.join(os.path.dirname(__file__), 'skill_book_pages.json')
        try:
            with open(pages_path, 'r', encoding='utf-8') as f:
                pages_config = json.load(f)
        except Exception as e:
            # 兜底：给予基本武功秘籍
            basic_skill_books = [
                'basic_jianfa_book', 'basic_daofa_book', 'basic_quanfa_book',
                'basic_qinggong_book', 'basic_neigong_book'
            ]
            chosen_id = random.choice(basic_skill_books)
            chosen_item = item_system.items.get(chosen_id)
            if chosen_item and server and user_id:
                success = await server.add_item_to_inventory(user_id, player, {
                    'id': chosen_item.id,
                    'quantity': 1
                })
                if success:
                    return chosen_item.name
            return ""

        # 根据地图等级筛选合适的残页
        suitable_pages = []
        for page in pages_config:
            min_level = page.get('min_map_level', 1)
            max_level = page.get('max_map_level', 30)

            if min_level <= map_level <= max_level:
                suitable_pages.append(page)

        if not suitable_pages:
            # 如果没有合适的残页，给予基础残页
            for page in pages_config:
                if page.get('quality') == 'common':
                    suitable_pages.append(page)

        if not suitable_pages:
            return ""

        chosen_page = random.choice(suitable_pages)
        page_id = chosen_page['id']
        page_name = chosen_page['name']

        # 尝试给予真正的残页物品
        if server and user_id:
            success = await server.add_item_to_inventory(user_id, player, {
                'id': page_id,
                'quantity': 1
            })

            if success:
                return page_name
            else:
                return "背包已满"

        return ""

    @staticmethod
    async def _give_equipment(special_config: dict, player: dict, server, user_id: str, rewards: dict) -> str:
        """给予装备奖励"""
        import random

        equipment_type = special_config.get('equipment_type', 'weapon')
        quality = special_config.get('quality', 'common')

        # 根据装备类型选择合适的装备
        equipment_pools = {
            'weapon': [
                {'id': 'chitie_jian', 'name': '赤铁剑'},
                {'id': 'qingtong_jian', 'name': '青铜剑'},
                {'id': 'chitie_dao', 'name': '赤铁刀'},
                {'id': 'qingtong_dao', 'name': '青铜刀'}
            ],
            'armor': [
                {'id': 'langpiyi', 'name': '狼皮衣'},
                {'id': 'tiejia_dunpai', 'name': '铁甲盾牌'}
            ],
            'helmet': [
                {'id': 'liumuguan', 'name': '柳木头盔'},
                {'id': 'qiaomuguan', 'name': '樵木头盔'},
                {'id': 'qingzhuguan', 'name': '青竹头盔'}
            ],
            'shoes': [
                {'id': 'caoxie', 'name': '草鞋'},
                {'id': 'buxie', 'name': '布鞋'}
            ],
            'necklace': [
                {'id': 'kucao_xianglian', 'name': '苦草项链'},
                {'id': 'qingshan_hufu', 'name': '青山护符'}
            ],
            'ring': [
                {'id': 'kucao_jiezhi', 'name': '枯草戒指'},
                {'id': 'baihua_jiezhi', 'name': '百花戒指'},
                {'id': 'qingtongjing_jiezhi', 'name': '青铜戒'},
                {'id': 'baiyin_jiezhi', 'name': '白银戒'}
            ],
            'shield': [
                {'id': 'tiejia_dunpai', 'name': '铁甲盾牌'},
                {'id': 'qingshan_hufu', 'name': '青山护符'}
            ]
        }

        # 获取对应类型的装备池
        equipment_list = equipment_pools.get(equipment_type, equipment_pools['weapon'])
        chosen_equipment = random.choice(equipment_list)

        # 尝试给予装备
        if server and user_id:
            # 先创建装备物品并进行属性随机化
            from item_system import item_system
            inventory_item = item_system.create_inventory_item(chosen_equipment['id'])

            if inventory_item and inventory_item.get('effects'):
                # 对事件获得的装备进行属性随机化（上下浮动30%）
                inventory_item['effects'] = item_system.randomize_equipment_effects(
                    inventory_item['effects'],
                    variation_percent=30
                )

                # 直接添加到背包，绕过create_inventory_item的重复创建
                if 'inventory' not in player:
                    player['inventory'] = []

                # 检查背包容量
                inventory_capacity = player.get('inventory_capacity', 50)
                unique_item_ids = set([i['id'] for i in player['inventory']])
                if chosen_equipment['id'] not in unique_item_ids and len(unique_item_ids) >= inventory_capacity:
                    return "背包已满"

                # 添加随机化后的装备
                player['inventory'].append(inventory_item)
                await server.save_player_data(user_id, player)

                return chosen_equipment['name']
            else:
                # 如果没有effects字段，使用原来的方法
                success = await server.add_item_to_inventory(user_id, player, {
                    'id': chosen_equipment['id'],
                    'quantity': 1
                })

                if success:
                    return chosen_equipment['name']
                else:
                    return "背包已满"

        return ""

    @staticmethod
    def _get_map_level(map_name: str) -> int:
        """获取地图等级"""
        # 地图等级映射表
        map_levels = {
            'changan': 1,
            'heifengzhai': 2,
            'yaowanggu': 3,
            'huashan': 4,
            'wudang': 5,
            'shaolin': 6,
            'emei': 7,
            'kunlun': 8,
            'tianshan': 9,
            'mingjiao': 10,
            'xiaoyao': 11,
            'murong': 12,
            'dali': 13,
            'xingxiu': 14,
            'beggar': 15,
            'quanzhen': 16,
            'gumu': 17,
            'peach': 18,
            'shenlong': 19,
            'tianlong': 20,
            'lingjiu': 21,
            'mantuo': 22,
            'langhuan': 23,
            'misty': 24,
            'void': 25,
            'chaos': 26,
            'supreme': 27,
            'divine': 28,
            'immortal': 29,
            'eternal': 30
        }

        return map_levels.get(map_name, 1)



    @staticmethod
    async def process_npc_encounter(player: dict, server=None, user_id=None) -> dict:
        # 只触发怪物遭遇
        # 这里直接调用服务端的 get_random_monster_for_map，触发战斗或返回怪物信息
        if server and hasattr(server, 'get_random_monster_for_map'):
            current_map_id = player.get('current_map', 'changan')
            monster = server.get_random_monster_for_map(current_map_id)
            if monster:
                # 直接触发战斗，由 server.handle_encounter_monster 处理
                await server.handle_encounter_monster(player, monster, None)
                return {'content': f'你遭遇了怪物：{monster.get("name", "未知怪物")}，准备战斗！', 'rewards': {}}
            else:
                return {'content': '你在江湖中游荡，但没有遇到任何怪物。', 'rewards': {}}
        else:
            return {'content': '你在江湖中游荡，但没有遇到任何怪物。', 'rewards': {}}

    @staticmethod
    async def process_event(event: dict, player: dict, map_level: int = 1, server=None, user_id=None) -> dict:
        event_type = event['type']
        if event_type == 1:
            return await EventManager.process_good_fortune(player, server, user_id)
        elif event_type == 2:
            return await EventManager.process_npc_encounter(player, server, user_id)
        elif event_type == 4:
            return await EventManager.process_empty_event(player, map_level)
        elif event_type == 5:
            return await EventManager.process_adventure(player, map_level, server, user_id)
        else:
            return {
                'content': '你遇到了一个神秘的事件...',
                'rewards': {'历练值': map_level * 3}
            }

    async def process_gathering(self, player: dict, gather_type: str = None, fixed_resource: str = None) -> dict:
        import random
        items_config = load_items_config()
        from map_data import load_maps_config

        # 修正：使用小写英文类型，与item_system.py中的ItemType一致
        gather_map = {
            'mining':   {'tool_type': 'pickaxe', 'resource_type': 'ore', 'tool_name': '矿镐', 'skill_name': '挖矿'},
            'logging':  {'tool_type': 'axe',     'resource_type': 'wood', 'tool_name': '斧头', 'skill_name': '伐木'},
            'herbalism':{'tool_type': 'sickle',  'resource_type': 'herb', 'tool_name': '镰刀', 'skill_name': '采药'},
            'skinning': {'tool_type': 'knife',   'resource_type': 'fur', 'tool_name': '小刀', 'skill_name': '剥皮'},
        }

        if not gather_type or gather_type not in gather_map:
            return {'content': '采集类型无效，无法采集。', 'rewards': {}}

        info = gather_map[gather_type]

        # 初始化采集技能（如果不存在）
        if 'gather_skills' not in player:
            player['gather_skills'] = {
                'mining': {'level': 1, 'exp': 0},
                'logging': {'level': 1, 'exp': 0},
                'herbalism': {'level': 1, 'exp': 0},
                'skinning': {'level': 1, 'exp': 0}
            }

        # 查找背包中所有同类型采集工具
        tool_candidates = []
        for item in player.get('inventory', []):
            item_obj = items_config.get(item['id'])
            # 修正：使用正确的字段名 'type' 而不是 '类型'
            if item_obj and item_obj.get('type') == info['tool_type']:
                tool_candidates.append(item_obj)

        if not tool_candidates:
            return {'content': f'你未携带{info["tool_name"]}类工具，无法采集！', 'rewards': {}}

        # 取最高等级工具
        best_tool = max(tool_candidates, key=lambda x: int(x.get('level', 1)))
        gather_times = int(best_tool.get('gather_times', 1))
        tool_level = int(best_tool.get('level', 1))

        # 获取当前地图的采集物品配置
        maps_config = load_maps_config()
        current_map_id = player.get('current_map', 'changan')  # 默认长安城
        current_map = maps_config.get(current_map_id, {})
        map_gather_config = current_map.get('采集物品', {})

        print(f"[DEBUG] 当前地图: {current_map_id}")
        print(f"[DEBUG] 采集类型: {gather_type}")
        print(f"[DEBUG] 地图采集配置: {map_gather_config}")

        # 获取采集技能等级
        skill = player['gather_skills'].get(gather_type, {'level': 1, 'exp': 0})
        skill_level = skill['level']

        # 获取所有可采集的资源（基于技能等级：等级2资源需要20级技能）
        all_resources = [v for v in items_config.values() if v.get('type') == info['resource_type']]
        available_resources = []
        for r in all_resources:
            resource_level = int(r.get('level', 1))
            required_skill_level = resource_level * 10  # 等级2资源需要20级技能
            if skill_level >= required_skill_level:
                available_resources.append(r)
            else:
                print(f"[DEBUG] 资源 {r.get('name')} (等级{resource_level}) 需要技能等级{required_skill_level}，当前技能等级: {skill_level}")

        if not available_resources:
            return {'content': f'你的{info["skill_name"]}技能等级({skill_level})不足，无法采集任何资源！需要更高的技能等级。', 'rewards': {}}

        # 优先使用固定资源（事件生成时已选定）
        resource = None
        if fixed_resource:
            print(f"[DEBUG] 使用事件指定的固定资源: {fixed_resource}")
            # 查找指定的资源（在所有资源中查找，不限制技能等级）
            all_resources = [v for v in items_config.values() if v.get('type') == info['resource_type']]
            for res in all_resources:
                if res.get('name') == fixed_resource:
                    resource_level = int(res.get('level', 1))
                    required_skill_level = resource_level * 10
                    if skill_level >= required_skill_level:
                        resource = res
                        print(f"[DEBUG] 找到固定资源: {resource.get('name')} (等级{resource_level})")
                        break
                    else:
                        # 技能等级不足，直接返回错误
                        print(f"[DEBUG] 固定资源 {fixed_resource} 需要技能等级{required_skill_level}，当前技能等级: {skill_level}")
                        return {
                            'content': f'你的{info["skill_name"]}技能等级不足！\n需要{required_skill_level}级{info["skill_name"]}技能才能采集{fixed_resource}，当前等级：{skill_level}',
                            'rewards': {}
                        }

            if not resource:
                print(f"[DEBUG] 固定资源 {fixed_resource} 不存在")

        # 如果没有固定资源或固定资源不可用，则根据地图配置选择采集物品
        if not resource:
            selected_items = {}

            if map_gather_config:
                # 检查是否是新格式（支持权重配置）
                if '采集类型权重' in map_gather_config and '采集物品配置' in map_gather_config:
                    # 新格式：有权重配置
                    items_config_map = map_gather_config['采集物品配置']
                    if gather_type in items_config_map:
                        selected_items = items_config_map[gather_type]
                        print(f"[DEBUG] 新格式(权重) - 使用{gather_type}类型的配置: {selected_items}")
                elif gather_type in map_gather_config and isinstance(map_gather_config[gather_type], dict):
                    # 旧格式：按采集类型分组
                    selected_items = map_gather_config[gather_type]
                    print(f"[DEBUG] 旧格式(分组) - 使用{gather_type}类型的配置: {selected_items}")
                elif isinstance(map_gather_config, dict) and any(isinstance(v, (int, float)) for v in map_gather_config.values()):
                    # 最旧格式：直接列出物品（兼容性）
                    selected_items = map_gather_config
                    print(f"[DEBUG] 最旧格式 - 使用全部配置: {selected_items}")

            if selected_items:
                # 过滤出地图配置中存在的资源，并检查工具等级
                map_available_resources = []
                for res in available_resources:
                    resource_name = res.get('name', '')
                    resource_level = int(res.get('level', 1))
                    if resource_name in selected_items:
                        # 检查技能等级是否足够
                        required_skill_level = resource_level * 10
                        if skill_level >= required_skill_level:
                            map_available_resources.append(res)
                            print(f"[DEBUG] 资源 {resource_name} (等级{resource_level}) 可采集，技能等级: {skill_level}")
                        else:
                            print(f"[DEBUG] 资源 {resource_name} (等级{resource_level}) 需要技能等级{required_skill_level}，当前技能等级: {skill_level}")

                print(f"[DEBUG] 可用资源: {[(r.get('name'), r.get('level')) for r in available_resources]}")
                print(f"[DEBUG] 地图可用资源: {[(r.get('name'), r.get('level')) for r in map_available_resources]}")

                if map_available_resources:
                    # 根据概率权重选择资源
                    weights = []
                    for res in map_available_resources:
                        resource_name = res.get('name', '')
                        weight = selected_items.get(resource_name, 0.1)
                        weights.append(weight)

                    print(f"[DEBUG] 资源权重: {dict(zip([r.get('name') for r in map_available_resources], weights))}")

                    # 使用权重随机选择
                    resource = random.choices(map_available_resources, weights=weights)[0]
                    print(f"[DEBUG] 选中的资源: {resource.get('name')} (等级{resource.get('level')})")
                else:
                    # 如果地图配置的资源都不可用，检查是否有其他可采集资源
                    if available_resources:
                        resource = random.choice(available_resources)
                        print(f"[DEBUG] 回退选择资源: {resource.get('name')} (等级{resource.get('level')})")
                    else:
                        return {'content': f'你的{info["skill_name"]}技能等级({skill_level})不足，无法采集此地的任何资源！', 'rewards': {}}
            else:
                # 没有地图配置，使用普通随机选择
                if available_resources:
                    resource = random.choice(available_resources)
                    print(f"[DEBUG] 普通随机选择资源: {resource.get('name')} (等级{resource.get('level')})")
                else:
                    return {'content': f'你的{info["skill_name"]}技能等级({skill_level})不足，无法采集任何资源！', 'rewards': {}}

        # 技能等级已在上面获取
        current_level = skill_level

        # 计算采集成功率
        base_success_rate = 0.70  # 基础成功率70%
        skill_success_bonus = skill_level * 0.001  # 每级+0.1%
        max_success_rate = 0.95  # 最高成功率95%（250级达到）
        success_rate = min(base_success_rate + skill_success_bonus, max_success_rate)

        print(f"[DEBUG] 采集成功率计算 - 技能等级: {skill_level}, 基础成功率: {base_success_rate:.1%}, 技能加成: {skill_success_bonus:.1%}, 最终成功率: {success_rate:.1%}")

        # 判断采集是否成功
        if random.random() > success_rate:
            # 采集失败
            print(f"[DEBUG] 采集失败 - 随机数超过成功率")
            return {
                'content': f'采集失败！你的{info["skill_name"]}技能还不够熟练。\n成功率：{success_rate:.1%}（基础{base_success_rate:.1%} + 技能加成{skill_success_bonus:.1%}）',
                'rewards': {}
            }

        print(f"[DEBUG] 采集成功！")

        # 计算暴击率（每10级技能等级提升1%暴击率）
        crit_rate = min(skill_level // 10 * 0.01, 0.20)  # 最高20%暴击率
        is_critical = random.random() < crit_rate

        print(f"[DEBUG] 暴击率计算 - 技能等级: {skill_level}, 暴击率: {crit_rate:.1%}, 是否暴击: {is_critical}")

        # 计算采集数量（重新设计平衡性）

        # 单次采集的基础数量：固定1个
        base_quantity_per_gather = 1

        # 技能概率加成：每10级技能提升1%概率获得额外1个采集品
        skill_bonus_chance = (skill_level // 10) * 0.01  # 每10级1%概率
        skill_bonus_per_gather = 0
        if random.random() < skill_bonus_chance:
            skill_bonus_per_gather = 1  # 固定额外1个

        # 单次采集获得的数量
        quantity_per_gather = base_quantity_per_gather + skill_bonus_per_gather

        # 暴击效果：双倍采集物资
        if is_critical:
            quantity_per_gather *= 2
            print(f"[DEBUG] 暴击！采集数量翻倍: {quantity_per_gather}")

        # 工具决定采集次数，总数量 = 单次数量 × 采集次数
        total_quantity = quantity_per_gather * gather_times

        print(f"[DEBUG] 采集数量计算 - 技能等级: {skill_level}, 基础: {base_quantity_per_gather}, 技能加成概率: {skill_bonus_chance:.1%}, 技能加成: {skill_bonus_per_gather}")
        print(f"[DEBUG] 单次采集数量: {quantity_per_gather}, 采集次数: {gather_times}, 总数量: {total_quantity}")

        quantity = total_quantity

        resource_data = {
            'id': resource['id'],
            'name': resource['name'],
            'quantity': quantity
        }

        # 技能经验增长（只根据采集品等级计算，工具不提供经验加成）
        # 基础经验获得：采集品等级 + 1
        resource_level = int(resource.get('level', 1))
        exp_gain = resource_level + 1

        print(f"[DEBUG] 采集经验计算 - 资源等级: {resource_level}, 获得经验: {exp_gain}")
        skill['exp'] += exp_gain

        # 使用武功系统的二次方升级公式：经验需求 = 系数 × (等级+1)²
        # 采集技能使用较低的系数，比基础武功稍难一些
        coefficient = 60  # 比基础武功(50)稍高，但比稀有武功(80)低

        leveled_up = False
        new_level = current_level
        current_exp = skill['exp']

        # 检查升级（可能连续升级）
        while True:
            exp_needed = coefficient * (new_level + 1) ** 2
            if current_exp >= exp_needed:
                current_exp -= exp_needed
                new_level += 1
                leveled_up = True
            else:
                break

        # 更新技能数据
        skill['level'] = new_level
        skill['exp'] = current_exp
        need_exp = coefficient * (new_level + 1) ** 2

        player['gather_skills'][gather_type] = skill

        # 同步到武学界面的生活技能中
        skill_name = info['skill_name']
        if 'martial_skills' not in player:
            player['martial_skills'] = []

        # 查找或创建对应的生活技能
        martial_skill = None
        for ms in player['martial_skills']:
            if ms.get('name') == skill_name or ms.get('名称') == skill_name:
                martial_skill = ms
                break

        if martial_skill is None:
            # 创建新的生活技能
            martial_skill = {
                'name': skill_name,
                '名称': skill_name,
                'type': '生活技能',
                '类型': '生活技能',
                'quality': '普通',
                '品质': '普通',
                'level': 0,
                '等级': 0,
                'exp': 0,
                '经验': 0,
                'unlocked': True,
                '解锁': True,
                'equipped': False,
                '装备': False
            }
            player['martial_skills'].append(martial_skill)

        # 同步经验和等级
        martial_skill['level'] = new_level
        martial_skill['等级'] = new_level
        martial_skill['exp'] = current_exp
        martial_skill['经验'] = current_exp
        martial_skill['maxExp'] = need_exp
        martial_skill['最大经验'] = need_exp

        print(f"[DEBUG] 同步生活技能到武学界面: {skill_name} Lv{new_level} ({current_exp}/{need_exp})")

        # 构建采集结果描述
        content = f"你采集到了{quantity}个{resource['name']}！"
        if is_critical:
            content += f"\n💥 暴击！获得双倍采集物资！"
        content += f"\n使用工具：{best_tool['name']}（等级{tool_level}，可采集{gather_times}次）"
        content += f"\n暴击率：{crit_rate:.1%}（技能等级{skill_level}）"

        # 详细经验获得说明
        skill_name = info['skill_name']
        exp_detail = f"\n获得经验：+{exp_gain} 点（{resource['name']}等级{resource_level}）"
        content += exp_detail

        content += f"\n{skill_name}技能：Lv{new_level} ({current_exp}/{need_exp})"

        if leveled_up:
            level_diff = new_level - current_level
            if level_diff > 1:
                content += f"\n🎉 {skill_name}技能连续升级{level_diff}级！当前等级Lv{new_level}"
            else:
                content += f"\n🎉 {skill_name}技能升级！当前等级Lv{new_level}"

        # 添加技能提示
        if new_level >= 20:
            content += f"\n🏆 你已经是{skill_name}宗师了！"
        elif new_level >= 10:
            content += f"\n💪 你已经是{skill_name}高手了！"
        elif new_level >= 5:
            content += f"\n👍 你的{skill_name}技能日渐精进！"

        return {'content': content, 'rewards': {'物品': resource_data}}

    @staticmethod
    def get_gathering_tool_and_resource_map():
        """
        自动生成采集类型-工具-可采集物品的对照表。
        返回格式：
        {
            'mining': {
                'tool_type': 'pickaxe',
                'tools': [物品对象...],
                'resource_type': 'ore',
                'resources': [物品对象...]
            },
            ...
        }
        """
        items_config = load_items_config()
        # 修正：使用小写英文类型
        gather_map = {
            'mining':   {'tool_type': 'pickaxe', 'resource_type': 'ore'},
            'logging':  {'tool_type': 'axe',     'resource_type': 'wood'},
            'herbalism':{'tool_type': 'sickle',  'resource_type': 'herb'},
            'skinning': {'tool_type': 'knife',   'resource_type': 'fur'},
        }
        result = {}
        for gather_type, info in gather_map.items():
            # 修正：使用正确的字段名 'type' 而不是 '类型'
            tools = [v for v in items_config.values() if v.get('type') == info['tool_type']]
            resources = [v for v in items_config.values() if v.get('type') == info['resource_type']]
            result[gather_type] = {
                'tool_type': info['tool_type'],
                'tools': [{'id': t['id'], 'name': t['name'], 'level': int(t.get('level', 1))} for t in tools],
                'resource_type': info['resource_type'],
                'resources': [{'id': r['id'], 'name': r['name'], 'level': int(r.get('level', 0))} for r in resources]
            }
        return result

    @staticmethod
    def get_gather_event_for_map_level(gather_type: str, map_level: int):
        """
        根据采集类型和地图等级，返回最合适的采集工具和可采集物品。
        返回格式：
        {
            'tool': {'id': ..., 'name': ..., 'level': ...},
            'resource': {'id': ..., 'name': ..., 'level': ...}
        }
        """
        mapping = EventManager.get_gathering_tool_and_resource_map()
        if gather_type not in mapping:
            return None
        tools = mapping[gather_type]['tools']
        resources = mapping[gather_type]['resources']
        # 选择level<=map_level的最高级工具/物品，否则取level最接近的
        tool = sorted(tools, key=lambda x: (abs(x['level'] - map_level), -x['level']))[0] if tools else None
        resource = sorted(resources, key=lambda x: (abs(x['level'] - map_level), -x['level']))[0] if resources else None
        return {'tool': tool, 'resource': resource}

    @staticmethod
    async def process_empty_event(player: dict, map_level: int = 1) -> dict:
        # 普通事件奖励历练值，数量等于地图等级
        exp = max(1, int(map_level))
        player['exp'] = player.get('exp', 0) + exp
        # 读取普通事件内容
        import os, json, random
        events_path = os.path.join(os.path.dirname(__file__), 'empty_events.json')
        try:
            with open(events_path, 'r', encoding='utf-8') as f:
                events = json.load(f)
            content = random.choice(events)['content']
        except Exception as e:
            content = f'你在江湖中漫步，感受武侠世界的魅力。 (加载普通事件失败)'
        # 用HTML高亮奖励部分
        highlight = f'<span style="color:#e4393c;font-weight:bold;">历练值+{exp}</span>'
        return {
            'content': f'{content} {highlight}',
            'rewards': {'历练值+': exp}
        }

    @staticmethod
    async def process_adventure(player: dict, map_level: int = 1, server=None, user_id=None) -> dict:
        """
        处理奇遇事件 - 统一版本
        结合传统奖励和物品奖励
        """
        import random
        import os
        import json
        from item_system import item_system, ItemType, ItemQuality

        # 奇遇事件内容
        adventure_contents = [
            "你发现了一个神秘的洞穴，在其中找到了宝物！",
            "你遇到了一位隐世高人，他传授了你一些武学心得。",
            "你在古迹中发现了前人留下的秘籍残页。",
            "你救助了一位受伤的武者，他感激地给了你一些珍贵物品。",
            "你在山林中偶遇一位江湖前辈，获得了意外收获。",
            "你在古庙中发现了一个隐藏的密室，里面有珍贵的武学典籍。"
        ]

        content = random.choice(adventure_contents)
        rewards = {}
        reward_text_parts = []

        # 基础奖励：历练值和银两
        base_exp = random.randint(100, 300)
        base_money = random.randint(100, 500)

        # 应用境界增益
        exp_gain_bonus = player.get('experience_gain_bonus', 0)
        money_gain_bonus = player.get('money_gain_bonus', 0)

        final_exp = base_exp + exp_gain_bonus
        final_money = base_money + money_gain_bonus

        player['exp'] = player.get('exp', 0) + final_exp
        player['money'] = player.get('money', 0) + final_money

        rewards['历练值'] = final_exp
        rewards['银两'] = final_money
        reward_text_parts.append(f'历练值+{final_exp}')
        reward_text_parts.append(f'银两+{final_money}')

        # 30% 概率获得武学点
        if random.random() < 0.3:
            skill_points = random.randint(1, 5)
            player['skill_points'] = player.get('skill_points', 0) + skill_points
            rewards['武学点'] = skill_points
            reward_text_parts.append(f'武学点+{skill_points}')

        # 特殊奖励：秘籍残页或装备
        special_reward_text = await EventManager._process_adventure_special_reward(
            player, map_level, server, user_id, rewards
        )
        if special_reward_text:
            reward_text_parts.append(special_reward_text)

        # 生成最终内容
        if reward_text_parts:
            reward_highlight = ', '.join(reward_text_parts)
            final_content = f'{content} <span style="color:#e4393c;font-weight:bold;">{reward_highlight}</span>'
        else:
            final_content = content

        return {
            'content': final_content,
            'rewards': rewards
        }

    @staticmethod
    async def _process_adventure_special_reward(player: dict, map_level: int, server=None, user_id=None, rewards=None) -> str:
        """
        处理奇遇事件的特殊奖励
        """
        import random
        import os
        import json
        from item_system import item_system, ItemType, ItemQuality

        # 60% 概率获得特殊奖励
        if random.random() > 0.6:
            return ""

        # 80% 概率获得完整秘籍，20% 概率获得装备
        if random.random() < 0.8:
            return await EventManager._give_adventure_skill_book(player, map_level, server, user_id, rewards)
        else:
            return await EventManager._give_adventure_equipment(player, map_level, server, user_id, rewards)

    @staticmethod
    async def _give_adventure_skill_book(player: dict, map_level: int, server=None, user_id=None, rewards=None) -> str:
        """
        给予完整武功秘籍（奇遇事件专用）
        根据地图等级和items_book.json筛选合适的秘籍
        """
        import random
        import os
        import json
        from item_system import item_system, ItemType

        # 加载秘籍配置
        books_path = os.path.join(os.path.dirname(__file__), 'items_book.json')
        try:
            with open(books_path, 'r', encoding='utf-8') as f:
                books_config = json.load(f)
        except Exception as e:
            # 兜底：使用物品系统中的秘籍
            skill_books = []
            for item in item_system.items.values():
                if item.type == ItemType.BOOK:
                    skill_books.append(item)

            if skill_books:
                chosen_item = random.choice(skill_books)
                if server and user_id:
                    success = await server.add_item_to_inventory(user_id, player, {
                        'id': chosen_item.id,
                        'quantity': 1
                    })
                    if success:
                        return chosen_item.name
                    else:
                        return "背包已满"
            return ""

        # 根据地图等级筛选合适的秘籍
        suitable_books = []
        for book in books_config:
            quality = book.get('quality', 'common')

            if map_level == 1:
                # 1级地图：只给普通品质秘籍
                if quality == 'common':
                    suitable_books.append(book)
            elif map_level == 2:
                # 2级地图：普通和稀有品质秘籍
                if quality in ['common', 'rare']:
                    suitable_books.append(book)
            elif map_level >= 3:
                # 3级及以上地图：所有品质秘籍
                suitable_books.append(book)

        if not suitable_books:
            return ""

        chosen_book = random.choice(suitable_books)
        book_id = chosen_book['id']

        # 检查物品是否在物品系统中存在
        book_item = item_system.items.get(book_id)
        if book_item and server and user_id:
            success = await server.add_item_to_inventory(user_id, player, {
                'id': book_item.id,
                'quantity': 1
            })

            if success:
                return book_item.name
            else:
                return "背包已满"

        return ""

    @staticmethod
    async def _give_adventure_equipment(player: dict, map_level: int, server=None, user_id=None, rewards=None) -> str:
        """
        给予奇遇装备
        """
        import random
        from item_system import item_system, ItemType, ItemQuality

        # 装备类型
        equipment_types = [ItemType.WEAPON, ItemType.ARMOR, ItemType.HELMET, ItemType.NECKLACE,
                          ItemType.RING, ItemType.BRACELET, ItemType.MEDAL]

        candidates = []
        for item in item_system.items.values():
            if item.type in equipment_types:
                quality = item.quality.value if hasattr(item.quality, 'value') else item.quality

                # 根据地图等级筛选品质
                if map_level == 1 and quality == 'common':
                    candidates.append(item)
                elif map_level == 2 and quality in ['common', 'uncommon']:
                    candidates.append(item)
                elif map_level >= 3:
                    candidates.append(item)

        if not candidates:
            return ""

        chosen_item = random.choice(candidates)

        if server and user_id:
            success = await server.add_item_to_inventory(user_id, player, {
                'id': chosen_item.id,
                'quantity': 1
            })

            if success:
                return chosen_item.name
            else:
                return "背包已满"

        return ""

# 创建全局事件管理器实例
event_manager = EventManager() 