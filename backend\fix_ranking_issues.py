#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复排行榜问题的综合脚本
1. 更新玩家数据，补全缺失字段
2. 创建测试数据
3. 验证排行榜功能
"""

import aiosqlite
import asyncio
import json
import os
import random
from datetime import datetime

# 数据库路径
DB_PATH = os.path.join(os.path.dirname(__file__), 'game.db')

async def fix_player_data():
    """修复玩家数据，补全所有必要字段"""
    print("🔧 修复玩家数据...")
    print("-" * 30)
    
    async with aiosqlite.connect(DB_PATH) as db:
        # 获取所有玩家
        async with db.execute("""
            SELECT p.user_id, p.data, u.character_name
            FROM players p
            JOIN users u ON p.user_id = u.id
            WHERE u.status = 'active'
        """) as cursor:
            rows = await cursor.fetchall()
        
        print(f"📊 找到 {len(rows)} 个玩家")
        
        fixed_count = 0
        for user_id, data_str, character_name in rows:
            try:
                player_data = json.loads(data_str) if data_str else {}
                has_updates = False
                
                # 必要字段及其默认值
                required_fields = {
                    'adventure_count': 0,
                    'experience': 0,
                    'skill_points': 0,
                    'money': 1000,
                    'level': 1,
                    'hp': 100,
                    'max_hp': 100,
                    'mp': 50,
                    'max_mp': 50,
                    'energy': 100,
                    'max_energy': 100,
                    'spirit': 100,
                    'max_spirit': 100,
                    'attack': 10,
                    'defense': 5,
                    'reputation': 0,
                    'karma': 0,
                    'current_map': 'changan'
                }
                
                # 检查并补全字段
                for field, default_value in required_fields.items():
                    if field not in player_data:
                        player_data[field] = default_value
                        has_updates = True
                
                # 确保数值字段为正确类型
                numeric_fields = ['adventure_count', 'experience', 'skill_points', 'money', 'level', 
                                'hp', 'max_hp', 'mp', 'max_mp', 'energy', 'max_energy', 
                                'spirit', 'max_spirit', 'attack', 'defense', 'reputation', 'karma']
                
                for field in numeric_fields:
                    if field in player_data:
                        try:
                            player_data[field] = max(0, int(player_data[field]))
                        except (ValueError, TypeError):
                            player_data[field] = required_fields.get(field, 0)
                            has_updates = True
                
                # 确保背包存在
                if 'inventory' not in player_data:
                    player_data['inventory'] = []
                    has_updates = True
                
                # 确保装备存在
                if 'equipment' not in player_data:
                    player_data['equipment'] = {}
                    has_updates = True
                
                # 保存更新
                if has_updates:
                    await db.execute("""
                        UPDATE players 
                        SET data = ? 
                        WHERE user_id = ?
                    """, (json.dumps(player_data, ensure_ascii=False), user_id))
                    fixed_count += 1
                    print(f"  ✅ {character_name}: 数据已修复")
                
            except Exception as e:
                print(f"  ❌ {character_name}: 修复失败 - {e}")
        
        await db.commit()
        print(f"✅ 修复完成，共处理 {fixed_count} 个玩家")

async def create_test_ranking_data():
    """创建测试排行榜数据"""
    print("\n🎮 创建测试排行榜数据...")
    print("-" * 30)
    
    async with aiosqlite.connect(DB_PATH) as db:
        # 获取所有玩家
        async with db.execute("""
            SELECT p.user_id, p.data, u.character_name
            FROM players p
            JOIN users u ON p.user_id = u.id
            WHERE u.status = 'active'
        """) as cursor:
            rows = await cursor.fetchall()
        
        if len(rows) == 0:
            print("❌ 没有找到玩家数据")
            return
        
        # 为每个玩家生成随机的排行榜数据
        for user_id, data_str, character_name in rows:
            try:
                player_data = json.loads(data_str)
                
                # 生成随机数据
                player_data['money'] = random.randint(1000, 100000)
                player_data['experience'] = random.randint(0, 50000)
                player_data['adventure_count'] = random.randint(0, 1000)
                player_data['level'] = min(100, max(1, player_data['experience'] // 1000 + 1))
                
                # 保存更新
                await db.execute("""
                    UPDATE players 
                    SET data = ? 
                    WHERE user_id = ?
                """, (json.dumps(player_data, ensure_ascii=False), user_id))
                
                print(f"  🎲 {character_name}: 银两={player_data['money']}, 经验={player_data['experience']}, 闯江湖={player_data['adventure_count']}次")
                
            except Exception as e:
                print(f"  ❌ {character_name}: 生成数据失败 - {e}")
        
        await db.commit()
        print("✅ 测试数据生成完成")

async def test_ranking_queries():
    """测试排行榜查询"""
    print("\n🏆 测试排行榜查询...")
    print("-" * 30)
    
    async with aiosqlite.connect(DB_PATH) as db:
        # 测试富豪榜
        print("💰 富豪榜前10名:")
        async with db.execute("""
            SELECT u.character_name, CAST(COALESCE(json_extract(p.data, '$.money'), 0) AS INTEGER) as money
            FROM users u 
            JOIN players p ON u.id = p.user_id 
            WHERE u.status = 'active' AND p.data IS NOT NULL
            ORDER BY CAST(COALESCE(json_extract(p.data, '$.money'), 0) AS INTEGER) DESC 
            LIMIT 10
        """) as cursor:
            rows = await cursor.fetchall()
            for i, (name, money) in enumerate(rows, 1):
                print(f"  {i:2d}. {name:<15} - {money:,}两")
        
        # 测试经验榜
        print("\n⭐ 经验榜前10名:")
        async with db.execute("""
            SELECT u.character_name, CAST(COALESCE(json_extract(p.data, '$.experience'), 0) AS INTEGER) as experience
            FROM users u 
            JOIN players p ON u.id = p.user_id 
            WHERE u.status = 'active' AND p.data IS NOT NULL
            ORDER BY CAST(COALESCE(json_extract(p.data, '$.experience'), 0) AS INTEGER) DESC 
            LIMIT 10
        """) as cursor:
            rows = await cursor.fetchall()
            for i, (name, exp) in enumerate(rows, 1):
                print(f"  {i:2d}. {name:<15} - {exp:,}点")
        
        # 测试肝帝榜
        print("\n🗡️ 肝帝榜前10名:")
        async with db.execute("""
            SELECT u.character_name, CAST(COALESCE(json_extract(p.data, '$.adventure_count'), 0) AS INTEGER) as adventure_count
            FROM users u 
            JOIN players p ON u.id = p.user_id 
            WHERE u.status = 'active' AND p.data IS NOT NULL
            ORDER BY CAST(COALESCE(json_extract(p.data, '$.adventure_count'), 0) AS INTEGER) DESC 
            LIMIT 10
        """) as cursor:
            rows = await cursor.fetchall()
            for i, (name, count) in enumerate(rows, 1):
                print(f"  {i:2d}. {name:<15} - {count:,}次")

async def verify_data_integrity():
    """验证数据完整性"""
    print("\n🔍 验证数据完整性...")
    print("-" * 30)
    
    async with aiosqlite.connect(DB_PATH) as db:
        # 检查字段完整性
        async with db.execute("""
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN json_extract(data, '$.adventure_count') IS NOT NULL THEN 1 END) as has_adventure_count,
                COUNT(CASE WHEN json_extract(data, '$.experience') IS NOT NULL THEN 1 END) as has_experience,
                COUNT(CASE WHEN json_extract(data, '$.money') IS NOT NULL THEN 1 END) as has_money,
                COUNT(CASE WHEN json_extract(data, '$.level') IS NOT NULL THEN 1 END) as has_level
            FROM players p
            JOIN users u ON p.user_id = u.id
            WHERE u.status = 'active'
        """) as cursor:
            row = await cursor.fetchone()
            total, has_adventure_count, has_experience, has_money, has_level = row
            
            print(f"📊 字段完整性检查:")
            print(f"  总玩家数: {total}")
            print(f"  adventure_count: {has_adventure_count}/{total} ({'✅' if has_adventure_count == total else '❌'})")
            print(f"  experience: {has_experience}/{total} ({'✅' if has_experience == total else '❌'})")
            print(f"  money: {has_money}/{total} ({'✅' if has_money == total else '❌'})")
            print(f"  level: {has_level}/{total} ({'✅' if has_level == total else '❌'})")
            
            if has_adventure_count == total and has_experience == total and has_money == total and has_level == total:
                print("✅ 所有必要字段完整！")
                return True
            else:
                print("❌ 仍有字段缺失")
                return False

async def main():
    """主函数"""
    print("🛠️ 排行榜问题修复工具")
    print("=" * 50)
    
    # 检查数据库
    if not os.path.exists(DB_PATH):
        print("❌ 数据库文件不存在，请先启动游戏服务器")
        return
    
    try:
        # 1. 修复玩家数据
        await fix_player_data()
        
        # 2. 验证数据完整性
        is_complete = await verify_data_integrity()
        
        if not is_complete:
            print("❌ 数据修复失败，请检查错误信息")
            return
        
        # 3. 询问是否创建测试数据
        print("\n" + "=" * 50)
        create_test = input("是否创建测试排行榜数据？(y/n): ").strip().lower()
        if create_test == 'y':
            await create_test_ranking_data()
        
        # 4. 测试排行榜查询
        await test_ranking_queries()
        
        print("\n" + "=" * 50)
        print("✅ 所有问题已修复！")
        print("🎮 现在可以在游戏中测试排行榜功能了")
        print("\n📝 修复内容:")
        print("1. ✅ 补全了所有玩家的 adventure_count 字段")
        print("2. ✅ 修复了数据类型问题")
        print("3. ✅ 优化了排行榜查询SQL")
        print("4. ✅ 增加了排行榜显示范围到30名")
        print("5. ✅ 美化了前端排行榜样式")
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
