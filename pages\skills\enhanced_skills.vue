<template>
	<view class="container">
		<!-- 武学分组选项卡 -->
		<view class="category-tabs">
			<view 
				class="tab-item" 
				:class="{ active: activeTab === 'all' }"
				@click="switchTab('all')"
			>
				<text class="tab-text">全部</text>
			</view>
			<view 
				class="tab-item" 
				:class="{ active: activeTab === '拳法' }"
				@click="switchTab('拳法')"
			>
				<text class="tab-text">拳法</text>
			</view>
			<view 
				class="tab-item" 
				:class="{ active: activeTab === '剑法' }"
				@click="switchTab('剑法')"
			>
				<text class="tab-text">剑法</text>
			</view>
			<view 
				class="tab-item" 
				:class="{ active: activeTab === '轻功' }"
				@click="switchTab('轻功')"
			>
				<text class="tab-text">轻功</text>
			</view>
			<view 
				class="tab-item" 
				:class="{ active: activeTab === '内功' }"
				@click="switchTab('内功')"
			>
				<text class="tab-text">内功</text>
			</view>
		</view>

		<!-- 筛选器 -->
		<view class="filter-section">
			<view class="filter-row">
				<view class="filter-item">
					<text class="filter-label">等级:</text>
					<picker @change="onLevelChange" :value="levelIndex" :range="levelOptions">
						<view class="picker-text">{{ levelOptions[levelIndex] }}</view>
					</picker>
				</view>
				<view class="filter-item">
					<text class="filter-label">门派:</text>
					<picker @change="onSchoolChange" :value="schoolIndex" :range="schoolOptions">
						<view class="picker-text">{{ schoolOptions[schoolIndex] }}</view>
					</picker>
				</view>
			</view>
		</view>

		<!-- 武学列表 -->
		<scroll-view class="skills-list" scroll-y="true">
			<view class="skill-group">
				<view class="group-header">
					<text class="group-title">{{ getCategoryTitle() }}</text>
					<text class="group-desc">{{ getCategoryDescription() }}</text>
				</view>
				<view 
					class="skill-item" 
					v-for="(skill, index) in filteredSkills" 
					:key="skill.名称 || skill.name + '-' + index"
					@click="showSkillDetail(skill)"
				>
					<view class="skill-info">
						<view class="skill-header">
							<text class="skill-name">{{ skill.名称 || skill.name }}</text>
							<view class="skill-status-group">
								<text class="skill-level-text">{{ skill.等级 || skill.level }}级</text>
								<text class="skill-school" v-if="skill.门派 || (skill.school && skill.school !== '无门派')">{{ skill.门派 || skill.school }}</text>
								<text class="skill-equipped" v-if="skill.装备 || skill.equipped">已装备</text>
							</view>
						</view>
						<text class="skill-desc">{{ skill.描述 || skill.description }}</text>
						<view class="skill-progress" v-if="skill.等级 > 0">
							<view class="progress-bg">
								<view class="progress-fill" :style="{ width: getSkillProgress(skill) + '%' }"></view>
							</view>
							<text class="progress-text">{{ skill.经验 || skill.exp }}/{{ skill.最大经验 || skill.maxExp }}</text>
						</view>
						<view class="skill-status">
							<text class="skill-unlock" v-if="skill.解锁 || skill.unlocked">已解锁</text>
							<text class="skill-locked" v-else>未解锁</text>
						</view>
					</view>
					<view class="skill-actions">
						<button 
							class="action-btn study-btn" 
							@click.stop="studyMartial(skill)"
							:disabled="!isAuthed || !skill.解锁 || skill.unlocked"
						>
							学习
						</button>
						<button 
							v-if="canUseMartial(skill)"
							class="action-btn use-btn" 
							:class="{ 'unequip': skill.装备 || skill.equipped }"
							@click.stop="toggleMartialUse(skill)"
							:disabled="!isAuthed"
						>
							{{ skill.装备 || skill.equipped ? '卸下' : '使用' }}
						</button>
						<button 
							class="action-btn moves-btn" 
							@click.stop="showMoves(skill)"
							:disabled="!isAuthed"
						>
							招式
						</button>
					</view>
				</view>
				<view class="empty-skills" v-if="filteredSkills.length === 0">
					<text>暂无符合条件的武功</text>
				</view>
			</view>
		</scroll-view>

		<!-- 技能详情弹窗 -->
		<view class="modal-overlay" v-if="showDetail" @click="closeDetail">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">武学详情</text>
					<text class="modal-close" @click="closeDetail">×</text>
				</view>
				<view class="modal-body" v-if="selectedSkill">
					<view class="detail-header">
						<text class="detail-name">{{ selectedSkill.名称 || selectedSkill.name }}</text>
						<view class="detail-status">
							<text class="detail-level">{{ selectedSkill.等级 || selectedSkill.level }}级</text>
							<text class="detail-school" v-if="selectedSkill.门派 || (selectedSkill.school && selectedSkill.school !== '无门派')">{{ selectedSkill.门派 || selectedSkill.school }}</text>
							<text class="detail-equipped" v-if="selectedSkill.装备 || selectedSkill.equipped">已装备</text>
						</view>
					</view>
					<text class="detail-desc">{{ selectedSkill.描述 || selectedSkill.description }}</text>
					<view class="detail-progress" v-if="selectedSkill.等级 > 0">
						<text class="progress-title">进度:</text>
						<view class="progress-bar">
							<view class="progress-bg">
								<view class="progress-fill" :style="{ width: getSkillProgress(selectedSkill) + '%' }"></view>
							</view>
							<text class="progress-text">{{ selectedSkill.经验 || selectedSkill.exp }}/{{ selectedSkill.最大经验 || selectedSkill.maxExp }}</text>
						</view>
					</view>
					<view class="detail-effects" v-if="selectedSkill.等级 > 0">
						<text class="effects-title">当前效果:</text>
						<text class="effects-text">{{ getSkillEffects(selectedSkill) }}</text>
					</view>
					<view class="detail-category">
						<text class="category-title">武学分类:</text>
						<text class="category-text">{{ selectedSkill.类别 || selectedSkill.category }} - {{ selectedSkill.等级 || selectedSkill.level }}</text>
					</view>
					<view class="detail-moves" v-if="selectedSkill.招式 || selectedSkill.moves">
						<text class="moves-title">招式列表:</text>
						<view class="moves-list">
							<view class="move-item" v-for="move in selectedSkill.moves" :key="move.name">
								<text class="move-name">{{ move.name }}</text>
								<text class="move-unlock-level">（解锁等级: {{ move.unlock_level }}）</text>
								<text class="move-status" :style="{color: move.unlocked ? '#4caf50' : '#aaa'}">
									{{ move.unlocked ? '已解锁' : '未解锁' }}
								</text>
							</view>
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeDetail">关闭</button>
					<button 
						class="modal-btn confirm-btn" 
						@click="studyMartial(selectedSkill)"
						:disabled="!isAuthed || !selectedSkill.解锁 || selectedSkill.unlocked"
					>
						学习
					</button>
					<button 
						v-if="canUseMartial(selectedSkill)"
						class="modal-btn use-btn" 
						:class="{ 'unequip': selectedSkill.装备 || selectedSkill.equipped }"
						@click="toggleMartialUse(selectedSkill)"
						:disabled="!isAuthed"
					>
						{{ selectedSkill.装备 || selectedSkill.equipped ? '卸下' : '使用' }}
					</button>
				</view>
			</view>
		</view>

		<!-- 招式弹窗 -->
		<view class="modal-overlay" v-if="showMovesModal" @click="closeMovesModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">{{ selectedSkill?.名称 || selectedSkill?.name }} - 招式</text>
					<text class="modal-close" @click="closeMovesModal">×</text>
				</view>
				<view class="modal-body" v-if="selectedSkill">
					<view class="moves-container">
						<view class="move-item" v-for="(move, index) in selectedSkill.招式 || selectedSkill.moves" :key="index">
							<text class="move-name">{{ move }}</text>
							<text class="move-desc">{{ getMoveDescription(selectedSkill.名称 || selectedSkill.name, move) }}</text>
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<button class="modal-btn cancel-btn" @click="closeMovesModal">关闭</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import gameState from '../../utils/gameState.js'
import { gameUtils } from '../../utils/gameData.js'

export default {
	data() {
		return {
			player: {},
			allSkills: [],
			activeTab: 'all',
			levelIndex: 0,
			schoolIndex: 0,
			levelOptions: ['全部等级', '基础', '初级', '中级', '高级', '绝学'],
			schoolOptions: ['全部门派', '华山', '武当', '少林', '峨眉', '丐帮', '明教', '逍遥派', '天山派', '古墓派', '无门派'],
			showDetail: false,
			showMovesModal: false,
			selectedSkill: null,
			isAuthed: false
		}
	},
	
	computed: {
		filteredSkills() {
			let skills = this.allSkills
			
			// 按分类筛选
			if (this.activeTab !== 'all') {
				skills = skills.filter(skill => skill.类别 === this.activeTab)
			}
			
			// 按等级筛选
			if (this.levelIndex > 0) {
				const level = this.levelOptions[this.levelIndex]
				skills = skills.filter(skill => skill.等级 === level)
			}
			
			// 按门派筛选
			if (this.schoolIndex > 0) {
				const school = this.schoolOptions[this.schoolIndex]
				skills = skills.filter(skill => skill.门派 === school)
			}
			
			return skills
		}
	},
	
	onLoad() {
		this.updateData()
	},
	
	onShow() {
		this.updateData()
	},
	
	methods: {
		updateData() {
			this.player = gameState.getPlayerData()
			this.isAuthed = gameState.isAuthenticated ? gameState.isAuthenticated() : gameState.isAuthed
			this.loadMartialSkills()
		},
		
		async loadMartialSkills() {
			if (!this.isAuthed) return

			try {
				const response = await gameUtils.sendMessage({
					type: 'get_martial_data',
					data: {}
				})
				if (response && response.武学数据) {
					// 转换数据格式，适配增强武功系统
					let allSkills = this.convertMartialData(response.武学数据)
					// 兼容 allSkills 为 dict 或 list
					if (allSkills && !Array.isArray(allSkills)) {
						allSkills = Object.keys(allSkills).map(name => ({ name, ...allSkills[name] }))
					}
					this.allSkills = allSkills || []
				}
			} catch (e) {
				console.error('加载武学数据失败', e)
			}
		},
		
		convertMartialData(martialSkills) {
			const allSkills = []
			
			// 遍历所有武功分类
			Object.keys(martialSkills).forEach(category => {
				const skills = martialSkills[category] || []
				skills.forEach(skill => {
					// 添加武功分类信息
					allSkills.push({
						...skill,
						类别: this.getSkillCategory(skill.名称 || skill.name),
						门派: this.getSkillSchool(skill.名称 || skill.name),
						等级: this.getSkillLevel(skill.名称 || skill.name),
						描述: this.getSkillDescription(skill.名称 || skill.name),
						招式: this.getSkillMoves(skill.名称 || skill.name),
						效果: this.getSkillEffects(skill.名称 || skill.name)
					})
				})
			})
			
			return allSkills
		},
		
		getSkillCategory(skillName) {
			// 根据武功名称判断分类
			const categoryMap = {
				'基本拳法': '拳法',
				'太极拳': '拳法',
				'基本剑法': '剑法',
				'独孤九剑': '剑法',
				'凌波微步': '轻功',
				'神行百变': '轻功',
				'九阳神功': '内功',
				'九阴真经': '内功',
				'降龙十八掌': '掌法',
				'打狗棒法': '棍法'
			}
			return categoryMap[skillName] || '其他'
		},
		
		getSkillSchool(skillName) {
			// 根据武功名称判断门派
			const schoolMap = {
				'独孤九剑': '华山',
				'太极拳': '武当',
				'九阳神功': '少林',
				'九阴真经': '古墓派',
				'降龙十八掌': '丐帮',
				'打狗棒法': '丐帮',
				'凌波微步': '逍遥派',
				'神行百变': '逍遥派'
			}
			return schoolMap[skillName] || '无门派'
		},
		
		getSkillLevel(skillName) {
			// 根据武功名称判断等级
			const levelMap = {
				'基本拳法': '基础',
				'基本剑法': '基础',
				'太极拳': '高级',
				'独孤九剑': '绝学',
				'凌波微步': '绝学',
				'神行百变': '绝学',
				'九阳神功': '绝学',
				'九阴真经': '绝学',
				'降龙十八掌': '绝学',
				'打狗棒法': '绝学'
			}
			return levelMap[skillName] || '基础'
		},
		
		getSkillDescription(skillName) {
			// 根据武功名称获取描述
			const descMap = {
				'基本拳法': '最基础的拳法，习武之初必学',
				'太极拳': '武当派绝学，以柔克刚',
				'独孤九剑': '华山派绝学，破尽天下武功',
				'凌波微步': '逍遥派绝学，身法如鬼魅',
				'九阳神功': '少林派绝学，内力深厚',
				'九阴真经': '古墓派绝学，内力深厚',
				'降龙十八掌': '丐帮绝学，掌力雄浑',
				'打狗棒法': '丐帮绝学，棒法精妙'
			}
			return descMap[skillName] || '武功描述待补充'
		},
		
		getSkillMoves(skillName) {
			// 根据武功名称获取招式列表
			const movesMap = {
				'基本拳法': ['基本拳法1', '基本拳法2', '基本拳法3', '基本拳法4', '基本拳法5'],
				'太极拳': ['白鹤亮翅', '野马分鬃', '搂膝拗步', '倒卷肱', '揽雀尾'],
				'独孤九剑': ['总诀式', '破剑式', '破刀式', '破枪式', '破鞭式', '破索式', '破掌式', '破箭式', '破气式'],
				'凌波微步': ['凌波微步1', '凌波微步2', '凌波微步3', '凌波微步4'],
				'九阳神功': ['九阳护体', '九阳真气', '九阳神功'],
				'九阴真经': ['九阴护体', '九阴真气', '九阴神功'],
				'降龙十八掌': ['亢龙有悔', '飞龙在天', '见龙在田', '鸿渐于陆', '潜龙勿用'],
				'打狗棒法': ['棒打双犬', '棒打狗头', '棒打狗腿', '棒打狗尾']
			}
			return movesMap[skillName] || []
		},
		
		getMoveDescription(skillName, moveName) {
			// 根据武功和招式名称获取描述
			const descMap = {
				'基本拳法': {
					'基本拳法1': '对准敌人的胸口打出一拳！',
					'基本拳法2': '双拳齐出，敌人连连后退！',
					'基本拳法3': '对准敌人的腹部一拳！',
					'基本拳法4': '对准敌人的头部一拳！',
					'基本拳法5': '对准敌人的腿部一拳！'
				},
				'太极拳': {
					'白鹤亮翅': '一式「白鹤亮翅」，双手成白鹤亮翅之势，敌人连连后退！',
					'野马分鬃': '一式「野马分鬃」，双手成野马分鬃之势，敌人连连后退！',
					'搂膝拗步': '一式「搂膝拗步」，双手成搂膝拗步之势，敌人连连后退！',
					'倒卷肱': '一式「倒卷肱」，双手成倒卷肱之势，敌人连连后退！',
					'揽雀尾': '一式「揽雀尾」，双手成揽雀尾之势，敌人连连后退！'
				},
				'独孤九剑': {
					'总诀式': '一式「总诀式」，剑势如虹，敌人连连后退！',
					'破剑式': '一式「破剑式」，专门破解剑法，敌人连连后退！',
					'破刀式': '一式「破刀式」，专门破解刀法，敌人连连后退！',
					'破枪式': '一式「破枪式」，专门破解枪法，敌人连连后退！',
					'破鞭式': '一式「破鞭式」，专门破解鞭法，敌人连连后退！'
				}
			}
			return descMap[skillName]?.[moveName] || '招式描述待补充'
		},
		
		getSkillEffects(skill) {
			if (!skill || skill.等级 === 0) return '暂无效果'
			
			const effects = []
			const level = skill.等级
			
			// 根据武功类型和等级计算效果
			if (skill.类别 === '拳法') {
				effects.push(`攻击 +${Math.floor(level * 1.5)}`)
				effects.push(`力量 +${Math.floor(level / 10)}`)
			} else if (skill.类别 === '剑法') {
				effects.push(`攻击 +${Math.floor(level * 2)}`)
				effects.push(`悟性 +${Math.floor(level / 10)}`)
			} else if (skill.类别 === '轻功') {
				effects.push(`防御 +${Math.floor(level * 1.5)}`)
				effects.push(`闪避 +${Math.floor(level * 1.2)}`)
				effects.push(`身法 +${Math.floor(level / 10)}`)
			} else if (skill.类别 === '内功') {
				effects.push(`气血 +${Math.floor(level * 3)}`)
				effects.push(`内力 +${Math.floor(level * 2)}`)
				effects.push(`根骨 +${Math.floor(level / 10)}`)
			}
			
			return effects.length > 0 ? effects.join(', ') : '暂无效果'
		},
		
		switchTab(tab) {
			this.activeTab = tab
		},
		
		onLevelChange(e) {
			this.levelIndex = e.detail.value
		},
		
		onSchoolChange(e) {
			this.schoolIndex = e.detail.value
		},
		
		getCategoryTitle() {
			if (this.activeTab === 'all') return '全部武功'
			return this.activeTab
		},
		
		getCategoryDescription() {
			const descriptions = {
				'all': '所有可学习的武功',
				'拳法': '拳法类武功，以拳为主',
				'剑法': '剑法类武功，以剑为主',
				'轻功': '轻功类武功，提升身法',
				'内功': '内功类武功，提升内力'
			}
			return descriptions[this.activeTab] || '武功描述'
		},
		
		getSkillProgress(skill) {
			if (skill.最大经验 === 0) return 0
			return Math.min((skill.经验 / skill.最大经验) * 100, 100)
		},
		
		canUseMartial(skill) {
			// 基础武功不能使用/装备
			return skill.等级 !== '基础' && skill.解锁
		},
		
		async toggleMartialUse(skill) {
			if (!this.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' })
				return
			}
			if (!skill.解锁 && !skill.unlocked) {
				uni.showToast({ title: '该武学尚未解锁', icon: 'none' })
				return
			}
			try {
				const action = (skill.装备 || skill.equipped) ? 'unequip_martial' : 'use_martial'
				const response = await gameUtils.sendMessage({
					type: action,
					data: { skill_name: skill.名称 || skill.name }
				})
				if (response.type === action + '_success') {
					await this.loadMartialSkills();
					uni.showToast({ title: response.data.message, icon: 'success' })
				} else {
					uni.showToast({ title: response.data.message, icon: 'none' })
				}
			} catch (error) {
				console.error('武学操作失败:', error)
				uni.showToast({ title: '操作失败', icon: 'none' })
			}
		},
		
		showSkillDetail(skill) {
			this.selectedSkill = skill
			this.showDetail = true
		},
		
		closeDetail() {
			this.showDetail = false
			this.selectedSkill = null
		},
		
		showMoves(skill) {
			this.selectedSkill = skill
			this.showMovesModal = true
		},
		
		closeMovesModal() {
			this.showMovesModal = false
			this.selectedSkill = null
		},
		
		async studyMartial(skill) {
			if (!this.isAuthed) {
				uni.showToast({ title: '请先登录', icon: 'none' })
				return
			}
			if (!skill.解锁) {
				uni.showToast({ title: '该武学尚未解锁', icon: 'none' })
				return
			}
			try {
				const response = await gameUtils.sendMessage({
					type: 'study_martial',
					data: { 武学名称: skill.名称 || skill.name }
				})
				if (response.type === 'study_martial_success') {
					this.loadMartialSkills()
					uni.showToast({ title: response.data.message, icon: 'success' })
				} else {
					uni.showToast({ title: response.data.message, icon: 'none' })
				}
			} catch (error) {
				console.error('学习武学失败:', error)
				uni.showToast({ title: '学习失败', icon: 'none' })
			}
		}
	}
}
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.category-tabs {
	display: flex;
	background: #fff;
	border-radius: 20rpx;
	padding: 10rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.tab-item {
	flex: 1;
	text-align: center;
	padding: 20rpx 10rpx;
	border-radius: 15rpx;
	transition: all 0.3s;
}

.tab-item.active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tab-text {
	font-size: 28rpx;
	color: #333;
}

.tab-item.active .tab-text {
	color: #fff;
}

.filter-section {
	background: #fff;
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.filter-row {
	display: flex;
	gap: 20rpx;
}

.filter-item {
	flex: 1;
	display: flex;
	align-items: center;
}

.filter-label {
	font-size: 28rpx;
	color: #333;
	margin-right: 10rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	padding: 10rpx 20rpx;
	border-radius: 10rpx;
}

.skills-list {
	height: calc(100vh - 400rpx);
}

.skill-group {
	margin-bottom: 40rpx;
}

.group-header {
	background: #fff;
	padding: 30rpx;
	border-radius: 20rpx 20rpx 0 0;
	border-bottom: 2rpx solid #f0f0f0;
}

.group-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.group-desc {
	font-size: 24rpx;
	color: #666;
}

.skill-item {
	background: #fff;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.skill-item:last-child {
	border-bottom: none;
	border-radius: 0 0 20rpx 20rpx;
}

.skill-info {
	flex: 1;
	margin-right: 20rpx;
}

.skill-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.skill-name {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
}

.skill-status-group {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.skill-level-text {
	font-size: 24rpx;
	color: #667eea;
	background: rgba(102, 126, 234, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	margin-bottom: 5rpx;
}

.skill-school {
	font-size: 20rpx;
	color: #1890ff;
	background: rgba(24, 144, 255, 0.1);
	padding: 3rpx 10rpx;
	border-radius: 10rpx;
	margin-bottom: 5rpx;
}

.skill-equipped {
	font-size: 20rpx;
	color: #52c41a;
	background: rgba(82, 196, 26, 0.1);
	padding: 3rpx 10rpx;
	border-radius: 10rpx;
}

.skill-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 15rpx;
	line-height: 1.4;
}

.skill-progress {
	display: flex;
	align-items: center;
	margin-bottom: 10rpx;
}

.progress-bg {
	flex: 1;
	height: 20rpx;
	background: #f0f0f0;
	border-radius: 10rpx;
	margin-right: 15rpx;
	overflow: hidden;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
	border-radius: 10rpx;
	transition: width 0.3s;
}

.progress-text {
	font-size: 22rpx;
	color: #666;
	min-width: 80rpx;
}

.skill-status {
	margin-top: 10rpx;
}

.skill-unlock {
	font-size: 22rpx;
	color: #52c41a;
}

.skill-locked {
	font-size: 22rpx;
	color: #ff4d4f;
}

.skill-actions {
	display: flex;
	flex-direction: column;
	gap: 10rpx;
}

.action-btn {
	padding: 15rpx 30rpx;
	border-radius: 15rpx;
	font-size: 24rpx;
	border: none;
}

.study-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.study-btn:disabled {
	background: #ccc;
	color: #999;
}

.use-btn {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
	color: #fff;
}

.use-btn.unequip {
	background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
}

.use-btn:disabled {
	background: #ccc;
	color: #999;
}

.moves-btn {
	background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
	color: #fff;
}

.moves-btn:disabled {
	background: #ccc;
	color: #999;
}

.empty-skills {
	text-align: center;
	padding: 100rpx 0;
	color: #999;
	font-size: 28rpx;
}

/* 弹窗样式 */
.modal-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0,0,0,0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 1000;
}

.modal-content {
	background: #fff;
	border-radius: 20rpx;
	width: 80%;
	max-width: 600rpx;
	max-height: 80vh;
	overflow: hidden;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 40rpx;
	color: #999;
	cursor: pointer;
}

.modal-body {
	padding: 30rpx;
	max-height: 60vh;
	overflow-y: auto;
}

.detail-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.detail-name {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.detail-status {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.detail-level {
	font-size: 28rpx;
	color: #667eea;
	margin-bottom: 5rpx;
}

.detail-school {
	font-size: 24rpx;
	color: #1890ff;
	background: rgba(24, 144, 255, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
	margin-bottom: 5rpx;
}

.detail-equipped {
	font-size: 24rpx;
	color: #52c41a;
	background: rgba(82, 196, 26, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
}

.detail-desc {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	margin-bottom: 30rpx;
}

.detail-progress {
	margin-bottom: 30rpx;
}

.progress-title {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 15rpx;
	display: block;
}

.progress-bar {
	display: flex;
	align-items: center;
}

.detail-effects {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f6ffed;
	border-radius: 10rpx;
	border-left: 4rpx solid #52c41a;
}

.effects-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}

.effects-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.detail-category {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #f0f8ff;
	border-radius: 10rpx;
	border-left: 4rpx solid #1890ff;
}

.category-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}

.category-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.detail-moves {
	margin-bottom: 30rpx;
	padding: 20rpx;
	background: #fff7e6;
	border-radius: 10rpx;
	border-left: 4rpx solid #faad14;
}

.moves-title {
	font-size: 26rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 15rpx;
	display: block;
}

.moves-list {
	display: flex;
	flex-wrap: wrap;
	gap: 10rpx;
}

.move-item {
	font-size: 22rpx;
	color: #666;
	background: rgba(250, 173, 20, 0.1);
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
}

.moves-container {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.moves-container .move-item {
	background: #f8f9fa;
	padding: 20rpx;
	border-radius: 10rpx;
	border-left: 4rpx solid #667eea;
}

.move-name {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	margin-bottom: 10rpx;
	display: block;
}

.move-desc {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.modal-footer {
	display: flex;
	padding: 30rpx;
	border-top: 2rpx solid #f0f0f0;
	gap: 10rpx;
}

.modal-btn {
	flex: 1;
	padding: 20rpx;
	border-radius: 15rpx;
	font-size: 28rpx;
	border: none;
}

.cancel-btn {
	background: #f0f0f0;
	color: #666;
}

.confirm-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.confirm-btn:disabled {
	background: #ccc;
}

.use-btn {
	background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
	color: #fff;
}

.use-btn.unequip {
	background: linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%);
}
</style> 