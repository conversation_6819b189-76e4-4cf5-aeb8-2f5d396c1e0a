#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
武功系统处理模块
负责武功相关的业务逻辑处理
"""

import json
import os
import logging

try:
    from enhanced_martial_system import get_martial_info, get_martial_by_level
    from martial_system import load_wugong_config
except ImportError:
    # 如果模块不存在，提供默认实现
    def get_martial_info(name):
        return {'类型': '未知', 'type': '未知'}

    def get_martial_by_level(level):
        return []

    def load_wugong_config():
        return {}

logger = logging.getLogger(__name__)

# 基础武学到入门武学的映射
BASIC_TO_ENTRY = {
    '基础内功': '入门内功',
    '基础剑法': '入门剑法',
    '基础刀法': '入门刀法',
    '基础拳法': '入门拳法',
    '基础掌法': '入门掌法',
    '基础腿法': '入门腿法',
    '基础轻功': '入门轻功',
    '基础医术': '入门医术'
}

class MartialHandler:
    """武功系统处理器"""
    
    def __init__(self, game_data: dict):
        self.game_data = game_data
        self.wugong_config = game_data.get('wugong', {})
    
    def init_martial_skills(self) -> list:
        """初始化武功技能，返回list"""
        # 加载武功配置
        wugong_path = os.path.join(os.path.dirname(__file__), 'wugong_enhanced.json')
        with open(wugong_path, encoding='utf-8') as f:
            wugong_config = {item['武功名']: item for item in json.load(f)}
        
        martial_skills = []
        
        # 添加生活技能
        life_skills = ['采药', '伐木', '挖矿', '剥皮']
        for skill in life_skills:
            # 生活技能使用系数60计算经验需求
            coefficient = 60
            level = 0
            need_exp = coefficient * (level + 1) ** 2

            martial_skills.append({
                'name': skill,
                '名称': skill,
                'level': level,
                '等级': level,
                'exp': 0,
                '经验': 0,
                'maxExp': need_exp,
                '最大经验': need_exp,
                'unlocked': True,
                '解锁': True,
                'equipped': False,
                '装备': False,
                'type': '生活技能',
                '类型': '生活技能',
                'quality': '普通',
                '品质': '普通',
                '是否可招架': '否'
            })
        
        # 添加增强武功系统的普通武功（默认解锁）
        common_martials = get_martial_by_level('普通')
        for martial_name in common_martials:
            wcfg = wugong_config.get(martial_name, {})
            martial_skills.append({
                'name': martial_name,
                'level': 0, 
                'exp': 0, 
                'unlocked': True, 
                'equipped': False, 
                'type': self.get_martial_type(martial_name),
                '是否可招架': wcfg.get('是否可招架', '否')
            })
        
        # 添加其他品质的武功（未解锁）
        for quality in ['稀有', '绝世', '传说']:
            martials = get_martial_by_level(quality)
            for martial_name in martials:
                wcfg = wugong_config.get(martial_name, {})
                martial_skills.append({
                    'name': martial_name,
                    'level': 0, 
                    'exp': 0, 
                    'unlocked': False, 
                    'equipped': False, 
                    'type': self.get_martial_type(martial_name),
                    '是否可招架': wcfg.get('是否可招架', '否')
                })
        
        return martial_skills
    
    def get_martial_type(self, martial_name: str) -> str:
        """获取武功类型，兼容 enhanced_martial_system"""
        info = get_martial_info(martial_name)
        return info.get('类型') or info.get('type') or '未知'
    
    def calculate_martial_exp_gain(self, skill_name: str, current_level: int, player: dict) -> tuple:
        """计算武功经验值增长
        
        Returns:
            tuple: (exp_gain, intelligence_bonus_exp)
        """
        # 使用增强武功系统计算经验值增长
        martial_info = self.wugong_config.get(skill_name)
        if martial_info:
            # 使用增强武功系统的系数
            coefficient = martial_info.get('coefficient', 50)
            base_exp_gain = 10 + current_level * 2
        else:
            # 兼容旧系统
            coefficient = 1.0  # 默认系数
            base_exp_gain = 10 + current_level * 2
        
        # 悟性加成
        intelligence = player.get('talent', {}).get('悟性', 0)
        intelligence_bonus_exp = int(base_exp_gain * intelligence * 0.01)
        
        total_exp_gain = base_exp_gain + intelligence_bonus_exp
        
        return total_exp_gain, intelligence_bonus_exp
    
    def process_martial_level_up(self, skill_data: dict, exp_gain: int) -> tuple:
        """处理武功升级
        
        Returns:
            tuple: (new_level, level_up)
        """
        current_level = skill_data.get('level', 0)
        current_exp = skill_data.get('exp', 0)
        
        # 添加经验值
        new_exp = current_exp + exp_gain
        skill_data['exp'] = new_exp
        
        # 检查是否升级
        level_up = False
        new_level = current_level
        
        while True:
            # 计算升级所需经验值
            exp_needed = self.calculate_level_up_exp(new_level)
            
            if new_exp >= exp_needed:
                new_exp -= exp_needed
                new_level += 1
                level_up = True
                skill_data['exp'] = new_exp
                skill_data['level'] = new_level
            else:
                break
        
        return new_level, level_up
    
    def calculate_level_up_exp(self, level: int) -> int:
        """计算升级所需经验值"""
        return 100 + level * 50
    
    def check_martial_unlock(self, skill_name: str, new_level: int, martial_skills: dict) -> str:
        """检查是否解锁新武学
        
        Returns:
            str: 解锁的武学名称，如果没有则返回空字符串
        """
        if skill_name in BASIC_TO_ENTRY and new_level >= 20:
            entry_skill_name = BASIC_TO_ENTRY[skill_name]
            if entry_skill_name in martial_skills:
                entry_skill = martial_skills[entry_skill_name]
                entry_skill['unlocked'] = True
                return entry_skill_name
        
        return ""
    
    def apply_martial_attribute_bonus(self, player: dict, skill_name: str, old_level: int, new_level: int):
        """应用武功属性加成"""
        # 根据武功类型和等级差异应用属性加成
        martial_type = self.get_martial_type(skill_name)
        level_diff = new_level - old_level
        
        if level_diff <= 0:
            return
        
        # 不同武功类型的属性加成
        attribute_bonuses = {
            '内功': {'max_hp': 5, 'max_mp': 3},
            '剑法': {'attack': 2, 'crit': 0.01},
            '刀法': {'attack': 3, 'defense': 1},
            '拳法': {'attack': 2, 'max_hp': 2},
            '掌法': {'attack': 2, 'max_mp': 2},
            '腿法': {'attack': 1, 'dodge': 0.02},
            '轻功': {'dodge': 0.03, 'max_energy': 2},
            '医术': {'max_hp': 8, 'hp_regen': 0.1}
        }
        
        bonuses = attribute_bonuses.get(martial_type, {})
        
        for attr, bonus_per_level in bonuses.items():
            total_bonus = bonus_per_level * level_diff
            
            if attr in ['max_hp', 'max_mp', 'max_energy', 'attack', 'defense']:
                player[attr] = player.get(attr, 0) + total_bonus
                # 如果是生命值相关，同时增加当前生命值
                if attr == 'max_hp':
                    player['hp'] = player.get('hp', 0) + total_bonus
            elif attr in ['crit', 'dodge', 'hp_regen']:
                player[attr] = player.get(attr, 0.0) + total_bonus
    
    def get_martial_configs(self) -> list:
        """获取武功配置数据"""
        wugong_path = os.path.join(os.path.dirname(__file__), 'wugong_enhanced.json')
        try:
            with open(wugong_path, encoding='utf-8') as f:
                configs = json.load(f)
                logger.info(f"成功加载武功配置，共 {len(configs)} 个武功")
                return configs
        except Exception as e:
            logger.error(f"加载武功配置失败: {e}")
            return []
