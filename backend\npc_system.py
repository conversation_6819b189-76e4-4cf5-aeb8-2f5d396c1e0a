#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
NPC系统管理
"""

import json
import os
import random
from typing import Dict, List, Optional

class NPCSystem:
    def __init__(self, server=None):
        self.server = server
        self.npcs_config = {}
        self.load_npc_config()
    
    def load_npc_config(self):
        """加载NPC配置"""
        config_path = os.path.join(os.path.dirname(__file__), 'npc_config.json')
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.npcs_config = config.get('npcs', {})
        except Exception as e:
            print(f"加载NPC配置失败: {e}")
            self.npcs_config = {}
    
    def get_npc_info(self, npc_name: str) -> Optional[Dict]:
        """获取NPC信息"""
        return self.npcs_config.get(npc_name)
    
    def get_map_npcs(self, map_id: str, npc_names: List[str]) -> List[Dict]:
        """获取地图上的NPC列表"""
        npcs = []
        for i, npc_name in enumerate(npc_names):
            npc_config = self.get_npc_info(npc_name)
            if npc_config:
                npc_data = {
                    'id': f"{map_id}_{npc_config['id']}",
                    'name': npc_config['name'],
                    'avatar': npc_config.get('avatar', 'static/npc/default.png'),
                    'desc': npc_config.get('description', f'{npc_name}：一位神秘的江湖人物。'),
                    'functions': npc_config.get('functions', [])
                }
            else:
                # 兜底处理
                npc_data = {
                    'id': f"{map_id}_npc_{i}",
                    'name': npc_name,
                    'avatar': 'static/npc/default.png',
                    'desc': f'{npc_name}：一位神秘的江湖人物。',
                    'functions': [
                        {'key': 'talk', 'label': '对话'},
                        {'key': 'shop', 'label': '交易'}
                    ]
                }
            npcs.append(npc_data)
        return npcs
    
    async def handle_npc_function(self, player: Dict, user_id: str, npc_name: str, function_key: str, data: Dict = None) -> Dict:
        """处理NPC功能"""
        npc_config = self.get_npc_info(npc_name)
        if not npc_config:
            return {'type': 'error', 'data': {'message': 'NPC不存在'}}
        
        if function_key == 'talk':
            return await self._handle_talk(player, npc_config)
        elif function_key == 'shop':
            return await self._handle_shop(player, user_id, npc_config, data)
        elif function_key == 'sell':
            return await self._handle_sell(player, user_id, npc_config, data)
        elif function_key == 'repair':
            return await self._handle_repair(player, user_id, npc_config, data)
        elif function_key == 'upgrade':
            return await self._handle_upgrade(player, user_id, npc_config, data)
        elif function_key == 'escort':
            return await self._handle_escort(player, user_id, npc_config, data)
        elif function_key == 'transport':
            return await self._handle_transport(player, user_id, npc_config, data)
        elif function_key == 'storage':
            return await self._handle_storage(player, user_id, npc_config, data)
        elif function_key == 'alchemy':
            return await self._handle_alchemy(player, user_id, npc_config, data)
        elif function_key == 'heal':
            return await self._handle_heal(player, user_id, npc_config, data)
        elif function_key == 'identify':
            return await self._handle_identify(player, user_id, npc_config, data)
        elif function_key == 'info':
            return await self._handle_info(player, user_id, npc_config, data)
        elif function_key == 'learn':
            return await self._handle_learn(player, user_id, npc_config, data)
        else:
            return {'type': 'error', 'data': {'message': '未知功能'}}
    
    async def _handle_talk(self, player: Dict, npc_config: Dict) -> Dict:
        """处理对话"""
        dialogues = npc_config.get('dialogues', ['你好，有什么可以帮助你的吗？'])
        dialogue = random.choice(dialogues)
        return {
            'type': 'npc_talk',
            'data': {
                'npc_name': npc_config['name'],
                'dialogue': dialogue
            }
        }
    
    async def _handle_shop(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理商店功能"""
        action = data.get('action', 'list') if data else 'list'
        
        if action == 'list':
            # 返回商店物品列表，包含完整物品信息
            shop_items = npc_config.get('shop_items', [])

            # 获取物品系统实例
            from item_system import ItemSystem
            item_system = ItemSystem()

            # 构建包含完整信息的物品列表
            enriched_items = []
            for shop_item in shop_items:
                item_id = shop_item['item_id']
                stock = shop_item.get('stock', 999)

                # 从物品系统获取物品信息
                if item_id in item_system.items:
                    item_obj = item_system.items[item_id]
                    enriched_items.append({
                        'item_id': item_id,
                        'name': item_obj.name,
                        'price': item_obj.price,
                        'stock': stock,
                        'description': item_obj.description
                    })
                else:
                    # 如果物品不存在，使用默认信息
                    enriched_items.append({
                        'item_id': item_id,
                        'name': item_id,
                        'price': 0,
                        'stock': stock,
                        'description': '未知物品'
                    })

            return {
                'type': 'shop_items',
                'data': {
                    'npc_name': npc_config['name'],
                    'items': enriched_items
                }
            }
        elif action == 'buy':
            # 购买物品
            item_id = data.get('item_id')
            quantity = data.get('quantity', 1)
            return await self._buy_item(player, user_id, npc_config, item_id, quantity)
        else:
            return {'type': 'error', 'data': {'message': '未知商店操作'}}
    
    async def _buy_item(self, player: Dict, user_id: str, npc_config: Dict, item_id: str, quantity: int) -> Dict:
        """购买物品"""
        shop_items = npc_config.get('shop_items', [])
        item_config = next((item for item in shop_items if item['item_id'] == item_id), None)

        if not item_config:
            return {'type': 'error', 'data': {'message': '该NPC不出售此物品'}}

        # 获取物品系统实例，使用与显示一致的价格
        from item_system import ItemSystem
        item_system = ItemSystem()

        if item_id in item_system.items:
            item_obj = item_system.items[item_id]
            item_price = item_obj.price
        else:
            # 如果物品不存在，使用配置中的价格作为后备
            item_price = item_config.get('price', 0)

        total_price = item_price * quantity
        player_money = player.get('money', 0)
        
        if player_money < total_price:
            return {'type': 'error', 'data': {'message': f'银两不足，需要{total_price}银两'}}
        
        # 检查库存
        if item_config.get('stock', 0) < quantity:
            return {'type': 'error', 'data': {'message': '库存不足'}}
        
        # 扣除银两
        player['money'] = player_money - total_price
        
        # 添加物品到背包
        if self.server:
            success = await self.server.add_item_to_inventory(user_id, player, {
                'id': item_id,
                'quantity': quantity
            })
            
            if success:
                # 减少库存（如果不是无限库存）
                if item_config.get('stock', 999) < 999:
                    item_config['stock'] -= quantity
                
                # 保存玩家数据
                await self.server.save_player_data(user_id, player)
                
                return {
                    'type': 'buy_success',
                    'data': {
                        'message': f'成功购买{item_id} x{quantity}，花费{total_price}银两',
                        'item_id': item_id,
                        'quantity': quantity,
                        'cost': total_price
                    }
                }
            else:
                # 购买失败，退还银两
                player['money'] = player_money
                return {'type': 'error', 'data': {'message': '背包已满，购买失败'}}
        
        return {'type': 'error', 'data': {'message': '服务器错误'}}
    
    async def _handle_sell(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理出售物品"""
        item_id = data.get('item_id') if data else None
        quantity = data.get('quantity', 1) if data else 1
        
        if not item_id:
            return {'type': 'error', 'data': {'message': '请指定要出售的物品'}}
        
        # 检查背包中是否有该物品
        inventory = player.get('inventory', [])
        item_in_inventory = next((item for item in inventory if item['id'] == item_id), None)
        
        if not item_in_inventory or item_in_inventory.get('quantity', 0) < quantity:
            return {'type': 'error', 'data': {'message': '背包中该物品数量不足'}}
        
        # 计算出售价格（通常是购买价格的一半）
        from item_system import item_system
        item_obj = item_system.items.get(item_id)
        if not item_obj:
            return {'type': 'error', 'data': {'message': '物品不存在'}}
        
        base_price = getattr(item_obj, 'price', 0)
        sell_price = int(base_price * 0.5) * quantity
        
        if sell_price <= 0:
            return {'type': 'error', 'data': {'message': '该物品不可出售'}}
        
        # 移除物品
        item_in_inventory['quantity'] -= quantity
        if item_in_inventory['quantity'] <= 0:
            inventory.remove(item_in_inventory)
        
        # 增加银两
        player['money'] = player.get('money', 0) + sell_price
        
        # 保存玩家数据
        if self.server:
            await self.server.save_player_data(user_id, player)
        
        return {
            'type': 'sell_success',
            'data': {
                'message': f'成功出售{item_obj.name} x{quantity}，获得{sell_price}银两',
                'item_id': item_id,
                'quantity': quantity,
                'earned': sell_price
            }
        }
    
    async def _handle_repair(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理装备修理"""
        return {'type': 'info', 'data': {'message': '修理功能暂未开放'}}
    
    async def _handle_upgrade(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理装备强化"""
        return {'type': 'info', 'data': {'message': '强化功能暂未开放'}}
    
    async def _handle_escort(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理护镖任务"""
        return {'type': 'info', 'data': {'message': '护镖功能暂未开放'}}
    
    async def _handle_transport(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理快速传送"""
        destination = data.get('destination') if data else None
        if not destination:
            # 返回可传送的目的地列表
            services = npc_config.get('services', {})
            transport_service = services.get('transport', {})
            destinations = transport_service.get('destinations', [])
            
            return {
                'type': 'transport_destinations',
                'data': {
                    'destinations': destinations,
                    'description': transport_service.get('description', '快速传送服务')
                }
            }
        
        # 执行传送
        services = npc_config.get('services', {})
        transport_service = services.get('transport', {})
        destinations = transport_service.get('destinations', [])
        
        dest_config = next((dest for dest in destinations if dest['map'] == destination), None)
        if not dest_config:
            return {'type': 'error', 'data': {'message': '无法传送到该地点'}}
        
        price = dest_config['price']
        if player.get('money', 0) < price:
            return {'type': 'error', 'data': {'message': f'银两不足，需要{price}银两'}}
        
        # 扣除费用并传送
        player['money'] -= price
        player['current_map'] = destination
        
        if self.server:
            await self.server.save_player_data(user_id, player)
        
        return {
            'type': 'transport_success',
            'data': {
                'message': f'成功传送到{destination}，花费{price}银两',
                'destination': destination,
                'cost': price
            }
        }
    
    async def _handle_storage(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理物品寄存"""
        return {'type': 'info', 'data': {'message': '寄存功能暂未开放'}}
    
    async def _handle_alchemy(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理炼药服务"""
        return {'type': 'info', 'data': {'message': '炼药功能暂未开放'}}
    
    async def _handle_heal(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理治疗服务"""
        services = npc_config.get('services', {})
        heal_service = services.get('heal', {})
        price = heal_service.get('full_heal_price', 100)
        
        if player.get('money', 0) < price:
            return {'type': 'error', 'data': {'message': f'银两不足，需要{price}银两'}}
        
        # 扣除费用并恢复生命值
        player['money'] -= price
        max_hp = player.get('maxHp', 100)
        player['hp'] = max_hp
        
        if self.server:
            await self.server.save_player_data(user_id, player)
        
        return {
            'type': 'heal_success',
            'data': {
                'message': f'治疗完成，生命值已恢复至{max_hp}，花费{price}银两',
                'cost': price,
                'hp': max_hp
            }
        }
    
    async def _handle_identify(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理物品鉴定"""
        return {'type': 'info', 'data': {'message': '鉴定功能暂未开放'}}
    
    async def _handle_info(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理信息服务"""
        info_type = data.get('info_type') if data else None
        if not info_type:
            # 返回可用的信息服务列表
            info_services = npc_config.get('info_services', [])
            return {
                'type': 'info_services',
                'data': {
                    'services': info_services
                }
            }
        
        # 处理具体的信息请求
        info_services = npc_config.get('info_services', [])
        service = next((s for s in info_services if s['type'] == info_type), None)
        
        if not service:
            return {'type': 'error', 'data': {'message': '不提供该类型的信息'}}
        
        price = service['price']
        if player.get('money', 0) < price:
            return {'type': 'error', 'data': {'message': f'银两不足，需要{price}银两'}}
        
        # 扣除费用并提供信息
        player['money'] -= price
        
        if self.server:
            await self.server.save_player_data(user_id, player)
        
        # 根据信息类型返回不同的信息
        info_content = self._generate_info_content(info_type, player)
        
        return {
            'type': 'info_success',
            'data': {
                'message': info_content,
                'info_type': info_type,
                'cost': price
            }
        }
    
    def _generate_info_content(self, info_type: str, player: Dict) -> str:
        """生成信息内容"""
        if info_type == 'map_info':
            current_map = player.get('current_map', 'changan')
            return f"当前地图 {current_map} 的详细信息：这里有丰富的资源和强大的怪物。"
        elif info_type == 'monster_info':
            return "附近的怪物信息：小心那些实力强大的BOSS，它们掉落珍贵的装备。"
        elif info_type == 'treasure_info':
            return "宝藏线索：据说在某个隐秘的地方，埋藏着古代武林高手的秘宝。"
        else:
            return "暂无相关信息。"

    async def _handle_learn(self, player: Dict, user_id: str, npc_config: Dict, data: Dict) -> Dict:
        """处理学习功能 - 读书写字技能"""
        try:
            # 检查玩家银两
            player_money = player.get('money', 0)
            learning_cost = 10  # 学费10银两

            if player_money < learning_cost:
                return {'type': 'error', 'data': {'message': '银两不足，学费需要10银两'}}

            # 扣除学费
            player['money'] = player_money - learning_cost

            # 获取武功技能数据
            martial_skills = player.get('martial_skills', {})
            if isinstance(martial_skills, list):
                try:
                    martial_skills = {entry['name']: entry for entry in martial_skills if isinstance(entry, dict) and 'name' in entry}
                except Exception as e:
                    print(f"转换martial_skills时出错: {e}")
                    martial_skills = {}

            skill_name = '读书写字'

            # 如果没有读书写字技能，则自动掌握
            if skill_name not in martial_skills:
                martial_skills[skill_name] = {
                    'name': skill_name,
                    '名称': skill_name,
                    'level': 0,
                    '等级': 0,
                    'exp': 0,
                    '经验': 0,
                    'type': '生活技能',
                    '类型': '生活技能',
                    'quality': '普通',
                    '品质': '普通',
                    'unlocked': True,
                    '解锁': True,
                    'equipped': False,
                    '装备': False
                }
                player['martial_skills'] = martial_skills

            skill_data = martial_skills[skill_name]
            current_level = skill_data.get('level', 0)
            current_exp = skill_data.get('exp', 0)

            # 使用系统现有的经验计算机制
            base_exp_gain = 10  # 基础经验获得

            # 应用悟性增益到经验值获取（使用系统现有机制）
            intelligence = player.get('talent', {}).get('悟性', 15)
            intelligence_bonus = max(0, intelligence - 15) * 0.01  # 每点悟性+1%经验获取
            intelligence_multiplier = 1.0 + intelligence_bonus

            # 计算最终经验值获取
            exp_gain = int(base_exp_gain * intelligence_multiplier)

            # 更新经验值
            new_exp = current_exp + exp_gain
            skill_data['exp'] = new_exp

            # 使用生活技能的升级公式：经验需求 = 系数 × (等级+1)²
            coefficient = 60  # 生活技能使用60的系数，比基础武功稍难
            level_up = False
            new_level = current_level

            if new_exp >= coefficient * (current_level + 1) ** 2:
                new_level = current_level + 1
                skill_data['level'] = new_level
                skill_data['exp'] = new_exp - coefficient * (current_level + 1) ** 2
                level_up = True

            # 构建返回消息
            base_exp = base_exp_gain
            intelligence_bonus_exp = exp_gain - base_exp

            if current_level == 0:
                message = f'恭喜你掌握了{skill_name}技能！获得{exp_gain}经验值'
            else:
                message = f'学习了{skill_name}，获得{exp_gain}经验值'

            if intelligence_bonus_exp > 0:
                message += f'(悟性加成+{intelligence_bonus_exp})'
            if level_up:
                message += f'，{skill_name}提升到{new_level}级！'

            # 注意：玩家数据会在server.py的handle_npc_function中统一保存

            return {
                'type': 'learn_success',
                'data': {
                    'message': message,
                    'cost': learning_cost,
                    'skill': skill_name,
                    'exp_gained': exp_gain,
                    'base_exp': base_exp,
                    'intelligence_bonus_exp': intelligence_bonus_exp,
                    'intelligence_bonus_percentage': round(intelligence_bonus * 100, 1),
                    'level_up': level_up,
                    'new_level': new_level,
                    'current_money': player['money']
                }
            }

        except Exception as e:
            print(f"学习功能处理时出错: {e}")
            import traceback
            traceback.print_exc()
            return {'type': 'error', 'data': {'message': f'学习时发生错误: {str(e)}'}}

# 全局NPC系统实例
npc_system = NPCSystem()
