{"version": 3, "file": "GatheringPopup.js", "sources": ["components/GatheringPopup.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovempqaHgv5LuX5YmR5rGf5rmW6KGML2NvbXBvbmVudHMvR2F0aGVyaW5nUG9wdXAudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"gathering-popup-mask\" @click.self=\"onClose\">\r\n    <view class=\"gathering-popup\">\r\n      <view class=\"gather-title\">采集事件</view>\r\n      <view class=\"gather-desc\">{{ event.content }}</view>\r\n      <view class=\"gather-info\">\r\n        <text>需要工具：</text>\r\n        <text class=\"tool-type\">{{ event.toolName || toolNameMap[event.requiredTool] || event.requiredTool }}</text>\r\n      </view>\r\n      <view class=\"gather-info\">\r\n        <text>可采集次数：</text>\r\n        <text class=\"gather-times\">{{ times }}</text>\r\n      </view>\r\n      <view class=\"gather-result\" v-if=\"result\">{{ result }}</view>\r\n      <view class=\"gather-btns\">\r\n        <button class=\"gather-btn\" type=\"primary\" @click=\"onGather\" :disabled=\"!hasRequiredTool || times<=0\">采集</button>\r\n        <button class=\"close-btn\" @click=\"onClose\">关闭</button>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'GatheringPopup',\r\n  props: {\r\n    visible: Boolean,\r\n    event: Object,\r\n    times: Number,\r\n    result: String,\r\n    inventory: Array\r\n  },\r\n  data() {\r\n    return {\r\n      toolNameMap: {\r\n        hoe: '锄头',\r\n        axe: '斧头',\r\n        pickaxe: '鹤嘴锄',\r\n        fishing_rod: '鱼竿',\r\n        net: '捕网'\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    hasRequiredTool() {\r\n      if (!this.event || !this.event.requiredTool || !this.inventory) return false;\r\n      return this.inventory.some(item => {\r\n        return item.name === this.event.requiredTool || item.id === this.event.requiredTool;\r\n      });\r\n    }\r\n  },\r\n  methods: {\r\n    onClose() {\r\n      this.$emit('close')\r\n    },\r\n    onGather() {\r\n      this.$emit('do-gather')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.gathering-popup-mask {\r\n  position: fixed;\r\n  left: 0; top: 0; right: 0; bottom: 0;\r\n  background: rgba(0,0,0,0.35);\r\n  z-index: 9999;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n.gathering-popup {\r\n  background: #fff;\r\n  border-radius: 18rpx;\r\n  padding: 40rpx 32rpx 32rpx 32rpx;\r\n  min-width: 480rpx;\r\n  max-width: 90vw;\r\n  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n.gather-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #4b3fa7;\r\n  margin-bottom: 18rpx;\r\n}\r\n.gather-desc {\r\n  font-size: 26rpx;\r\n  color: #333;\r\n  margin-bottom: 18rpx;\r\n  text-align: center;\r\n}\r\n.gather-info {\r\n  font-size: 24rpx;\r\n  color: #666;\r\n  margin-bottom: 10rpx;\r\n}\r\n.tool-type {\r\n  color: #27ae60;\r\n  font-weight: bold;\r\n  margin-left: 8rpx;\r\n}\r\n.gather-times {\r\n  color: #e67e22;\r\n  font-weight: bold;\r\n  margin-left: 8rpx;\r\n}\r\n.gather-result {\r\n  color: #e74c3c;\r\n  font-size: 24rpx;\r\n  margin: 12rpx 0 0 0;\r\n  text-align: center;\r\n}\r\n.gather-btns {\r\n  display: flex;\r\n  gap: 24rpx;\r\n  margin-top: 24rpx;\r\n}\r\n.gather-btn {\r\n  background: linear-gradient(90deg, #27ae60, #4bfa7b);\r\n  color: #fff;\r\n  font-size: 26rpx;\r\n  border-radius: 24rpx;\r\n  padding: 12rpx 36rpx;\r\n  border: none;\r\n}\r\n.close-btn {\r\n  background: #eee;\r\n  color: #888;\r\n  font-size: 24rpx;\r\n  border-radius: 24rpx;\r\n  padding: 12rpx 36rpx;\r\n  border: none;\r\n}\r\n.gather-btn[disabled] {\r\n  opacity: 0.5;\r\n}\r\n</style> ", "import Component from 'D:/zjjhx/仗剑江湖行/components/GatheringPopup.vue'\nwx.createComponent(Component)"], "names": [], "mappings": ";;AAuBA,MAAK,YAAU;AAAA,EACb,MAAM;AAAA,EACN,OAAO;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,IACP,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,WAAW;AAAA,EACZ;AAAA,EACD,OAAO;AACL,WAAO;AAAA,MACL,aAAa;AAAA,QACX,KAAK;AAAA,QACL,KAAK;AAAA,QACL,SAAS;AAAA,QACT,aAAa;AAAA,QACb,KAAK;AAAA,MACP;AAAA,IACF;AAAA,EACD;AAAA,EACD,UAAU;AAAA,IACR,kBAAkB;AAChB,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,MAAM,gBAAgB,CAAC,KAAK;AAAW,eAAO;AACvE,aAAO,KAAK,UAAU,KAAK,UAAQ;AACjC,eAAO,KAAK,SAAS,KAAK,MAAM,gBAAgB,KAAK,OAAO,KAAK,MAAM;AAAA,MACzE,CAAC;AAAA,IACH;AAAA,EACD;AAAA,EACD,SAAS;AAAA,IACP,UAAU;AACR,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,IACD,WAAW;AACT,WAAK,MAAM,WAAW;AAAA,IACxB;AAAA,EACF;AACF;;;;;;;;;;;;;;;;;AC1DA,GAAG,gBAAgB,SAAS;"}