{"version": 3, "file": "gameData.js", "sources": ["utils/gameData.js"], "sourcesContent": ["import wsManager from './websocket.js'\r\n// 游戏数据模型\r\nexport default {\r\n  // 玩家基础属性\r\n  playerStats: {\r\n    // 战斗属性\r\n    hp: 100,           // 气血\r\n    maxHp: 100,        // 最大气血\r\n    mp: 50,            // 内力\r\n    maxMp: 50,         // 最大内力\r\n    attack: 10,        // 攻击\r\n    defense: 5,        // 防御\r\n    hitRate: 0.8,      // 命中率\r\n    dodgeRate: 0.1,    // 闪避率\r\n    critRate: 0.05,    // 暴击率\r\n    critDamage: 1.5,   // 暴击伤害\r\n    blockRate: 0.05,   // 招架率\r\n    blockValue: 3,     // 格挡值\r\n    \r\n    // 武学属性\r\n    agility: 10,       // 身法\r\n    internalPower: 5,  // 内功修为\r\n    meridians: 1,      // 经脉通畅度\r\n    \r\n    // 成长属性\r\n    stamina: 100,      // 体力值\r\n    maxStamina: 100,   // 最大体力值\r\n    energy: 100,       // 精力值\r\n    maxEnergy: 100,    // 最大精力值\r\n    wisdom: 10,        // 悟性\r\n    \r\n    // 江湖属性\r\n    reputation: 0,     // 声望\r\n    karma: 0,          // 因果值\r\n    bloodlust: 0,      // 杀气\r\n    friendliness: 0,   // 友好度\r\n  },\r\n\r\n  // 装备类型\r\n  equipmentTypes: {\r\n    WEAPON: 'weapon',      // 武器\r\n    ARMOR: 'armor',        // 护甲\r\n    NECKLACE: 'necklace',  // 项链\r\n    BRACELET: 'bracelet',  // 手镯\r\n    MOUNT: 'mount',        // 坐骑\r\n    TOOL: 'tool'           // 生活工具\r\n  },\r\n\r\n  // 装备品质\r\n  equipmentQuality: {\r\n    COMMON: { name: '普通', color: '#9e9e9e', multiplier: 1.0 },\r\n    FINE: { name: '精良', color: '#4caf50', multiplier: 1.2 },\r\n    RARE: { name: '稀有', color: '#2196f3', multiplier: 1.5 },\r\n    EPIC: { name: '传说', color: '#9c27b0', multiplier: 2.0 },\r\n    LEGENDARY: { name: '神品', color: '#ff9800', multiplier: 3.0 }\r\n  },\r\n\r\n  // 武功类型\r\n  skillTypes: {\r\n    EXTERNAL: 'external',  // 外功\r\n    INTERNAL: 'internal',  // 内功\r\n    LIGHTNESS: 'lightness', // 轻功\r\n    MIND: 'mind',          // 心法\r\n    SPECIAL: 'special'     // 特技\r\n  },\r\n\r\n  // 武功品级\r\n  skillGrades: {\r\n    YELLOW: { name: '黄级', color: '#ffeb3b', multiplier: 1.0 },\r\n    MYSTIC: { name: '玄级', color: '#9c27b0', multiplier: 1.5 },\r\n    EARTH: { name: '地级', color: '#795548', multiplier: 2.0 },\r\n    HEAVEN: { name: '天级', color: '#2196f3', multiplier: 3.0 },\r\n    SUPREME: { name: '绝世', color: '#f44336', multiplier: 5.0 }\r\n  },\r\n\r\n  // 事件类型\r\n  eventTypes: {\r\n    GOOD_FORTUNE: 1,    // 好运事件\r\n    NPC_ENCOUNTER: 2,   // 遭遇NPC\r\n    GATHERING: 3,       // 采集事件\r\n    EMPTY: 4,           // 空事件\r\n    ADVENTURE: 5,       // 奇遇事件\r\n    ENMITY: 6,          // 恩怨事件\r\n    TEAM: 7,            // 组队事件\r\n    CARAVAN: 8,         // 商队事件\r\n    RUMOR: 9,           // 江湖传闻\r\n    WEATHER: 10,        // 天气事件\r\n    MYSTERY: 11,        // 神秘事件\r\n    FESTIVAL: 12        // 节日事件\r\n  },\r\n\r\n  // 资源类型\r\n  resourceTypes: {\r\n    WOOD: 'wood',       // 木材\r\n    ORE: 'ore',         // 矿石\r\n    LEATHER: 'leather', // 皮毛\r\n    HERB: 'herb'        // 草药\r\n  },\r\n\r\n  // 状态类型\r\n  statusTypes: {\r\n    NORMAL: 'normal',     // 正常\r\n    INJURED: 'injured',   // 重伤\r\n    INTERNAL_INJURY: 'internal_injury', // 内伤\r\n    POISONED: 'poisoned', // 中毒\r\n    TIRED: 'tired'        // 疲劳\r\n  }\r\n}\r\n\r\n// 游戏工具函数\r\nexport const gameUtils = {\r\n  // 计算伤害\r\n  calculateDamage(attacker, defender, skill = null) {\r\n    let baseDamage = attacker.attack;\r\n    \r\n    // 技能加成\r\n    if (skill) {\r\n      baseDamage *= skill.damageMultiplier || 1.0;\r\n    }\r\n    \r\n    // 暴击判定\r\n    const isCrit = Math.random() < attacker.critRate;\r\n    if (isCrit) {\r\n      baseDamage *= attacker.critDamage;\r\n    }\r\n    \r\n    // 防御减伤\r\n    const finalDamage = Math.max(1, baseDamage - defender.defense);\r\n    \r\n    return {\r\n      damage: Math.floor(finalDamage),\r\n      isCrit: isCrit\r\n    };\r\n  },\r\n\r\n  // 命中判定\r\n  isHit(attacker, defender) {\r\n    const hitChance = attacker.hitRate - defender.dodgeRate;\r\n    return Math.random() < hitChance;\r\n  },\r\n\r\n  // 招架判定\r\n  isBlocked(defender) {\r\n    return Math.random() < defender.blockRate;\r\n  },\r\n\r\n  // 随机事件生成（前端不参与计算，仅用于显示）\r\n  generateRandomEvent() {\r\n    // 前端不参与事件计算，所有逻辑在后端处理\r\n    // 这里仅保留基本的事件定义用于显示\r\n    const events = [\r\n      { type: 1, name: '好运事件', description: '你遇到了一个善良的商人，获得了一些奖励。' },\r\n      { type: 2, name: '遭遇NPC', description: '你遇到了一个神秘的江湖人士。' },\r\n      { type: 3, name: '采集事件', description: '你发现了一片资源丰富的区域。' },\r\n      { type: 4, name: '空事件', description: '你漫步在江湖中，感受着武侠世界的魅力。' },\r\n      { type: 5, name: '奇遇事件', description: '你遇到了一个千载难逢的奇遇！' },\r\n      { type: 6, name: '恩怨事件', description: '江湖恩怨，是非难辨。' },\r\n      { type: 7, name: '组队事件', description: '你遇到了其他江湖人士，可以组队冒险。' }\r\n    ];\r\n    \r\n    // 返回空事件作为默认值（实际事件由后端生成）\r\n    return events[3];\r\n  },\r\n\r\n  // 格式化数字\r\n  formatNumber(num) {\r\n    if (num >= 10000) {\r\n      return (num / 10000).toFixed(1) + '万';\r\n    }\r\n    return num.toString();\r\n  },\r\n\r\n  // 获取品质颜色\r\n  getQualityColor(quality) {\r\n    const qualities = {\r\n      'common': '#9e9e9e',\r\n      'fine': '#4caf50',\r\n      'rare': '#2196f3',\r\n      'epic': '#9c27b0',\r\n      'legendary': '#ff9800'\r\n    };\r\n    return qualities[quality] || '#9e9e9e';\r\n  },\r\n\r\n  sendMessage(msg) {\r\n    return new Promise((resolve, reject) => {\r\n      // 兼容 type/data 结构和完整对象\r\n      let type = msg.type;\r\n      let data = msg.data || {};\r\n      \r\n      console.log(`【发送消息】类型: ${type}, 数据:`, data);\r\n      \r\n      // 特殊处理装备数据请求\r\n      if (type === 'get_equipment_data') {\r\n        console.log('特殊处理装备数据请求，使用get_player_data代替');\r\n        // 使用get_player_data代替，因为后端可能不支持get_equipment_data\r\n        type = 'get_player_data';\r\n      }\r\n      \r\n      // 添加超时处理\r\n      const timeoutDuration = type === 'escape_battle' ? 15000 : 10000; // 逃跑请求15秒超时，其他10秒\r\n      const timeout = setTimeout(() => {\r\n        console.error(`【消息超时】类型: ${type}, 数据:`, data);\r\n        cleanup();\r\n        resolve({\r\n          type: type + '_timeout',\r\n          data: { message: '请求超时，请检查网络连接' }\r\n        });\r\n      }, timeoutDuration);\r\n      \r\n      // 清理函数\r\n      const cleanup = () => {\r\n        clearTimeout(timeout);\r\n        wsManager.off(type + '_success', handler);\r\n        wsManager.off(type + '_failed', handler);\r\n        wsManager.off(type + '_error', handler);\r\n        wsManager.off('error', errorHandler);\r\n        // 兼容后端直接返回type为market_list等的事件\r\n        if (type === 'market_action') {\r\n          wsManager.off('market_list', handler);\r\n        }\r\n        // 兼容地图NPC请求\r\n        if (type === 'get_map_npcs') {\r\n          wsManager.off('map_npcs', handler);\r\n        }\r\n        // 兼容天赋加成请求\r\n        if (type === 'get_bonus_summary') {\r\n          wsManager.off('bonus_summary', handler);\r\n        }\r\n        \r\n        // 兼容背包数据请求\r\n        if (type === 'get_inventory_data') {\r\n          wsManager.off('inventory_data', handler);\r\n        }\r\n        \r\n        // 兼容武功数据请求\r\n        if (type === 'get_skills_data') {\r\n          wsManager.off('skills_data', handler);\r\n        }\r\n        \r\n        // 兼容装备请求\r\n        if (type === 'equip_item') {\r\n          wsManager.off('equip_success', handler);\r\n          wsManager.off('equip_failed', handler);\r\n        }\r\n        \r\n        // 兼容player_data事件\r\n        if (type === 'get_player_data') {\r\n          wsManager.off('player_data', handler);\r\n        }\r\n        \r\n        // 兼容get_player_data_success等事件\r\n        if (type === 'get_player_data') {\r\n          wsManager.off('get_player_data_success', handler);\r\n          wsManager.off('get_player_data_failed', handler);\r\n          wsManager.off('get_player_data_error', handler);\r\n        }\r\n        // 兼容打造请求\r\n        if (type === 'crafting_action') {\r\n          wsManager.off('get_craftable_success', handler);\r\n          wsManager.off('get_craftable_failed', handler);\r\n          wsManager.off('craft_success', handler);\r\n          wsManager.off('craft_failed', handler);\r\n        }\r\n        \r\n        // 兼容战斗开始请求\r\n        if (type === 'start_battle_from_encounter') {\r\n          wsManager.off('start_battle_from_encounter_success', handler);\r\n          wsManager.off('start_battle_from_encounter_failed', handler);\r\n        }\r\n        \r\n        // 兼容逃跑请求\r\n        if (type === 'escape_battle') {\r\n          wsManager.off('escape_battle_result', handler);\r\n        }\r\n      };\r\n      \r\n      // 监听一次性响应\r\n      const handler = (resp) => {\r\n        console.log(`【收到响应】类型: ${type}, 响应:`, resp);\r\n        cleanup();\r\n        \r\n        // 如果是get_player_data但原请求是get_equipment_data，提取装备数据\r\n        if (type === 'get_player_data' && msg.type === 'get_equipment_data') {\r\n          // 从玩家数据中提取装备数据\r\n          if (resp && resp.equipment) {\r\n            resolve({\r\n              type: 'equipment_data',\r\n              data: resp.equipment\r\n            });\r\n            return;\r\n          }\r\n        }\r\n        \r\n        // 特殊处理某些响应类型\r\n        let responseType = type + (resp.success !== false ? '_success' : '_failed');\r\n        \r\n        // 对于背包数据，直接返回inventory_data类型\r\n        if (type === 'get_inventory_data' && resp.inventory !== undefined) {\r\n          responseType = 'inventory_data';\r\n        }\r\n        \r\n        // 对于武功数据，直接返回skills_data类型\r\n        if (type === 'get_skills_data' && resp.skills !== undefined) {\r\n          responseType = 'skills_data';\r\n        }\r\n        \r\n        // 对于装备请求，直接返回equip_success或equip_failed类型\r\n        if (type === 'equip_item') {\r\n          if (resp.success !== false) {\r\n            responseType = 'equip_success';\r\n          } else {\r\n            responseType = 'equip_failed';\r\n          }\r\n        }\r\n        \r\n        // 对于玩家数据请求，直接返回player_data类型\r\n        if (type === 'get_player_data' && resp.type === 'player_data') {\r\n          responseType = 'player_data';\r\n        }\r\n\r\n        // 特殊处理打造请求\r\n        if (type === 'crafting_action' && resp.type === 'get_craftable_success') {\r\n          responseType = 'get_craftable_success';\r\n        }\r\n        if (type === 'crafting_action' && resp.type === 'craft_success') {\r\n          responseType = 'craft_success';\r\n        }\r\n        if (type === 'crafting_action' && resp.type === 'craft_failed') {\r\n          responseType = 'craft_failed';\r\n        }\r\n        \r\n        resolve({\r\n          type: responseType,\r\n          data: resp\r\n        });\r\n      };\r\n      \r\n      // 错误处理\r\n      const errorHandler = (error) => {\r\n        console.error(`【请求错误】类型: ${type}, 错误:`, error);\r\n        cleanup();\r\n        \r\n        // 如果是装备数据请求，返回空对象\r\n        if (msg.type === 'get_equipment_data') {\r\n          resolve({\r\n            type: 'equipment_data',\r\n            data: {}\r\n          });\r\n          return;\r\n        }\r\n        \r\n        resolve({\r\n          type: type + '_failed',\r\n          data: { message: error.message || '请求失败' }\r\n        });\r\n      };\r\n      \r\n      // 注册事件监听\r\n      wsManager.on(type + '_success', handler);\r\n      wsManager.on(type + '_failed', handler);\r\n      wsManager.on(type + '_error', handler);\r\n      wsManager.on('error', errorHandler);\r\n      \r\n              // 兼容打造请求\r\n        if (type === 'crafting_action') {\r\n          wsManager.on('get_craftable_success', handler);\r\n          wsManager.on('get_craftable_failed', handler);\r\n          wsManager.on('craft_success', handler);\r\n          wsManager.on('craft_failed', handler);\r\n        }\r\n        \r\n        // 兼容战斗开始请求\r\n        if (type === 'start_battle_from_encounter') {\r\n          wsManager.on('start_battle_from_encounter_success', handler);\r\n          wsManager.on('start_battle_from_encounter_failed', handler);\r\n        }\r\n        \r\n        // 兼容逃跑请求\r\n        if (type === 'escape_battle') {\r\n          wsManager.on('escape_battle_result', handler);\r\n        }\r\n      \r\n      // 兼容player_data事件\r\n        if (type === 'get_player_data') {\r\n          wsManager.on('player_data', handler);\r\n        }\r\n        \r\n        // 兼容get_player_data_success等事件\r\n        if (type === 'get_player_data') {\r\n          wsManager.on('get_player_data_success', handler);\r\n          wsManager.on('get_player_data_failed', handler);\r\n          wsManager.on('get_player_data_error', handler);\r\n        }\r\n      \r\n      // 兼容后端直接返回type为market_list等的事件\r\n      if (type === 'market_action') {\r\n        wsManager.on('market_list', handler);\r\n      }\r\n      \r\n      // 兼容地图NPC请求\r\n      if (type === 'get_map_npcs') {\r\n        wsManager.on('map_npcs', handler);\r\n      }\r\n      \r\n      // 兼容天赋加成请求\r\n      if (type === 'get_bonus_summary') {\r\n        wsManager.on('bonus_summary', handler);\r\n      }\r\n      \r\n      // 兼容背包数据请求\r\n      if (type === 'get_inventory_data') {\r\n        wsManager.on('inventory_data', handler);\r\n      }\r\n      \r\n      // 兼容武功数据请求\r\n      if (type === 'get_skills_data') {\r\n        wsManager.on('skills_data', handler);\r\n      }\r\n      \r\n      // 兼容装备请求\r\n      if (type === 'equip_item') {\r\n        wsManager.on('equip_success', handler);\r\n        wsManager.on('equip_failed', handler);\r\n      }\r\n      \r\n      // 确保WebSocket已连接\r\n      if (!wsManager.isConnected) {\r\n        console.log('WebSocket未连接，尝试连接...');\r\n        wsManager.connect().then(() => {\r\n          console.log('WebSocket连接成功，发送消息');\r\n          wsManager.sendMessage(type, data);\r\n        }).catch(err => {\r\n          console.error('WebSocket连接失败:', err);\r\n          cleanup();\r\n          resolve({\r\n            type: type + '_failed',\r\n            data: { message: '连接服务器失败，请检查网络' }\r\n          });\r\n        });\r\n      } else {\r\n        wsManager.sendMessage(type, data);\r\n      }\r\n    });\r\n  }\r\n}"], "names": ["uni", "wsManager"], "mappings": ";;;AA8GY,MAAC,YAAY;AAAA;AAAA,EAEvB,gBAAgB,UAAU,UAAU,QAAQ,MAAM;AAChD,QAAI,aAAa,SAAS;AAG1B,QAAI,OAAO;AACT,oBAAc,MAAM,oBAAoB;AAAA,IACzC;AAGD,UAAM,SAAS,KAAK,OAAM,IAAK,SAAS;AACxC,QAAI,QAAQ;AACV,oBAAc,SAAS;AAAA,IACxB;AAGD,UAAM,cAAc,KAAK,IAAI,GAAG,aAAa,SAAS,OAAO;AAE7D,WAAO;AAAA,MACL,QAAQ,KAAK,MAAM,WAAW;AAAA,MAC9B;AAAA,IACN;AAAA,EACG;AAAA;AAAA,EAGD,MAAM,UAAU,UAAU;AACxB,UAAM,YAAY,SAAS,UAAU,SAAS;AAC9C,WAAO,KAAK,OAAQ,IAAG;AAAA,EACxB;AAAA;AAAA,EAGD,UAAU,UAAU;AAClB,WAAO,KAAK,WAAW,SAAS;AAAA,EACjC;AAAA;AAAA,EAGD,sBAAsB;AAGpB,UAAM,SAAS;AAAA,MACb,EAAE,MAAM,GAAG,MAAM,QAAQ,aAAa,uBAAwB;AAAA,MAC9D,EAAE,MAAM,GAAG,MAAM,SAAS,aAAa,iBAAkB;AAAA,MACzD,EAAE,MAAM,GAAG,MAAM,QAAQ,aAAa,iBAAkB;AAAA,MACxD,EAAE,MAAM,GAAG,MAAM,OAAO,aAAa,sBAAuB;AAAA,MAC5D,EAAE,MAAM,GAAG,MAAM,QAAQ,aAAa,iBAAkB;AAAA,MACxD,EAAE,MAAM,GAAG,MAAM,QAAQ,aAAa,aAAc;AAAA,MACpD,EAAE,MAAM,GAAG,MAAM,QAAQ,aAAa,qBAAsB;AAAA,IAClE;AAGI,WAAO,OAAO,CAAC;AAAA,EAChB;AAAA;AAAA,EAGD,aAAa,KAAK;AAChB,QAAI,OAAO,KAAO;AAChB,cAAQ,MAAM,KAAO,QAAQ,CAAC,IAAI;AAAA,IACnC;AACD,WAAO,IAAI;EACZ;AAAA;AAAA,EAGD,gBAAgB,SAAS;AACvB,UAAM,YAAY;AAAA,MAChB,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,aAAa;AAAA,IACnB;AACI,WAAO,UAAU,OAAO,KAAK;AAAA,EAC9B;AAAA,EAED,YAAY,KAAK;AACf,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEtC,UAAI,OAAO,IAAI;AACf,UAAI,OAAO,IAAI,QAAQ;AAEvBA,mEAAY,aAAa,IAAI,SAAS,IAAI;AAG1C,UAAI,SAAS,sBAAsB;AACjCA,sBAAAA,MAAY,MAAA,OAAA,4BAAA,gCAAgC;AAE5C,eAAO;AAAA,MACR;AAGD,YAAM,kBAAkB,SAAS,kBAAkB,OAAQ;AAC3D,YAAM,UAAU,WAAW,MAAM;AAC/BA,4BAAc,MAAA,SAAA,4BAAA,aAAa,IAAI,SAAS,IAAI;AAC5C;AACA,gBAAQ;AAAA,UACN,MAAM,OAAO;AAAA,UACb,MAAM,EAAE,SAAS,eAAgB;AAAA,QAC3C,CAAS;AAAA,MACF,GAAE,eAAe;AAGlB,YAAM,UAAU,MAAM;AACpB,qBAAa,OAAO;AACpBC,wBAAAA,UAAU,IAAI,OAAO,YAAY,OAAO;AACxCA,wBAAAA,UAAU,IAAI,OAAO,WAAW,OAAO;AACvCA,wBAAAA,UAAU,IAAI,OAAO,UAAU,OAAO;AACtCA,wBAAAA,UAAU,IAAI,SAAS,YAAY;AAEnC,YAAI,SAAS,iBAAiB;AAC5BA,0BAAAA,UAAU,IAAI,eAAe,OAAO;AAAA,QACrC;AAED,YAAI,SAAS,gBAAgB;AAC3BA,0BAAAA,UAAU,IAAI,YAAY,OAAO;AAAA,QAClC;AAED,YAAI,SAAS,qBAAqB;AAChCA,0BAAAA,UAAU,IAAI,iBAAiB,OAAO;AAAA,QACvC;AAGD,YAAI,SAAS,sBAAsB;AACjCA,0BAAAA,UAAU,IAAI,kBAAkB,OAAO;AAAA,QACxC;AAGD,YAAI,SAAS,mBAAmB;AAC9BA,0BAAAA,UAAU,IAAI,eAAe,OAAO;AAAA,QACrC;AAGD,YAAI,SAAS,cAAc;AACzBA,0BAAAA,UAAU,IAAI,iBAAiB,OAAO;AACtCA,0BAAAA,UAAU,IAAI,gBAAgB,OAAO;AAAA,QACtC;AAGD,YAAI,SAAS,mBAAmB;AAC9BA,0BAAAA,UAAU,IAAI,eAAe,OAAO;AAAA,QACrC;AAGD,YAAI,SAAS,mBAAmB;AAC9BA,0BAAAA,UAAU,IAAI,2BAA2B,OAAO;AAChDA,0BAAAA,UAAU,IAAI,0BAA0B,OAAO;AAC/CA,0BAAAA,UAAU,IAAI,yBAAyB,OAAO;AAAA,QAC/C;AAED,YAAI,SAAS,mBAAmB;AAC9BA,0BAAAA,UAAU,IAAI,yBAAyB,OAAO;AAC9CA,0BAAAA,UAAU,IAAI,wBAAwB,OAAO;AAC7CA,0BAAAA,UAAU,IAAI,iBAAiB,OAAO;AACtCA,0BAAAA,UAAU,IAAI,gBAAgB,OAAO;AAAA,QACtC;AAGD,YAAI,SAAS,+BAA+B;AAC1CA,0BAAAA,UAAU,IAAI,uCAAuC,OAAO;AAC5DA,0BAAAA,UAAU,IAAI,sCAAsC,OAAO;AAAA,QAC5D;AAGD,YAAI,SAAS,iBAAiB;AAC5BA,0BAAAA,UAAU,IAAI,wBAAwB,OAAO;AAAA,QAC9C;AAAA,MACT;AAGM,YAAM,UAAU,CAAC,SAAS;AACxBD,4BAAY,MAAA,OAAA,4BAAA,aAAa,IAAI,SAAS,IAAI;AAC1C;AAGA,YAAI,SAAS,qBAAqB,IAAI,SAAS,sBAAsB;AAEnE,cAAI,QAAQ,KAAK,WAAW;AAC1B,oBAAQ;AAAA,cACN,MAAM;AAAA,cACN,MAAM,KAAK;AAAA,YACzB,CAAa;AACD;AAAA,UACD;AAAA,QACF;AAGD,YAAI,eAAe,QAAQ,KAAK,YAAY,QAAQ,aAAa;AAGjE,YAAI,SAAS,wBAAwB,KAAK,cAAc,QAAW;AACjE,yBAAe;AAAA,QAChB;AAGD,YAAI,SAAS,qBAAqB,KAAK,WAAW,QAAW;AAC3D,yBAAe;AAAA,QAChB;AAGD,YAAI,SAAS,cAAc;AACzB,cAAI,KAAK,YAAY,OAAO;AAC1B,2BAAe;AAAA,UAC3B,OAAiB;AACL,2BAAe;AAAA,UAChB;AAAA,QACF;AAGD,YAAI,SAAS,qBAAqB,KAAK,SAAS,eAAe;AAC7D,yBAAe;AAAA,QAChB;AAGD,YAAI,SAAS,qBAAqB,KAAK,SAAS,yBAAyB;AACvE,yBAAe;AAAA,QAChB;AACD,YAAI,SAAS,qBAAqB,KAAK,SAAS,iBAAiB;AAC/D,yBAAe;AAAA,QAChB;AACD,YAAI,SAAS,qBAAqB,KAAK,SAAS,gBAAgB;AAC9D,yBAAe;AAAA,QAChB;AAED,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QAChB,CAAS;AAAA,MACT;AAGM,YAAM,eAAe,CAAC,UAAU;AAC9BA,4BAAc,MAAA,SAAA,4BAAA,aAAa,IAAI,SAAS,KAAK;AAC7C;AAGA,YAAI,IAAI,SAAS,sBAAsB;AACrC,kBAAQ;AAAA,YACN,MAAM;AAAA,YACN,MAAM,CAAE;AAAA,UACpB,CAAW;AACD;AAAA,QACD;AAED,gBAAQ;AAAA,UACN,MAAM,OAAO;AAAA,UACb,MAAM,EAAE,SAAS,MAAM,WAAW,OAAQ;AAAA,QACpD,CAAS;AAAA,MACT;AAGMC,sBAAAA,UAAU,GAAG,OAAO,YAAY,OAAO;AACvCA,sBAAAA,UAAU,GAAG,OAAO,WAAW,OAAO;AACtCA,sBAAAA,UAAU,GAAG,OAAO,UAAU,OAAO;AACrCA,sBAAAA,UAAU,GAAG,SAAS,YAAY;AAGhC,UAAI,SAAS,mBAAmB;AAC9BA,wBAAAA,UAAU,GAAG,yBAAyB,OAAO;AAC7CA,wBAAAA,UAAU,GAAG,wBAAwB,OAAO;AAC5CA,wBAAAA,UAAU,GAAG,iBAAiB,OAAO;AACrCA,wBAAAA,UAAU,GAAG,gBAAgB,OAAO;AAAA,MACrC;AAGD,UAAI,SAAS,+BAA+B;AAC1CA,wBAAAA,UAAU,GAAG,uCAAuC,OAAO;AAC3DA,wBAAAA,UAAU,GAAG,sCAAsC,OAAO;AAAA,MAC3D;AAGD,UAAI,SAAS,iBAAiB;AAC5BA,wBAAAA,UAAU,GAAG,wBAAwB,OAAO;AAAA,MAC7C;AAGD,UAAI,SAAS,mBAAmB;AAC9BA,wBAAAA,UAAU,GAAG,eAAe,OAAO;AAAA,MACpC;AAGD,UAAI,SAAS,mBAAmB;AAC9BA,wBAAAA,UAAU,GAAG,2BAA2B,OAAO;AAC/CA,wBAAAA,UAAU,GAAG,0BAA0B,OAAO;AAC9CA,wBAAAA,UAAU,GAAG,yBAAyB,OAAO;AAAA,MAC9C;AAGH,UAAI,SAAS,iBAAiB;AAC5BA,wBAAAA,UAAU,GAAG,eAAe,OAAO;AAAA,MACpC;AAGD,UAAI,SAAS,gBAAgB;AAC3BA,wBAAAA,UAAU,GAAG,YAAY,OAAO;AAAA,MACjC;AAGD,UAAI,SAAS,qBAAqB;AAChCA,wBAAAA,UAAU,GAAG,iBAAiB,OAAO;AAAA,MACtC;AAGD,UAAI,SAAS,sBAAsB;AACjCA,wBAAAA,UAAU,GAAG,kBAAkB,OAAO;AAAA,MACvC;AAGD,UAAI,SAAS,mBAAmB;AAC9BA,wBAAAA,UAAU,GAAG,eAAe,OAAO;AAAA,MACpC;AAGD,UAAI,SAAS,cAAc;AACzBA,wBAAAA,UAAU,GAAG,iBAAiB,OAAO;AACrCA,wBAAAA,UAAU,GAAG,gBAAgB,OAAO;AAAA,MACrC;AAGD,UAAI,CAACA,gBAAS,UAAC,aAAa;AAC1BD,sBAAAA,MAAY,MAAA,OAAA,4BAAA,sBAAsB;AAClCC,kCAAU,UAAU,KAAK,MAAM;AAC7BD,wBAAAA,MAAY,MAAA,OAAA,4BAAA,oBAAoB;AAChCC,0BAAAA,UAAU,YAAY,MAAM,IAAI;AAAA,QAC1C,CAAS,EAAE,MAAM,SAAO;AACdD,wBAAc,MAAA,MAAA,SAAA,4BAAA,kBAAkB,GAAG;AACnC;AACA,kBAAQ;AAAA,YACN,MAAM,OAAO;AAAA,YACb,MAAM,EAAE,SAAS,gBAAiB;AAAA,UAC9C,CAAW;AAAA,QACX,CAAS;AAAA,MACT,OAAa;AACLC,wBAAAA,UAAU,YAAY,MAAM,IAAI;AAAA,MACjC;AAAA,IACP,CAAK;AAAA,EACF;AACH;;"}