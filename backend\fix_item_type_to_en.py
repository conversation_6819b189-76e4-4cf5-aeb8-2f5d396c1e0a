import os
import sqlite3
import json

# 中英文 type 映射表
TYPE_MAP = {
    '防具': 'armor', '头盔': 'helmet', '项链': 'necklace', '衣服': 'armor', '披风': 'cloak', '裤子': 'pants', '鞋子': 'shoes',
    '手镯': 'bracelet', '戒指': 'ring', '武器': 'weapon', '盾牌': 'shield',
    '消耗品': 'consumable', '药品': 'medicine', '丹药': 'pill',
    '材料': 'material', '矿石': 'ore', '木材': 'wood', '草药': 'herb', '兽皮': 'fur',
    '工具': 'tool', '矿镐': 'pickaxe', '斧头': 'axe', '镰刀': 'sickle', '小刀': 'knife',
    '特殊': 'special', '任务': 'quest', '货币': 'currency',
}

def fix_type(item):
    t = item.get('type')
    if t in TYPE_MAP:
        item['type'] = TYPE_MAP[t]
    return item

def fix_inventory(inv):
    for item in inv:
        fix_type(item)
    return inv

def main():
    db_path = os.path.join(os.path.dirname(__file__), 'game.db')
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    cursor.execute('SELECT id, data FROM players')
    rows = cursor.fetchall()
    for row in rows:
        pid, data = row
        try:
            pdata = json.loads(data)
            if 'inventory' in pdata:
                pdata['inventory'] = fix_inventory(pdata['inventory'])
            if 'equipment' in pdata and isinstance(pdata['equipment'], dict):
                for k, v in pdata['equipment'].items():
                    if isinstance(v, dict):
                        fix_type(v)
            new_data = json.dumps(pdata, ensure_ascii=False)
            cursor.execute('UPDATE players SET data=? WHERE id=?', (new_data, pid))
        except Exception as e:
            print(f'修正玩家 {pid} 数据失败:', e)
    conn.commit()
    conn.close()
    print('所有物品 type 字段已批量修正为英文！')

if __name__ == '__main__':
    main() 