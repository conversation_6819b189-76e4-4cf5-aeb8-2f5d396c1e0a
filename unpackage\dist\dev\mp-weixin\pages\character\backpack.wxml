<view class="backpack-container data-v-5c7554f9"><view class="data-v-5c7554f9" style="background:red;color:white;padding:10rpx;margin:10rpx"><text class="data-v-5c7554f9">调试信息：背包页面已加载</text><text class="data-v-5c7554f9">背包数据长度：{{a}}</text><text class="data-v-5c7554f9">背包容量：{{b}}</text></view><view class="backpack-header data-v-5c7554f9"><text class="backpack-title data-v-5c7554f9">🎒 背包</text><text class="backpack-info data-v-5c7554f9">{{c}}/{{d}}</text></view><view class="backpack-grid data-v-5c7554f9"><view wx:for="{{e}}" wx:for-item="item" wx:key="e" class="{{['item-slot', 'data-v-5c7554f9', item.f]}}" bindtap="{{item.g}}"><text class="item-icon data-v-5c7554f9">{{item.a}}</text><text class="item-name data-v-5c7554f9">{{item.b}}</text><text wx:if="{{item.c}}" class="item-quantity data-v-5c7554f9">{{item.d}}</text></view><view wx:for="{{f}}" wx:for-item="i" wx:key="a" class="item-slot empty data-v-5c7554f9"><text class="empty-text data-v-5c7554f9">空</text></view></view><view class="backpack-actions data-v-5c7554f9"><button class="action-btn expand-btn data-v-5c7554f9" bindtap="{{g}}"><text class="data-v-5c7554f9">📦 扩充背包</text></button><button class="action-btn sort-btn data-v-5c7554f9" bindtap="{{h}}"><text class="data-v-5c7554f9">🔄 整理背包</text></button><button class="action-btn clear-btn data-v-5c7554f9" bindtap="{{j}}" disabled="{{k}}"><text class="data-v-5c7554f9">{{i}}</text></button></view><view class="backpack-actions back-actions data-v-5c7554f9"><button class="action-btn back-btn data-v-5c7554f9" bindtap="{{l}}"><text class="data-v-5c7554f9">⬅️ 返回角色</text></button></view><view wx:if="{{m}}" class="item-detail-modal data-v-5c7554f9" bindtap="{{F}}"><view class="item-detail-content data-v-5c7554f9" catchtap="{{E}}"><view class="item-detail-header data-v-5c7554f9"><text class="item-detail-title data-v-5c7554f9">{{n}}</text><text class="item-detail-quality data-v-5c7554f9">{{o}}</text></view><view class="item-detail-info data-v-5c7554f9"><text class="item-detail-type data-v-5c7554f9">类型：{{p}}</text><text class="item-detail-quality-text data-v-5c7554f9">品质：{{q}}</text><text wx:if="{{r}}" class="item-detail-quantity data-v-5c7554f9">数量：{{s}}</text><view wx:if="{{t}}" class="item-effects data-v-5c7554f9"><text class="effects-title data-v-5c7554f9">属性加成：</text><view class="effects-list data-v-5c7554f9"><text wx:for="{{v}}" wx:for-item="effect" wx:key="c" class="effect-item data-v-5c7554f9">{{effect.a}}：+{{effect.b}}</text></view></view><text wx:if="{{w}}" class="item-detail-desc data-v-5c7554f9">{{x}}</text></view><view class="item-detail-actions data-v-5c7554f9"><button wx:if="{{y}}" class="detail-action-btn equip-btn data-v-5c7554f9" bindtap="{{z}}"><text class="data-v-5c7554f9">⚔️ 装备</text></button><button wx:if="{{A}}" class="detail-action-btn use-btn data-v-5c7554f9" bindtap="{{B}}"><text class="data-v-5c7554f9">💊 使用</text></button><button class="detail-action-btn destroy-btn data-v-5c7554f9" bindtap="{{C}}"><text class="data-v-5c7554f9">🗑️ 销毁</text></button><button class="detail-action-btn cancel-btn data-v-5c7554f9" bindtap="{{D}}"><text class="data-v-5c7554f9">❌ 取消</text></button></view></view></view></view>