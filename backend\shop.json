[{"general_id": "shop_general", "general_名称": "杂货铺", "general_物品": "basic_jianfa_book:99,basic_daofa_book:99,basic_quanfa_book:99,basic_zhaojia_book:99,basic_qinggong_book:99,basic_neigong_book:99,basic_anqi_book:99,basic_dushu_book:99,taijiquan_book:99,dugujiujian_book:99,xian<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_book:99,dagoubangfa_book:99,lingboweibu_book:99", "general_价格": "basic_jianfa_book:1,basic_daofa_book:1,basic_quanfa_book:1,basic_zhaojia_book:1,basic_qinggong_book:1,basic_neigong_book:1,basic_anqi_book:1,basic_dushu_book:1,taijiquan_book:1,dugu<PERSON><PERSON>jian_book:1,xian<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_book:1,dagoubangfa_book:1,lingboweibu_book:1", "general_描述": "日常用品一应俱全，兼售所有武学书籍。"}, {"medicine_id": "shop_medicine", "medicine_名称": "药铺", "medicine_物品": "heal_potion:20,herb_qinglingcao:5", "medicine_价格": "heal_potion:45,herb_qinglingcao:8", "medicine_描述": "专售各类药材和疗伤药。"}, {"yaodian_id": "shop_yaodian", "yaodian_名称": "药店", "yaodian_物品": "heal_potion:20,herb_qinglingcao:10", "yaodian_价格": "heal_potion:45,herb_qinglingcao:8", "yaodian_描述": "专售各类药材和疗伤药。"}, {"shenmi_id": "shop_shenmi", "shenmi_名称": "神秘商店", "shenmi_物品": "rare_sword:1,elixir_life:2", "shenmi_价格": "rare_sword:888,elixir_life:500", "shenmi_描述": "江湖罕见之物，机缘难得。"}]