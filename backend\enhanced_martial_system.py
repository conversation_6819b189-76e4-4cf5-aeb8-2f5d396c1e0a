# -*- coding: utf-8 -*-
"""
增强武功系统模块
基于武功文档的详细武功配置，包含招式描述、战斗效果等
"""

import random
from typing import Dict, List, Optional, Tuple
import os
import logging
import json
import sqlite3
import aiosqlite
import asyncio
import unicodedata

logger = logging.getLogger(__name__)

def load_wugong_config():
    """
    从backend/wugong_enhanced.json加载所有武功配置，返回字典结构
    """
    wugong_path = os.path.join(os.path.dirname(__file__), 'wugong_enhanced.json')
    martials = {}
    if not os.path.exists(wugong_path):
        return martials
    with open(wugong_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    for item in data:
        name = item.get('武功名') or item.get('name')
        if name:
            martials[name] = item
    logger.debug(f"最终martials: {martials}")
    return martials

# ========== 自动补全API ==========

class WugongHelper:
    def __init__(self):
        self._data = load_wugong_config()
        # 事件类型描述模板
        self.event_templates = {
            'hit': [
                "{attacker}使出{move}，{desc}，{defender}难以招架，受到{damage}{damage_word}！",
                "{attacker}一招{move}，{desc}，{defender}被击中，损失{damage}{damage_word}！",
                "{attacker}身形一展，施展{move}，{desc}，{defender}来不及防御，吃了{damage}{damage_word}！",
                "{attacker}突然攻出{move}，{desc}，{defender}猝不及防，受创{damage}{damage_word}！"
            ],
            'miss': [
                "{attacker}使出{move}，{desc}，但{defender}身形一闪，巧妙躲开了攻击！",
                "{attacker}一招{move}，{desc}，却被{defender}机敏避开！",
                "{attacker}的{move}未能命中，{defender}轻松闪避。"
            ],
            'crit': [
                "{attacker}怒喝一声，爆发出惊天一击——{move}！{desc}，{defender}惨叫一声，受到{damage}{damage_word}！",
                "{attacker}全力施展{move}，{desc}，{defender}遭受重创，损失{damage}{damage_word}！"
            ],
            'block': [
                "{defender}及时招架，化解了{attacker}的{move}！",
                "{defender}以巧妙身法挡下了{attacker}的{move}。"
            ],
            'poison': [
                "{defender}只觉一阵眩晕，竟已中毒！",
                "一缕青烟缭绕，{defender}脸色瞬间变得铁青。"
            ],
            'dodge': [
                "{defender}身形一转，巧妙地避开了{attacker}的攻势！",
                "{defender}脚下生风，轻松闪过{attacker}的{move}。"
            ],
            'escape_success': [
                "{attacker}身形一闪，成功脱离了战斗！",
                "{attacker}趁乱逃遁，消失在战场之外。"
            ],
            'escape_fail': [
                "{attacker}试图逃脱，但被{defender}拦住了！",
                "{attacker}刚要脱身，却被{defender}死死缠住，未能逃脱。"
            ],
            'win': [
                "{attacker}击败了{defender}，获得胜利！",
                "{defender}倒下了，{attacker}成为胜者！"
            ],
            'lose': [
                "{attacker}力竭倒地，败于{defender}之手……",
                "{defender}一招制胜，{attacker}败下阵来。"
            ]
        }
        # 伤害修饰词
        self.damage_words = [
            (80, "点致命伤害"),
            (50, "点重伤"),
            (30, "点伤害"),
            (10, "点轻伤"),
            (0, "点擦伤")
        ]
        self.default_attack_desc = [
            "你挥出一击，试图击中对手！",
            "你奋力攻击，招式虽简单但气势十足！",
            "你使出全身力气，向对手发起攻击！"
        ]

    def get_martial_by_quality(self, quality):
        """返回指定品质所有武学名称列表"""
        return [name for name, info in self._data.items() if info.get('品质') == quality or info.get('quality') == quality]

    def get_martial_info(self, name):
        """返回指定武学详细信息dict"""
        return self._data.get(name)

    def get_martial_move(self, name, player_martial_skills=None):
        import unicodedata
        def normalize_name(n):
            return unicodedata.normalize('NFKC', n.strip()) if isinstance(n, str) else n
        norm_name = normalize_name(name)
        print(f'[DEBUG] get_martial_move: name={name}, norm_name={norm_name}, player_martial_skills={player_martial_skills}')
        info = wugong_helper._data.get(norm_name)
        if not info:
            print(f'[DEBUG] get_martial_move: 未找到武功 {norm_name}，返回普通攻击')
            return ('普通攻击', '挥出一击')
        moves = info.get('招式列表')
        unlocked_moves = []
        if player_martial_skills:
            if isinstance(player_martial_skills, list):
                for skill in player_martial_skills:
                    if normalize_name(skill.get('name')) == norm_name and skill.get('unlocked'):
                        level = skill.get('level', 0)
                        unlocked_moves = [m for m in moves if level >= m.get('解锁等级', 0)]
                        break
            elif isinstance(player_martial_skills, dict):
                for k, v in player_martial_skills.items():
                    if normalize_name(k) == norm_name and v.get('unlocked'):
                        level = v.get('level', 0)
                        unlocked_moves = [m for m in moves if level >= m.get('解锁等级', 0)]
                        break
        if unlocked_moves:
            import random
            move = random.choice(unlocked_moves)
            move_name = move.get('名称')
            # 随机取攻击描述
            desc_tpls = info.get('攻击描述')
            if desc_tpls:
                desc_tpl = random.choice(desc_tpls)
                desc = desc_tpl.format(attacker='{attacker}', defender='{defender}', move=move_name, damage='{damage}')
            else:
                desc = f"{{attacker}}使出{norm_name}中的【{move_name}】，造成{{damage}}点伤害！"
            return (move_name, desc)
        # 没有解锁招式
        print(f'[DEBUG] get_martial_move: {norm_name} 没有解锁招式，返回普通攻击')
        return ('普通攻击', '挥出一击')

    def format_battle_description(self, event_type, attacker, defender, move, desc, damage=0, state=None):
        desc = str(desc)
        desc = desc.format(attacker=attacker, defender=defender, damage=damage)
        # 选择事件模板
        templates = self.event_templates.get(event_type, ["{attacker}对{defender}发动攻击！"])
        template = random.choice(templates)
        # 伤害修饰词
        damage_word = ""
        if damage > 0:
            for threshold, word in self.damage_words:
                if damage >= threshold:
                    damage_word = word
                    break
        # 状态修饰
        state_word = ""
        if state == 'poison':
            state_word = "（中毒）"
        elif state == 'dizzy':
            state_word = "（眩晕）"
        # 插值
        return template.format(attacker=attacker, defender=defender, move=move, desc=desc, damage=damage, damage_word=damage_word) + state_word

    def get_martial_coefficient(self, name):
        """返回武学升级经验系数，根据品质确定"""
        info = self._data.get(name)
        if info:
            quality = info.get('quality', '普通')
            # 根据品质设置不同的经验系数
            quality_coefficients = {
                '普通': 50,    # 基础武学
                '稀有': 80,    # 初级武学  
                '绝世': 120,   # 高级武学
                '传说': 200    # 绝学
            }
            return quality_coefficients.get(quality, 50)
        return 50

    def get_attack_description(self, player: dict) -> str:
        """
        根据玩家装备的武功和武器返回攻击描述，若都未装备则返回通用描述。
        """
        martial = player.get('equipped_martial')
        weapon = player.get('equipped_weapon')
        # 1. 优先用装备的武功
        if martial and martial in self._data:
            descs = self._data[martial].get('攻击描述', [])
            if descs:
                return random.choice(descs)
        # 2. 根据武器类型选默认武学
        if weapon:
            if '剑' in weapon and '基本剑法' in self._data:
                descs = self._data['基本剑法'].get('攻击描述', [])
            elif '刀' in weapon and '基本刀法' in self._data:
                descs = self._data['基本刀法'].get('攻击描述', [])
            elif '拳' in weapon and '基本拳法' in self._data:
                descs = self._data['基本拳法'].get('攻击描述', [])
            elif '暗器' in weapon and '基本暗器法' in self._data:
                descs = self._data['基本暗器法'].get('攻击描述', [])
            else:
                descs = self._data.get('基本拳法', {}).get('攻击描述', [])
            if descs:
                return random.choice(descs)
        # 3. 没有武器和武功，使用通用描述
        return random.choice(self.default_attack_desc)

# 单例
wugong_helper = WugongHelper()

# 兼容server.py的API
get_martial_by_level = wugong_helper.get_martial_by_quality  # 保持兼容性
get_martial_info = wugong_helper.get_martial_info
get_martial_move = wugong_helper.get_martial_move
format_battle_description = wugong_helper.format_battle_description
get_martial_coefficient = wugong_helper.get_martial_coefficient 
get_attack_description = wugong_helper.get_attack_description

# ========== 兼容server.py的武学判断API ==========
def is_life_skill(name):
    return name in ['采药', '伐木', '挖矿', '剥皮']

def get_martial_category(name):
    info = wugong_helper.get_martial_info(name)
    return info.get('类型') or info.get('type') or '未知' if info else '未知'

def can_use_martial(name, equipped_martials):
    """每种类型可装备一个武学"""
    category = get_martial_category(name)
    # 统计每种类型已装备数量
    equipped_by_category = {}
    for skill in equipped_martials:
        cat = get_martial_category(skill)
        equipped_by_category[cat] = equipped_by_category.get(cat, 0) + 1
    # 只要本类型未装备即可
    return equipped_by_category.get(category, 0) == 0

DB_PATH = os.path.join(os.path.dirname(__file__), 'backend', 'game.db')

async def fix_martial_skills():
    async with aiosqlite.connect(DB_PATH) as db:
        async with db.execute("SELECT id, data FROM players") as cursor:
            rows = await cursor.fetchall()
            for row in rows:
                player_id, data_json = row
                try:
                    data = json.loads(data_json)
                    ms = data.get('martial_skills')
                    if isinstance(ms, list):
                        # 转为 dict
                        new_ms = {entry['name']: entry for entry in ms if 'name' in entry}
                        data['martial_skills'] = new_ms
                        new_data_json = json.dumps(data, ensure_ascii=False)
                        await db.execute("UPDATE players SET data = ? WHERE id = ?", (new_data_json, player_id))
                        print(f"玩家ID {player_id} martial_skills 已修正为 dict")
                except Exception as e:
                    print(f"玩家ID {player_id} 修正失败: {e}")
        await db.commit()
    print('所有玩家 martial_skills 字段已批量修正为 dict')

if __name__ == "__main__":
    asyncio.run(fix_martial_skills()) 