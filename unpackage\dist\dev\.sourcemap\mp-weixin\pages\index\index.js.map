{"version": 3, "file": "index.js", "sources": ["pages/index/index.vue", "../../HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5kZXgvaW5kZXgudnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 顶部地图信息 -->\n\t\t<view class=\"map-bar\">\n\t\t\t<view class=\"map-bar-left\">\n\t\t\t\t<text class=\"user-name\">{{ player && player.name ? player.name : '' }}</text>\n\t\t\t\t<text class=\"user-label\">｜</text>\n\t\t\t\t<text class=\"map-label\">当前地图：</text>\n\t\t\t\t<text class=\"map-name\">{{ currentMapName || '' }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"map-bar-right\">\n\t\t\t\t<text class=\"connection-status\" :class=\"connectionStatusClass\">{{ connectionStatus }}</text>\n\t\t\t\t<button class=\"map-btn\" @click=\"showMapPopup = true\">切换地图</button>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 系统公告栏 -->\n\t\t<view class=\"announcement-bar\">\n\t\t\t<view class=\"announcement-icon\">📢</view>\n\t\t\t<view class=\"announcement-content\">\n\t\t\t\t<view class=\"announcement-scroll\">\n\t\t\t\t\t<text class=\"announcement-text\">{{ announcementText }}</text>\n\t\t\t\t\t<text class=\"announcement-text\">{{ announcementText }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 角色信息卡片（去除角色名和等级，仅保留属性条） -->\n\t\t<view class=\"character-card\">\n\t\t\t<view class=\"progress-bar\">\n\t\t\t\t<text class=\"progress-label\">气血</text>\n\t\t\t\t<view class=\"progress-bg\">\n\t\t\t\t\t<view class=\"progress-fill hp-fill\" :style=\"{ width: hpPercent + '%' }\"></view>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"progress-text\">{{ Math.floor(player.hp !== undefined ? player.hp : 100) }}/{{ Math.floor(player.max_hp || player.base_max_hp || 100) }} (原始:{{player.hp}}/{{player.max_hp}})</text>\n\t\t\t</view>\n\t\t\t<view class=\"progress-bar\">\n\t\t\t\t<text class=\"progress-label\">内力</text>\n\t\t\t\t<view class=\"progress-bg\">\n\t\t\t\t\t<view class=\"progress-fill mp-fill\" :style=\"{ width: mpPercent + '%' }\"></view>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"progress-text\">{{ Math.floor(player.mp || 50) }}/{{ Math.floor(player.max_mp || player.base_max_mp || 50) }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"progress-bar\">\n\t\t\t\t<text class=\"progress-label\">体力</text>\n\t\t\t\t<view class=\"progress-bg\">\n\t\t\t\t\t<view class=\"progress-fill stamina-fill\" :style=\"{ width: staminaPercent + '%' }\"></view>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"progress-text\">{{ Math.floor(player.energy || 100) }}/{{ Math.floor(player.max_energy || player.base_max_energy || 100) }}</text>\n\t\t\t</view>\n\t\t\t<view class=\"progress-bar\">\n\t\t\t\t<text class=\"progress-label\">精力</text>\n\t\t\t\t<view class=\"progress-bg\">\n\t\t\t\t\t<view class=\"progress-fill energy-fill\" :style=\"{ width: energyPercent + '%' }\"></view>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"progress-text\">{{ Math.floor(player.spirit || 100) }}/{{ Math.floor(player.max_spirit || 100) }}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 天赋增益信息 -->\n\t\t<view class=\"talent-bonus-card\" v-if=\"hasTalentBonuses\">\n\t\t\t<view class=\"talent-bonus-title\">天赋增益</view>\n\t\t\t<view class=\"talent-bonus-grid\">\n\t\t\t\t<view class=\"talent-bonus-item\" v-if=\"getStrengthBonus() > 0\">\n\t\t\t\t\t<text class=\"talent-bonus-label\">力量</text>\n\t\t\t\t\t<text class=\"talent-bonus-value\">+{{ Math.floor(getStrengthBonus() || 0) }}%攻击</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"talent-bonus-item\" v-if=\"getIntelligenceBonus() > 0\">\n\t\t\t\t\t<text class=\"talent-bonus-label\">悟性</text>\n\t\t\t\t\t<text class=\"talent-bonus-value\">+{{ Math.floor(getIntelligenceBonus() || 0) }}%经验</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"talent-bonus-item\" v-if=\"getAgilityDefenseBonus() > 0\">\n\t\t\t\t\t<text class=\"talent-bonus-label\">身法</text>\n\t\t\t\t\t<text class=\"talent-bonus-value\">+{{ Math.floor(getAgilityDefenseBonus() || 0) }}%防御</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"talent-bonus-item\" v-if=\"getConstitutionHpBonus() > 0\">\n\t\t\t\t\t<text class=\"talent-bonus-label\">根骨</text>\n\t\t\t\t\t<text class=\"talent-bonus-value\">+{{ Math.floor(getConstitutionHpBonus() || 0) }}%气血</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 江湖操作按钮区（美观优化版） -->\n\t\t<view class=\"jianghu-section-flex-opt\">\n\t\t\t<button class=\"side-btn\" @click=\"handleHealing\" :disabled=\"!isAuthed || status !== 'normal'\">\n\t\t\t\t<text class=\"side-btn-icon\">🏥</text>\n\t\t\t\t<text class=\"side-btn-text\">疗伤</text>\n\t\t\t</button>\n\t\t\t<button class=\"main-btn\" @click=\"triggerAdventure\" :disabled=\"!isAuthed || status !== 'normal'\">\n\t\t\t\t<text class=\"main-btn-text\">闯</text>\n\t\t\t</button>\n\t\t\t<button class=\"side-btn\" @click=\"handleMeditation\" :disabled=\"!isAuthed || status !== 'normal'\">\n\t\t\t\t<text class=\"side-btn-icon\">🧘‍♂️</text>\n\t\t\t\t<text class=\"side-btn-text\">打坐</text>\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 新增：功能性NPC展示区域 -->\n\t\t<view class=\"npc-bar\" v-if=\"displayMapNpcs.length > 0\">\n\t\t\t<view class=\"npc-item\" v-for=\"npc in displayMapNpcs\" :key=\"npc.id\" @click=\"showNpcMenu(npc)\">\n\t\t\t\t<image class=\"npc-avatar\" :src=\"npc.avatar\" mode=\"aspectFill\" />\n\t\t\t\t<text class=\"npc-name\">{{ npc && npc.name ? npc.name : '' }}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- NPC功能菜单弹窗 -->\n\t\t<view class=\"npc-menu-modal\" v-if=\"showNpcMenuModal\" @click=\"closeNpcMenu\">\n\t\t\t<view class=\"npc-menu-content\" @click.stop>\n\t\t\t\t<view class=\"npc-menu-header\">\n\t\t\t\t\t<image class=\"npc-menu-avatar\" :src=\"selectedNpc.avatar\" mode=\"aspectFill\" />\n\t\t\t\t\t<text class=\"npc-menu-name\">{{ selectedNpc && selectedNpc.name ? selectedNpc.name : '' }}</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"npc-menu-desc\">{{ selectedNpc.desc }}</text>\n\t\t\t\t<view class=\"npc-menu-list\">\n\t\t\t\t\t<button v-for=\"func in selectedNpc.functions\" :key=\"func.key\" class=\"npc-menu-btn\" @click=\"onNpcFunction(func, selectedNpc)\">{{ func.label }}</button>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"npc-menu-close\" @click=\"closeNpcMenu\">关闭</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 状态显示 -->\n\t\t<view class=\"status-display\" v-if=\"status !== 'normal'\">\n\t\t\t<text class=\"status-text\" :class=\"'status-' + status\">{{ getStatusText() }}</text>\n\t\t</view>\n\n\t\t<!-- 事件日志 -->\n\t\t<view class=\"event-log\">\n\t\t\t<view class=\"log-header\">\n\t\t\t\t<text class=\"log-title\">江湖日志</text>\n\t\t\t\t<text class=\"log-clear\" @click=\"clearLog\">清空</text>\n\t\t\t</view>\n\t\t\t<scroll-view class=\"log-content\" scroll-y=\"true\">\n\t\t\t\t<view class=\"log-item\" v-for=\"(event, index) in eventLog\" :key=\"index\">\n\t\t\t\t\t<view class=\"log-header-line\">\n\t\t\t\t\t\t<text class=\"log-time\">{{ event.timestamp }}</text>\n\t\t\t\t\t\t<text class=\"log-event\">{{ event && event.name ? event.name : '' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"log-content-line\">\n\t\t\t\t\t\t<view class=\"log-desc\" v-html=\"event.displayText\"></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"log-empty\" v-if=\"eventLog.length === 0\">\n\t\t\t\t\t<text>暂无江湖记录</text>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\n\t\t<gathering-popup\n\t\t\tv-if=\"showGatheringPopup\"\n\t\t\t:visible=\"showGatheringPopup\"\n\t\t\t:event=\"gatheringEvent\"\n\t\t\t:times=\"gatheringTimes\"\n\t\t\t:result=\"gatheringResult\"\n\t\t\t@close=\"closeGatheringPopup\"\n\t\t\t@do-gather=\"doGather\"\n\t\t/>\n\n\t\t<!-- 地图切换弹窗 -->\n\t\t<view v-if=\"showMapPopup\" class=\"map-popup-mask\" @click=\"showMapPopup = false\">\n\t\t\t<view class=\"map-popup\" @click.stop>\n\t\t\t\t<view class=\"map-popup-title\">切换地图</view>\n\t\t\t\t<view class=\"map-list\">\n\t\t\t\t\t<view v-for=\"map in mapList\" :key=\"map.id\" class=\"map-item\" :class=\"{ active: currentMap && currentMap.id === map.id, locked: !canEnterMap(map) }\" @click=\"selectMap(map)\">\n\t\t\t\t\t\t<view class=\"map-item-title\">{{ map.名称 || map.name || '' }}</view>\n\t\t\t\t\t\t<view class=\"map-item-desc\">{{ map.描述 || map.desc }}</view>\n\t\t\t\t\t\t<view class=\"map-item-npc\" v-if=\"map.NPC && map.NPC.length\">NPC：{{ map.NPC.map(n=>n.名称||n.name||'').join('、') }}</view>\n\t\t\t\t\t\t<view class=\"map-item-monster\" v-if=\"(map.怪物||map.monsters) && (map.怪物||map.monsters).length\">怪物：{{ (map.怪物||map.monsters).map(m=>m.名称||m.name||'').join('、') }}</view>\n\t\t\t\t\t\t<view class=\"map-item-gather\" v-if=\"(map.采集物品||map.gather_items) && (map.采集物品||map.gather_items).length\">采集物品：{{ (map.采集物品||map.gather_items).map(g=>g.物品||g.item+':'+(g.概率||g.prob)).join('、') }}</view>\n\t\t\t\t\t\t<view class=\"map-item-req\" v-if=\"map.进入要求||map.enter_requirements\">进入条件：{{ Object.entries(map.进入要求||map.enter_requirements).map(([k,v])=>k+':'+v).join('、') }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"close-btn\" @click=\"showMapPopup = false\">关闭</button>\n\t\t\t</view>\n\t\t</view>\n\t\t<view style=\"margin: 10px 0;\">\n    <button @click=\"testLog\">调试：打印日志数据</button>\n\t\t</view>\n\t\t\n\t\t<!-- 底部间距，避免被固定的江湖按钮遮挡 -->\n\t\t<view style=\"height: 140rpx;\"></view>\n\t\t<BattlePopup\n\t\t\tv-if=\"showBattlePopup\"\n\t\t\t:visible=\"showBattlePopup\"\n\t\t\t:battleLog=\"battleLog\"\n\t\t\t:player=\"battlePlayer\"\n\t\t\t:monster=\"battleMonster\"\n\t\t\t:battleStage=\"battleStage\"\n\t\t\t:attackMode=\"battleAttackMode\"\n\t\t\t@attack=\"handleBattleAttack\"\n\t\t\t@escape=\"handleBattleEscape\"\n\t\t\t@close=\"handleBattleClose\"\n\t\t/>\n\n\t\t<!-- 疗伤/打坐弹窗描述区（美化+居中+转圈） -->\n\t\t<view v-if=\"healingMeditationMessages.length > 0\" class=\"healing-meditation-popup-centered\">\n\t\t\t<view v-if=\"healingMeditationLoading\" class=\"healing-meditation-loading\">\n\t\t\t\t<view class=\"spinner\"></view>\n\t\t\t</view>\n\t\t\t<view v-for=\"(msg, idx) in healingMeditationMessages\" :key=\"idx\" class=\"healing-meditation-msg-centered\">{{ msg }}</view>\n\t\t</view>\n\n\t\t<!-- tabBar导航栏（仅在未显示战斗弹窗时显示） -->\n\t\t<myTabBar v-if=\"!showBattlePopup\" />\n\t\t<!-- 若用原生tabBar，可用 :style=\"showBattlePopup ? 'display:none' : ''\" 控制 -->\n\t</view>\n</template>\n\n<script>\nimport gameState from '../../utils/gameState.js'\nimport { gameUtils } from '../../utils/gameData.js'\nimport wsManager from '../../utils/websocket.js'\nimport GatheringPopup from '../../components/GatheringPopup.vue'\nimport BattlePopup from '../../components/BattlePopup.vue'\nimport customTabBar from '@/custom-tab-bar/index.vue'\n\n\texport default {\n\t\tcomponents: { GatheringPopup, BattlePopup, myTabBar: customTabBar },\n\t\tdata() {\n\t\t\treturn {\n\t\t\tplayer: {},\n\t\t\tmoney: 0,\n\t\t\tgold: 0,\n\t\t\tstatus: 'normal',\n\t\t\teventLog: [],\n\t\t\trequiredExp: 0,\n\t\t\tconnectionStatus: '未连接',\n\t\t\tisAuthed: false,\n\t\t\tshowGatheringPopup: false,\n\t\t\tgatheringEvent: null,\n\t\t\tgatheringTimes: 0,\n\t\t\tgatheringResult: '',\n\t\t\tcurrentMap: null,\n\t\t\tshowMapPopup: false,\n\t\t\tannouncementText: '欢迎来到仗剑江湖行！系统运行正常，祝您游戏愉快！新版本已上线，新增多种武功秘籍和装备道具，快来体验吧！',\n\t\t\tmapNpcs: [],\n\t\t\tshowNpcMenuModal: false,\n\t\t\tselectedNpc: {},\n\t\t\tmapsConfig: {},\n\t\t\tshowBattlePopup: false,\n\t\t\tbattleLog: [],\n\t\t\tbattlePlayer: {},\n\t\t\tbattleMonster: {},\n\t\t\tbattleStage: '',\n\t\t\tbattleAttackMode: '',\n\t\t\thealingMeditationMessages: [],\n\t\t\thealingMeditationLoading: false, // 新增：疗伤/打坐loading\n\t\t}\n\t},\n\t\n\tcomputed: {\n\t\thpPercent() {\n\t\t\tconst hp = this.player.hp || 0;\n\t\t\tconst maxHp = this.player.max_hp || this.player.base_max_hp || 100;\n\t\t\tconst percent = (hp / maxHp) * 100;\n\t\t\tconsole.log('[首页] hpPercent计算:', { hp, maxHp, percent });\n\t\t\treturn percent;\n\t\t},\n\t\tmpPercent() {\n\t\t\treturn this.player.mp && (this.player.max_mp || this.player.base_max_mp) ? (this.player.mp / (this.player.max_mp || this.player.base_max_mp)) * 100 : 100\n\t\t},\n\t\tstaminaPercent() {\n\t\t\treturn this.player.energy && (this.player.max_energy || this.player.base_max_energy) ? (this.player.energy / (this.player.max_energy || this.player.base_max_energy)) * 100 : 100\n\t\t},\n\t\tenergyPercent() {\n\t\t\treturn this.player.spirit && this.player.max_spirit ? (this.player.spirit / this.player.max_spirit) * 100 : 100\n\t\t},\n\t\texpPercent() {\n\t\t\treturn this.player.exp && this.requiredExp ? (this.player.exp / this.requiredExp) * 100 : 0\n\t\t},\n\t\tconnectionStatusClass() {\n\t\t\tconst status = this.connectionStatus;\n\t\t\tif (status === '已连接') return 'connected';\n\t\t\tif (status === '未连接') return 'disconnected';\n\t\t\tif (status === '连接失败') return 'disconnected';\n\t\t\treturn 'disconnected';\n\t\t},\n\t\tcurrentMapName() {\n\t\t\tconst mapId = this.player && this.player.current_map;\n\t\t\tconst mapObj = this.mapsConfig && mapId ? this.mapsConfig[mapId] : null;\n\t\t\tconsole.log('[首页] currentMapName计算:', { mapId, mapObj, mapsConfig: this.mapsConfig });\n\t\t\treturn mapObj ? (mapObj.名称 || mapObj.name) : '未知';\n\t\t},\n\t\tmapList() {\n\t\t\tconsole.log('[首页] mapList计算，mapsConfig:', this.mapsConfig);\n\t\t\tif (!this.mapsConfig) return [];\n\t\t\treturn Object.values(this.mapsConfig).map(m => {\n\t\t\t\tconst map = { ...m };\n\t\t\t\t// NPC 字段兼容\n\t\t\t\tif (!Array.isArray(map.NPC)) {\n\t\t\t\t\tif (typeof map.NPC === 'string') {\n\t\t\t\t\t\tmap.NPC = map.NPC.split(',').map(n => ({ 名称: n.trim() }));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmap.NPC = [];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 怪物字段兼容\n\t\t\t\tif (!Array.isArray(map.怪物)) {\n\t\t\t\t\tif (typeof map.怪物 === 'string') {\n\t\t\t\t\t\tmap.怪物 = map.怪物.split(',').map(n => ({ 名称: n.trim() }));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmap.怪物 = [];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!Array.isArray(map.monsters)) {\n\t\t\t\t\tif (typeof map.monsters === 'string') {\n\t\t\t\t\t\tmap.monsters = map.monsters.split(',').map(n => ({ name: n.trim() }));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmap.monsters = [];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 采集物品字段兼容\n\t\t\t\tif (!Array.isArray(map.采集物品)) {\n\t\t\t\t\tif (typeof map.采集物品 === 'string') {\n\t\t\t\t\t\tmap.采集物品 = map.采集物品.split(',').map(item => {\n\t\t\t\t\t\t\tconst [name, prob] = item.split(':');\n\t\t\t\t\t\t\treturn { 物品: name.trim(), 概率: prob ? parseFloat(prob) : undefined };\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmap.采集物品 = [];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (!Array.isArray(map.gather_items)) {\n\t\t\t\t\tif (typeof map.gather_items === 'string') {\n\t\t\t\t\t\tmap.gather_items = map.gather_items.split(',').map(item => {\n\t\t\t\t\t\t\tconst [name, prob] = item.split(':');\n\t\t\t\t\t\t\treturn { item: name.trim(), prob: prob ? parseFloat(prob) : undefined };\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmap.gather_items = [];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn map;\n\t\t\t});\n\t\t},\n\t\tcurrentMapNpcsLocal() {\n\t\t\tconst mapId = this.player && this.player.current_map;\n\t\t\tconst map = this.mapsConfig && this.mapsConfig[mapId];\n\t\t\tif (!map || !Array.isArray(map.NPC)) return [];\n\t\t\treturn map.NPC.map((name, idx) => ({\n\t\t\t\tid: `npc_${idx}`,\n\t\t\t\tname,\n\t\t\t\tavatar: 'static/npc/default.png',\n\t\t\t\tdesc: `${name}：一位神秘的江湖人物。`,\n\t\t\t\tfunctions: [\n\t\t\t\t\t{ key: 'talk', label: '对话' },\n\t\t\t\t\t{ key: 'shop', label: '交易' }\n\t\t\t\t]\n\t\t\t}));\n\t\t},\n\t\tdisplayMapNpcs() {\n\t\t\treturn this.mapNpcs && this.mapNpcs.length > 0 ? this.mapNpcs : this.currentMapNpcsLocal;\n\t\t},\n\t\tgetStrengthBonus() {\n\t\t\treturn this.player?.talent_bonuses?.strength?.bonus_percentage || 0;\n\t\t},\n\t\tgetIntelligenceBonus() {\n\t\t\treturn this.player?.talent_bonuses?.intelligence?.bonus_percentage || 0;\n\t\t},\n\t\tgetAgilityDefenseBonus() {\n\t\t\treturn this.player?.talent_bonuses?.agility?.defense_bonus_percentage || 0;\n\t\t},\n\t\tgetConstitutionBonus() {\n\t\t\t// 根骨百分比加成依然由后端体力恢复详情返回，或为0\n\t\t\treturn 0;\n\t\t},\n\t\tgetConstitutionHpBonus() {\n\t\t\treturn this.player?.talent_bonuses?.constitution?.hp_bonus_percentage || 0;\n\t\t},\n\t\tget hasTalentBonuses() {\n\t\t\treturn (this.player?.talent_bonuses?.strength?.bonus_percentage || 0) > 0 || \n\t\t\t\t   (this.player?.talent_bonuses?.intelligence?.bonus_percentage || 0) > 0 || \n\t\t\t\t   (this.player?.talent_bonuses?.agility?.defense_bonus_percentage || 0) > 0 || \n\t\t\t\t   (this.player?.talent_bonuses?.constitution?.hp_bonus_percentage || 0) > 0;\n\t\t},\n\t},\n\t\n\tasync onLoad() {\n\t\tconsole.log('【调试】onLoad已执行');\n\t\tawait this.loadMapsConfig(); // 确保加载地图配置\n\t\tconsole.log('【调试】主页面加载，开始检查登录状态...');\n\t\t// 检查登录状态\n\t\tthis.checkLoginAndInit();\n\t\t// 注册状态更新回调\n\t\tif (typeof gameState.onUpdate === 'function') {\n\t\t\tgameState.onUpdate(this.handleStateUpdate);\n\t\t\tconsole.log('【调试】已注册gameState.onUpdate');\n\t\t}\n\t\t// 主动拉取一次数据\n\t\tthis.updateData();\n\t\t// 注册全服公告监听\n\t\twsManager.on('announcement', (data) => {\n\t\t\tif (data && data.content) {\n\t\t\t\tthis.announcementText = data.content;\n\t\t\t}\n\t\t});\n\t\t\n\t\t// 添加对game_event事件的监听\n\t\twsManager.on('game_event', this.handleGameEvent);\n\t\tconsole.log('【调试】已注册game_event事件监听器');\n\t\t\n\t\t// 获取天赋加成信息\n\t\tthis.fetchBonusSummary();\n\t\t\n\t\t// 新增：onLoad时也主动拉取一次地图列表\n\t\t// this.fetchMapList(); // 移除对 fetchMapList 的依赖\n\t\tconsole.log('【调试】setTimeout准备调用fetchMapNpcs');\n\t\t// 强制2秒后拉取一次NPC，便于调试\n\t\tsetTimeout(() => {\n\t\t\tconsole.log('【调试】setTimeout内，准备调用fetchMapNpcs');\n\t\t\tthis.fetchMapNpcs();\n\t\t}, 2000);\n\t\twsManager.on('encounter_monster', this.handleEncounterMonster);\n\t\twsManager.on('battle_round', this.handleBattleRound);\n\t\twsManager.on('battle_result', this.handleBattleResult);\n\t\twsManager.on('player_data', this.handleBattlePlayerData);\n\t\tconsole.log('[首页] 战斗相关事件监听器已注册');\n\t\tconsole.log('[首页] 注册的事件: encounter_monster, battle_round, battle_result, player_data');\n\t},\n\t\n\tonReady() {\n\t\t// 页面渲染完成\n\t},\n\t\n\tonShow() {\n\t\tconsole.log('=== 首页onShow被调用 ===');\n\t\tconsole.log('当前时间:', new Date().toLocaleString());\n\t\tconsole.log('当前页面路径:', getCurrentPages()[getCurrentPages().length - 1].route);\n\t\t\n\t\t// 每次显示页面时更新数据\n\t\tthis.updateData()\n\t\t// 更新连接状态\n\t\tthis.updateConnectionStatus()\n\t\t\n\t\t// 检查认证状态\n\t\tconsole.log('this.isAuthed:', this.isAuthed);\n\t\tconsole.log('gameState.isAuthed:', gameState.isAuthed);\n\t\tconsole.log('wsManager.isConnected:', wsManager.isConnected);\n\t\t\n\t\t// 移除自动触发闯江湖的逻辑，只在用户主动点击时才触发\n\t\tconsole.log('页面显示完成，等待用户操作');\n\t},\n\t\n\tonTabItemTap(item) {\n\t\tconsole.log('=== 首页onTabItemTap被调用 ===');\n\t\tconsole.log('点击的tab项:', item);\n\t\tconsole.log('当前时间:', new Date().toLocaleString());\n\t\t\n\t\t// 移除自动触发闯江湖的逻辑，让用户主动点击页面内的按钮才触发\n\t\tif (item.index === 2) { // 闯按钮的索引是2\n\t\t\tconsole.log('检测到闯按钮被点击，但不自动触发闯江湖，等待用户主动操作');\n\t\t}\n\t},\n\t\n\tonUnload() {\n\t\t// 页面卸载时移除回调\n\t\tgameState.offUpdate(this.handleStateUpdate)\n\t\t// 新增：移除 game_event 监听，防止重复注册\n\t\twsManager.off('game_event', this.handleGameEvent)\n\t\t// wsManager.off('map_list', this.onMapList) // 移除对 onMapList 的依赖\n\t\twsManager.off('select_map_success', this.onSelectMapSuccess)\n\t\twsManager.off('error', this.onMapError)\n\t},\n\t\n\tcreated() {\n\t\t// 不再直接使用wsManager.on，改为在各方法中使用gameUtils.sendMessage\n\t\tconsole.log('[调试] 初始化index.vue');\n\t},\n\t\n\twatch: {\n\t\t'player.current_map': {\n\t\t\thandler(newVal) {\n\t\t\t\tif (newVal && this.mapList && this.mapList.length) {\n\t\t\t\t\tthis.fetchMapNpcs();\n\t\t\t\t}\n\t\t\t},\n\t\t\timmediate: true\n\t\t},\n\t\tmapList: {\n\t\t\thandler(newVal) {\n\t\t\t\tif (gameState.player && gameState.player.current_map && newVal && newVal.length) {\n\t\t\t\t\tthis.fetchMapNpcs();\n\t\t\t\t}\n\t\t\t},\n\t\t\timmediate: true\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\tcheckLoginAndInit() {\n\t\t\t// 检查是否有登录信息\n\t\t\tconst token = uni.getStorageSync('token');\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\n\t\t\t\n\t\t\tconsole.log('检查登录信息 - token:', token ? '存在' : '不存在');\n\t\t\tconsole.log('检查登录信息 - userInfo:', userInfo ? '存在' : '不存在');\n\t\t\t\n\t\t\tif (!token || !userInfo) {\n\t\t\t\tconsole.log('未找到登录信息，跳转到登录页面');\n\t\t\t\tuni.reLaunch({\n\t\t\t\t\turl: '/pages/login/login'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('找到登录信息，开始初始化游戏');\n\t\t\tconsole.log('userInfo详情:', userInfo);\n\t\t\tthis.initGame();\n\t\t},\n\t\t\n\t\tasync initGame() {\n\t\t\ttry {\n\t\t\t\tconsole.log('开始初始化游戏...');\n\t\t\t\t// 注册状态更新回调\n\t\t\t\tgameState.onUpdate(this.handleStateUpdate);\n\t\t\t\tconsole.log('状态更新回调已注册');\n\t\t\t\t\n\t\t\t\t// 初始化游戏状态\n\t\t\t\tawait gameState.init();\n\t\t\t\tthis.updateConnectionStatus();\n\t\t\t\tthis.updateData();\n\t\t\t\tconsole.log('游戏初始化完成，认证状态:', this.isAuthed);\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('游戏初始化失败:', error);\n\t\t\t\tthis.connectionStatus = '连接失败';\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '游戏初始化失败: ' + error.message,\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 3000\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\tupdateConnectionStatus() {\n\t\t\tthis.connectionStatus = wsManager.isConnected ? '已连接' : '未连接';\n\t\t\tconsole.log('连接状态已更新为:', this.connectionStatus);\n\t\t\t// 断开时自动取消认证\n\t\t\tif (!wsManager.isConnected) {\n\t\t\t\tthis.isAuthed = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\thandleStateUpdate(type, gameStateInstance) {\n\t\t\tconsole.log('收到状态更新:', type, gameStateInstance);\n\t\t\tconsole.log('更新前的this.player:', this.player);\n\t\t\tconsole.log('更新前的认证状态:', this.isAuthed);\n\t\t\t\n\t\t\t// 根据更新类型更新对应数据\n\t\t\tswitch (type) {\n\t\t\t\tcase 'player':\n\t\t\t\t\tif (gameStateInstance.player) {\n\t\t\t\t\t\t// 检查是否在战斗中，如果是则保留当前的气血值\n\t\t\t\t\t\tconst currentHp = this.player.hp;\n\t\t\t\t\t\tconst isInBattle = this.showBattlePopup && this.battleStage === 'battle';\n\t\t\t\t\t\t\n\t\t\t\t\t\tthis.player = { ...gameStateInstance.player };\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 如果在战斗中且当前气血不为undefined，则保留当前气血\n\t\t\t\t\t\tif (isInBattle && currentHp !== undefined) {\n\t\t\t\t\t\t\tthis.player.hp = currentHp;\n\t\t\t\t\t\t\tconsole.log('[首页] 战斗中保留气血值:', currentHp);\n\t\t\t\t\t\t}\n\t\t\t\t\t\t\n\t\t\t\t\t\tconsole.log('player已同步:', this.player);\n\t\t\t\t\t\tconsole.log('当前地图ID:', this.player.current_map);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.warn('收到player更新但gameState.player为空');\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'currency':\n\t\t\t\t\tthis.money = gameStateInstance.money;\n\t\t\t\t\tthis.gold = gameStateInstance.gold;\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'status':\n\t\t\t\t\tthis.status = gameStateInstance.status;\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'eventLog':\n\t\t\t\t\tconsole.log('收到eventLog更新，gameState.eventLog:', gameStateInstance.eventLog);\n\t\t\t\t\t// 所有事件都使用打字机效果，包括装备名称\n\t\t\t\t\tthis.eventLog = gameStateInstance.eventLog.map((log, idx) => {\n\t\t\t\t\t\tif (idx === 0) {\n\t\t\t\t\t\t\treturn { ...log, displayText: '' };\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\treturn { ...log, displayText: log.description };\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t\tif (this.eventLog.length > 0) {\n\t\t\t\t\t\tthis.typeWriterEffect(this.eventLog[0], 0);\n\t\t\t\t\t}\n\t\t\t\t\tconsole.log('赋值后this.eventLog:', this.eventLog);\n\t\t\t\t\tbreak;\n\t\t\t\tcase 'auth':\n\t\t\t\t\tthis.isAuthed = gameStateInstance.isAuthed || false;\n\t\t\t\t\tconsole.log('认证状态已更新为:', this.isAuthed);\n\t\t\t\t\t// 新增：认证成功后自动拉取地图列表\n\t\t\t\t\tif (this.isAuthed) {\n\t\t\t\t\t\tconsole.log('[自动修复] 认证成功，主动请求地图列表');\n\t\t\t\t\t\t// this.fetchMapList(); // 移除对 fetchMapList 的依赖\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\tthis.updateData();\n\t\t\t}\n\t\t\t\n\t\t\t// 确保认证状态始终同步（优先使用WebSocket管理器的状态）\n\t\t\tif (type !== 'auth') {\n\t\t\t\tthis.isAuthed = wsManager.isAuthed || gameStateInstance.isAuthed || false;\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('状态更新完成，认证状态:', this.isAuthed, '当前player:', this.player);\n\t\t},\n\t\t\n\t\tupdateData() {\n\t\t\t// 始终用最新的 gameState.player\n\t\t\tthis.player = gameState.getPlayerData ? gameState.getPlayerData() : gameState.player;\n\t\t\tif (gameState.player) {\n\t\t\t\tthis.player = { ...gameState.player };\n\t\t\t\tif (this.mapList && this.mapList.length && gameState.player.current_map) {\n\t\t\t\t\tthis.currentMap = this.mapList.find(m => m.id === gameState.player.current_map) || null;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis.money = gameState.money || 0;\n\t\t\tthis.gold = gameState.gold || 0;\n\t\t\tthis.status = gameState.status || 'normal';\n\t\t\tthis.eventLog = [...(gameState.eventLog || [])];\n\t\t\t\n\t\t\t// 获取天赋加成信息\n\t\t\tthis.fetchBonusSummary();\n\t\t\t\n\t\t\t// 获取地图NPC信息\n\t\t\tthis.fetchMapNpcs();\n\t\t},\n\t\t\n\t\tformatNumber(num) {\n\t\t\treturn gameUtils.formatNumber(num)\n\t\t},\n\t\t\n\t\tgetStatusText() {\n\t\t\tconst statusTexts = {\n\t\t\t\t'injured': '重伤',\n\t\t\t\t'internal_injury': '内伤',\n\t\t\t\t'poisoned': '中毒',\n\t\t\t\t'tired': '疲劳'\n\t\t\t}\n\t\t\treturn statusTexts[this.status] || '正常'\n\t\t},\n\t\t\n\t\ttriggerAdventure() {\n\t\t\tconsole.log('=== 触发闯江湖功能 ===');\n\t\t\tconsole.log('当前时间:', new Date().toLocaleString());\n\t\t\tconsole.log('当前认证状态:', this.isAuthed);\n\t\t\tconsole.log('gameState认证状态:', gameState.isAuthed);\n\t\t\tconsole.log('WebSocket认证状态:', wsManager.isAuthed);\n\t\t\tconsole.log('WebSocket连接状态:', wsManager.isConnected);\n\t\t\tconsole.log('WebSocket服务器地址:', wsManager.serverUrl);\n\t\t\t\n\t\t\t// 优先使用WebSocket管理器的认证状态\n\t\t\tconst isAuthenticated = wsManager.isAuthed || gameState.isAuthed || this.isAuthed;\n\t\t\t\n\t\t\tif (!isAuthenticated) {\n\t\t\t\tconsole.log('❌ 未认证，显示提示');\n\t\t\t\tuni.showToast({ title: '请先登录', icon: 'none' });\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (!wsManager.isConnected) {\n\t\t\t\tconsole.log('❌ WebSocket未连接，显示提示');\n\t\t\t\tuni.showToast({ title: '网络连接失败', icon: 'none' });\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tconsole.log('✅ 开始调用gameState.triggerAdventure()');\n\t\t\tgameState.triggerAdventure();\n\t\t\tconsole.log('✅ gameState.triggerAdventure()调用完成');\n\t\t},\n\t\t\n\t\tclearLog() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认清空',\n\t\t\t\tcontent: '确定要清空江湖日志吗？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tgameState.eventLog = []\n\t\t\t\t\t\tthis.eventLog = []\n\t\t\t\t\t\tgameState.save()\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\t\t\n\t\tnavigateTo(path) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: path\n\t\t\t})\n\t\t},\n\t\t\n\t\ttestConnection() {\n\t\t\tconsole.log('=== 连接状态测试 ===');\n\t\t\tconsole.log('当前时间:', new Date().toLocaleString());\n\t\t\tconsole.log('当前WebSocket连接状态:', wsManager.isConnected);\n\t\t\tconsole.log('WebSocket认证状态:', wsManager.isAuthed);\n\t\t\tconsole.log('当前认证状态:', this.isAuthed);\n\t\t\tconsole.log('gameState认证状态:', gameState.isAuthed);\n\t\t\tconsole.log('gameState玩家数据:', gameState.player);\n\t\t\t\n\t\t\t// 检查本地存储\n\t\t\tconst token = uni.getStorageSync('token');\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\n\t\t\tconsole.log('本地存储检查:');\n\t\t\tconsole.log('- token存在:', !!token);\n\t\t\tconsole.log('- userInfo存在:', !!userInfo);\n\t\t\t\n\t\t\t// 显示当前状态\n\t\t\tconst statusInfo = `WebSocket连接: ${wsManager.isConnected ? '已连接' : '未连接'}\n服务器地址: ${wsManager.serverUrl}\nWebSocket认证: ${wsManager.isAuthed ? '已认证' : '未认证'}\n页面认证: ${this.isAuthed ? '已认证' : '未认证'}\nGameState认证: ${gameState.isAuthed ? '已认证' : '未认证'}\n本地token: ${token ? '存在' : '不存在'}\n本地userInfo: ${userInfo ? '存在' : '不存在'}\n玩家数据: ${gameState.player ? '存在' : '不存在'}\n连接状态: ${this.connectionStatus}`;\n\t\t\t\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '连接状态详情',\n\t\t\t\tcontent: statusInfo,\n\t\t\t\tshowCancel: true,\n\t\t\t\tcancelText: '手动认证',\n\t\t\t\tconfirmText: '确定',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.cancel) {\n\t\t\t\t\t\t// 手动触发认证\n\t\t\t\t\t\tconsole.log('手动触发认证...');\n\t\t\t\t\t\twsManager.autoAuthenticate();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t\t\n\t\t\t// 如果未连接，尝试重新连接\n\t\t\tif (!wsManager.isConnected) {\n\t\t\t\tconsole.log('尝试重新连接...');\n\t\t\t\twsManager.connect().then(() => {\n\t\t\t\t\tconsole.log('重新连接成功');\n\t\t\t\t\tthis.updateConnectionStatus();\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tconsole.error('重新连接失败:', error);\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 新增：根据装备动态计算可采集次数\n\t\tgetGatherToolInfo(requiredTool) {\n\t\t\t// 先查装备栏\n\t\t\tif (gameState.equipment) {\n\t\t\t\tconst tool = Object.values(gameState.equipment).find(e => e && e.type === requiredTool);\n\t\t\t\tif (tool) return tool;\n\t\t\t}\n\t\t\t// 再查背包，确保 inventory 一定为数组\n\t\t\tif (Array.isArray(gameState.inventory)) {\n\t\t\t\tconst tool = gameState.inventory.find(i => i && i.type === requiredTool);\n\t\t\t\tif (tool) return tool;\n\t\t\t}\n\t\t\treturn null;\n\t\t},\n\n\t\t// 工具等级与可采集次数映射\n\t\tgetGatherTimesByTool(tool) {\n\t\t\tif (!tool) return 0;\n\t\t\t// 与后端一致，直接读取工具的 gather_times 属性\n\t\t\treturn tool.gather_times || 1;\n\t\t},\n\n\t\thandleGameEvent(data) {\n\t\t\t// 调试：打印所有收到的事件内容和字段\n\t\t\tconsole.log('[handleGameEvent] 收到事件:', JSON.stringify(data));\n\t\t\t\n\t\t\t// 确保数据格式正确\n\t\t\tconst content = (data.content || data.description || '').toString();\n\t\t\tconst eventType = data.eventType || data.type || 5;\n\t\t\t\n\t\t\t// 直接调用gameState的处理方法，确保事件被正确记录\n\t\t\tif (gameState && typeof gameState.handleGameEvent === 'function') {\n\t\t\t\tconsole.log('[handleGameEvent] 转发事件到gameState处理');\n\t\t\t\tgameState.handleGameEvent({\n\t\t\t\t\ttype: eventType,\n\t\t\t\t\tcontent: content,\n\t\t\t\t\trewards: data.rewards || {},\n\t\t\t\t\trealm_breakthrough: data.realm_breakthrough || null\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 以下是原来的逻辑，作为备用\n\t\t\tconst toolField = data.requiredTool || data.toolType || data.tool || data.gatherTool || '';\n\t\t\tconst gatherType = data.gatherType || data.type || '';\n\t\t\tconst hasToolField = !!(data.requiredTool || data.toolType || data.tool || data.gatherTool);\n\t\t\tconst isGatherPoint =\n\t\t\t\tdata.eventType === 'gathering' ||\n\t\t\t\tgatherType === 'gathering' ||\n\t\t\t\tgatherType === 'gather' ||\n\t\t\t\t/(采集点|可以采集|发现资源|发现矿脉|发现草药|发现|可采集)/.test(content) ||\n\t\t\t\thasToolField;\n\t\t\tconst isGatherResult = /采集到|获得了|挖到了|收获了/.test(content);\n\t\t\tif (isGatherPoint && !isGatherResult) {\n\t\t\t\tconsole.log('[handleGameEvent] 识别为采集点事件，弹窗');\n\t\t\t\tconst tool = this.getGatherToolInfo(toolField || 'hoe');\n\t\t\t\tconst maxTimes = this.getGatherTimesByTool(tool);\n\t\t\t\t// 优先显示实际拥有的工具名称，否则用后端配置\n\t\t\t\tconst toolName = tool ? tool.name : (toolField || '采集工具');\n\t\t\t\tthis.gatheringEvent = {\n\t\t\t\t\tcontent: content || '你发现了可采集的资源。',\n\t\t\t\t\tgatherType,\n\t\t\t\t\trequiredTool: toolField || 'hoe',\n\t\t\t\t\tgatherTimes: maxTimes,\n\t\t\t\t\ttoolName\n\t\t\t\t};\n\t\t\t\tthis.gatheringTimes = maxTimes;\n\t\t\t\tthis.gatheringResult = '';\n\t\t\t\tthis.showGatheringPopup = true;\n\t\t\t} else if (isGatherResult) {\n\t\t\t\tconsole.log('[handleGameEvent] 识别为采集结果事件，交给 gameState 处理');\n\t\t\t\t// 采集弹窗内回显多行内容\n\t\t\t\tthis.gatheringResult = content.replace(/\\n/g, '\\n');\n\t\t\t\t// 统一交给 gameState 处理事件日志\n\t\t\t\tif (gameState && typeof gameState.handleGameEvent === 'function') {\n\t\t\t\t\tgameState.handleGameEvent({\n\t\t\t\t\t\ttype: eventType,\n\t\t\t\t\t\tcontent: content,\n\t\t\t\t\t\trewards: data.rewards || {}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tconsole.log('[handleGameEvent] 识别为普通事件，交给 gameState 处理');\n\t\t\t\tif (gameState && typeof gameState.handleGameEvent === 'function') {\n\t\t\t\t\tgameState.handleGameEvent({\n\t\t\t\t\t\ttype: eventType,\n\t\t\t\t\t\tcontent: content,\n\t\t\t\t\t\trewards: data.rewards || {}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tdoGather() {\n\t\t\tconst requiredTool = this.gatheringEvent.requiredTool;\n\t\t\tconst tool = this.getGatherToolInfo(requiredTool);\n\t\t\tif (!tool) {\n\t\t\t\tthis.gatheringResult = '请先装备对应的采集工具！';\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tif (this.gatheringTimes <= 0) {\n\t\t\t\tthis.gatheringResult = '本次采集已完成！';\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t// 使用gameUtils.sendMessage发送采集请求\n\t\t\tgameUtils.sendMessage({\n\t\t\t\ttype: 'gather_action',\n\t\t\t\tdata: { gatherType: this.gatheringEvent.gatherType }\n\t\t\t});\n\t\t\tthis.gatheringTimes--;\n\t\t\tif (this.gatheringTimes <= 0) {\n\t\t\t\tsetTimeout(() => this.closeGatheringPopup(), 800);\n\t\t\t}\n\t\t},\n\t\t\n\t\tcloseGatheringPopup() {\n\t\t\tthis.showGatheringPopup = false;\n\t\t\tthis.gatheringEvent = null;\n\t\t\tthis.gatheringResult = '';\n\t\t},\n\t\t\n\t\t// 天赋增益计算方法\n\t\tgetStrengthBonus() {\n\t\t\treturn this.player?.talent_bonuses?.strength?.bonus_percentage || 0;\n\t\t},\n\t\t\n\t\tgetIntelligenceBonus() {\n\t\t\treturn this.player?.talent_bonuses?.intelligence?.bonus_percentage || 0;\n\t\t},\n\t\t\n\t\tgetAgilityDefenseBonus() {\n\t\t\treturn this.player?.talent_bonuses?.agility?.defense_bonus_percentage || 0;\n\t\t},\n\t\t\n\t\tgetConstitutionBonus() {\n\t\t\t// 根骨百分比加成依然由后端体力恢复详情返回，或为0\n\t\t\treturn 0;\n\t\t},\n\t\t\n\t\tgetConstitutionHpBonus() {\n\t\t\treturn this.player?.talent_bonuses?.constitution?.hp_bonus_percentage || 0;\n\t\t},\n\t\t\n\t\tget hasTalentBonuses() {\n\t\t\treturn (this.player?.talent_bonuses?.strength?.bonus_percentage || 0) > 0 || \n\t\t\t\t   (this.player?.talent_bonuses?.intelligence?.bonus_percentage || 0) > 0 || \n\t\t\t\t   (this.player?.talent_bonuses?.agility?.defense_bonus_percentage || 0) > 0 || \n\t\t\t\t   (this.player?.talent_bonuses?.constitution?.hp_bonus_percentage || 0) > 0;\n\t\t},\n\t\t\n\t\t// 获取天赋加成信息\n\t\tasync fetchBonusSummary() {\n\t\t\ttry {\n\t\t\t\tconsole.log('【调试】开始获取天赋加成信息');\n\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\ttype: 'get_bonus_summary',\n\t\t\t\t\tdata: {}\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconsole.log('【调试】获取天赋加成响应:', response);\n\t\t\t\t\n\t\t\t\t// 处理超时情况\n\t\t\t\tif (response.type === 'get_bonus_summary_timeout') {\n\t\t\t\t\tconsole.log('【调试】获取天赋加成超时，使用默认值');\n\t\t\t\t\t// 超时时不做特殊处理，使用player中已有的talent_bonuses\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 处理成功情况\n\t\t\t\tif (response.type === 'get_bonus_summary_success' || response.type === 'bonus_summary') {\n\t\t\t\t\tconst bonusData = response.data || {};\n\t\t\t\t\tconsole.log('【调试】解析天赋加成数据:', bonusData);\n\t\t\t\t\t\n\t\t\t\t\t// 更新玩家天赋加成数据\n\t\t\t\t\tif (!this.player.talent_bonuses) {\n\t\t\t\t\t\tthis.player.talent_bonuses = {};\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理力量加成\n\t\t\t\t\tif (bonusData.strength) {\n\t\t\t\t\t\tthis.player.talent_bonuses.strength = {\n\t\t\t\t\t\t\tbonus_percentage: bonusData.strength.attack_bonus || 0\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理悟性加成\n\t\t\t\t\tif (bonusData.intelligence) {\n\t\t\t\t\t\tthis.player.talent_bonuses.intelligence = {\n\t\t\t\t\t\t\tbonus_percentage: bonusData.intelligence.exp_bonus || 0\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理身法加成\n\t\t\t\t\tif (bonusData.agility) {\n\t\t\t\t\t\tthis.player.talent_bonuses.agility = {\n\t\t\t\t\t\t\tdefense_bonus_percentage: bonusData.agility.defense_bonus || 0\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 处理根骨加成\n\t\t\t\t\tif (bonusData.constitution) {\n\t\t\t\t\t\tthis.player.talent_bonuses.constitution = {\n\t\t\t\t\t\t\thp_bonus_percentage: bonusData.constitution.hp_bonus || 0\n\t\t\t\t\t\t};\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tconsole.log('【调试】更新后的天赋加成数据:', this.player.talent_bonuses);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('【调试】获取天赋加成异常:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 移除 fetchMapList 和 onMapList 相关逻辑\n\t\t// fetchMapList() {\n\t\t// \tconsole.log('【调试】请求地图列表');\n\t\t// \twsManager.sendMessage('get_map_list');\n\t\t// },\n\t\t// onMapList(data) {\n\t\t// \tconsole.log('【调试】onMapList被调用');\n\t\t// \tlet maps = [];\n\t\t// \tif (Array.isArray(data)) {\n\t\t// \t\tmaps = data;\n\t\t// \t} else if (Array.isArray(data.map_list)) {\n\t\t// \t\tmaps = data.map_list;\n\t\t// \t} else if (Array.isArray(data.data)) {\n\t\t// \t\tmaps = data.data;\n\t\t// \t} else if (typeof data === 'object' && data !== null) {\n\t\t// \t\t// 兼容后端返回对象（字典）结构\n\t\t// \t\tmaps = Object.values(data).filter(m => m && m.id);\n\t\t// \t}\n\t\t// \t// 新增：兼容所有相关字段为字符串的情况，强制转为数组对象\n\t\t// \tmaps.forEach(m => {\n\t\t// \t\t// NPC 字段兼容\n\t\t// \t\tif (m.NPC && typeof m.NPC === 'string') {\n\t\t// \t\t\tm.NPC = m.NPC.split(',').map(n => ({ 名称: n.trim() }));\n\t\t// \t\t}\n\t\t// \t\t// 怪物字段兼容\n\t\t// \t\tif (m.怪物 && typeof m.怪物 === 'string') {\n\t\t// \t\t\tm.怪物 = m.怪物.split(',').map(n => ({ 名称: n.trim() }));\n\t\t// \t\t}\n\t\t// \t\t// 采集物品字段兼容\n\t\t// \t\tif (m.采集物品 && typeof m.采集物品 === 'string') {\n\t\t// \t\t\tm.采集物品 = m.采集物品.split(',').map(item => {\n\t\t// \t\t\t\tconst [name, prob] = item.split(':');\n\t\t// \t\t\t\treturn { 物品: name.trim(), 概率: prob ? parseFloat(prob) : undefined };\n\t\t// \t\t\t});\n\t\t// \t\t}\n\t\t// \t\t// 英文 monsters 字段兼容\n\t\t// \t\tif (m.monsters && typeof m.monsters === 'string') {\n\t\t// \t\t\tm.monsters = m.monsters.split(',').map(n => ({ name: n.trim() }));\n\t\t// \t\t}\n\t\t// \t\t// 英文 gather_items 字段兼容\n\t\t// \t\tif (m.gather_items && typeof m.gather_items === 'string') {\n\t\t// \t\t\tm.gather_items = m.gather_items.split(',').map(item => {\n\t\t// \t\t\t\tconst [name, prob] = item.split(':');\n\t\t// \t\t\t\treturn { item: name.trim(), prob: prob ? parseFloat(prob) : undefined };\n\t\t// \t\t\t});\n\t\t// \t\t}\n\t\t// \t\t// 事件概率字段兼容\n\t\t// \t\tif (m.事件概率 && typeof m.事件概率 === 'string') {\n\t\t// \t\t\tm.事件概率 = m.事件概率.split(',').map(item => {\n\t\t// \t\t\t\tconst [name, weight] = item.split(':');\n\t\t// \t\t\t\treturn { 事件: name.trim(), 权重: weight ? parseFloat(weight) : undefined };\n\t\t// \t\t\t});\n\t\t// \t\t}\n\t\t// \t\tif (m.event_weights && typeof m.event_weights === 'string') {\n\t\t// \t\t\tm.event_weights = m.event_weights.split(',').map(item => {\n\t\t// \t\t\t\tconst [name, weight] = item.split(':');\n\t\t// \t\t\t\treturn { event: name.trim(), weight: weight ? parseFloat(weight) : undefined };\n\t\t// \t\t\t});\n\t\t// \t\t}\n\t\t// \t});\n\t\t// \tthis.mapList = maps;\n\t\t// \tconsole.log('[调试] 解析后mapList:', this.mapList);\n\t\t// \tconsole.log('[调试] player.current_map:', this.player.current_map);\n\t\t// \t// 自动同步当前地图\n\t\t// \tif (this.player && this.player.current_map) {\n\t\t// \t\tthis.currentMap = this.mapList.find(m => m.id === this.player.current_map || m.map_id === this.player.current_map) || null;\n\t\t// \t\tconsole.log('[调试] currentMap:', this.currentMap);\n\t\t// \t}\n\t\t// },\n\t\tcanEnterMap(map) {\n\t\t\tconst player = gameState.player || {};\n\t\t\t// 兼容结构化进入要求\n\t\t\tconst req = map.进入要求 || map.enter_requirements || {};\n\t\t\tif (req.item) {\n\t\t\t\tconst inv = (player.inventory || []);\n\t\t\t\tif (!inv.some(i => i.name === req.item)) return false;\n\t\t\t}\n\t\t\tif (req.attack) {\n\t\t\t\tif ((player.attack || 0) < req.attack) return false;\n\t\t\t}\n\t\t\t// 其他条件可扩展\n\t\t\treturn true;\n\t\t},\n\t\tasync selectMap(map) {\n\t\t\tif (!map || !map.id) {\n\t\t\t\tconsole.error('切换地图失败：map 或 map.id 无效', map);\n\t\t\t\tuni.showToast({ title: '地图ID无效', icon: 'none' });\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tconsole.log('切换地图ID:', map.id, map);\n\t\t\tthis.currentMap = map;\n\t\t\tif (!this.canEnterMap(map)) {\n\t\t\t\tuni.showToast({ title: '不满足进入条件', icon: 'none' });\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\t// 显示加载提示\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '正在切换地图...',\n\t\t\t\t\tmask: true\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 使用gameUtils.sendMessage发送请求并处理响应\n\t\t\t\tconst response = await gameUtils.sendMessage({\n\t\t\t\t\ttype: 'select_map',\n\t\t\t\t\tdata: { map_id: map.id }\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\t// 隐藏加载提示\n\t\t\t\tuni.hideLoading();\n\t\t\t\t\n\t\t\t\tif (response.type === 'select_map_success') {\n\t\t\t\t\tconsole.log('[调试] 切换地图成功:', response.data);\n\t\t\t\t\tthis.currentMap = this.mapList.find(m => m.id === response.data.map_id);\n\t\t\t\t\tif (gameState.player) {\n\t\t\t\t\t\tgameState.player.current_map = response.data.map_id;\n\t\t\t\t\t\tgameState.notifyUpdate('player');\n\t\t\t\t\t}\n\t\t\t\t\t// 切换地图后主动刷新玩家数据\n\t\t\t\t\tthis.refreshPlayerData();\n\t\t\t\t\tthis.showMapPopup = false;\n\t\t\t\t\tuni.showToast({ title: '切换成功', icon: 'success' });\n\t\t\t\t\tconsole.log('[调试] 切换后currentMap:', this.currentMap);\n\t\t\t\t} else if (response.type === 'error') {\n\t\t\t\t\tconsole.error('切换地图失败:', response.data);\n\t\t\t\t\tif (response.data && response.data.message) {\n\t\t\t\t\t\tuni.showToast({ title: response.data.message, icon: 'none' });\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tuni.hideLoading();\n\t\t\t\tconsole.error('切换地图失败:', error);\n\t\t\t\tuni.showToast({ \n\t\t\t\t\ttitle: '切换地图失败: ' + (error.message || '未知错误'), \n\t\t\t\t\ticon: 'none' \n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 移除onSelectMapSuccess和onMapError方法，因为我们直接在selectMap中处理响应\n\t\t\n\t\tasync fetchMapNpcs() {\n\t\t\tconsole.log('【调试】fetchMapNpcs方法被调用');\n\t\t\ttry {\n\t\t\t\tconst mapId = gameState.player?.current_map || (gameState.player && gameState.player.current_map);\n\t\t\t\tif (!mapId) {\n\t\t\t\t\tconsole.log('【调试】未获取到当前地图ID，跳过NPC获取');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('【调试】开始请求地图NPC，地图ID:', mapId);\n\t\t\t\tconst response = await gameUtils.sendMessage({ \n\t\t\t\t\ttype: 'get_map_npcs',\n\t\t\t\t\tdata: { map_id: mapId }\n\t\t\t\t});\n\t\t\t\t\n\t\t\t\tconsole.log('【调试】fetchMapNpcs收到response:', response);\n\t\t\t\t\n\t\t\t\t// 处理超时情况\n\t\t\t\tif (response.type === 'get_map_npcs_timeout') {\n\t\t\t\t\tconsole.log('【调试】获取地图NPC超时，使用默认NPC数据');\n\t\t\t\t\t// 使用地图配置中的默认NPC数据\n\t\t\t\t\tif (this.mapsConfig && this.mapsConfig[mapId]) {\n\t\t\t\t\t\tconst mapConfig = this.mapsConfig[mapId];\n\t\t\t\t\t\tif (mapConfig.NPC && Array.isArray(mapConfig.NPC)) {\n\t\t\t\t\t\t\tthis.mapNpcs = mapConfig.NPC.map((npc, idx) => {\n\t\t\t\t\t\t\t\t// 如果NPC已经是对象格式，直接使用\n\t\t\t\t\t\t\t\tif (typeof npc === 'object') {\n\t\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\t\tid: npc.id || `npc_${idx}`,\n\t\t\t\t\t\t\t\t\t\tname: npc.名称 || npc.name || '未知NPC',\n\t\t\t\t\t\t\t\t\t\tavatar: 'static/npc/default.png',\n\t\t\t\t\t\t\t\t\t\tdesc: npc.描述 || npc.desc || `${npc.名称 || npc.name || '未知NPC'}：一位神秘的江湖人物。`,\n\t\t\t\t\t\t\t\t\t\tfunctions: [\n\t\t\t\t\t\t\t\t\t\t\t{ key: 'talk', label: '对话' },\n\t\t\t\t\t\t\t\t\t\t\t{ key: 'shop', label: '交易' }\n\t\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t// 如果NPC是字符串格式，转换为对象\n\t\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\t\tid: `npc_${idx}`,\n\t\t\t\t\t\t\t\t\tname: npc,\n\t\t\t\t\t\t\t\t\tavatar: 'static/npc/default.png',\n\t\t\t\t\t\t\t\t\tdesc: `${npc}：一位神秘的江湖人物。`,\n\t\t\t\t\t\t\t\t\tfunctions: [\n\t\t\t\t\t\t\t\t\t\t{ key: 'talk', label: '对话' },\n\t\t\t\t\t\t\t\t\t\t{ key: 'shop', label: '交易' }\n\t\t\t\t\t\t\t\t\t]\n\t\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tlet npcs = [];\n\t\t\t\tif (Array.isArray(response.data)) {\n\t\t\t\t\tnpcs = response.data;\n\t\t\t\t} else if (response.data && Array.isArray(response.data.npcs)) {\n\t\t\t\t\tnpcs = response.data.npcs;\n\t\t\t\t} else if (response.data && response.data.data && Array.isArray(response.data.data)) {\n\t\t\t\t\tnpcs = response.data.data;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tconsole.log('【调试】解析出的npcs:', npcs);\n\t\t\t\tif (npcs.length > 0) {\n\t\t\t\t\tthis.mapNpcs = npcs;\n\t\t\t\t\tconsole.log('【调试】mapNpcs赋值后:', this.mapNpcs);\n\t\t\t\t} else {\n\t\t\t\t\t// 使用默认NPC数据\n\t\t\t\t\tconsole.log('【调试】未获取到NPC数据，使用默认数据');\n\t\t\t\t\tthis.mapNpcs = [];\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\tconsole.error('【调试】fetchMapNpcs异常', e);\n\t\t\t\tif (typeof e === 'object') {\n\t\t\t\t\tfor (const key in e) {\n\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(e, key)) {\n\t\t\t\t\t\t\tconsole.log('【调试】异常属性', key, e[key]);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t// 出错时使用空数组\n\t\t\t\tthis.mapNpcs = [];\n\t\t\t\t\n\t\t\t\t// 显示错误提示\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '获取NPC数据失败',\n\t\t\t\t\ticon: 'none',\n\t\t\t\t\tduration: 2000\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\tshowNpcMenu(npc) {\n\t\t\tthis.selectedNpc = npc\n\t\t\tthis.showNpcMenuModal = true\n\t\t},\n\t\tcloseNpcMenu() {\n\t\t\tthis.showNpcMenuModal = false\n\t\t\tthis.selectedNpc = {}\n\t\t},\n\t\tonNpcFunction(func, npc) {\n\t\t\t// 这里只弹窗提示，后续可扩展具体功能\n\t\t\tuni.showToast({ title: `功能：${func.label}`, icon: 'none' })\n\t\t\tthis.closeNpcMenu()\n\t\t},\n\t\tdebugSendMapList() {\n\t\t\tconsole.log('【调试】手动触发 get_map_list');\n\t\t\t// wsManager.sendMessage('get_map_list'); // 移除对 fetchMapList 的依赖\n\t\t},\n\t\tasync loadMapsConfig() {\n\t\t\tconsole.log('[首页] 开始加载地图配置...');\n\t\t\tthis.mapsConfig = await gameState.getMapsConfig();\n\t\t\tconsole.log('[首页] 地图配置加载完成:', this.mapsConfig);\n\t\t},\n\t\tasync refreshPlayerData() {\n\t\t\t// 假设有 gameState.requestAllData 方法\n\t\t\tif (gameState.requestAllData) {\n\t\t\t\tawait gameState.requestAllData();\n\t\t\t} else {\n\t\t\t\t// 兼容直接请求 get_player_data\n\t\t\t\tawait gameUtils.sendMessage({ type: 'get_player_data' });\n\t\t\t}\n\t\t\tthis.updateData();\n\t\t},\n\t\t// 可在需要时通过 this.mapsConfig[mapId] 获取地图详情\n\t\ttestLog() {\n\t\t\tconsole.log('【调试】this.eventLog:', this.eventLog);\n\t\t\tconsole.log('【调试】gameState.eventLog:', gameState.eventLog);\n\t\t},\n\t\ttypeWriterEffect(log, idx) {\n\t\t\t// 只对最新一条日志做打字机动画\n\t\t\tlog.displayText = '';\n\t\t\tconst fullText = log.description;\n\t\t\t\n\t\t\t// 将文本分割成普通字符和HTML标签\n\t\t\tconst segments = [];\n\t\t\tlet currentSegment = '';\n\t\t\tlet inTag = false;\n\t\t\t\n\t\t\tfor (let i = 0; i < fullText.length; i++) {\n\t\t\t\tconst char = fullText[i];\n\t\t\t\tif (char === '<') {\n\t\t\t\t\tif (currentSegment) {\n\t\t\t\t\t\tsegments.push({ type: 'text', content: currentSegment });\n\t\t\t\t\t\tcurrentSegment = '';\n\t\t\t\t\t}\n\t\t\t\t\tinTag = true;\n\t\t\t\t\tcurrentSegment = char;\n\t\t\t\t} else if (char === '>') {\n\t\t\t\t\tcurrentSegment += char;\n\t\t\t\t\tsegments.push({ type: 'tag', content: currentSegment });\n\t\t\t\t\tcurrentSegment = '';\n\t\t\t\t\tinTag = false;\n\t\t\t\t} else {\n\t\t\t\t\tcurrentSegment += char;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t// 处理最后一段\n\t\t\tif (currentSegment) {\n\t\t\t\tsegments.push({ type: inTag ? 'tag' : 'text', content: currentSegment });\n\t\t\t}\n\t\t\t\n\t\t\tlet segmentIndex = 0;\n\t\t\tlet charIndex = 0;\n\t\t\t\n\t\t\tconst printNext = () => {\n\t\t\t\tif (segmentIndex < segments.length) {\n\t\t\t\t\tconst segment = segments[segmentIndex];\n\t\t\t\t\t\n\t\t\t\t\tif (segment.type === 'tag') {\n\t\t\t\t\t\t// HTML标签一次性添加\n\t\t\t\t\t\tlog.displayText += segment.content;\n\t\t\t\t\t\tsegmentIndex++;\n\t\t\t\t\t\tcharIndex = 0;\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 普通文本逐字符添加\n\t\t\t\t\t\tif (charIndex < segment.content.length) {\n\t\t\t\t\t\t\tlog.displayText += segment.content[charIndex];\n\t\t\t\t\t\t\tcharIndex++;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tsegmentIndex++;\n\t\t\t\t\t\t\tcharIndex = 0;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.$set(this.eventLog, idx, { ...log });\n\t\t\t\t\tsetTimeout(printNext, 30);\n\t\t\t\t} else {\n\t\t\t\t\tlog.displayText = fullText;\n\t\t\t\t\tthis.$set(this.eventLog, idx, { ...log });\n\t\t\t\t}\n\t\t\t};\n\t\t\t\n\t\t\tprintNext();\n\t\t},\n\t\thandleEncounterMonster(data) {\n\t\t\t// 怪物数据、attack_mode\n\t\t\tconst monster = data.monster || {};\n\t\t\tthis.battleMonster = { ...monster };\n\t\t\tthis.battlePlayer = { ...this.player };\n\t\t\tthis.battleLog = [];\n\t\t\tthis.battleStage = 'encounter';\n\t\t\tthis.battleAttackMode = monster.attack_mode || 'passive';\n\t\t\tthis.showBattlePopup = true;\n\t\t\t\n\t\t\t// 添加遇怪提示到江湖日志\n\t\t\tlet encounterDescription = '';\n\t\t\tif (monster.attack_mode === 'active') {\n\t\t\t\tencounterDescription = `${monster.name || '未知怪物'}向你大喊一声扑了上来！`;\n\t\t\t} else {\n\t\t\t\tencounterDescription = `你遇到了${monster.name || '未知怪物'}，你要怎么办呢？`;\n\t\t\t}\n\t\t\t\n\t\t\tconst encounterLog = {\n\t\t\t\ttimestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),\n\t\t\t\tname: '遭遇战斗',\n\t\t\t\tdescription: encounterDescription\n\t\t\t};\n\t\t\tgameState.eventLog.unshift(encounterLog);\n\t\t\t// 最多保留50条\n\t\t\tif (gameState.eventLog.length > 50) gameState.eventLog = gameState.eventLog.slice(0, 50);\n\t\t\tgameState.notifyUpdate('eventLog');\n\t\t\t\n\t\t\t// 如果是主动攻击型怪物，自动开始战斗\n\t\t\tif (monster.attack_mode === 'active') {\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tgameUtils.sendMessage({ type: 'start_battle_from_encounter', data: { monster_id: this.battleMonster.id } });\n\t\t\t\t\tthis.battleStage = 'battle';\n\t\t\t\t}, 1000); // 延迟1秒开始战斗，让玩家看到弹窗\n\t\t\t}\n\t\t},\n\t\t\thandleBattleAttack() {\n\t\t// 仅被动怪物遇怪阶段才需要发送攻击\n\t\tif (this.battleStage === 'encounter' && this.battleAttackMode === 'passive') {\n\t\t\tgameUtils.sendMessage({ type: 'start_battle_from_encounter', data: { monster_id: this.battleMonster.id } });\n\t\t\tthis.battleStage = 'battle';\n\t\t}\n\t},\n\t\thandleBattleEscape() {\n\t\t\t// 防止重复发送逃跑请求\n\t\t\tif (this.escapeRequested) {\n\t\t\t\tconsole.log('[首页] 逃跑请求已发送，跳过重复请求');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 标记已发送逃跑请求\n\t\t\tthis.escapeRequested = true;\n\t\t\t\n\t\t\t// 发送逃跑请求到后端\n\t\t\tgameUtils.sendMessage({ type: 'escape_battle', data: { monster_id: this.battleMonster.id } })\n\t\t\t\t.then(response => {\n\t\t\t\t\tconsole.log('[首页] 逃跑请求响应:', response);\n\t\t\t\t\t// 重置标记，允许下次逃跑\n\t\t\t\t\tthis.escapeRequested = false;\n\t\t\t\t})\n\t\t\t\t.catch(error => {\n\t\t\t\t\tconsole.error('[首页] 逃跑请求失败:', error);\n\t\t\t\t\t// 重置标记，允许重试\n\t\t\t\t\tthis.escapeRequested = false;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '逃跑请求失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t},\n\t\thandleBattleClose() {\n\t\t\tthis.showBattlePopup = false;\n\t\t\tthis.battleLog = [];\n\t\t\tthis.battleStage = '';\n\t\t\tthis.battleAttackMode = '';\n\t\t\tthis.battlePlayer = {};\n\t\t\tthis.battleMonster = {};\n\t\t\t// 重置处理标记\n\t\t\tthis.escapeProcessed = false;\n\t\t\tthis.battleResultProcessed = false;\n\t\t\tthis.escapeRequested = false;\n\t\t},\n\t\thandleBattleRound(data) {\n\t\t\t// 追加回合描述，实时同步属性\n\t\t\t// console.log('[首页] 收到战斗回合数据:', data);\n\t\t\t// console.log('[首页] 当前战斗日志数量:', this.battleLog ? this.battleLog.length : 0);\n\t\t\tconst roundData = {\n\t\t\t\tround: data.round || 0,\n\t\t\t\tattacker: data.attacker || '',\n\t\t\t\tdefender: data.defender || '',\n\t\t\t\tdamage: data.damage || 0,\n\t\t\t\tdesc: data.desc || '',\n\t\t\t\tmove: data.move || '',\n\t\t\t\tmartial: data.martial || '',\n\t\t\t\tplayer_hp: data.player_hp || 0,\n\t\t\t\tenemy_hp: data.enemy_hp || 0,\n\t\t\t\tspecial_effect: data.special_effect || null,\n\t\t\t\teffect_desc: data.effect_desc || '',\n\t\t\t\ttimestamp: new Date().toLocaleTimeString('zh-CN', { \n\t\t\t\t\thour12: false, \n\t\t\t\t\thour: '2-digit', \n\t\t\t\t\tminute: '2-digit', \n\t\t\t\t\tsecond: '2-digit' \n\t\t\t\t})\n\t\t\t};\n\t\t\tthis.battleLog.push(roundData);\n\t\t\t// console.log('[首页] 添加后战斗日志数量:', this.battleLog.length);\n\t\t\t// console.log('[首页] 最新日志内容:', roundData);\n\t\t\t// console.log('[首页] 日志描述:', roundData.desc);\n\t\t\t// 更新属性\n\t\t\tif (typeof data.player_hp !== 'undefined') this.battlePlayer.hp = data.player_hp;\n\t\t\tif (typeof data.enemy_hp !== 'undefined') this.battleMonster.hp = data.enemy_hp;\n\t\t\tif (typeof data.enemy_max_hp !== 'undefined') this.battleMonster.max_hp = data.enemy_max_hp;\n\t\t},\n\t\thandleBattleResult(data) {\n\t\t\t// 防止重复处理\n\t\t\tif (this.battleResultProcessed) {\n\t\t\t\tconsole.log('[首页] 战斗结果已处理，跳过重复处理');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 检查是否已经逃离战斗\n\t\t\tif (this.escapeProcessed) {\n\t\t\t\tconsole.log('[首页] 已经逃离战斗，跳过战斗结果处理');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 标记已处理\n\t\t\tthis.battleResultProcessed = true;\n\t\t\t\n\t\t\t// 推送最终战斗结果\n\t\t\tthis.battleStage = 'end';\n\t\t\t\n\t\t\t// 添加战斗结果到江湖日志\n\t\t\tconst battleResultLog = {\n\t\t\t\ttimestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),\n\t\t\t\tname: '战斗结束',\n\t\t\t\tdescription: data.win ? \n\t\t\t\t\t`你击败了${this.battleMonster.name || '怪物'}，战斗胜利！` : \n\t\t\t\t\t`你被${this.battleMonster.name || '怪物'}击败了，战斗失败！`\n\t\t\t};\n\t\t\tgameState.eventLog.unshift(battleResultLog);\n\t\t\t// 最多保留50条\n\t\t\tif (gameState.eventLog.length > 50) gameState.eventLog = gameState.eventLog.slice(0, 50);\n\t\t\tgameState.notifyUpdate('eventLog');\n\t\t\t\n\t\t\t// 处理战斗奖励\n\t\t\tif (data.win && data.rewards) {\n\t\t\t\tconst rewards = data.rewards;\n\t\t\t\tlet rewardText = '战斗胜利！获得奖励：\\n';\n\t\t\t\t\n\t\t\t\t// 显示历练值奖励\n\t\t\t\tif (rewards['历练值']) {\n\t\t\t\t\trewardText += `历练值 +${rewards['历练值']}\\n`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示银两奖励\n\t\t\t\tif (rewards['银两']) {\n\t\t\t\t\trewardText += `银两 +${rewards['银两']}\\n`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示物品奖励\n\t\t\t\tif (rewards['物品'] && rewards['物品'].length > 0) {\n\t\t\t\t\trewardText += '物品：\\n';\n\t\t\t\t\trewards['物品'].forEach(item => {\n\t\t\t\t\t\trewardText += `  ${item.id} x${item.quantity}\\n`;\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示武学点奖励\n\t\t\t\tif (rewards['武学点']) {\n\t\t\t\t\trewardText += `武学点 +${rewards['武学点']}\\n`;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 显示奖励弹窗\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '战斗胜利',\n\t\t\t\t\t\tcontent: rewardText,\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '确定'\n\t\t\t\t\t});\n\t\t\t\t}, 1000);\n\t\t\t} else if (!data.win) {\n\t\t\t\t// 战斗失败\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\ttitle: '战斗失败',\n\t\t\t\t\t\tcontent: '你被击败了，受了重伤。',\n\t\t\t\t\t\tshowCancel: false,\n\t\t\t\t\t\tconfirmText: '确定'\n\t\t\t\t\t});\n\t\t\t\t}, 1000);\n\t\t\t}\n\t\t},\n\t\thandleBattlePlayerData(data) {\n\t\t\tconsole.log('[首页] 收到player_data更新:', data);\n\t\t\tconsole.log('[首页] 更新前player.hp:', this.player.hp);\n\t\t\t\n\t\t\t// 战斗中同步玩家属性\n\t\t\tif (this.showBattlePopup && this.battleStage === 'battle') {\n\t\t\t\tthis.battlePlayer = { ...this.battlePlayer, ...data };\n\t\t\t}\n\t\t\t// 同步主界面属性\n\t\t\tthis.player = { ...this.player, ...data };\n\t\t\t\n\t\t\tconsole.log('[首页] 更新后player.hp:', this.player.hp);\n\t\t\tconsole.log('[首页] 更新后player.base_max_hp:', this.player.base_max_hp);\n\t\t\t\n\t\t\t// 强制触发Vue更新\n\t\t\tthis.$forceUpdate();\n\t\t},\n\t\thandleEscapeBattleResult(data) {\n\t\t\t// 防止重复处理\n\t\t\tif (this.escapeProcessed) {\n\t\t\t\tconsole.log('[首页] 逃跑结果已处理，跳过重复处理');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (data.success) {\n\t\t\t\t// 标记已处理\n\t\t\t\tthis.escapeProcessed = true;\n\t\t\t\t\n\t\t\t\t// 逃跑成功，添加到战斗日志\n\t\t\t\tconst escapeLog = {\n\t\t\t\t\tround: this.battleLog.length + 1,\n\t\t\t\t\tattacker: '系统',\n\t\t\t\t\tdefender: '玩家',\n\t\t\t\t\tdamage: 0,\n\t\t\t\t\tdesc: data.message || '你成功逃离了战斗！',\n\t\t\t\t\tmove: '逃跑',\n\t\t\t\t\tmartial: '',\n\t\t\t\t\tplayer_hp: this.battlePlayer.hp,\n\t\t\t\t\tenemy_hp: this.battleMonster.hp,\n\t\t\t\t\tspecial_effect: null,\n\t\t\t\t\teffect_desc: '',\n\t\t\t\t\ttimestamp: new Date().toLocaleTimeString('zh-CN', { \n\t\t\t\t\t\thour12: false, \n\t\t\t\t\t\thour: '2-digit', \n\t\t\t\t\t\tminute: '2-digit', \n\t\t\t\t\t\tsecond: '2-digit' \n\t\t\t\t\t})\n\t\t\t\t};\n\t\t\t\tthis.battleLog.push(escapeLog);\n\t\t\t\t\n\t\t\t\t// 添加逃跑成功到江湖日志\n\t\t\t\tconst escapeResultLog = {\n\t\t\t\t\ttimestamp: new Date().toLocaleTimeString('zh-CN', { hour12: false }),\n\t\t\t\t\tname: '战斗结束',\n\t\t\t\t\tdescription: `你成功从${this.battleMonster.name || '怪物'}的追击中逃脱，逃之夭夭！`\n\t\t\t\t};\n\t\t\t\tgameState.eventLog.unshift(escapeResultLog);\n\t\t\t\t// 最多保留50条\n\t\t\t\tif (gameState.eventLog.length > 50) gameState.eventLog = gameState.eventLog.slice(0, 50);\n\t\t\t\tgameState.notifyUpdate('eventLog');\n\t\t\t\t\n\t\t\t\t// 延迟关闭战斗弹窗\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.showBattlePopup = false;\n\t\t\t\t\t// 重置标记\n\t\t\t\t\tthis.escapeProcessed = false;\n\t\t\t\t}, 2000);\n\t\t\t} else {\n\t\t\t\t// 逃跑失败，添加到战斗日志\n\t\t\t\tconst escapeLog = {\n\t\t\t\t\tround: this.battleLog.length + 1,\n\t\t\t\t\tattacker: '系统',\n\t\t\t\t\tdefender: '玩家',\n\t\t\t\t\tdamage: 0,\n\t\t\t\t\tdesc: data.message || '你试图逃跑，但被怪物拦住了！',\n\t\t\t\t\tmove: '逃跑',\n\t\t\t\t\tmartial: '',\n\t\t\t\t\tplayer_hp: this.battlePlayer.hp,\n\t\t\t\t\tenemy_hp: this.battleMonster.hp,\n\t\t\t\t\tspecial_effect: null,\n\t\t\t\t\teffect_desc: '',\n\t\t\t\t\ttimestamp: new Date().toLocaleTimeString('zh-CN', { \n\t\t\t\t\t\thour12: false, \n\t\t\t\t\t\thour: '2-digit', \n\t\t\t\t\t\tminute: '2-digit', \n\t\t\t\t\t\tsecond: '2-digit' \n\t\t\t\t\t})\n\t\t\t\t};\n\t\t\t\tthis.battleLog.push(escapeLog);\n\t\t\t}\n\t\t},\n\t\tasync handleHealing() {\n\t\t\ttry {\n\t\t\t\tthis.healingMeditationMessages = [];\n\t\t\t\tthis.healingMeditationLoading = true;\n\t\t\t\tuni.showLoading({ title: '正在疗伤...' });\n\t\t\t\twsManager.sendMessage('healing', {});\n\t\t\t} catch (error) {\n\t\t\t\tthis.healingMeditationLoading = false;\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({ title: '疗伤失败: ' + error.message, icon: 'none' });\n\t\t\t}\n\t\t},\n\t\tasync handleMeditation() {\n\t\t\ttry {\n\t\t\t\tthis.healingMeditationMessages = [];\n\t\t\t\tthis.healingMeditationLoading = true;\n\t\t\t\tuni.showLoading({ title: '正在打坐...' });\n\t\t\t\twsManager.sendMessage('meditation', {});\n\t\t\t} catch (error) {\n\t\t\t\tthis.healingMeditationLoading = false;\n\t\t\t\tuni.hideLoading();\n\t\t\t\tuni.showToast({ title: '打坐失败: ' + error.message, icon: 'none' });\n\t\t\t}\n\t\t},\n\t\thandleHealingLog(data) {\n\t\t\tif (data && data.message) {\n\t\t\t\tthis.healingMeditationMessages.push(data.message);\n\t\t\t\tif (this.healingMeditationMessages.length > 10) {\n\t\t\t\t\tthis.healingMeditationMessages = this.healingMeditationMessages.slice(-10);\n\t\t\t\t}\n\t\t\t\tthis.healingMeditationLoading = !/疗伤结束|收功|耗尽/.test(data.message);\n\t\t\t}\n\t\t},\n\t\thandleMeditationLog(data) {\n\t\t\tif (data && data.message) {\n\t\t\t\tthis.healingMeditationMessages.push(data.message);\n\t\t\t\tif (this.healingMeditationMessages.length > 10) {\n\t\t\t\t\tthis.healingMeditationMessages = this.healingMeditationMessages.slice(-10);\n\t\t\t\t}\n\t\t\t\tthis.healingMeditationLoading = !/打坐结束|收功|耗尽/.test(data.message);\n\t\t\t}\n\t\t},\n\t},\n\tmounted() {\n\t\t// 页面挂载后自动请求地图配置\n\t\tgameState.requestMapsConfig().then(config => {\n\t\t\tthis.mapsConfig = config;\n\t\t\tconsole.log('[首页] 地图配置已加载:', config);\n\t\t});\n\t\twsManager.on('escape_battle_result', this.handleEscapeBattleResult);\n\t\twsManager.on('healing_log', this.handleHealingLog);\n\t\twsManager.on('meditation_log', this.handleMeditationLog);\n\t}\n}\n</script>\n\n<style scoped>\n.container {\n\tpadding: 15rpx;\n\tbackground: linear-gradient(135deg, #f5f7fa, #c3cfe2);\n\tmin-height: 100vh;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\tgap: 10rpx;\n}\n\n.top-info-bar {\n\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\tflex-wrap: wrap;\n\tpadding: 8rpx 0 0 0;\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tbackground: transparent;\n\tmargin-bottom: 8rpx;\n\tline-height: 1.7;\n}\n.player-name {\n\tfont-weight: bold;\n\tfont-size: 32rpx;\n\tcolor: #4b3fa7;\n\tmargin-right: 4rpx;\n}\n.player-level, .player-exp, .conn-text {\n\tfont-size: 26rpx;\n\tcolor: #333;\n\tfont-weight: 500;\n}\n.info-sep {\n\tmargin: 0 8rpx;\n\tcolor: #bbb;\n\tfont-size: 22rpx;\n}\n.conn-dot {\n\tdisplay: inline-block;\n\twidth: 14rpx;\n\theight: 14rpx;\n\tborder-radius: 50%;\n\tmargin-right: 4rpx;\n\tbackground: #bbb;\n}\n.conn-dot.connected { background: #27ae60; }\n.conn-dot.disconnected, .conn-dot.failed { background: #e74c3c; }\n.player-attr {\n\tfont-size: 22rpx;\n\tcolor: #666;\n\tmargin: 0 2rpx;\n}\n.attr-sep {\n\tcolor: #ccc;\n\tfont-size: 20rpx;\n\tmargin: 0 2rpx;\n}\n\n.character-card {\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));\n\tborder-radius: 20rpx;\n\tpadding: 15rpx;\n\tmargin-bottom: 10rpx;\n\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);\n\tborder: 1px solid rgba(102, 126, 234, 0.1);\n}\n\n.character-header {\n\tdisplay: none;\n}\n\n.progress-bar {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.progress-label {\n\twidth: 80rpx;\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n.progress-bg {\n\tflex: 1;\n\theight: 20rpx;\n\tbackground: #f0f0f0;\n\tborder-radius: 10rpx;\n\tmargin: 0 20rpx;\n\toverflow: hidden;\n}\n\n.progress-fill {\n\theight: 100%;\n\tborder-radius: 10rpx;\n\ttransition: width 0.3s ease;\n}\n\n.hp-fill {\n\tbackground: linear-gradient(90deg, #ff6b6b, #ee5a52);\n}\n\n.mp-fill {\n\tbackground: linear-gradient(90deg, #4ecdc4, #44a08d);\n}\n\n.stamina-fill {\n\tbackground: linear-gradient(90deg, #ffe66d, #f7b731);\n}\n\n.energy-fill {\n\tbackground: linear-gradient(90deg, #a8edea, #fed6e3);\n}\n\n.progress-text {\n\twidth: 120rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n\ttext-align: right;\n}\n\n.status-display {\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));\n\tborder-radius: 20rpx;\n\tpadding: 15rpx;\n\tmargin-bottom: 10rpx;\n\ttext-align: center;\n\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);\n\tborder: 1px solid rgba(102, 126, 234, 0.1);\n}\n\n.status-text {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n}\n\n.status-injured {\n\tcolor: #e74c3c;\n}\n\n.status-internal_injury {\n\tcolor: #9b59b6;\n}\n\n.status-poisoned {\n\tcolor: #27ae60;\n}\n\n.status-tired {\n\tcolor: #95a5a6;\n}\n\n.event-log {\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));\n\tborder-radius: 20rpx;\n\tpadding: 20rpx;\n\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);\n\tborder: 1px solid rgba(102, 126, 234, 0.1);\n\tflex: 1;\n\tmargin-bottom: calc(5rpx + env(safe-area-inset-bottom));\n\theight: calc(100vh - 350rpx - env(safe-area-inset-bottom));\n\tmin-height: 700rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.log-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.log-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #667eea;\n}\n\n.log-clear {\n\tfont-size: 28rpx;\n\tcolor: #764ba2;\n\tfont-weight: 500;\n}\n\n.log-content {\n\theight: calc(100% - 60rpx);\n\tmax-height: none;\n\tflex: 1;\n\toverflow-y: auto;\n\t-webkit-overflow-scrolling: touch;\n}\n\n.log-item {\n\tpadding: 15rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n\ttransition: background-color 0.2s ease;\n}\n\n.log-item:hover {\n\tbackground-color: rgba(102, 126, 234, 0.05);\n}\n\n.log-item:last-child {\n\tborder-bottom: none;\n}\n\n.log-header-line {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 8rpx;\n}\n\n.log-content-line {\n\tmargin-left: 0;\n}\n\n.log-time {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tmargin-right: 15rpx;\n}\n\n.log-event {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.log-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tline-height: 1.5;\n}\n\n.log-empty {\n\ttext-align: center;\n\tpadding: 100rpx 0;\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n\n.map-bar {\n\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\tpadding: 12px 15px;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tborder-radius: 0 0 15px 15px;\n\tbox-shadow: 0 4px 15px rgba(0,0,0,0.2);\n\tmargin-bottom: 5px;\n\twhite-space: nowrap;\n\toverflow: hidden;\n}\n\n.map-bar-left {\n\tdisplay: flex;\n\talign-items: center;\n\tflex: 1;\n\tmin-width: 0;\n\tgap: 6px;\n}\n\n.map-bar-right {\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8px;\n\tflex-shrink: 0;\n}\n\n.user-name {\n\tcolor: #ffffff;\n\tfont-weight: bold;\n\tfont-size: 14px;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tmax-width: 150px;\n\tflex-shrink: 0;\n}\n\n.user-label {\n\tcolor: #e8e8e8;\n\tmargin: 0 6px;\n\tfont-size: 12px;\n}\n\n.map-label {\n\tcolor: #e8e8e8;\n\tfont-size: 12px;\n}\n\n.map-name {\n\tcolor: #ffd700;\n\tfont-weight: bold;\n\tmargin: 0 6px;\n\tfont-size: 14px;\n\twhite-space: nowrap;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\tmax-width: 80px;\n\tflex-shrink: 0;\n}\n\n.map-btn {\n\tfont-size: 12px;\n\tpadding: 4px 12px;\n\tborder-radius: 15px;\n\tbackground: rgba(255, 255, 255, 0.2);\n\tcolor: #ffffff;\n\tborder: 1px solid rgba(255, 255, 255, 0.3);\n\tbackdrop-filter: blur(10px);\n\twhite-space: nowrap;\n}\n\n.connection-status {\n\tfont-size: 12px;\n\tfont-weight: 500;\n\tpadding: 3px 8px;\n\tborder-radius: 12px;\n\tbackground: rgba(255, 255, 255, 0.1);\n\twhite-space: nowrap;\n}\n\n.connection-status.connected {\n\tcolor: #4ade80;\n\tbackground: rgba(74, 222, 128, 0.1);\n\tborder: 1px solid rgba(74, 222, 128, 0.3);\n}\n\n.connection-status.disconnected {\n\tcolor: #f87171;\n\tbackground: rgba(248, 113, 113, 0.1);\n\tborder: 1px solid rgba(248, 113, 113, 0.3);\n}\n\n.map-popup-mask {\n\tposition: fixed; left: 0; top: 0; right: 0; bottom: 0;\n\tbackground: rgba(0,0,0,0.25); z-index: 9999; display: flex; align-items: center; justify-content: center;\n}\n.map-popup {\n\tbackground: #fff; border-radius: 18rpx; padding: 32rpx 24rpx; min-width: 540rpx; max-width: 90vw; box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18); display: flex; flex-direction: column; align-items: center;\n}\n.map-popup-title { font-size: 32rpx; font-weight: bold; color: #4b3fa7; margin-bottom: 18rpx; }\n.map-list { max-height: 600rpx; min-width: 480rpx; overflow-y: auto; }\n.map-item { background: #f7f7f7; border-radius: 12rpx; margin-bottom: 16rpx; padding: 18rpx 16rpx; cursor: pointer; transition: background 0.2s; }\n.map-item.active { background: #e6f7ff; border: 2rpx solid #4b3fa7; }\n.map-item.locked { opacity: 0.5; pointer-events: none; }\n.map-item-title { font-size: 28rpx; font-weight: bold; color: #333; margin-bottom: 6rpx; }\n.map-item-desc { font-size: 24rpx; color: #666; margin-bottom: 4rpx; }\n.map-item-npc { font-size: 22rpx; color: #27ae60; margin-bottom: 2rpx; }\n.map-item-monster { font-size: 22rpx; color: #e74c3c; margin-bottom: 2rpx; }\n.map-item-gather { font-size: 22rpx; color: #2980b9; margin-bottom: 2rpx; }\n.map-item-req { font-size: 20rpx; color: #888; }\n.close-btn { background: #eee; color: #888; font-size: 24rpx; border-radius: 24rpx; padding: 12rpx 36rpx; border: none; margin-top: 18rpx; }\n\n.announcement-bar {\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));\n\tborder-radius: 20rpx;\n\tpadding: 15rpx 20rpx;\n\tmargin-bottom: 10rpx;\n\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.1);\n\tborder: 1px solid rgba(102, 126, 234, 0.1);\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 15rpx;\n\theight: 60rpx;\n\toverflow: hidden;\n}\n\n.announcement-icon {\n\tfont-size: 28rpx;\n\tflex-shrink: 0;\n\tanimation: announcement-bounce 1.5s ease-in-out infinite;\n}\n\n.announcement-content {\n\tflex: 1;\n\toverflow: hidden;\n}\n\n.announcement-scroll {\n\t\tdisplay: flex;\n\talign-items: center;\n\theight: 100%;\n\tanimation: announcement-scroll 25s linear infinite;\n\twhite-space: nowrap;\n\tanimation-play-state: running;\n}\n\n.announcement-scroll:hover {\n\tanimation-play-state: paused;\n}\n\n@keyframes announcement-scroll {\n\t0% {\n\t\ttransform: translateX(100%);\n\t}\n\t100% {\n\t\ttransform: translateX(-100%);\n\t}\n}\n\n.announcement-text {\n\tfont-size: 26rpx;\n\tfont-weight: 500;\n\tcolor: #333;\n\twhite-space: nowrap;\n\tmargin-right: 50rpx;\n\t}\n\n.npc-bar {\n\tdisplay: flex;\n\tflex-direction: row;\n\tjustify-content: flex-start;\n\talign-items: center;\n\tmargin: 10px 0 10px 0;\n\tpadding: 8px 0;\n\tbackground: #f8f8f8;\n\tborder-radius: 8px;\n}\n\n.npc-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tmargin-right: 18px;\n\tcursor: pointer;\n}\n\n.npc-avatar {\n\twidth: 48px;\n\theight: 48px;\n\tborder-radius: 50%;\n\tborder: 2px solid #e0e0e0;\n\tmargin-bottom: 4px;\n}\n\n.npc-name {\n\tfont-size: 14px;\n\tcolor: #333;\n}\n\n.npc-menu-modal {\n\tposition: fixed;\n\tleft: 0; top: 0; right: 0; bottom: 0;\n\tbackground: rgba(0,0,0,0.3);\n\tz-index: 1000;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.npc-menu-content {\n\tbackground: #fff;\n\tborder-radius: 12px;\n\tpadding: 24px 20px 16px 20px;\n\tmin-width: 220px;\n\tmax-width: 80vw;\n\tbox-shadow: 0 2px 12px rgba(0,0,0,0.12);\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n}\n\n.npc-menu-header {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tmargin-bottom: 8px;\n}\n\n.npc-menu-avatar {\n\twidth: 60px;\n\theight: 60px;\n\tborder-radius: 50%;\n\tmargin-bottom: 6px;\n}\n\n.npc-menu-name {\n\tfont-size: 18px;\n\tfont-weight: bold;\n\tcolor: #222;\n}\n\n.npc-menu-desc {\n\tfont-size: 14px;\n\tcolor: #666;\n\tmargin-bottom: 12px;\n\ttext-align: center;\n}\n\n.npc-menu-list {\n\twidth: 100%;\n\tmargin-bottom: 10px;\n}\n\n.npc-menu-btn {\n\twidth: 100%;\n\tmargin-bottom: 8px;\n\tbackground: #409eff;\n\tcolor: #fff;\n\tborder: none;\n\tborder-radius: 6px;\n\tpadding: 8px 0;\n\tfont-size: 15px;\n}\n\n.npc-menu-close {\n\tmargin-top: 6px;\n\tbackground: #eee;\n\tcolor: #333;\n\tborder: none;\n\tborder-radius: 6px;\n\tpadding: 7px 0;\n\twidth: 100%;\n\tfont-size: 15px;\n}\n\n/* 天赋增益卡片样式 */\n.talent-bonus-card {\n\tbackground: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));\n\tborder-radius: 16rpx;\n\tpadding: 16rpx;\n\tmargin-bottom: 10rpx;\n\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.1);\n\tborder: 1px solid rgba(102, 126, 234, 0.1);\n}\n\n.talent-bonus-title {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #4b3fa7;\n\tmargin-bottom: 12rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tgap: 8rpx;\n}\n\n.talent-bonus-title::before {\n\tcontent: \"⭐\";\n\tfont-size: 28rpx;\n}\n\n.talent-bonus-grid {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tgap: 8rpx;\n}\n\n.talent-bonus-item {\n\tbackground: linear-gradient(135deg, #e8f4ff, #f0f8ff);\n\tborder-radius: 8rpx;\n\tpadding: 8rpx 12rpx;\n\tborder: 1px solid rgba(102, 126, 234, 0.2);\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tmin-width: 80rpx;\n}\n\n.talent-bonus-label {\n\tfont-size: 22rpx;\n\tfont-weight: bold;\n\tcolor: #4b3fa7;\n\tmargin-bottom: 4rpx;\n}\n\n.talent-bonus-value {\n\tfont-size: 20rpx;\n\tcolor: #27ae60;\n\tfont-weight: 500;\n}\n\n/* 江湖按钮样式 */\n.jianghu-section {\n\tposition: fixed;\n\tbottom: 20rpx;\n\tleft: 20rpx;\n\tright: 20rpx;\n\tz-index: 100;\n}\n\n.jianghu-btn {\n\twidth: 100%;\n\theight: 100rpx;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tborder-radius: 50rpx;\n\tborder: none;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tbox-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);\n\ttransition: all 0.3s ease;\n\tposition: relative;\n\toverflow: hidden;\n}\n\n.jianghu-btn:active {\n\ttransform: translateY(2rpx);\n\tbox-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);\n}\n\n.jianghu-btn:disabled {\n\tbackground: linear-gradient(135deg, #ccc 0%, #999 100%);\n\tbox-shadow: none;\n\topacity: 0.6;\n}\n\n.jianghu-btn-text {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #ffffff;\n\ttext-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);\n}\n\n.jianghu-btn::before {\n\tcontent: '';\n\tposition: absolute;\n\ttop: 0;\n\tleft: -100%;\n\twidth: 100%;\n\theight: 100%;\n\tbackground: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n\ttransition: left 0.5s;\n}\n\n.jianghu-btn:not(:disabled):hover::before {\n\tleft: 100%;\n}\n\n.jianghu-section-flex {\n  position: fixed;\n  bottom: 20rpx;\n  left: 20rpx;\n  right: 20rpx;\n  z-index: 100;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: center;\n  gap: 32rpx;\n}\n.healing-btn {\n  height: 100rpx;\n  min-width: 160rpx;\n  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);\n  border-radius: 50rpx;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 24rpx rgba(67, 206, 162, 0.2);\n  font-size: 32rpx;\n  color: #fff;\n  font-weight: bold;\n  margin-right: 0;\n}\n.healing-btn:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(67, 206, 162, 0.3);\n}\n.healing-btn:disabled {\n  background: linear-gradient(135deg, #ccc 0%, #999 100%);\n  box-shadow: none;\n  opacity: 0.6;\n}\n.healing-btn-text {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #ffffff;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n}\n\n.meditation-btn {\n  height: 100rpx;\n  min-width: 160rpx;\n  background: linear-gradient(135deg, #f7971e 0%, #ffd200 100%);\n  border-radius: 50rpx;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 24rpx rgba(255, 215, 0, 0.15);\n  font-size: 32rpx;\n  color: #fff;\n  font-weight: bold;\n  margin-left: 0;\n}\n.meditation-btn:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(255, 215, 0, 0.25);\n}\n.meditation-btn:disabled {\n  background: linear-gradient(135deg, #ccc 0%, #999 100%);\n  box-shadow: none;\n  opacity: 0.6;\n}\n.meditation-btn-text {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #ffffff;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n}\n\n.jianghu-section-flex-opt {\n  position: fixed;\n  bottom: 20rpx;\n  left: 0;\n  right: 0;\n  z-index: 100;\n  display: flex;\n  flex-direction: row;\n  align-items: flex-end;\n  justify-content: center;\n  gap: 48rpx;\n}\n.side-btn {\n  width: 90rpx;\n  height: 90rpx;\n  background: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);\n  border-radius: 50%;\n  border: none;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 24rpx;\n  box-shadow: 0 4rpx 12rpx rgba(67, 206, 162, 0.15);\n  transition: box-shadow 0.2s, transform 0.2s;\n}\n.side-btn:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 2rpx 6rpx rgba(67, 206, 162, 0.25);\n}\n.side-btn:disabled {\n  background: linear-gradient(135deg, #ccc 0%, #999 100%);\n  box-shadow: none;\n  opacity: 0.6;\n}\n.side-btn-icon {\n  font-size: 36rpx;\n  line-height: 1;\n}\n.side-btn-text {\n  font-size: 20rpx;\n  margin-top: 2rpx;\n  line-height: 1;\n}\n.main-btn {\n  width: 160rpx;\n  height: 90rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 45rpx;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #fff;\n  font-size: 36rpx;\n  font-weight: bold;\n  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);\n  margin: 0 8rpx;\n  transition: box-shadow 0.2s, transform 0.2s;\n}\n.main-btn:active {\n  transform: translateY(2rpx);\n  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);\n}\n.main-btn:disabled {\n  background: linear-gradient(135deg, #ccc 0%, #999 100%);\n  box-shadow: none;\n  opacity: 0.6;\n}\n.main-btn-text {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #ffffff;\n  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);\n}\n\n.healing-meditation-popup-centered {\n  position: fixed;\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  z-index: 9999;\n  min-width: 520rpx;\n  max-width: 90vw;\n  min-height: 80rpx;\n  max-height: 420rpx;\n  background: rgba(255,255,255,0.85);\n  backdrop-filter: blur(12px);\n  border-radius: 32rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);\n  border: 2rpx solid #e0cda2;\n  padding: 36rpx 40rpx 24rpx 40rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: 36rpx;\n  color: #3e2c13;\n  text-align: center;\n  pointer-events: auto;\n  font-family: 'STKaiti', 'KaiTi', 'FZKai-Z03', '楷体', serif;\n  letter-spacing: 1.5rpx;\n  animation: fadeInScale 0.3s;\n}\n.healing-meditation-loading {\n  margin-bottom: 24rpx;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.spinner {\n  width: 64rpx;\n  height: 64rpx;\n  border: 8rpx solid #e0cda2;\n  border-top: 8rpx solid #bfa76a;\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n.healing-meditation-msg-centered {\n  margin-bottom: 12rpx;\n  line-height: 2.1;\n  word-break: break-all;\n  text-shadow: 0 2rpx 8rpx rgba(224,205,162,0.18);\n}\n@keyframes fadeInScale {\n  0% { opacity: 0; transform: scale(0.8) translate(-50%, -50%); }\n  100% { opacity: 1; transform: scale(1) translate(-50%, -50%); }\n}\n</style>\n\n", "import MiniProgramPage from 'D:/zjjhx/仗剑江湖行/pages/index/index.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "gameState", "wsManager", "gameUtils"], "mappings": ";;;;;AAmNA,MAAK,iBAAkB,MAAW;AAClC,MAAK,cAAe,MAAW;AAC/B,qBAAqB,MAAW;AAE/B,MAAK,YAAU;AAAA,EACd,YAAY,EAAE,gBAAgB,aAAa,UAAU,aAAc;AAAA,EACnE,OAAO;AACN,WAAO;AAAA,MACP,QAAQ,CAAE;AAAA,MACV,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,UAAU,CAAE;AAAA,MACZ,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,UAAU;AAAA,MACV,oBAAoB;AAAA,MACpB,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,SAAS,CAAE;AAAA,MACX,kBAAkB;AAAA,MAClB,aAAa,CAAE;AAAA,MACf,YAAY,CAAE;AAAA,MACd,iBAAiB;AAAA,MACjB,WAAW,CAAE;AAAA,MACb,cAAc,CAAE;AAAA,MAChB,eAAe,CAAE;AAAA,MACjB,aAAa;AAAA,MACb,kBAAkB;AAAA,MAClB,2BAA2B,CAAE;AAAA,MAC7B,0BAA0B;AAAA;AAAA,IAC3B;AAAA,EACA;AAAA,EAED,UAAU;AAAA,IACT,YAAY;AACX,YAAM,KAAK,KAAK,OAAO,MAAM;AAC7B,YAAM,QAAQ,KAAK,OAAO,UAAU,KAAK,OAAO,eAAe;AAC/D,YAAM,UAAW,KAAK,QAAS;AAC/BA,0BAAY,MAAA,OAAA,gCAAA,qBAAqB,EAAE,IAAI,OAAO,QAAM,CAAG;AACvD,aAAO;AAAA,IACP;AAAA,IACD,YAAY;AACX,aAAO,KAAK,OAAO,OAAO,KAAK,OAAO,UAAU,KAAK,OAAO,eAAgB,KAAK,OAAO,MAAM,KAAK,OAAO,UAAU,KAAK,OAAO,eAAgB,MAAM;AAAA,IACtJ;AAAA,IACD,iBAAiB;AAChB,aAAO,KAAK,OAAO,WAAW,KAAK,OAAO,cAAc,KAAK,OAAO,mBAAoB,KAAK,OAAO,UAAU,KAAK,OAAO,cAAc,KAAK,OAAO,mBAAoB,MAAM;AAAA,IAC9K;AAAA,IACD,gBAAgB;AACf,aAAO,KAAK,OAAO,UAAU,KAAK,OAAO,aAAc,KAAK,OAAO,SAAS,KAAK,OAAO,aAAc,MAAM;AAAA,IAC5G;AAAA,IACD,aAAa;AACZ,aAAO,KAAK,OAAO,OAAO,KAAK,cAAe,KAAK,OAAO,MAAM,KAAK,cAAe,MAAM;AAAA,IAC1F;AAAA,IACD,wBAAwB;AACvB,YAAM,SAAS,KAAK;AACpB,UAAI,WAAW;AAAO,eAAO;AAC7B,UAAI,WAAW;AAAO,eAAO;AAC7B,UAAI,WAAW;AAAQ,eAAO;AAC9B,aAAO;AAAA,IACP;AAAA,IACD,iBAAiB;AAChB,YAAM,QAAQ,KAAK,UAAU,KAAK,OAAO;AACzC,YAAM,SAAS,KAAK,cAAc,QAAQ,KAAK,WAAW,KAAK,IAAI;AACnEA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,0BAA0B,EAAE,OAAO,QAAQ,YAAY,KAAK,WAAY,CAAA;AACpF,aAAO,SAAU,OAAO,MAAM,OAAO,OAAQ;AAAA,IAC7C;AAAA,IACD,UAAU;AACTA,oBAAY,MAAA,MAAA,OAAA,gCAAA,8BAA8B,KAAK,UAAU;AACzD,UAAI,CAAC,KAAK;AAAY,eAAO;AAC7B,aAAO,OAAO,OAAO,KAAK,UAAU,EAAE,IAAI,OAAK;AAC9C,cAAM,MAAM,EAAE,GAAG;AAEjB,YAAI,CAAC,MAAM,QAAQ,IAAI,GAAG,GAAG;AAC5B,cAAI,OAAO,IAAI,QAAQ,UAAU;AAChC,gBAAI,MAAM,IAAI,IAAI,MAAM,GAAG,EAAE,IAAI,QAAM,EAAE,IAAI,EAAE,KAAK,EAAA,EAAI;AAAA,iBAClD;AACN,gBAAI,MAAM;UACX;AAAA,QACD;AAEA,YAAI,CAAC,MAAM,QAAQ,IAAI,EAAE,GAAG;AAC3B,cAAI,OAAO,IAAI,OAAO,UAAU;AAC/B,gBAAI,KAAK,IAAI,GAAG,MAAM,GAAG,EAAE,IAAI,QAAM,EAAE,IAAI,EAAE,KAAK,EAAA,EAAI;AAAA,iBAChD;AACN,gBAAI,KAAK;UACV;AAAA,QACD;AACA,YAAI,CAAC,MAAM,QAAQ,IAAI,QAAQ,GAAG;AACjC,cAAI,OAAO,IAAI,aAAa,UAAU;AACrC,gBAAI,WAAW,IAAI,SAAS,MAAM,GAAG,EAAE,IAAI,QAAM,EAAE,MAAM,EAAE,KAAK,EAAA,EAAI;AAAA,iBAC9D;AACN,gBAAI,WAAW;UAChB;AAAA,QACD;AAEA,YAAI,CAAC,MAAM,QAAQ,IAAI,IAAI,GAAG;AAC7B,cAAI,OAAO,IAAI,SAAS,UAAU;AACjC,gBAAI,OAAO,IAAI,KAAK,MAAM,GAAG,EAAE,IAAI,UAAQ;AAC1C,oBAAM,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG;AACnC,qBAAO,EAAE,IAAI,KAAK,QAAQ,IAAI,OAAO,WAAW,IAAI,IAAI;YACzD,CAAC;AAAA,iBACK;AACN,gBAAI,OAAO;UACZ;AAAA,QACD;AACA,YAAI,CAAC,MAAM,QAAQ,IAAI,YAAY,GAAG;AACrC,cAAI,OAAO,IAAI,iBAAiB,UAAU;AACzC,gBAAI,eAAe,IAAI,aAAa,MAAM,GAAG,EAAE,IAAI,UAAQ;AAC1D,oBAAM,CAAC,MAAM,IAAI,IAAI,KAAK,MAAM,GAAG;AACnC,qBAAO,EAAE,MAAM,KAAK,QAAQ,MAAM,OAAO,WAAW,IAAI,IAAI;YAC7D,CAAC;AAAA,iBACK;AACN,gBAAI,eAAe;UACpB;AAAA,QACD;AACA,eAAO;AAAA,MACR,CAAC;AAAA,IACD;AAAA,IACD,sBAAsB;AACrB,YAAM,QAAQ,KAAK,UAAU,KAAK,OAAO;AACzC,YAAM,MAAM,KAAK,cAAc,KAAK,WAAW,KAAK;AACpD,UAAI,CAAC,OAAO,CAAC,MAAM,QAAQ,IAAI,GAAG;AAAG,eAAO;AAC5C,aAAO,IAAI,IAAI,IAAI,CAAC,MAAM,SAAS;AAAA,QAClC,IAAI,OAAO,GAAG;AAAA,QACd;AAAA,QACA,QAAQ;AAAA,QACR,MAAM,GAAG,IAAI;AAAA,QACb,WAAW;AAAA,UACV,EAAE,KAAK,QAAQ,OAAO,KAAM;AAAA,UAC5B,EAAE,KAAK,QAAQ,OAAO,KAAK;AAAA,QAC5B;AAAA,MACA,EAAC;AAAA,IACF;AAAA,IACD,iBAAiB;AAChB,aAAO,KAAK,WAAW,KAAK,QAAQ,SAAS,IAAI,KAAK,UAAU,KAAK;AAAA,IACrE;AAAA,IACD,mBAAmB;;AAClB,eAAO,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,aAA7B,mBAAuC,qBAAoB;AAAA,IAClE;AAAA,IACD,uBAAuB;;AACtB,eAAO,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,iBAA7B,mBAA2C,qBAAoB;AAAA,IACtE;AAAA,IACD,yBAAyB;;AACxB,eAAO,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,YAA7B,mBAAsC,6BAA4B;AAAA,IACzE;AAAA,IACD,uBAAuB;AAEtB,aAAO;AAAA,IACP;AAAA,IACD,yBAAyB;;AACxB,eAAO,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,iBAA7B,mBAA2C,wBAAuB;AAAA,IACzE;AAAA,IACD,IAAI,mBAAmB;;AACtB,gBAAQ,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,aAA7B,mBAAuC,qBAAoB,KAAK,QACnE,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,iBAA7B,mBAA2C,qBAAoB,KAAK,QACpE,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,YAA7B,mBAAsC,6BAA4B,KAAK,QACvE,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,iBAA7B,mBAA2C,wBAAuB,KAAK;AAAA,IAC5E;AAAA,EACD;AAAA,EAED,MAAM,SAAS;AACdA,kBAAAA,MAAY,MAAA,OAAA,gCAAA,eAAe;AAC3B,UAAM,KAAK;AACXA,kBAAAA,MAAY,MAAA,OAAA,gCAAA,uBAAuB;AAEnC,SAAK,kBAAiB;AAEtB,QAAI,OAAOC,gBAAAA,UAAU,aAAa,YAAY;AAC7CA,sBAAAA,UAAU,SAAS,KAAK,iBAAiB;AACzCD,oBAAAA,MAAY,MAAA,OAAA,gCAAA,2BAA2B;AAAA,IACxC;AAEA,SAAK,WAAU;AAEfE,oBAAAA,UAAU,GAAG,gBAAgB,CAAC,SAAS;AACtC,UAAI,QAAQ,KAAK,SAAS;AACzB,aAAK,mBAAmB,KAAK;AAAA,MAC9B;AAAA,IACD,CAAC;AAGDA,oBAAAA,UAAU,GAAG,cAAc,KAAK,eAAe;AAC/CF,kBAAAA,MAAY,MAAA,OAAA,gCAAA,wBAAwB;AAGpC,SAAK,kBAAiB;AAItBA,kBAAAA,MAAA,MAAA,OAAA,gCAAY,gCAAgC;AAE5C,eAAW,MAAM;AAChBA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,kCAAkC;AAC9C,WAAK,aAAY;AAAA,IACjB,GAAE,GAAI;AACPE,oBAAAA,UAAU,GAAG,qBAAqB,KAAK,sBAAsB;AAC7DA,oBAAAA,UAAU,GAAG,gBAAgB,KAAK,iBAAiB;AACnDA,oBAAAA,UAAU,GAAG,iBAAiB,KAAK,kBAAkB;AACrDA,oBAAAA,UAAU,GAAG,eAAe,KAAK,sBAAsB;AACvDF,kBAAAA,mDAAY,mBAAmB;AAC/BA,kBAAAA,MAAY,MAAA,OAAA,gCAAA,yEAAyE;AAAA,EACrF;AAAA,EAED,UAAU;AAAA,EAET;AAAA,EAED,SAAS;AACRA,kBAAAA,MAAY,MAAA,OAAA,gCAAA,qBAAqB;AACjCA,wBAAY,MAAA,OAAA,gCAAA,UAAS,oBAAI,KAAI,GAAG,eAAc,CAAE;AAChDA,kBAAAA,MAAA,MAAA,OAAA,gCAAY,WAAW,kBAAkB,gBAAiB,EAAC,SAAS,CAAC,EAAE,KAAK;AAG5E,SAAK,WAAW;AAEhB,SAAK,uBAAuB;AAG5BA,kBAAA,MAAA,MAAA,OAAA,gCAAY,kBAAkB,KAAK,QAAQ;AAC3CA,qEAAY,uBAAuBC,gBAAAA,UAAU,QAAQ;AACrDD,qEAAY,0BAA0BE,gBAAAA,UAAU,WAAW;AAG3DF,kBAAAA,MAAY,MAAA,OAAA,gCAAA,eAAe;AAAA,EAC3B;AAAA,EAED,aAAa,MAAM;AAClBA,kBAAAA,MAAA,MAAA,OAAA,gCAAY,2BAA2B;AACvCA,kBAAY,MAAA,MAAA,OAAA,gCAAA,YAAY,IAAI;AAC5BA,wBAAY,MAAA,OAAA,gCAAA,UAAS,oBAAI,KAAI,GAAG,eAAc,CAAE;AAGhD,QAAI,KAAK,UAAU,GAAG;AACrBA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,8BAA8B;AAAA,IAC3C;AAAA,EACA;AAAA,EAED,WAAW;AAEVC,8BAAU,UAAU,KAAK,iBAAiB;AAE1CC,oBAAAA,UAAU,IAAI,cAAc,KAAK,eAAe;AAEhDA,oBAAAA,UAAU,IAAI,sBAAsB,KAAK,kBAAkB;AAC3DA,oBAAAA,UAAU,IAAI,SAAS,KAAK,UAAU;AAAA,EACtC;AAAA,EAED,UAAU;AAETF,kBAAAA,mDAAY,mBAAmB;AAAA,EAC/B;AAAA,EAED,OAAO;AAAA,IACN,sBAAsB;AAAA,MACrB,QAAQ,QAAQ;AACf,YAAI,UAAU,KAAK,WAAW,KAAK,QAAQ,QAAQ;AAClD,eAAK,aAAY;AAAA,QAClB;AAAA,MACA;AAAA,MACD,WAAW;AAAA,IACX;AAAA,IACD,SAAS;AAAA,MACR,QAAQ,QAAQ;AACf,YAAIC,gBAAS,UAAC,UAAUA,0BAAU,OAAO,eAAe,UAAU,OAAO,QAAQ;AAChF,eAAK,aAAY;AAAA,QAClB;AAAA,MACA;AAAA,MACD,WAAW;AAAA,IACZ;AAAA,EACA;AAAA,EAED,SAAS;AAAA,IACR,oBAAoB;AAEnB,YAAM,QAAQD,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAE9CA,0BAAA,MAAA,OAAA,gCAAY,mBAAmB,QAAQ,OAAO,KAAK;AACnDA,0BAAY,MAAA,OAAA,gCAAA,sBAAsB,WAAW,OAAO,KAAK;AAEzD,UAAI,CAAC,SAAS,CAAC,UAAU;AACxBA,sBAAAA,MAAA,MAAA,OAAA,gCAAY,iBAAiB;AAC7BA,sBAAAA,MAAI,SAAS;AAAA,UACZ,KAAK;AAAA,QACN,CAAC;AACD;AAAA,MACD;AAEAA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,gBAAgB;AAC5BA,oBAAA,MAAA,MAAA,OAAA,gCAAY,eAAe,QAAQ;AACnC,WAAK,SAAQ;AAAA,IACb;AAAA,IAED,MAAM,WAAW;AAChB,UAAI;AACHA,sBAAAA,MAAY,MAAA,OAAA,gCAAA,YAAY;AAExBC,wBAAAA,UAAU,SAAS,KAAK,iBAAiB;AACzCD,sBAAAA,MAAY,MAAA,OAAA,gCAAA,WAAW;AAGvB,cAAMC,gBAAAA,UAAU;AAChB,aAAK,uBAAsB;AAC3B,aAAK,WAAU;AACfD,sBAAA,MAAA,MAAA,OAAA,gCAAY,iBAAiB,KAAK,QAAQ;AAAA,MACzC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,gCAAc,YAAY,KAAK;AAC/B,aAAK,mBAAmB;AACxBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,cAAc,MAAM;AAAA,UAC3B,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IAED,yBAAyB;AACxB,WAAK,mBAAmBE,gBAAAA,UAAU,cAAc,QAAQ;AACxDF,oBAAA,MAAA,MAAA,OAAA,gCAAY,aAAa,KAAK,gBAAgB;AAE9C,UAAI,CAACE,gBAAS,UAAC,aAAa;AAC3B,aAAK,WAAW;AAAA,MACjB;AAAA,IACA;AAAA,IAED,kBAAkB,MAAM,mBAAmB;AAC1CF,oBAAA,MAAA,MAAA,OAAA,gCAAY,WAAW,MAAM,iBAAiB;AAC9CA,oBAAY,MAAA,MAAA,OAAA,gCAAA,oBAAoB,KAAK,MAAM;AAC3CA,oBAAY,MAAA,MAAA,OAAA,gCAAA,aAAa,KAAK,QAAQ;AAGtC,cAAQ,MAAI;AAAA,QACX,KAAK;AACJ,cAAI,kBAAkB,QAAQ;AAE7B,kBAAM,YAAY,KAAK,OAAO;AAC9B,kBAAM,aAAa,KAAK,mBAAmB,KAAK,gBAAgB;AAEhE,iBAAK,SAAS,EAAE,GAAG,kBAAkB;AAGrC,gBAAI,cAAc,cAAc,QAAW;AAC1C,mBAAK,OAAO,KAAK;AACjBA,4BAAA,MAAA,MAAA,OAAA,gCAAY,kBAAkB,SAAS;AAAA,YACxC;AAEAA,0BAAY,MAAA,MAAA,OAAA,gCAAA,cAAc,KAAK,MAAM;AACrCA,gCAAA,MAAA,OAAA,gCAAY,WAAW,KAAK,OAAO,WAAW;AAAA,iBACxC;AACNA,0BAAAA,oDAAa,+BAA+B;AAAA,UAC7C;AACA;AAAA,QACD,KAAK;AACJ,eAAK,QAAQ,kBAAkB;AAC/B,eAAK,OAAO,kBAAkB;AAC9B;AAAA,QACD,KAAK;AACJ,eAAK,SAAS,kBAAkB;AAChC;AAAA,QACD,KAAK;AACJA,2EAAY,oCAAoC,kBAAkB,QAAQ;AAE1E,eAAK,WAAW,kBAAkB,SAAS,IAAI,CAAC,KAAK,QAAQ;AAC5D,gBAAI,QAAQ,GAAG;AACd,qBAAO,EAAE,GAAG,KAAK,aAAa,GAAC;AAAA,mBACzB;AACN,qBAAO,EAAE,GAAG,KAAK,aAAa,IAAI;YACnC;AAAA,UACD,CAAC;AACD,cAAI,KAAK,SAAS,SAAS,GAAG;AAC7B,iBAAK,iBAAiB,KAAK,SAAS,CAAC,GAAG,CAAC;AAAA,UAC1C;AACAA,wBAAY,MAAA,MAAA,OAAA,gCAAA,qBAAqB,KAAK,QAAQ;AAC9C;AAAA,QACD,KAAK;AACJ,eAAK,WAAW,kBAAkB,YAAY;AAC9CA,2EAAY,aAAa,KAAK,QAAQ;AAEtC,cAAI,KAAK,UAAU;AAClBA,0BAAAA,MAAA,MAAA,OAAA,gCAAY,sBAAsB;AAAA,UAEnC;AACA;AAAA,QACD;AACC,eAAK,WAAU;AAAA,MACjB;AAGA,UAAI,SAAS,QAAQ;AACpB,aAAK,WAAWE,gBAAS,UAAC,YAAY,kBAAkB,YAAY;AAAA,MACrE;AAEAF,oBAAAA,MAAA,MAAA,OAAA,gCAAY,gBAAgB,KAAK,UAAU,aAAa,KAAK,MAAM;AAAA,IACnE;AAAA,IAED,aAAa;AAEZ,WAAK,SAASC,gBAAAA,UAAU,gBAAgBA,gBAAS,UAAC,cAAc,IAAIA,gBAAS,UAAC;AAC9E,UAAIA,gBAAAA,UAAU,QAAQ;AACrB,aAAK,SAAS,EAAE,GAAGA,gBAAS,UAAC;AAC7B,YAAI,KAAK,WAAW,KAAK,QAAQ,UAAUA,gBAAS,UAAC,OAAO,aAAa;AACxE,eAAK,aAAa,KAAK,QAAQ,KAAK,OAAK,EAAE,OAAOA,gBAAAA,UAAU,OAAO,WAAW,KAAK;AAAA,QACpF;AAAA,MACD;AACA,WAAK,QAAQA,0BAAU,SAAS;AAChC,WAAK,OAAOA,0BAAU,QAAQ;AAC9B,WAAK,SAASA,0BAAU,UAAU;AAClC,WAAK,WAAW,CAAC,GAAIA,gBAAS,UAAC,YAAY,CAAE,CAAA;AAG7C,WAAK,kBAAiB;AAGtB,WAAK,aAAY;AAAA,IACjB;AAAA,IAED,aAAa,KAAK;AACjB,aAAOE,eAAS,UAAC,aAAa,GAAG;AAAA,IACjC;AAAA,IAED,gBAAgB;AACf,YAAM,cAAc;AAAA,QACnB,WAAW;AAAA,QACX,mBAAmB;AAAA,QACnB,YAAY;AAAA,QACZ,SAAS;AAAA,MACV;AACA,aAAO,YAAY,KAAK,MAAM,KAAK;AAAA,IACnC;AAAA,IAED,mBAAmB;AAClBH,oBAAAA,MAAY,MAAA,OAAA,gCAAA,iBAAiB;AAC7BA,0BAAA,MAAA,OAAA,gCAAY,UAAS,oBAAI,KAAI,GAAG,eAAc,CAAE;AAChDA,oBAAY,MAAA,MAAA,OAAA,gCAAA,WAAW,KAAK,QAAQ;AACpCA,oBAAA,MAAA,MAAA,OAAA,gCAAY,kBAAkBC,gBAAAA,UAAU,QAAQ;AAChDD,oBAAA,MAAA,MAAA,OAAA,gCAAY,kBAAkBE,gBAAAA,UAAU,QAAQ;AAChDF,oBAAA,MAAA,MAAA,OAAA,gCAAY,kBAAkBE,gBAAAA,UAAU,WAAW;AACnDF,oBAAA,MAAA,MAAA,OAAA,gCAAY,mBAAmBE,gBAAAA,UAAU,SAAS;AAGlD,YAAM,kBAAkBA,gBAAAA,UAAU,YAAYD,gBAAS,UAAC,YAAY,KAAK;AAEzE,UAAI,CAAC,iBAAiB;AACrBD,sBAAAA,MAAY,MAAA,OAAA,gCAAA,YAAY;AACxBA,sBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,OAAK,CAAG;AAC7C;AAAA,MACD;AAEA,UAAI,CAACE,gBAAS,UAAC,aAAa;AAC3BF,sBAAAA,MAAY,MAAA,OAAA,gCAAA,qBAAqB;AACjCA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAK,CAAG;AAC/C;AAAA,MACD;AAEAA,oBAAAA,MAAA,MAAA,OAAA,gCAAY,oCAAoC;AAChDC,sBAAS,UAAC,iBAAgB;AAC1BD,oBAAAA,MAAA,MAAA,OAAA,gCAAY,oCAAoC;AAAA,IAChD;AAAA,IAED,WAAW;AACVA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChBC,4BAAS,UAAC,WAAW,CAAC;AACtB,iBAAK,WAAW,CAAC;AACjBA,4BAAAA,UAAU,KAAK;AAAA,UAChB;AAAA,QACD;AAAA,OACA;AAAA,IACD;AAAA,IAED,WAAW,MAAM;AAChBD,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,OACL;AAAA,IACD;AAAA,IAED,iBAAiB;AAChBA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,gBAAgB;AAC5BA,0BAAA,MAAA,OAAA,gCAAY,UAAS,oBAAI,KAAI,GAAG,eAAc,CAAE;AAChDA,oBAAY,MAAA,MAAA,OAAA,gCAAA,oBAAoBE,gBAAAA,UAAU,WAAW;AACrDF,oBAAA,MAAA,MAAA,OAAA,gCAAY,kBAAkBE,gBAAAA,UAAU,QAAQ;AAChDF,oBAAY,MAAA,MAAA,OAAA,gCAAA,WAAW,KAAK,QAAQ;AACpCA,oBAAA,MAAA,MAAA,OAAA,gCAAY,kBAAkBC,gBAAAA,UAAU,QAAQ;AAChDD,oBAAA,MAAA,MAAA,OAAA,gCAAY,kBAAkBC,gBAAAA,UAAU,MAAM;AAG9C,YAAM,QAAQD,cAAAA,MAAI,eAAe,OAAO;AACxC,YAAM,WAAWA,cAAAA,MAAI,eAAe,UAAU;AAC9CA,oBAAAA,MAAY,MAAA,OAAA,gCAAA,SAAS;AACrBA,oBAAA,MAAA,MAAA,OAAA,gCAAY,cAAc,CAAC,CAAC,KAAK;AACjCA,oBAAY,MAAA,MAAA,OAAA,gCAAA,iBAAiB,CAAC,CAAC,QAAQ;AAGvC,YAAM,aAAa,gBAAgBE,gBAAS,UAAC,cAAc,QAAQ,KAAK;AAAA,SAClEA,gBAAAA,UAAU,SAAS;AAAA,eACbA,0BAAU,WAAW,QAAQ,KAAK;AAAA,QACzC,KAAK,WAAW,QAAQ,KAAK;AAAA,eACtBD,0BAAU,WAAW,QAAQ,KAAK;AAAA,WACtC,QAAQ,OAAO,KAAK;AAAA,cACjB,WAAW,OAAO,KAAK;AAAA,QAC7BA,0BAAU,SAAS,OAAO,KAAK;AAAA,QAC/B,KAAK,gBAAgB;AAE1BD,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,QAAQ;AAEfA,0BAAAA,MAAA,MAAA,OAAA,gCAAY,WAAW;AACvBE,4BAAS,UAAC,iBAAgB;AAAA,UAC3B;AAAA,QACD;AAAA,MACD,CAAC;AAGD,UAAI,CAACA,gBAAS,UAAC,aAAa;AAC3BF,sBAAAA,MAAY,MAAA,OAAA,gCAAA,WAAW;AACvBE,kCAAU,UAAU,KAAK,MAAM;AAC9BF,wBAAAA,mDAAY,QAAQ;AACpB,eAAK,uBAAsB;AAAA,QAC5B,CAAC,EAAE,MAAM,WAAS;AACjBA,6EAAc,WAAW,KAAK;AAAA,QAC/B,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAGD,kBAAkB,cAAc;AAE/B,UAAIC,gBAAAA,UAAU,WAAW;AACxB,cAAM,OAAO,OAAO,OAAOA,gBAAAA,UAAU,SAAS,EAAE,KAAK,OAAK,KAAK,EAAE,SAAS,YAAY;AACtF,YAAI;AAAM,iBAAO;AAAA,MAClB;AAEA,UAAI,MAAM,QAAQA,gBAAS,UAAC,SAAS,GAAG;AACvC,cAAM,OAAOA,0BAAU,UAAU,KAAK,OAAK,KAAK,EAAE,SAAS,YAAY;AACvE,YAAI;AAAM,iBAAO;AAAA,MAClB;AACA,aAAO;AAAA,IACP;AAAA;AAAA,IAGD,qBAAqB,MAAM;AAC1B,UAAI,CAAC;AAAM,eAAO;AAElB,aAAO,KAAK,gBAAgB;AAAA,IAC5B;AAAA,IAED,gBAAgB,MAAM;AAErBD,0BAAY,MAAA,OAAA,gCAAA,2BAA2B,KAAK,UAAU,IAAI,CAAC;AAG3D,YAAM,WAAW,KAAK,WAAW,KAAK,eAAe,IAAI;AACzD,YAAM,YAAY,KAAK,aAAa,KAAK,QAAQ;AAGjD,UAAIC,6BAAa,OAAOA,0BAAU,oBAAoB,YAAY;AACjED,sBAAAA,mDAAY,oCAAoC;AAChDC,wBAAAA,UAAU,gBAAgB;AAAA,UACzB,MAAM;AAAA,UACN;AAAA,UACA,SAAS,KAAK,WAAW,CAAE;AAAA,UAC3B,oBAAoB,KAAK,sBAAsB;AAAA,QAChD,CAAC;AACD;AAAA,MACD;AAGA,YAAM,YAAY,KAAK,gBAAgB,KAAK,YAAY,KAAK,QAAQ,KAAK,cAAc;AACxF,YAAM,aAAa,KAAK,cAAc,KAAK,QAAQ;AACnD,YAAM,eAAe,CAAC,EAAE,KAAK,gBAAgB,KAAK,YAAY,KAAK,QAAQ,KAAK;AAChF,YAAM,gBACL,KAAK,cAAc,eACnB,eAAe,eACf,eAAe,YACf,mCAAmC,KAAK,OAAO,KAC/C;AACD,YAAM,iBAAiB,kBAAkB,KAAK,OAAO;AACrD,UAAI,iBAAiB,CAAC,gBAAgB;AACrCD,sBAAAA,MAAY,MAAA,OAAA,gCAAA,+BAA+B;AAC3C,cAAM,OAAO,KAAK,kBAAkB,aAAa,KAAK;AACtD,cAAM,WAAW,KAAK,qBAAqB,IAAI;AAE/C,cAAM,WAAW,OAAO,KAAK,OAAQ,aAAa;AAClD,aAAK,iBAAiB;AAAA,UACrB,SAAS,WAAW;AAAA,UACpB;AAAA,UACA,cAAc,aAAa;AAAA,UAC3B,aAAa;AAAA,UACb;AAAA;AAED,aAAK,iBAAiB;AACtB,aAAK,kBAAkB;AACvB,aAAK,qBAAqB;AAAA,MACzB,WAAS,gBAAgB;AAC1BA,sBAAAA,MAAA,MAAA,OAAA,gCAAY,6CAA6C;AAEzD,aAAK,kBAAkB,QAAQ,QAAQ,OAAO,IAAI;AAElD,YAAIC,6BAAa,OAAOA,0BAAU,oBAAoB,YAAY;AACjEA,0BAAAA,UAAU,gBAAgB;AAAA,YACzB,MAAM;AAAA,YACN;AAAA,YACA,SAAS,KAAK,WAAW,CAAC;AAAA,UAC3B,CAAC;AAAA,QACF;AAAA,aACM;AACND,sBAAAA,MAAA,MAAA,OAAA,gCAAY,2CAA2C;AACvD,YAAIC,6BAAa,OAAOA,0BAAU,oBAAoB,YAAY;AACjEA,0BAAAA,UAAU,gBAAgB;AAAA,YACzB,MAAM;AAAA,YACN;AAAA,YACA,SAAS,KAAK,WAAW,CAAC;AAAA,UAC3B,CAAC;AAAA,QACF;AAAA,MACD;AAAA,IACA;AAAA,IAED,WAAW;AACV,YAAM,eAAe,KAAK,eAAe;AACzC,YAAM,OAAO,KAAK,kBAAkB,YAAY;AAChD,UAAI,CAAC,MAAM;AACV,aAAK,kBAAkB;AACvB;AAAA,MACD;AACA,UAAI,KAAK,kBAAkB,GAAG;AAC7B,aAAK,kBAAkB;AACvB;AAAA,MACD;AAEAE,qBAAAA,UAAU,YAAY;AAAA,QACrB,MAAM;AAAA,QACN,MAAM,EAAE,YAAY,KAAK,eAAe,WAAW;AAAA,MACpD,CAAC;AACD,WAAK;AACL,UAAI,KAAK,kBAAkB,GAAG;AAC7B,mBAAW,MAAM,KAAK,oBAAqB,GAAE,GAAG;AAAA,MACjD;AAAA,IACA;AAAA,IAED,sBAAsB;AACrB,WAAK,qBAAqB;AAC1B,WAAK,iBAAiB;AACtB,WAAK,kBAAkB;AAAA,IACvB;AAAA;AAAA,IAGD,mBAAmB;;AAClB,eAAO,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,aAA7B,mBAAuC,qBAAoB;AAAA,IAClE;AAAA,IAED,uBAAuB;;AACtB,eAAO,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,iBAA7B,mBAA2C,qBAAoB;AAAA,IACtE;AAAA,IAED,yBAAyB;;AACxB,eAAO,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,YAA7B,mBAAsC,6BAA4B;AAAA,IACzE;AAAA,IAED,uBAAuB;AAEtB,aAAO;AAAA,IACP;AAAA,IAED,yBAAyB;;AACxB,eAAO,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,iBAA7B,mBAA2C,wBAAuB;AAAA,IACzE;AAAA,IAED,IAAI,mBAAmB;;AACtB,gBAAQ,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,aAA7B,mBAAuC,qBAAoB,KAAK,QACnE,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,iBAA7B,mBAA2C,qBAAoB,KAAK,QACpE,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,YAA7B,mBAAsC,6BAA4B,KAAK,QACvE,sBAAK,WAAL,mBAAa,mBAAb,mBAA6B,iBAA7B,mBAA2C,wBAAuB,KAAK;AAAA,IAC5E;AAAA;AAAA,IAGD,MAAM,oBAAoB;AACzB,UAAI;AACHH,sBAAAA,MAAA,MAAA,OAAA,gCAAY,gBAAgB;AAC5B,cAAM,WAAW,MAAMG,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,CAAC;AAAA,QACR,CAAC;AAEDH,sBAAA,MAAA,MAAA,OAAA,gCAAY,iBAAiB,QAAQ;AAGrC,YAAI,SAAS,SAAS,6BAA6B;AAClDA,wBAAAA,MAAA,MAAA,OAAA,gCAAY,oBAAoB;AAEhC;AAAA,QACD;AAGA,YAAI,SAAS,SAAS,+BAA+B,SAAS,SAAS,iBAAiB;AACvF,gBAAM,YAAY,SAAS,QAAQ;AACnCA,2EAAY,iBAAiB,SAAS;AAGtC,cAAI,CAAC,KAAK,OAAO,gBAAgB;AAChC,iBAAK,OAAO,iBAAiB;UAC9B;AAGA,cAAI,UAAU,UAAU;AACvB,iBAAK,OAAO,eAAe,WAAW;AAAA,cACrC,kBAAkB,UAAU,SAAS,gBAAgB;AAAA;UAEvD;AAGA,cAAI,UAAU,cAAc;AAC3B,iBAAK,OAAO,eAAe,eAAe;AAAA,cACzC,kBAAkB,UAAU,aAAa,aAAa;AAAA;UAExD;AAGA,cAAI,UAAU,SAAS;AACtB,iBAAK,OAAO,eAAe,UAAU;AAAA,cACpC,0BAA0B,UAAU,QAAQ,iBAAiB;AAAA;UAE/D;AAGA,cAAI,UAAU,cAAc;AAC3B,iBAAK,OAAO,eAAe,eAAe;AAAA,cACzC,qBAAqB,UAAU,aAAa,YAAY;AAAA;UAE1D;AAEAA,2EAAY,mBAAmB,KAAK,OAAO,cAAc;AAAA,QAC1D;AAAA,MACC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,gCAAA,iBAAiB,KAAK;AAAA,MACrC;AAAA,IACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAuED,YAAY,KAAK;AAChB,YAAM,SAASC,gBAAAA,UAAU,UAAU;AAEnC,YAAM,MAAM,IAAI,QAAQ,IAAI,sBAAsB,CAAA;AAClD,UAAI,IAAI,MAAM;AACb,cAAM,MAAO,OAAO,aAAa,CAAE;AACnC,YAAI,CAAC,IAAI,KAAK,OAAK,EAAE,SAAS,IAAI,IAAI;AAAG,iBAAO;AAAA,MACjD;AACA,UAAI,IAAI,QAAQ;AACf,aAAK,OAAO,UAAU,KAAK,IAAI;AAAQ,iBAAO;AAAA,MAC/C;AAEA,aAAO;AAAA,IACP;AAAA,IACD,MAAM,UAAU,KAAK;AACpB,UAAI,CAAC,OAAO,CAAC,IAAI,IAAI;AACpBD,sBAAA,MAAA,MAAA,SAAA,iCAAc,0BAA0B,GAAG;AAC3CA,sBAAG,MAAC,UAAU,EAAE,OAAO,UAAU,MAAM,OAAK,CAAG;AAC/C;AAAA,MACD;AACAA,0BAAA,MAAA,OAAA,iCAAY,WAAW,IAAI,IAAI,GAAG;AAClC,WAAK,aAAa;AAClB,UAAI,CAAC,KAAK,YAAY,GAAG,GAAG;AAC3BA,sBAAG,MAAC,UAAU,EAAE,OAAO,WAAW,MAAM,OAAK,CAAG;AAChD;AAAA,MACD;AAEA,UAAI;AAEHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAGD,cAAM,WAAW,MAAMG,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,EAAE,QAAQ,IAAI,GAAG;AAAA,QACxB,CAAC;AAGDH,sBAAG,MAAC,YAAW;AAEf,YAAI,SAAS,SAAS,sBAAsB;AAC3CA,4EAAY,gBAAgB,SAAS,IAAI;AACzC,eAAK,aAAa,KAAK,QAAQ,KAAK,OAAK,EAAE,OAAO,SAAS,KAAK,MAAM;AACtE,cAAIC,gBAAAA,UAAU,QAAQ;AACrBA,4BAAAA,UAAU,OAAO,cAAc,SAAS,KAAK;AAC7CA,sCAAU,aAAa,QAAQ;AAAA,UAChC;AAEA,eAAK,kBAAiB;AACtB,eAAK,eAAe;AACpBD,wBAAG,MAAC,UAAU,EAAE,OAAO,QAAQ,MAAM,UAAQ,CAAG;AAChDA,4EAAY,uBAAuB,KAAK,UAAU;AAAA,QACnD,WAAW,SAAS,SAAS,SAAS;AACrCA,wBAAc,MAAA,MAAA,SAAA,iCAAA,WAAW,SAAS,IAAI;AACtC,cAAI,SAAS,QAAQ,SAAS,KAAK,SAAS;AAC3CA,gCAAI,UAAU,EAAE,OAAO,SAAS,KAAK,SAAS,MAAM,OAAO,CAAC;AAAA,UAC7D;AAAA,QACD;AAAA,MACC,SAAO,OAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAA,MAAA,MAAA,SAAA,iCAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO,cAAc,MAAM,WAAW;AAAA,UACtC,MAAM;AAAA,QACP,CAAC;AAAA,MACF;AAAA,IACA;AAAA;AAAA,IAID,MAAM,eAAe;;AACpBA,oBAAAA,MAAA,MAAA,OAAA,iCAAY,uBAAuB;AACnC,UAAI;AACH,cAAM,UAAQC,+BAAU,WAAVA,mBAAkB,gBAAgBA,0BAAU,UAAUA,gBAAS,UAAC,OAAO;AACrF,YAAI,CAAC,OAAO;AACXD,wBAAAA,MAAY,MAAA,OAAA,iCAAA,wBAAwB;AACpC;AAAA,QACD;AAEAA,sBAAA,MAAA,MAAA,OAAA,iCAAY,uBAAuB,KAAK;AACxC,cAAM,WAAW,MAAMG,eAAS,UAAC,YAAY;AAAA,UAC5C,MAAM;AAAA,UACN,MAAM,EAAE,QAAQ,MAAM;AAAA,QACvB,CAAC;AAEDH,sBAAA,MAAA,MAAA,OAAA,iCAAY,+BAA+B,QAAQ;AAGnD,YAAI,SAAS,SAAS,wBAAwB;AAC7CA,wBAAAA,MAAY,MAAA,OAAA,iCAAA,yBAAyB;AAErC,cAAI,KAAK,cAAc,KAAK,WAAW,KAAK,GAAG;AAC9C,kBAAM,YAAY,KAAK,WAAW,KAAK;AACvC,gBAAI,UAAU,OAAO,MAAM,QAAQ,UAAU,GAAG,GAAG;AAClD,mBAAK,UAAU,UAAU,IAAI,IAAI,CAAC,KAAK,QAAQ;AAE9C,oBAAI,OAAO,QAAQ,UAAU;AAC5B,yBAAO;AAAA,oBACN,IAAI,IAAI,MAAM,OAAO,GAAG;AAAA,oBACxB,MAAM,IAAI,MAAM,IAAI,QAAQ;AAAA,oBAC5B,QAAQ;AAAA,oBACR,MAAM,IAAI,MAAM,IAAI,QAAQ,GAAG,IAAI,MAAM,IAAI,QAAQ,OAAO;AAAA,oBAC5D,WAAW;AAAA,sBACV,EAAE,KAAK,QAAQ,OAAO,KAAM;AAAA,sBAC5B,EAAE,KAAK,QAAQ,OAAO,KAAK;AAAA,oBAC5B;AAAA;gBAEF;AAEA,uBAAO;AAAA,kBACN,IAAI,OAAO,GAAG;AAAA,kBACd,MAAM;AAAA,kBACN,QAAQ;AAAA,kBACR,MAAM,GAAG,GAAG;AAAA,kBACZ,WAAW;AAAA,oBACV,EAAE,KAAK,QAAQ,OAAO,KAAM;AAAA,oBAC5B,EAAE,KAAK,QAAQ,OAAO,KAAK;AAAA,kBAC5B;AAAA;cAEF,CAAC;AAAA,YACF;AAAA,UACD;AACA;AAAA,QACD;AAEA,YAAI,OAAO,CAAA;AACX,YAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AACjC,iBAAO,SAAS;AAAA,mBACN,SAAS,QAAQ,MAAM,QAAQ,SAAS,KAAK,IAAI,GAAG;AAC9D,iBAAO,SAAS,KAAK;AAAA,QACtB,WAAW,SAAS,QAAQ,SAAS,KAAK,QAAQ,MAAM,QAAQ,SAAS,KAAK,IAAI,GAAG;AACpF,iBAAO,SAAS,KAAK;AAAA,QACtB;AAEAA,sBAAY,MAAA,MAAA,OAAA,iCAAA,iBAAiB,IAAI;AACjC,YAAI,KAAK,SAAS,GAAG;AACpB,eAAK,UAAU;AACfA,wBAAA,MAAA,MAAA,OAAA,iCAAY,mBAAmB,KAAK,OAAO;AAAA,eACrC;AAENA,wBAAAA,MAAY,MAAA,OAAA,iCAAA,sBAAsB;AAClC,eAAK,UAAU;QAChB;AAAA,MACD,SAAS,GAAG;AACXA,sBAAA,MAAA,MAAA,SAAA,iCAAc,sBAAsB,CAAC;AACrC,YAAI,OAAO,MAAM,UAAU;AAC1B,qBAAW,OAAO,GAAG;AACpB,gBAAI,OAAO,UAAU,eAAe,KAAK,GAAG,GAAG,GAAG;AACjDA,kCAAY,MAAA,OAAA,iCAAA,YAAY,KAAK,EAAE,GAAG,CAAC;AAAA,YACpC;AAAA,UACD;AAAA,QACD;AAEA,aAAK,UAAU;AAGfA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACX,CAAC;AAAA,MACF;AAAA,IACA;AAAA,IACD,YAAY,KAAK;AAChB,WAAK,cAAc;AACnB,WAAK,mBAAmB;AAAA,IACxB;AAAA,IACD,eAAe;AACd,WAAK,mBAAmB;AACxB,WAAK,cAAc,CAAC;AAAA,IACpB;AAAA,IACD,cAAc,MAAM,KAAK;AAExBA,oBAAAA,MAAI,UAAU,EAAE,OAAO,MAAM,KAAK,KAAK,IAAI,MAAM,QAAQ;AACzD,WAAK,aAAa;AAAA,IAClB;AAAA,IACD,mBAAmB;AAClBA,oBAAAA,MAAA,MAAA,OAAA,iCAAY,uBAAuB;AAAA,IAEnC;AAAA,IACD,MAAM,iBAAiB;AACtBA,oBAAAA,MAAY,MAAA,OAAA,iCAAA,kBAAkB;AAC9B,WAAK,aAAa,MAAMC,gBAAS,UAAC,cAAa;AAC/CD,oBAAA,MAAA,MAAA,OAAA,iCAAY,kBAAkB,KAAK,UAAU;AAAA,IAC7C;AAAA,IACD,MAAM,oBAAoB;AAEzB,UAAIC,gBAAAA,UAAU,gBAAgB;AAC7B,cAAMA,gBAAAA,UAAU;aACV;AAEN,cAAME,eAAS,UAAC,YAAY,EAAE,MAAM,kBAAmB,CAAA;AAAA,MACxD;AACA,WAAK,WAAU;AAAA,IACf;AAAA;AAAA,IAED,UAAU;AACTH,oBAAA,MAAA,MAAA,OAAA,iCAAY,sBAAsB,KAAK,QAAQ;AAC/CA,oBAAY,MAAA,MAAA,OAAA,iCAAA,2BAA2BC,gBAAAA,UAAU,QAAQ;AAAA,IACzD;AAAA,IACD,iBAAiB,KAAK,KAAK;AAE1B,UAAI,cAAc;AAClB,YAAM,WAAW,IAAI;AAGrB,YAAM,WAAW,CAAA;AACjB,UAAI,iBAAiB;AACrB,UAAI,QAAQ;AAEZ,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,cAAM,OAAO,SAAS,CAAC;AACvB,YAAI,SAAS,KAAK;AACjB,cAAI,gBAAgB;AACnB,qBAAS,KAAK,EAAE,MAAM,QAAQ,SAAS,eAAe,CAAC;AACvD,6BAAiB;AAAA,UAClB;AACA,kBAAQ;AACR,2BAAiB;AAAA,QAClB,WAAW,SAAS,KAAK;AACxB,4BAAkB;AAClB,mBAAS,KAAK,EAAE,MAAM,OAAO,SAAS,eAAe,CAAC;AACtD,2BAAiB;AACjB,kBAAQ;AAAA,eACF;AACN,4BAAkB;AAAA,QACnB;AAAA,MACD;AAGA,UAAI,gBAAgB;AACnB,iBAAS,KAAK,EAAE,MAAM,QAAQ,QAAQ,QAAQ,SAAS,eAAa,CAAG;AAAA,MACxE;AAEA,UAAI,eAAe;AACnB,UAAI,YAAY;AAEhB,YAAM,YAAY,MAAM;AACvB,YAAI,eAAe,SAAS,QAAQ;AACnC,gBAAM,UAAU,SAAS,YAAY;AAErC,cAAI,QAAQ,SAAS,OAAO;AAE3B,gBAAI,eAAe,QAAQ;AAC3B;AACA,wBAAY;AAAA,iBACN;AAEN,gBAAI,YAAY,QAAQ,QAAQ,QAAQ;AACvC,kBAAI,eAAe,QAAQ,QAAQ,SAAS;AAC5C;AAAA,mBACM;AACN;AACA,0BAAY;AAAA,YACb;AAAA,UACD;AAEA,eAAK,KAAK,KAAK,UAAU,KAAK,EAAE,GAAG,IAAE,CAAG;AACxC,qBAAW,WAAW,EAAE;AAAA,eAClB;AACN,cAAI,cAAc;AAClB,eAAK,KAAK,KAAK,UAAU,KAAK,EAAE,GAAG,IAAE,CAAG;AAAA,QACzC;AAAA;AAGD;IACA;AAAA,IACD,uBAAuB,MAAM;AAE5B,YAAM,UAAU,KAAK,WAAW;AAChC,WAAK,gBAAgB,EAAE,GAAG;AAC1B,WAAK,eAAe,EAAE,GAAG,KAAK;AAC9B,WAAK,YAAY;AACjB,WAAK,cAAc;AACnB,WAAK,mBAAmB,QAAQ,eAAe;AAC/C,WAAK,kBAAkB;AAGvB,UAAI,uBAAuB;AAC3B,UAAI,QAAQ,gBAAgB,UAAU;AACrC,+BAAuB,GAAG,QAAQ,QAAQ,MAAM;AAAA,aAC1C;AACN,+BAAuB,OAAO,QAAQ,QAAQ,MAAM;AAAA,MACrD;AAEA,YAAM,eAAe;AAAA,QACpB,YAAW,oBAAI,QAAO,mBAAmB,SAAS,EAAE,QAAQ,OAAO;AAAA,QACnE,MAAM;AAAA,QACN,aAAa;AAAA;AAEdA,sBAAAA,UAAU,SAAS,QAAQ,YAAY;AAEvC,UAAIA,0BAAU,SAAS,SAAS;AAAIA,wBAAS,UAAC,WAAWA,gBAAAA,UAAU,SAAS,MAAM,GAAG,EAAE;AACvFA,gCAAU,aAAa,UAAU;AAGjC,UAAI,QAAQ,gBAAgB,UAAU;AACrC,mBAAW,MAAM;AAChBE,yBAAAA,UAAU,YAAY,EAAE,MAAM,+BAA+B,MAAM,EAAE,YAAY,KAAK,cAAc,GAAC,EAAK,CAAA;AAC1G,eAAK,cAAc;AAAA,QACnB,GAAE,GAAI;AAAA,MACR;AAAA,IACA;AAAA,IACA,qBAAqB;AAEtB,UAAI,KAAK,gBAAgB,eAAe,KAAK,qBAAqB,WAAW;AAC5EA,uBAAAA,UAAU,YAAY,EAAE,MAAM,+BAA+B,MAAM,EAAE,YAAY,KAAK,cAAc,GAAC,EAAK,CAAA;AAC1G,aAAK,cAAc;AAAA,MACpB;AAAA,IACA;AAAA,IACA,qBAAqB;AAEpB,UAAI,KAAK,iBAAiB;AACzBH,sBAAAA,MAAY,MAAA,OAAA,iCAAA,qBAAqB;AACjC;AAAA,MACD;AAGA,WAAK,kBAAkB;AAGvBG,qBAAAA,UAAU,YAAY,EAAE,MAAM,iBAAiB,MAAM,EAAE,YAAY,KAAK,cAAc,MAAM,EAC1F,KAAK,cAAY;AACjBH,sBAAY,MAAA,MAAA,OAAA,iCAAA,gBAAgB,QAAQ;AAEpC,aAAK,kBAAkB;AAAA,OACvB,EACA,MAAM,WAAS;AACfA,sBAAA,MAAA,MAAA,SAAA,iCAAc,gBAAgB,KAAK;AAEnC,aAAK,kBAAkB;AACvBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACP,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAAA,IACD,oBAAoB;AACnB,WAAK,kBAAkB;AACvB,WAAK,YAAY;AACjB,WAAK,cAAc;AACnB,WAAK,mBAAmB;AACxB,WAAK,eAAe;AACpB,WAAK,gBAAgB;AAErB,WAAK,kBAAkB;AACvB,WAAK,wBAAwB;AAC7B,WAAK,kBAAkB;AAAA,IACvB;AAAA,IACD,kBAAkB,MAAM;AAIvB,YAAM,YAAY;AAAA,QACjB,OAAO,KAAK,SAAS;AAAA,QACrB,UAAU,KAAK,YAAY;AAAA,QAC3B,UAAU,KAAK,YAAY;AAAA,QAC3B,QAAQ,KAAK,UAAU;AAAA,QACvB,MAAM,KAAK,QAAQ;AAAA,QACnB,MAAM,KAAK,QAAQ;AAAA,QACnB,SAAS,KAAK,WAAW;AAAA,QACzB,WAAW,KAAK,aAAa;AAAA,QAC7B,UAAU,KAAK,YAAY;AAAA,QAC3B,gBAAgB,KAAK,kBAAkB;AAAA,QACvC,aAAa,KAAK,eAAe;AAAA,QACjC,YAAW,oBAAI,QAAO,mBAAmB,SAAS;AAAA,UACjD,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,SACR;AAAA;AAEF,WAAK,UAAU,KAAK,SAAS;AAK7B,UAAI,OAAO,KAAK,cAAc;AAAa,aAAK,aAAa,KAAK,KAAK;AACvE,UAAI,OAAO,KAAK,aAAa;AAAa,aAAK,cAAc,KAAK,KAAK;AACvE,UAAI,OAAO,KAAK,iBAAiB;AAAa,aAAK,cAAc,SAAS,KAAK;AAAA,IAC/E;AAAA,IACD,mBAAmB,MAAM;AAExB,UAAI,KAAK,uBAAuB;AAC/BA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,qBAAqB;AACjC;AAAA,MACD;AAGA,UAAI,KAAK,iBAAiB;AACzBA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,sBAAsB;AAClC;AAAA,MACD;AAGA,WAAK,wBAAwB;AAG7B,WAAK,cAAc;AAGnB,YAAM,kBAAkB;AAAA,QACvB,YAAW,oBAAI,QAAO,mBAAmB,SAAS,EAAE,QAAQ,OAAO;AAAA,QACnE,MAAM;AAAA,QACN,aAAa,KAAK,MACjB,OAAO,KAAK,cAAc,QAAQ,IAAI,WACtC,KAAK,KAAK,cAAc,QAAQ,IAAI;AAAA;AAEtCC,sBAAAA,UAAU,SAAS,QAAQ,eAAe;AAE1C,UAAIA,0BAAU,SAAS,SAAS;AAAIA,wBAAS,UAAC,WAAWA,gBAAAA,UAAU,SAAS,MAAM,GAAG,EAAE;AACvFA,gCAAU,aAAa,UAAU;AAGjC,UAAI,KAAK,OAAO,KAAK,SAAS;AAC7B,cAAM,UAAU,KAAK;AACrB,YAAI,aAAa;AAGjB,YAAI,QAAQ,KAAK,GAAG;AACnB,wBAAc,QAAQ,QAAQ,KAAK,CAAC;AAAA;AAAA,QACrC;AAGA,YAAI,QAAQ,IAAI,GAAG;AAClB,wBAAc,OAAO,QAAQ,IAAI,CAAC;AAAA;AAAA,QACnC;AAGA,YAAI,QAAQ,IAAI,KAAK,QAAQ,IAAI,EAAE,SAAS,GAAG;AAC9C,wBAAc;AACd,kBAAQ,IAAI,EAAE,QAAQ,UAAQ;AAC7B,0BAAc,KAAK,KAAK,EAAE,KAAK,KAAK,QAAQ;AAAA;AAAA,UAC7C,CAAC;AAAA,QACF;AAGA,YAAI,QAAQ,KAAK,GAAG;AACnB,wBAAc,QAAQ,QAAQ,KAAK,CAAC;AAAA;AAAA,QACrC;AAGA,mBAAW,MAAM;AAChBD,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,aAAa;AAAA,UACd,CAAC;AAAA,QACD,GAAE,GAAI;AAAA,MACR,WAAW,CAAC,KAAK,KAAK;AAErB,mBAAW,MAAM;AAChBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,aAAa;AAAA,UACd,CAAC;AAAA,QACD,GAAE,GAAI;AAAA,MACR;AAAA,IACA;AAAA,IACD,uBAAuB,MAAM;AAC5BA,oBAAY,MAAA,MAAA,OAAA,iCAAA,yBAAyB,IAAI;AACzCA,0BAAA,MAAA,OAAA,iCAAY,sBAAsB,KAAK,OAAO,EAAE;AAGhD,UAAI,KAAK,mBAAmB,KAAK,gBAAgB,UAAU;AAC1D,aAAK,eAAe,EAAE,GAAG,KAAK,cAAc,GAAG;MAChD;AAEA,WAAK,SAAS,EAAE,GAAG,KAAK,QAAQ,GAAG;AAEnCA,0BAAA,MAAA,OAAA,iCAAY,sBAAsB,KAAK,OAAO,EAAE;AAChDA,0BAAA,MAAA,OAAA,iCAAY,+BAA+B,KAAK,OAAO,WAAW;AAGlE,WAAK,aAAY;AAAA,IACjB;AAAA,IACD,yBAAyB,MAAM;AAE9B,UAAI,KAAK,iBAAiB;AACzBA,sBAAAA,MAAY,MAAA,OAAA,iCAAA,qBAAqB;AACjC;AAAA,MACD;AAEA,UAAI,KAAK,SAAS;AAEjB,aAAK,kBAAkB;AAGvB,cAAM,YAAY;AAAA,UACjB,OAAO,KAAK,UAAU,SAAS;AAAA,UAC/B,UAAU;AAAA,UACV,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,MAAM,KAAK,WAAW;AAAA,UACtB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,WAAW,KAAK,aAAa;AAAA,UAC7B,UAAU,KAAK,cAAc;AAAA,UAC7B,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,YAAW,oBAAI,QAAO,mBAAmB,SAAS;AAAA,YACjD,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,WACR;AAAA;AAEF,aAAK,UAAU,KAAK,SAAS;AAG7B,cAAM,kBAAkB;AAAA,UACvB,YAAW,oBAAI,QAAO,mBAAmB,SAAS,EAAE,QAAQ,OAAO;AAAA,UACnE,MAAM;AAAA,UACN,aAAa,OAAO,KAAK,cAAc,QAAQ,IAAI;AAAA;AAEpDC,wBAAAA,UAAU,SAAS,QAAQ,eAAe;AAE1C,YAAIA,0BAAU,SAAS,SAAS;AAAIA,0BAAS,UAAC,WAAWA,gBAAAA,UAAU,SAAS,MAAM,GAAG,EAAE;AACvFA,kCAAU,aAAa,UAAU;AAGjC,mBAAW,MAAM;AAChB,eAAK,kBAAkB;AAEvB,eAAK,kBAAkB;AAAA,QACvB,GAAE,GAAI;AAAA,aACD;AAEN,cAAM,YAAY;AAAA,UACjB,OAAO,KAAK,UAAU,SAAS;AAAA,UAC/B,UAAU;AAAA,UACV,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,MAAM,KAAK,WAAW;AAAA,UACtB,MAAM;AAAA,UACN,SAAS;AAAA,UACT,WAAW,KAAK,aAAa;AAAA,UAC7B,UAAU,KAAK,cAAc;AAAA,UAC7B,gBAAgB;AAAA,UAChB,aAAa;AAAA,UACb,YAAW,oBAAI,QAAO,mBAAmB,SAAS;AAAA,YACjD,QAAQ;AAAA,YACR,MAAM;AAAA,YACN,QAAQ;AAAA,YACR,QAAQ;AAAA,WACR;AAAA;AAEF,aAAK,UAAU,KAAK,SAAS;AAAA,MAC9B;AAAA,IACA;AAAA,IACD,MAAM,gBAAgB;AACrB,UAAI;AACH,aAAK,4BAA4B;AACjC,aAAK,2BAA2B;AAChCD,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAW,CAAA;AACpCE,wBAAAA,UAAU,YAAY,WAAW,CAAA,CAAE;AAAA,MAClC,SAAO,OAAO;AACf,aAAK,2BAA2B;AAChCF,sBAAG,MAAC,YAAW;AACfA,4BAAI,UAAU,EAAE,OAAO,WAAW,MAAM,SAAS,MAAM,OAAO,CAAC;AAAA,MAChE;AAAA,IACA;AAAA,IACD,MAAM,mBAAmB;AACxB,UAAI;AACH,aAAK,4BAA4B;AACjC,aAAK,2BAA2B;AAChCA,sBAAAA,MAAI,YAAY,EAAE,OAAO,UAAW,CAAA;AACpCE,wBAAAA,UAAU,YAAY,cAAc,CAAA,CAAE;AAAA,MACrC,SAAO,OAAO;AACf,aAAK,2BAA2B;AAChCF,sBAAG,MAAC,YAAW;AACfA,4BAAI,UAAU,EAAE,OAAO,WAAW,MAAM,SAAS,MAAM,OAAO,CAAC;AAAA,MAChE;AAAA,IACA;AAAA,IACD,iBAAiB,MAAM;AACtB,UAAI,QAAQ,KAAK,SAAS;AACzB,aAAK,0BAA0B,KAAK,KAAK,OAAO;AAChD,YAAI,KAAK,0BAA0B,SAAS,IAAI;AAC/C,eAAK,4BAA4B,KAAK,0BAA0B,MAAM,GAAG;AAAA,QAC1E;AACA,aAAK,2BAA2B,CAAC,aAAa,KAAK,KAAK,OAAO;AAAA,MAChE;AAAA,IACA;AAAA,IACD,oBAAoB,MAAM;AACzB,UAAI,QAAQ,KAAK,SAAS;AACzB,aAAK,0BAA0B,KAAK,KAAK,OAAO;AAChD,YAAI,KAAK,0BAA0B,SAAS,IAAI;AAC/C,eAAK,4BAA4B,KAAK,0BAA0B,MAAM,GAAG;AAAA,QAC1E;AACA,aAAK,2BAA2B,CAAC,aAAa,KAAK,KAAK,OAAO;AAAA,MAChE;AAAA,IACA;AAAA,EACD;AAAA,EACD,UAAU;AAETC,oBAAAA,UAAU,kBAAiB,EAAG,KAAK,YAAU;AAC5C,WAAK,aAAa;AAClBD,oBAAA,MAAA,MAAA,OAAA,iCAAY,iBAAiB,MAAM;AAAA,IACpC,CAAC;AACDE,oBAAAA,UAAU,GAAG,wBAAwB,KAAK,wBAAwB;AAClEA,oBAAAA,UAAU,GAAG,eAAe,KAAK,gBAAgB;AACjDA,oBAAAA,UAAU,GAAG,kBAAkB,KAAK,mBAAmB;AAAA,EACxD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvmDA,GAAG,WAAW,eAAe;"}