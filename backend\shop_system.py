import logging
from item_system import load_items_config

class ShopSystem:
    def __init__(self, server=None):
        # 商店配置：key为NPC id，value为售卖物品及价格
        self.shops = {
            'changan_coalboss': [
                # 基础武器
                {'item_id': 'chitie_jian', 'price': 30, 'stock': 999},
                {'item_id': 'qingtong_jian', 'price': 45, 'stock': 999},
                # 基础防具
                {'item_id': 'langpiyi', 'price': 25, 'stock': 999},
                # 基础消耗品
                {'item_id': 'heal_potion', 'price': 50, 'stock': 999},
                {'item_id': 'pojingdan', 'price': 100, 'stock': 999},
            ],
            'changan_baixiaosheng': [
                {'item_id': 'info_scroll', 'price': 200, 'stock': 10},
            ],
            # 可继续扩展其他NPC
        }
        self.server = server  # 需传入server实例

    def get_shop_items(self, npc_id):
        """返回指定NPC的售卖物品详细信息（含物品属性），自动加日志"""
        logger = logging.getLogger("shop_system")
        items = []
        items_config = load_items_config()
        for entry in self.shops.get(npc_id, []):
            item_obj = items_config.get(entry['item_id'])
            if not item_obj:
                logger.warning(f"[装备商] 未找到物品: npc_id={npc_id}, item_id={entry['item_id']}")
            else:
                logger.info(f"[装备商] 物品加载成功: npc_id={npc_id}, item_id={item_obj['id']}, name={item_obj['name']}")
                items.append({
                    'id': item_obj['id'],
                    'name': item_obj['name'],
                    'desc': item_obj.get('描述', ''),
                    'icon': item_obj.get('图标', ''),
                    'type': item_obj.get('类型', ''),
                    'quality': item_obj.get('品质', ''),
                    'price': entry['price'],
                    'stock': entry['stock'],
                })
        logger.info(f"[装备商] npc_id={npc_id} 返回商品数: {len(items)}")
        return items

    async def buy_item(self, player, user_id, npc_id, item_id, quantity):
        """玩家购买物品，统一走 add_item_to_inventory"""
        items_config = load_items_config()
        entry = next((e for e in self.shops.get(npc_id, []) if e['item_id'] == item_id), None)
        if not entry:
            return {'type': 'error', 'data': {'message': '该NPC不出售此物品'}}
        if entry['stock'] < quantity:
            return {'type': 'error', 'data': {'message': '库存不足'}}
        item_obj = items_config.get(item_id)
        if not item_obj:
            return {'type': 'error', 'data': {'message': '物品不存在'}}
        total_price = entry['price'] * quantity
        if player.get('money', 0) < total_price:
            return {'type': 'error', 'data': {'message': '银两不足'}}
        # 扣钱
        player['money'] -= total_price
        # 发放物品（标准化）
        if self.server:
            success = await self.server.add_item_to_inventory(user_id, player, {'id': item_id, 'quantity': quantity})
            if not success:
                return {'type': 'error', 'data': {'message': '背包已满，无法获得新物品'}}
        entry['stock'] -= quantity
        return {'type': 'success', 'data': {'message': f'成功购买{item_obj["name"]} x{quantity}'}}

    async def sell_item(self, player, user_id, item_id, quantity):
        """玩家向NPC商店出售物品，返还银两，移除物品"""
        if not item_id or quantity < 1:
            return {'type': 'error', 'data': {'message': '参数错误'}}
        items_config = load_items_config()
        item_obj = items_config.get(item_id)
        if not item_obj:
            return {'type': 'error', 'data': {'message': '物品不存在'}}
        inventory = player.get('inventory', [])
        exist_item = next((i for i in inventory if i['id'] == item_id), None)
        if not exist_item or exist_item.get('quantity', 0) < quantity:
            return {'type': 'error', 'data': {'message': '背包中该物品数量不足'}}
        sell_price = int(int(item_obj.get('价格', 0)) * 0.5) * quantity
        if sell_price <= 0:
            return {'type': 'error', 'data': {'message': '该物品不可出售'}}
        exist_item['quantity'] -= quantity
        if exist_item['quantity'] <= 0:
            inventory.remove(exist_item)
        player['silver'] = player.get('silver', 0) + sell_price
        if self.server:
            await self.server.save_player_data(user_id, player)
        return {'type': 'success', 'data': {'message': f'成功出售{item_obj["name"]} x{quantity}，获得{sell_price}银两'}}

shop_system = ShopSystem() 