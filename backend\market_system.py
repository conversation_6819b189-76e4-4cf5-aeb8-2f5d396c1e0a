import asyncio
import json
import os
import uuid
import time
from item_utils import standardize_item

class MarketSystem:
    def __init__(self, server):
        self.server = server
        self.market_orders = []  # [{id, item, quantity, price, seller, seller_id, created_at, expires_at}]
        self.order_id_counter = 1
        self.orders_file = os.path.join(os.path.dirname(__file__), 'market_orders.json')
        self.load_orders()
        # 启动定时清理过期订单的任务
        asyncio.create_task(self.cleanup_expired_orders())

    def load_orders(self):
        if os.path.exists(self.orders_file):
            try:
                with open(self.orders_file, 'r', encoding='utf-8') as f:
                    self.market_orders = json.load(f)
                if self.market_orders:
                    self.order_id_counter = max(int(o['id']) for o in self.market_orders) + 1
                    # 为旧订单添加时间戳（如果没有的话）
                    current_time = time.time()
                    updated = False
                    for order in self.market_orders:
                        if 'created_at' not in order:
                            order['created_at'] = current_time
                            order['expires_at'] = current_time + (3 * 24 * 3600)  # 3天后过期
                            updated = True
                    if updated:
                        self.save_orders()
            except Exception as e:
                self.market_orders = []
                self.order_id_counter = 1
        else:
            self.market_orders = []
            self.order_id_counter = 1

    def save_orders(self):
        try:
            with open(self.orders_file, 'w', encoding='utf-8') as f:
                json.dump(self.market_orders, f, ensure_ascii=False, indent=2)
        except Exception as e:
            pass

    async def cleanup_expired_orders(self):
        """定时清理过期订单，每小时检查一次"""
        while True:
            try:
                current_time = time.time()
                expired_orders = []
                valid_orders = []

                for order in self.market_orders:
                    # 检查订单是否过期（3天 = 3 * 24 * 3600 秒）
                    expires_at = order.get('expires_at', 0)
                    if current_time > expires_at:
                        expired_orders.append(order)
                    else:
                        valid_orders.append(order)

                # 处理过期订单：返还物品给卖家
                for order in expired_orders:
                    await self.return_item_to_seller(order)
                    print(f"【市场】订单 {order['id']} 已过期，物品已返还给 {order['seller']}")

                # 更新订单列表
                if expired_orders:
                    self.market_orders = valid_orders
                    self.save_orders()
                    print(f"【市场】清理了 {len(expired_orders)} 个过期订单")

            except Exception as e:
                print(f"【市场】清理过期订单时出错: {e}")

            # 每小时检查一次
            await asyncio.sleep(3600)

    async def return_item_to_seller(self, order):
        """将过期订单的物品返还给卖家"""
        try:
            seller_id = str(order['seller_id'])
            seller_player = self.server.player_data.get(seller_id)

            if seller_player:
                # 构造要返还的物品
                item_to_return = dict(order['item'])
                item_to_return['quantity'] = order['quantity']

                # 添加到卖家背包
                await self.server.add_item_to_inventory(seller_id, seller_player, {
                    'id': item_to_return['id'],
                    'quantity': item_to_return['quantity']
                })

                print(f"【市场】已将 {item_to_return['name']} x{item_to_return['quantity']} 返还给玩家 {order['seller']}")
            else:
                print(f"【市场】无法找到卖家 {order['seller']} 的数据，物品丢失")

        except Exception as e:
            print(f"【市场】返还物品时出错: {e}")

    async def handle_market_action(self, data, websocket):
        action = data.get('action')
        user_id = getattr(websocket, 'user_id', None)
        print(f"[市场] 收到请求: action={action}, user_id={user_id}, data={data}")
        if not user_id:
            return {'type': 'error', 'data': {'message': '未认证'}}
        player = self.server.player_data.get(str(user_id))
        if not player:
            return {'type': 'error', 'data': {'message': '玩家数据不存在'}}
        if action == 'get_market_list':
            return {'type': 'market_list', 'data': {'list': self.market_orders}}
        elif action == 'list':
            # 限制每个玩家最多10个未完成订单
            user_orders = [o for o in self.market_orders if o['seller_id'] == user_id]
            if len(user_orders) >= 10:
                return {'type': 'error', 'data': {'message': '每个玩家最多只能上架10个物品'}}
            item_id = data.get('item_id')
            price = int(data.get('price', 0))
            quantity = int(data.get('quantity', 1))
            if price <= 0 or quantity <= 0:
                return {'type': 'error', 'data': {'message': '价格和数量需大于0'}}
            # 计算上架手续费
            fee = int(price * quantity * 0.2)
            if player.get('money', 0) < fee:
                return {'type': 'error', 'data': {'message': f'银两不足，上架需手续费{fee}银两'}}
            # 在玩家背包中查找物品
            inv_item = next((i for i in player['inventory'] if i.get('id') == item_id), None)
            if not inv_item or inv_item.get('quantity', 1) < quantity:
                return {'type': 'error', 'data': {'message': '物品不存在或数量不足'}}
            # 扣除银两和物品
            player['money'] -= fee
            if inv_item['quantity'] == quantity:
                player['inventory'].remove(inv_item)
            else:
                inv_item['quantity'] -= quantity
            await self.server.save_player_data(str(user_id), player)
            # 生成市场订单
            item_for_order = {k: v for k, v in inv_item.items() if k != 'quantity'}
            item_for_order = standardize_item(item_for_order)
            if 'unique_id' not in item_for_order or not item_for_order['unique_id']:
                item_for_order['unique_id'] = str(uuid.uuid4())

            current_time = time.time()
            expires_at = current_time + (3 * 24 * 3600)  # 3天后过期

            order = {
                'id': self.order_id_counter,  # 订单ID为数字
                'item': item_for_order,
                'quantity': quantity,
                'price': price,
                'seller': player.get('name', '无名侠客'),
                'seller_id': user_id,
                'created_at': current_time,
                'expires_at': expires_at
            }
            self.order_id_counter += 1
            self.market_orders.append(order)
            self.save_orders()
            result = {'type': 'success', 'data': {'message': f'上架成功，已收取{fee}银两手续费'}}
            print(f"[市场] 上架成功，返回: {result}")
            return result
        elif action == 'buy':
            order_id = data.get('order_id')
            try:
                order_id = int(order_id)
            except Exception:
                return {'type': 'error', 'data': {'message': '订单ID格式错误'}}
            order = next((o for o in self.market_orders if o['id'] == order_id), None)
            if not order:
                return {'type': 'error', 'data': {'message': '订单不存在'}}
            if player.get('money', 0) < order['price']:
                return {'type': 'error', 'data': {'message': '银两不足'}}
            # 买家扣钱
            player['money'] -= order['price']
            # 买家加物品（标准化，统一走 add_item_to_inventory）
            item = dict(order['item'])
            item['quantity'] = order['quantity']
            if self.server:
                await self.server.add_item_to_inventory(user_id, player, {'id': item['id'], 'quantity': item['quantity']})
            # 卖家到账80%
            seller_player = self.server.player_data.get(str(order['seller_id']))
            seller_income = int(order['price'] * 0.8)
            if seller_player:
                seller_player['money'] = seller_player.get('money', 0) + seller_income
                await self.server.save_player_data(str(order['seller_id']), seller_player)
            # 删除订单
            self.market_orders = [o for o in self.market_orders if o['id'] != order_id]
            self.save_orders()
            return {'type': 'success', 'data': {'message': f'购买成功，卖家获得{seller_income}银两'}}
        elif action == 'unlist':
            # 下架物品
            order_id = data.get('order_id')
            try:
                order_id = int(order_id)
            except Exception:
                return {'type': 'error', 'data': {'message': '订单ID格式错误'}}

            order = next((o for o in self.market_orders if o['id'] == order_id), None)
            if not order:
                return {'type': 'error', 'data': {'message': '订单不存在'}}

            # 检查是否是订单的卖家
            if str(order['seller_id']) != str(user_id):
                return {'type': 'error', 'data': {'message': '只能下架自己的订单'}}

            # 返还物品给卖家
            await self.return_item_to_seller(order)

            # 删除订单
            self.market_orders = [o for o in self.market_orders if o['id'] != order_id]
            self.save_orders()

            return {'type': 'success', 'data': {'message': '下架成功，物品已返还到背包'}}
        else:
            return {'type': 'error', 'data': {'message': '未知市场操作'}}