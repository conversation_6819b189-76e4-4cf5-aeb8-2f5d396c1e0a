#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的武功数据结构
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from enhanced_martial_system import load_wugong_config, get_martial_info

def test_martial_data_structure():
    """测试武功数据结构"""
    print("=== 测试武功数据结构 ===")
    
    # 加载武功配置
    martials = load_wugong_config()
    
    # 模拟玩家武功数据
    player_martial_skills = [
        {'name': '基本剑法', 'level': 1, 'exp': 10, 'unlocked': True, 'equipped': False},
        {'name': '基本刀法', 'level': 1, 'exp': 10, 'unlocked': True, 'equipped': False},
        {'name': '基本拳法', 'level': 1, 'exp': 10, 'unlocked': True, 'equipped': False},
        {'name': '独孤九剑', 'level': 0, 'exp': 0, 'unlocked': True, 'equipped': False},
        {'name': '采药', 'level': 1, 'exp': 10, 'unlocked': True, 'equipped': False},
        {'name': '伐木', 'level': 1, 'exp': 10, 'unlocked': True, 'equipped': False}
    ]
    
    # 按武功类型分组
    grouped_skills = {
        '剑法': [],
        '刀法': [],
        '拳法': [],
        '空手': [],
        '招架': [],
        '轻功': [],
        '内功': [],
        '暗器': [],
        '生活技能': []
    }
    
    for skill in player_martial_skills:
        skill_name = skill['name']
        skill_data = skill
        # 使用增强武功系统获取武功信息
        martial_info = get_martial_info(skill_name)
        if martial_info:
            # 使用增强武功系统的系数计算经验值
            from enhanced_martial_system import get_martial_coefficient
            coefficient = get_martial_coefficient(skill_name)
            current_level = skill_data.get('level', 0)
            max_exp = coefficient * (current_level + 1) ** 2
            quality = martial_info.get('quality', '普通')
            martial_type = martial_info.get('type', '未知')
        else:
            # 兼容旧系统，直接用默认经验值
            current_level = skill_data.get('level', 0)
            max_exp = 50 * (current_level + 1) ** 2
            quality = '普通'
            martial_type = '未知'
        
        skill_info = {
            'name': skill_name,
            'level': skill_data.get('level', 0),
            'exp': skill_data.get('exp', 0),
            'unlocked': skill_data.get('unlocked', False),
            'max_exp': max_exp,
            'equipped': skill_data.get('equipped', False),
            'quality': quality,  # 添加品质信息
            'type': martial_type  # 添加类型信息
        }
        
        # 按武功类型分组
        if skill_name in ['采药', '伐木', '挖矿', '剥皮']:
            # 生活技能单独分组
            grouped_skills['生活技能'].append(skill_info)
        elif martial_type in grouped_skills:
            # 按武功类型分组
            grouped_skills[martial_type].append(skill_info)
        else:
            # 未知类型的武功暂时归类为生活技能
            grouped_skills['生活技能'].append(skill_info)
    
    print("按武功类型分组结果:")
    for martial_type, skills in grouped_skills.items():
        if skills:  # 只显示有武功的类型
            print(f"{martial_type}: {len(skills)} 个")
            for skill in skills:
                print(f"  - {skill['name']} (类型: {skill['type']}, 品质: {skill['quality']})")
            print()

if __name__ == "__main__":
    test_martial_data_structure() 