/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color:#333;//基本色
$uni-text-color-inverse:#fff;//反色
$uni-text-color-grey:#999;//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable:#c0c0c0;

/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:12px;
$uni-font-size-base:14px;
$uni-font-size-lg:16px;

/* 图片尺寸 */
$uni-img-size-sm:20px;
$uni-img-size-base:26px;
$uni-img-size-lg:40px;

/* Border Radius */
$uni-border-radius-sm: 2px;
$uni-border-radius-base: 3px;
$uni-border-radius-lg: 6px;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 5px;
$uni-spacing-row-base: 10px;
$uni-spacing-row-lg: 15px;

/* 垂直间距 */
$uni-spacing-col-sm: 4px;
$uni-spacing-col-base: 8px;
$uni-spacing-col-lg: 12px;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:20px;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:26px;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:15px;

/* 确保输入框可点击 */
input {
  cursor: text;
  color: #333;
  background-color: #fff;
  z-index: 100;
  position: relative;
  width: 100%;
  height: 80rpx;
  box-sizing: border-box;
  padding: 0 30rpx;
  font-size: 28rpx;
  border-radius: 20rpx;
}

input:focus {
  border-color: #667eea !important;
  outline: none;
  z-index: 101;
}

/* 修复小程序输入框样式 */
.uni-input-input {
  position: relative;
  z-index: 100;
  background-color: #fff;
  color: #333;
}

/* 确保输入框在小程序中可点击 */
.uni-input-wrapper {
  position: relative;
  z-index: 99;
}

/* 修复微信小程序输入框特定问题 */
.wx-input {
  z-index: 200 !important;
  position: relative !important;
}

page input {
  z-index: 200;
}

/* 确保登录页面输入框在最上层 */
.login-form input,
.register-form input {
  z-index: 1000;
}

/* 自定义底部导航栏样式 */
.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea, #764ba2);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 8rpx;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.uni-tabbar .uni-tabbar__item:nth-child(3) .uni-tabbar__label {
	color: #667eea;
	font-weight: bold;
}

/* 全局样式 */
page {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	min-height: 100vh;
	font-family: 'Microsoft YaHei', sans-serif;
}

/* TabBar美化样式 */
.tab-bar {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
	border-top: none !important;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1) !important;
}

.tab-bar-item {
	position: relative;
	transition: all 0.3s ease;
}

.tab-bar-item.active {
	transform: scale(1.1);
}

/* 特殊处理"闯"按钮 */
.tab-bar-item:nth-child(3) {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	border-radius: 50%;
	margin: -10rpx 0;
	box-shadow: 0 4rpx 15rpx rgba(255, 107, 107, 0.4);
	transform: scale(1.2);
}

.tab-bar-item:nth-child(3).active {
	background: linear-gradient(135deg, #ff5252 0%, #d63031 100%);
	transform: scale(1.3);
	box-shadow: 0 6rpx 20rpx rgba(255, 107, 107, 0.6);
}

.tab-bar-item:nth-child(3) .tab-bar-text {
	color: #fff !important;
	font-weight: bold;
	font-size: 32rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 其他按钮样式 */
.tab-bar-item:not(:nth-child(3)) .tab-bar-text {
	color: rgba(255, 255, 255, 0.8) !important;
	font-size: 24rpx;
}

.tab-bar-item:not(:nth-child(3)).active .tab-bar-text {
	color: #fff !important;
	font-weight: bold;
}

/* 容器样式 */
.container {
	padding: 20rpx;
	padding-bottom: 140rpx; /* 为tabBar留出空间 */
}

/* 顶部信息栏 */
.top-info-bar {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
	gap: 10rpx;
}

.player-name {
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.info-sep {
	color: rgba(255, 255, 255, 0.6);
	font-size: 24rpx;
}

.player-level {
	color: #ffd700;
	font-size: 28rpx;
	font-weight: bold;
}

.player-exp {
	color: #87ceeb;
	font-size: 24rpx;
}

.conn-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	display: inline-block;
	margin-right: 8rpx;
}

.conn-dot.connected {
	background: #4caf50;
	box-shadow: 0 0 10rpx rgba(76, 175, 80, 0.5);
}

.conn-dot.disconnected {
	background: #ff9800;
	box-shadow: 0 0 10rpx rgba(255, 152, 0, 0.5);
}

.conn-dot.failed {
	background: #f44336;
	box-shadow: 0 0 10rpx rgba(244, 67, 54, 0.5);
}

.conn-text {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
}

.player-attr {
	color: #ffb74d;
	font-size: 24rpx;
}

.attr-sep {
	color: rgba(255, 255, 255, 0.4);
	font-size: 20rpx;
}

/* 地图栏 */
.map-bar {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	flex-wrap: wrap;
	gap: 10rpx;
}

.map-label {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
}

.map-name {
	color: #fff;
	font-size: 28rpx;
	font-weight: bold;
}

.map-npc {
	color: rgba(255, 255, 255, 0.6);
	font-size: 20rpx;
}

.map-btn {
	background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
	color: #fff;
	border: none;
	border-radius: 25rpx;
	padding: 10rpx 20rpx;
	font-size: 24rpx;
	font-weight: bold;
	box-shadow: 0 4rpx 15rpx rgba(76, 175, 80, 0.3);
}

/* 角色卡片 */
.character-card {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.progress-bar {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.progress-label {
	color: #fff;
	font-size: 24rpx;
	font-weight: bold;
	width: 80rpx;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.progress-bg {
	flex: 1;
	height: 20rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 10rpx;
	margin: 0 20rpx;
	overflow: hidden;
	position: relative;
}

.progress-fill {
	height: 100%;
	border-radius: 10rpx;
	transition: width 0.3s ease;
	position: relative;
}

.progress-fill::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
	animation: shimmer 2s infinite;
}

@keyframes shimmer {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

.hp-fill {
	background: linear-gradient(90deg, #ff6b6b 0%, #ee5a24 100%);
}

.mp-fill {
	background: linear-gradient(90deg, #4ecdc4 0%, #44a08d 100%);
}

.stamina-fill {
	background: linear-gradient(90deg, #ffd93d 0%, #ff6b6b 100%);
}

.energy-fill {
	background: linear-gradient(90deg, #a8edea 0%, #fed6e3 100%);
}

.progress-text {
	color: #fff;
	font-size: 20rpx;
	font-weight: bold;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 属性面板 */
.stats-panel {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.stats-row {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

.stats-row:last-child {
	margin-bottom: 0;
}

.stat-item {
	text-align: center;
	flex: 1;
}

.stat-label {
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
	display: block;
	margin-bottom: 10rpx;
}

.stat-value {
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

/* 状态显示 */
.status-display {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
	text-align: center;
}

.status-text {
	font-size: 28rpx;
	font-weight: bold;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.status-injured {
	color: #ff6b6b;
}

.status-exhausted {
	color: #ffd93d;
}

.status-normal {
	color: #4caf50;
}

/* 操作按钮 */
.action-buttons {
	display: flex;
	justify-content: center;
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.action-btn {
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	font-weight: bold;
	box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
	transition: all 0.3s ease;
}

.action-btn:active {
	transform: scale(0.95);
}

.action-btn:disabled {
	opacity: 0.5;
	transform: none;
}

.secondary-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
}

.btn-text {
	color: inherit;
	font-weight: bold;
}

/* 事件日志 */
.event-log {
	background: rgba(255, 255, 255, 0.1);
	backdrop-filter: blur(10rpx);
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	flex: 1;
}

.log-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.log-title {
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.log-clear {
	color: rgba(255, 255, 255, 0.6);
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	border-radius: 15rpx;
	background: rgba(255, 255, 255, 0.1);
}

.log-content {
	height: 400rpx;
}

.log-item {
	background: rgba(255, 255, 255, 0.05);
	border-radius: 15rpx;
	padding: 20rpx;
	margin-bottom: 15rpx;
	border-left: 4rpx solid #4caf50;
}

.log-time {
	color: rgba(255, 255, 255, 0.6);
	font-size: 20rpx;
	display: block;
	margin-bottom: 8rpx;
}

.log-event {
	color: #fff;
	font-size: 24rpx;
	font-weight: bold;
	display: block;
	margin-bottom: 8rpx;
}

.log-desc {
	color: rgba(255, 255, 255, 0.8);
	font-size: 22rpx;
	line-height: 1.4;
}

.log-empty {
	text-align: center;
	padding: 60rpx 20rpx;
	color: rgba(255, 255, 255, 0.5);
	font-size: 24rpx;
}

/* 地图弹窗 */
.map-popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.map-popup {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-radius: 20rpx;
	width: 90%;
	max-height: 80%;
	overflow: hidden;
	box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.map-popup-title {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	padding: 30rpx;
	text-align: center;
}

.map-list {
	max-height: 600rpx;
	padding: 20rpx;
}

.map-item {
	background: rgba(255, 255, 255, 0.8);
	border-radius: 15rpx;
	padding: 20rpx;
	margin-bottom: 15rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
}

.map-item.active {
	border-color: #4caf50;
	background: rgba(76, 175, 80, 0.1);
}

.map-item.locked {
	opacity: 0.5;
	background: rgba(0, 0, 0, 0.1);
}

.map-item-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.map-item-desc {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 10rpx;
}

.map-item-npc {
	font-size: 22rpx;
	color: #888;
	margin-bottom: 10rpx;
}

.map-item-req {
	font-size: 20rpx;
	color: #999;
}

.close-btn {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	color: #fff;
	border: none;
	border-radius: 25rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	font-weight: bold;
	margin: 20rpx;
	width: calc(100% - 40rpx);
}
