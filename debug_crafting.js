// 调试打造系统材料匹配问题
// 在uni-app小程序控制台运行此脚本

function debugCraftingMaterials() {
    console.log('调试打造系统材料匹配');
    
    // 获取游戏状态数据
    var gameState = null;
    var craftingPage = null;
    
    // 在uni-app小程序环境中获取数据
    try {
        // 方法1: 从当前页面获取数据
        var pages = getCurrentPages();
        var currentPage = pages[pages.length - 1];
        
        if (currentPage) {
            console.log('当前页面:', currentPage.route);
            
            // 尝试从页面数据中获取
            if (currentPage.data && currentPage.data.gameState) {
                gameState = currentPage.data.gameState;
                console.log('从页面数据获取到gameState');
            }
            
            // 尝试从页面实例中获取
            if (currentPage.gameState) {
                gameState = currentPage.gameState;
                console.log('从页面实例获取到gameState');
            }
            
            // 如果是打造页面，获取craftableItems
            if (currentPage.route && currentPage.route.indexOf('crafting') !== -1) {
                craftingPage = currentPage;
                console.log('找到打造页面');
                
                // 尝试从打造页面获取数据
                if (currentPage.player) {
                    console.log('从打造页面获取到player数据');
                }
                if (currentPage.craftableItems) {
                    console.log('从打造页面获取到craftableItems数据，数量:', currentPage.craftableItems.length);
                }
            }
        }
        
        // 方法2: 从全局数据获取
        if (!gameState && typeof getApp !== 'undefined') {
            var app = getApp();
            if (app && app.globalData && app.globalData.gameState) {
                gameState = app.globalData.gameState;
                console.log('从全局数据获取到gameState');
            }
        }
        
        // 方法3: 从本地存储获取
        if (!gameState && typeof uni !== 'undefined') {
            try {
                var storedData = uni.getStorageSync('gameState');
                if (storedData) {
                    gameState = storedData;
                    console.log('从本地存储获取到gameState');
                }
            } catch (e) {
                console.log('本地存储获取失败:', e);
            }
        }
        
        // 方法4: 从window对象获取
        if (!gameState && typeof window !== 'undefined' && window.gameState) {
            gameState = window.gameState;
            console.log('从window对象获取到gameState');
        }
        
        // 方法5: 尝试直接访问gameState模块
        if (!gameState && typeof window !== 'undefined') {
            // 尝试从页面实例中获取gameState模块
            if (currentPage && currentPage.$vm && currentPage.$vm.gameState) {
                gameState = currentPage.$vm.gameState;
                console.log('从页面$vm获取到gameState');
            }
        }
        
    } catch (error) {
        console.error('获取数据时出错:', error);
    }
    
    // 如果还是无法获取gameState，尝试从打造页面直接获取数据
    if (!gameState && craftingPage) {
        console.log('尝试从打造页面直接获取数据...');
        
        // 显示打造页面的数据结构
        console.log('打造页面数据结构:');
        console.log('- player:', craftingPage.player);
        console.log('- craftableItems:', craftingPage.craftableItems);
        console.log('- data:', craftingPage.data);
        
        // 尝试从打造页面的player数据中获取背包信息
        if (craftingPage.player && craftingPage.player.inventory) {
            gameState = { inventory: craftingPage.player.inventory };
            console.log('从打造页面player获取到inventory数据');
        }
    }
    
    if (!gameState) {
        console.error('无法获取游戏状态数据');
        console.log('请尝试以下方法:');
        console.log('1. 确保在游戏页面中运行此脚本');
        console.log('2. 检查页面是否正确加载了游戏数据');
        console.log('3. 尝试刷新页面后再次运行');
        console.log('4. 检查打造页面是否正确加载了gameState模块');
        return;
    }
    
    console.log('成功获取游戏状态数据');
    
    // 1. 检查背包中的残页
    console.log('1. 背包中的残页:');
    var canyeItems = [];
    if (gameState.inventory) {
        for (var i = 0; i < gameState.inventory.length; i++) {
            var item = gameState.inventory[i];
            if (item.name && item.name.indexOf('残页') !== -1) {
                canyeItems.push(item);
            }
        }
    }
    
    for (var i = 0; i < canyeItems.length; i++) {
        var item = canyeItems[i];
        console.log('  - ' + item.name + ' (ID: ' + item.id + ') x' + item.quantity);
    }
    
    // 2. 检查可合成物品的配方
    console.log('2. 可合成物品配方:');
    var craftableItems = [];
    if (craftingPage && craftingPage.craftableItems) {
        craftableItems = craftingPage.craftableItems;
    }
    
    if (craftableItems.length === 0) {
        console.log('  未找到可合成物品，可能原因:');
        console.log('  - 打造页面未正确加载');
        console.log('  - 后端未返回可合成物品数据');
        console.log('  - 需要先调用loadCraftableItems()方法');
    } else {
        for (var i = 0; i < craftableItems.length; i++) {
            var item = craftableItems[i];
            if (item.craft_recipe) {
                console.log('  - ' + item.name + ': ' + item.craft_recipe + ' (可合成: ' + item.can_craft + ')');
            }
        }
    }
    
    // 3. 测试材料匹配
    console.log('3. 材料匹配测试:');
    for (var i = 0; i < craftableItems.length; i++) {
        var item = craftableItems[i];
        if (item.craft_recipe && item.name.indexOf('秘籍') !== -1) {
            var materials = parseRecipe(item.craft_recipe);
            console.log('  ' + item.name + ':');
            for (var material in materials) {
                if (materials.hasOwnProperty(material)) {
                    var quantity = materials[material];
                    var count = getMaterialCount(material);
                    var match = count >= quantity ? '匹配' : '不匹配';
                    console.log('    ' + material + ': 需要' + quantity + ', 现有' + count + ', ' + match);
                }
            }
        }
    }
    
    // 4. 显示背包所有物品
    console.log('4. 背包所有物品:');
    if (gameState.inventory && gameState.inventory.length > 0) {
        for (var i = 0; i < gameState.inventory.length; i++) {
            var item = gameState.inventory[i];
            console.log('  - ' + item.name + ' (ID: ' + item.id + ') x' + item.quantity);
        }
    } else {
        console.log('  背包为空或未找到背包数据');
    }
    
    // 5. 显示游戏状态基本信息
    console.log('5. 游戏状态信息:');
    console.log('  玩家ID: ' + (gameState.player_id || '未知'));
    console.log('  背包容量: ' + (gameState.inventory ? gameState.inventory.length : 0));
    
    // 6. 显示打造页面信息
    if (craftingPage) {
        console.log('6. 打造页面信息:');
        console.log('  体力: ' + (craftingPage.player ? craftingPage.player.energy : '未知'));
        console.log('  可合成物品数量: ' + (craftingPage.craftableItems ? craftingPage.craftableItems.length : 0));
    }
    
    // 辅助函数
    function parseRecipe(recipe) {
        var materials = {};
        if (!recipe) return materials;
        
        var parts = recipe.split(',');
        for (var i = 0; i < parts.length; i++) {
            var part = parts[i];
            if (part.indexOf(':') !== -1) {
                var materialParts = part.split(':');
                var material = materialParts[0].trim();
                var quantity = parseInt(materialParts[1].trim());
                materials[material] = quantity;
            }
        }
        return materials;
    }
    
    function getMaterialCount(materialName) {
        var count = 0;
        if (gameState.inventory) {
            for (var i = 0; i < gameState.inventory.length; i++) {
                var item = gameState.inventory[i];
                if (item.name === materialName) {
                    count += item.quantity || 1;
                }
            }
        }
        return count;
    }
}

// 运行调试
debugCraftingMaterials(); 