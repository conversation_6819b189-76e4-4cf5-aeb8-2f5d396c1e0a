import json
import os

base_dir = os.path.dirname(__file__)
input_file = os.path.join(base_dir, 'maps.txt')
output_file = os.path.join(base_dir, 'maps.json')

def parse_dict(s, vtype=float):
    d = {}
    for item in s.split(','):
        if ':' in item:
            k, v = item.split(':', 1)
            try:
                d[k.strip()] = vtype(v.strip())
            except:
                d[k.strip()] = v.strip()
    return d

def parse_list(s):
    return [x.strip() for x in s.split(',') if x.strip()]

maps = []
block = {}
prefix = None

with open(input_file, encoding='utf-8') as f:
    for line in f:
        line = line.strip()
        if not line or line.startswith('#'):
            if block:
                maps.append(block)
                block = {}
                prefix = None
            continue
        if '=' in line:
            k, v = line.split('=', 1)
            v = v.strip()
            if '_' in k:
                pf, key = k.split('_', 1)
                if not prefix:
                    prefix = pf
                if pf != prefix:
                    maps.append(block)
                    block = {}
                    prefix = pf
                k = key
            if k == '等级' or k == '历练要求':
                try:
                    block[k] = int(v)
                except:
                    block[k] = v
            elif k == '事件概率':
                block[k] = parse_dict(v, int)
            elif k == '采集物品':
                block[k] = parse_dict(v, float)
            elif k == 'NPC' or k == '怪物':
                block[k] = parse_list(v)
            elif k == '进入要求':
                block[k] = parse_dict(v, int) if v else {}
            else:
                block[k] = v
    if block:
        maps.append(block)

with open(output_file, 'w', encoding='utf-8') as f:
    json.dump(maps, f, ensure_ascii=False, indent=2)

print(f'已成功将 {input_file} 转换为 {output_file}，共 {len(maps)} 个地图。') 