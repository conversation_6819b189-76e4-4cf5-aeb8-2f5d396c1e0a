<template>
  <view class="gathering-popup-mask" @click.self="onClose">
    <view class="gathering-popup">
      <view class="gather-title">采集事件</view>
      <view class="gather-desc">{{ event.content }}</view>
      <view class="gather-info">
        <text>需要工具：</text>
        <text class="tool-type">{{ event.requiredToolDesc || event.toolName || toolNameMap[event.requiredTool] || event.requiredTool }}</text>
      </view>
      <view class="gather-info">
        <text>可采集次数：</text>
        <text class="gather-times">{{ times }}</text>
      </view>
      <view class="gather-result" v-if="result">{{ result }}</view>
      <view class="gather-btns">
        <button class="gather-btn" type="primary" @click.stop="onGather" :disabled="!hasRequiredTool || times<=0">采集</button>
        <button class="close-btn" @click.stop="onClose">关闭</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'GatheringPopup',
  props: {
    visible: Boolean,
    event: Object,
    times: Number,
    result: String,
    inventory: Array
  },
  data() {
    return {
      toolNameMap: {
        sickle: '镰刀',
        axe: '斧头',
        pickaxe: '矿镐',
        knife: '小刀',
        hoe: '锄头',
        fishing_rod: '鱼竿',
        net: '捕网'
      }
    }
  },
  computed: {
    hasRequiredTool() {
      console.log('检查工具:', {
        event: this.event,
        requiredTool: this.event?.requiredTool,
        resourceLevel: this.event?.resourceLevel,
        inventory: this.inventory,
        inventoryLength: this.inventory?.length
      });

      if (!this.event || !this.event.requiredTool || !this.inventory) {
        console.log('工具检查失败: 缺少必要数据');
        return false;
      }

      const requiredTool = this.event.requiredTool;
      const requiredLevel = this.event.resourceLevel || 1;

      // 工具类型映射
      const toolTypeMap = {
        '镰刀': 'sickle',
        '斧头': 'axe',
        '矿镐': 'pickaxe',
        '小刀': 'knife',
        'sickle': 'sickle',
        'axe': 'axe',
        'pickaxe': 'pickaxe',
        'knife': 'knife'
      };

      // 添加详细调试信息
      console.log('=== GatheringPopup 工具检查详细调试 ===');
      console.log('需要的工具:', requiredTool);
      console.log('需要的等级:', requiredLevel);
      console.log('工具类型映射:', toolTypeMap);
      console.log('需要的类型:', toolTypeMap[requiredTool]);
      console.log('背包物品数量:', this.inventory.length);

      // 显示所有背包物品
      this.inventory.forEach((item, index) => {
        console.log(`背包物品[${index}]:`, {
          id: item.id,
          name: item.name,
          type: item.type,
          level: item.level,
          quantity: item.quantity
        });
      });

      // 找到所有匹配类型的工具
      const matchingTools = this.inventory.filter(item => {
        // 直接名称匹配
        if (item.name === requiredTool || item.id === requiredTool) {
          return true;
        }

        // 类型匹配（如"镰刀"匹配所有sickle类型的工具）
        const requiredType = toolTypeMap[requiredTool];
        if (requiredType && item.type === requiredType) {
          return true;
        }

        // 名称包含匹配（如"简易镰刀"包含"镰刀"）
        if (item.name && item.name.includes(requiredTool)) {
          return true;
        }

        return false;
      });

      console.log('找到的匹配工具:', matchingTools);

      // 检查是否有等级足够的工具
      const validTools = matchingTools.filter(tool => {
        const toolLevel = parseInt(tool.level || 1);
        const isValid = toolLevel >= requiredLevel;
        console.log(`工具 ${tool.name} 等级${toolLevel} ${isValid ? '✅' : '❌'} 需要等级${requiredLevel}`);
        return isValid;
      });

      console.log('等级足够的工具:', validTools);
      const hasItem = validTools.length > 0;

      console.log('工具检查结果:', hasItem);
      return hasItem;
    }
  },
  methods: {
    onClose() {
      console.log('🚪 GatheringPopup.onClose 被调用');
      console.trace('调用栈:');
      this.$emit('close')
    },
    onGather() {
      console.log('🎯 GatheringPopup.onGather 被调用');
      this.$emit('do-gather')
    }
  }
}
</script>

<style scoped>
.gathering-popup-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.gathering-popup {
  background: #fff;
  border-radius: 18rpx;
  padding: 40rpx 32rpx 32rpx 32rpx;
  min-width: 480rpx;
  max-width: 90vw;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.18);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.gather-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #4b3fa7;
  margin-bottom: 18rpx;
}
.gather-desc {
  font-size: 26rpx;
  color: #333;
  margin-bottom: 18rpx;
  text-align: center;
}
.gather-info {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.tool-type {
  color: #27ae60;
  font-weight: bold;
  margin-left: 8rpx;
}
.gather-times {
  color: #e67e22;
  font-weight: bold;
  margin-left: 8rpx;
}
.gather-result {
  color: #e74c3c;
  font-size: 24rpx;
  margin: 12rpx 0 0 0;
  text-align: center;
}
.gather-btns {
  display: flex;
  gap: 24rpx;
  margin-top: 24rpx;
}
.gather-btn {
  background: linear-gradient(90deg, #27ae60, #4bfa7b);
  color: #fff;
  font-size: 26rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.close-btn {
  background: #eee;
  color: #888;
  font-size: 24rpx;
  border-radius: 24rpx;
  padding: 12rpx 36rpx;
  border: none;
}
.gather-btn[disabled] {
  opacity: 0.5;
}
</style> 