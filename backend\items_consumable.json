[{"id": "heal_potion", "name": "疗伤药", "type": "consumable", "quality": "common", "description": "可恢复一定生命值的常用药品。", "icon": "", "price": 50, "sell_price": 20, "craftable": 1, "craft_recipe": "herb:2,water:1", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 99, "effects": "heal:50", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}, {"id": "pojin<PERSON><PERSON>", "name": "破镜丹", "type": "consumable", "quality": "rare", "description": "传说中突破境界瓶颈的神奇丹药，服用后可助力修为精进。", "icon": "", "price": 50000, "sell_price": 1000, "craftable": 0, "craft_recipe": "", "sellable": 1, "tradable": 1, "usable": 1, "equipable": 0, "stackable": 1, "max_stack": 10, "effects": "breakthrough:1", "attack": 0, "defense": 0, "hp": 0, "mp": 0, "energy": 0, "energy_regen": 0.0, "level": 0, "equip_require": {}}]