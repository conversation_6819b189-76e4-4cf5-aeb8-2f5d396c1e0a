"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_gameState = require("../../utils/gameState.js");
const utils_gameData = require("../../utils/gameData.js");
require("../../utils/websocket.js");
const _sfc_main = {
  data() {
    return {
      money: 0,
      myItems: [],
      currentShopType: "market",
      showDetail: false,
      selectedItem: null,
      npcId: "changan_coalboss",
      shops: [
        {
          type: "market",
          name: "市场",
          goods: []
        }
      ],
      loading: false,
      // 新增：物品配置
      itemsConfig: {},
      marketList: [],
      // 玩家市场物品
      showListModal: false,
      // 上架弹窗
      listItemData: null,
      // 当前要上架的物品
      listPrice: "",
      // 上架价格
      listQuantity: 1,
      // 上架数量
      listTab: "item",
      // 上架弹窗的tab，'item'或'martial'
      myMartials: [],
      // 玩家武功列表
      listMartialData: null,
      // 当前要上架的武功
      showMyOrdersModal: false,
      // 控制订单弹窗显示
      myOrderList: []
      // 当前玩家订单
    };
  },
  computed: {
    currentShop() {
      return this.shops[0];
    },
    filteredMyItems() {
      return this.myItems.map((item) => {
        const config = this.itemsConfig[item.id] || {};
        return { ...item, ...config };
      }).filter(
        (item) => item.is_sellable === void 0 && item.sellable === void 0 || item.is_sellable === true || item.is_sellable === 1 || item.sellable === true || item.sellable === 1
      );
    }
  },
  // 处理NPC商店信息
  async handleNpcShopInfo(npcInfo) {
    try {
      console.log("处理NPC商店信息:", npcInfo);
      common_vendor.index.showToast({
        title: `进入${npcInfo.npc_name}的商店`,
        icon: "none",
        duration: 2e3
      });
      if (npcInfo.tab === "buy") {
        await this.fetchNpcShopItems(npcInfo.npc_name);
      } else if (npcInfo.tab === "sell") {
        common_vendor.index.showModal({
          title: npcInfo.npc_name,
          content: "请在背包中选择要出售的物品",
          showCancel: false,
          success: () => {
            common_vendor.index.navigateTo({
              url: "/pages/character/backpack?mode=sell&npc=" + encodeURIComponent(npcInfo.npc_name)
            });
          }
        });
      }
    } catch (error) {
      console.error("处理NPC商店信息失败:", error);
      common_vendor.index.showToast({
        title: "商店加载失败",
        icon: "none"
      });
    }
  },
  // 获取NPC商店物品
  async fetchNpcShopItems(npcName) {
    try {
      const response = await utils_gameData.gameUtils.sendMessage({
        type: "npc_function",
        npc_name: npcName,
        function: "shop",
        data: { action: "list" }
      });
      if (response && response.type === "shop_items") {
        console.log("获取到NPC商店物品:", response.data.items);
        this.showNpcShopItems(response.data.items, npcName);
      } else {
        console.error("获取NPC商店物品失败:", response);
        common_vendor.index.showToast({
          title: "获取商店物品失败",
          icon: "none"
        });
      }
    } catch (error) {
      console.error("获取NPC商店物品异常:", error);
      common_vendor.index.showToast({
        title: "网络错误",
        icon: "none"
      });
    }
  },
  // 显示NPC商店物品
  showNpcShopItems(items, npcName) {
    const itemList = items.map(
      (item) => `${item.item_id}: ${item.price}银两 (库存:${item.stock})`
    );
    common_vendor.index.showActionSheet({
      itemList: itemList.slice(0, 6),
      // 最多显示6个
      success: async (res) => {
        const selectedItem = items[res.tapIndex];
        await this.buyNpcItem(selectedItem, npcName);
      }
    });
  },
  // 购买NPC物品
  async buyNpcItem(item, npcName) {
    try {
      const quantity = await this.askQuantity(item.item_id, item.price);
      if (quantity <= 0)
        return;
      const response = await utils_gameData.gameUtils.sendMessage({
        type: "npc_function",
        npc_name: npcName,
        function: "shop",
        data: {
          action: "buy",
          item_id: item.item_id,
          quantity
        }
      });
      if (response && response.type === "buy_success") {
        common_vendor.index.showToast({
          title: "购买成功",
          icon: "success"
        });
        this.requestPlayerData();
        this.updateMoney();
      } else if (response && response.type === "error") {
        common_vendor.index.showToast({
          title: response.data.message,
          icon: "none"
        });
      }
    } catch (error) {
      console.error("购买NPC物品失败:", error);
      common_vendor.index.showToast({
        title: "购买失败",
        icon: "none"
      });
    }
  },
  // 询问购买数量
  askQuantity(itemId, price) {
    return new Promise((resolve) => {
      common_vendor.index.showModal({
        title: "购买数量",
        content: `${itemId} 单价:${price}银两
请输入购买数量:`,
        editable: true,
        placeholderText: "1",
        success: (res) => {
          if (res.confirm) {
            const quantity = parseInt(res.content) || 1;
            resolve(quantity > 0 ? quantity : 1);
          } else {
            resolve(0);
          }
        }
      });
    });
  },
  onLoad() {
    this.requestPlayerData();
    this.updateMoney();
    this.updateData();
    this.fetchShopGoods("changan_coalboss");
    this.loadItemsConfig();
    this.refreshMarket();
  },
  onShow() {
    this.requestPlayerData();
    this.updateMoney();
    this.updateData();
    this.refreshMarket();
  },
  methods: {
    async requestPlayerData() {
      try {
        const response = await utils_gameData.gameUtils.sendMessage({
          type: "get_player_data",
          data: {}
        });
        if (response && response.type === "player_data" && response.data) {
          utils_gameState.gameState.player = response.data;
          if (typeof response.data.money === "number") {
            this.money = response.data.money;
            utils_gameState.gameState.money = response.data.money;
          }
          if (response.data.inventory) {
            utils_gameState.gameState.inventory = response.data.inventory;
            this.myItems = [...response.data.inventory];
          }
        }
      } catch (error) {
        console.error("获取玩家数据失败:", error);
      }
    },
    updateData() {
      this.money = utils_gameState.gameState.money;
      this.myItems = [...utils_gameState.gameState.inventory];
    },
    formatNumber(num) {
      return utils_gameData.gameUtils.formatNumber(num);
    },
    getQualityColor(quality) {
      return utils_gameData.gameUtils.getQualityColor(quality);
    },
    getQualityName(quality) {
      const qualities = {
        "common": "普通",
        "uncommon": "精品",
        "rare": "稀有",
        "epic": "史诗",
        "legendary": "传说",
        "mythic": "仙品"
      };
      return qualities[quality] || "普通";
    },
    getTypeName(type) {
      const types = {
        "weapon": "武器",
        "armor": "护甲",
        "necklace": "项链",
        "bracelet": "手镯",
        "mount": "坐骑",
        "material": "材料",
        "herb": "草药"
      };
      return types[type] || "未知";
    },
    getSellPrice(item) {
      const prices = {
        "common": 10,
        "uncommon": 50,
        "rare": 200,
        "epic": 1e3,
        "legendary": 5e3,
        "mythic": 1e4
      };
      const basePrice = prices[item.quality] || 10;
      return basePrice * (item.quantity || 1);
    },
    switchShop(shopType) {
      this.currentShopType = shopType;
      if (shopType === "equipment")
        this.npcId = "changan_coalboss";
      else if (shopType === "herb")
        this.npcId = "changan_herbboss";
      else if (shopType === "material")
        this.npcId = "changan_materialboss";
      else
        this.npcId = "";
      if (this.npcId) {
        this.fetchShopGoods(this.npcId);
      }
    },
    showItemDetail(item) {
      this.selectedItem = item;
      this.showDetail = true;
    },
    closeDetail() {
      this.showDetail = false;
      this.selectedItem = null;
    },
    async buyItem(item) {
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      if (this.money < item.price) {
        common_vendor.index.showToast({
          title: "银两不足",
          icon: "none"
        });
        return;
      }
      try {
        const type = item.type || "";
        const sellable = (typeof item.sellable !== "undefined" ? item.sellable : true) ? true : false;
        const response = await this.$request({
          type: "shop_action",
          data: {
            action: "buy",
            item_id: item.id,
            quantity: 1,
            // 购买数量
            type,
            sellable
          }
        });
        if (response && response.type === "error") {
          common_vendor.index.showToast({
            title: response.data.message || "购买失败",
            icon: "none",
            duration: 3e3
          });
          return;
        }
        common_vendor.index.showToast({
          title: "购买成功",
          icon: "success"
        });
        this.closeDetail();
      } catch (error) {
        common_vendor.index.showToast({
          title: "购买失败，请重试",
          icon: "none"
        });
      }
    },
    sellMyItem(item) {
      if (!utils_gameState.gameState.isAuthed) {
        common_vendor.index.showToast({ title: "请先登录", icon: "none" });
        return;
      }
      const sellPrice = this.getSellPrice(item);
      common_vendor.index.showModal({
        title: "确认出售",
        content: `确定要出售 ${item.name} 吗？
获得: ${sellPrice} 银两`,
        success: (res) => {
          if (res.confirm) {
            utils_gameState.gameState.money += sellPrice;
            utils_gameState.gameState.removeItem(item.id, item.quantity || 1);
            this.updateData();
            utils_gameState.gameState.save();
            common_vendor.index.showToast({
              title: "出售成功",
              icon: "success"
            });
          }
        }
      });
    },
    async listItem() {
      try {
        await this.fetchPlayerInventory();
      } catch (e) {
      }
      this.updateData();
      this.listItemData = null;
      this.listPrice = "";
      this.showListModal = true;
    },
    async fetchPlayerInventory() {
      const resp = await utils_gameData.gameUtils.sendMessage({
        type: "get_inventory_data"
      });
      let inventory;
      if (resp && resp.data && Array.isArray(resp.data.inventory)) {
        inventory = resp.data.inventory;
      } else if (resp && Array.isArray(resp.inventory)) {
        inventory = resp.inventory;
      } else if (resp && resp.data && Array.isArray(resp.data)) {
        inventory = resp.data;
      }
      if (Array.isArray(inventory)) {
        utils_gameState.gameState.inventory = inventory;
      }
    },
    // 取消上架
    cancelListItem() {
      this.showListModal = false;
      this.listItemData = null;
      this.listPrice = "";
      this.listQuantity = 1;
    },
    // 增加数量
    increaseQuantity() {
      var _a;
      const maxQuantity = ((_a = this.listItemData) == null ? void 0 : _a.quantity) || 1;
      if (this.listQuantity < maxQuantity) {
        this.listQuantity++;
      }
    },
    // 减少数量
    decreaseQuantity() {
      if (this.listQuantity > 1) {
        this.listQuantity--;
      }
    },
    async confirmListItem() {
      if (!this.listItemData || !this.listPrice || !this.listQuantity) {
        common_vendor.index.showToast({ title: "请选择物品、输入价格和数量", icon: "none" });
        return;
      }
      const maxQuantity = this.listItemData.quantity || 1;
      if (this.listQuantity > maxQuantity) {
        common_vendor.index.showToast({ title: `数量不能超过库存${maxQuantity}`, icon: "none" });
        return;
      }
      if (this.listQuantity < 1) {
        common_vendor.index.showToast({ title: "数量不能小于1", icon: "none" });
        return;
      }
      common_vendor.index.showLoading({ title: "上架中..." });
      try {
        const resp = await utils_gameData.gameUtils.sendMessage({
          type: "market_action",
          data: {
            action: "list",
            item_id: this.listItemData.id,
            price: Number(this.listPrice),
            quantity: Number(this.listQuantity)
          }
        });
        common_vendor.index.hideLoading();
        if (resp && (resp.type === "success" || resp.type === "market_action_success")) {
          common_vendor.index.showToast({ title: resp.data.message || "上架成功", icon: "success" });
          this.showListModal = false;
          this.listItemData = null;
          this.listPrice = "";
          this.listQuantity = 1;
          this.refreshDataAfterList();
        } else if (resp && (resp.type === "error" || resp.type === "market_action_failed")) {
          common_vendor.index.showToast({ title: resp.data.message || "上架失败", icon: "none" });
        } else if (resp && resp.type && resp.type.includes("timeout")) {
          common_vendor.index.showToast({ title: "请求超时，请检查是否上架成功", icon: "none" });
          this.showListModal = false;
          this.listItemData = null;
          this.listPrice = "";
          this.listQuantity = 1;
          this.refreshDataAfterList();
        } else {
          console.error("未知响应格式:", resp);
          common_vendor.index.showToast({ title: "上架失败，响应格式错误", icon: "none" });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        console.error("上架失败:", error);
        common_vendor.index.showToast({ title: "网络错误，请重试", icon: "none" });
        this.showListModal = false;
        this.listItemData = null;
        this.listPrice = "";
        this.listQuantity = 1;
      }
    },
    // 上架后异步刷新数据
    async refreshDataAfterList() {
      try {
        await Promise.all([
          this.refreshMarket(),
          this.requestPlayerData()
        ]);
      } catch (error) {
        console.error("刷新数据失败:", error);
      }
    },
    async refreshMarket() {
      const resp = await utils_gameData.gameUtils.sendMessage({
        type: "market_action",
        data: { action: "get_market_list" }
      });
      const list = resp && resp.data && resp.data.list || [];
      this.marketList = list.map((order) => ({
        ...order,
        item: order.item || { name: "未知物品", quality: "common" }
      }));
    },
    async buyMarketItem(order) {
      var _a;
      try {
        const resp = await utils_gameData.gameUtils.sendMessage({
          type: "market_action",
          data: { action: "buy", order_id: Number(order.id) }
        });
        if (resp && (resp.type === "success" || resp.type === "market_action_success")) {
          common_vendor.index.showToast({ title: resp.data.message || "购买成功", icon: "success" });
          await this.refreshMarket();
          await this.requestPlayerData();
        } else {
          common_vendor.index.showToast({ title: ((_a = resp == null ? void 0 : resp.data) == null ? void 0 : _a.message) || "购买失败", icon: "none" });
        }
      } catch (error) {
        console.error("购买失败:", error);
        common_vendor.index.showToast({ title: "购买失败", icon: "none" });
      }
    },
    fetchShopGoods(npcId) {
      this.loading = true;
      utils_gameData.gameUtils.sendMessage({
        type: "shop_action",
        data: { npc_id: npcId }
      }).then((response) => {
        if (response && response.type === "shop_items" && response.data && response.data.items) {
          this.shops[0].goods = response.data.items.map((item) => ({
            id: item.id,
            name: item.name,
            description: item.desc,
            icon: item.icon,
            price: item.price,
            stock: item.stock,
            attack: item.attack,
            defense: item.defense,
            hp: item.hp,
            mp: item.mp,
            type: item.type,
            quality: item.quality
          }));
        }
        this.loading = false;
      }).catch((error) => {
      });
    },
    updateMoney() {
      if (utils_gameState.gameState.player && typeof utils_gameState.gameState.player.money === "number") {
        this.money = utils_gameState.gameState.player.money;
        utils_gameState.gameState.money = utils_gameState.gameState.player.money;
      } else if (typeof utils_gameState.gameState.money === "number") {
        this.money = utils_gameState.gameState.money;
      } else {
        this.money = 0;
      }
    },
    destroyed() {
    },
    async loadItemsConfig() {
      this.itemsConfig = await utils_gameState.gameState.getItemsConfig();
    },
    // 可在需要时通过 this.itemsConfig[itemId] 获取物品详情
    selectListItem(item) {
      this.listItemData = item;
      this.listPrice = "";
      this.listQuantity = 1;
    },
    selectListMartial(martial) {
      this.listMartialData = martial;
      this.listPrice = "";
    },
    async fetchMyMartials() {
      const resp = await utils_gameData.gameUtils.sendMessage({
        type: "get_player_martials"
      });
      this.myMartials = resp.martials || [];
    },
    async loadMyMartials() {
      await this.fetchMyMartials();
    },
    async showMyOrders() {
      await this.refreshMarket();
      const player = utils_gameState.gameState.player || {};
      const myName = player.name || player.username || "";
      this.myOrderList = this.marketList.filter((order) => order.seller === myName).slice(0, 10);
      this.showMyOrdersModal = true;
    },
    // 下架订单
    async unlinkOrder(order) {
      try {
        common_vendor.index.showModal({
          title: "确认下架",
          content: `确定要下架 ${order.item.name} 吗？物品将返还到背包。`,
          success: async (res) => {
            var _a, _b;
            if (res.confirm) {
              const resp = await utils_gameData.gameUtils.sendMessage({
                type: "market_action",
                data: {
                  action: "unlist",
                  order_id: order.id
                }
              });
              if (resp && (resp.type === "success" || resp.type === "market_action_success")) {
                common_vendor.index.showToast({ title: resp.data.message || "下架成功", icon: "success" });
                await this.refreshMarket();
                await this.requestPlayerData();
                const myName = ((_a = utils_gameState.gameState.player) == null ? void 0 : _a.name) || utils_gameState.gameState.name || "未知玩家";
                const oldCount = this.myOrderList.length;
                this.myOrderList = this.marketList.filter((order2) => order2.seller === myName).slice(0, 10);
              } else {
                common_vendor.index.showToast({ title: ((_b = resp == null ? void 0 : resp.data) == null ? void 0 : _b.message) || "下架失败", icon: "none" });
              }
            }
          }
        });
      } catch (error) {
        console.error("下架失败:", error);
        common_vendor.index.showToast({ title: "下架失败", icon: "none" });
      }
    },
    // 计算剩余时间（小时）
    getTimeRemaining(order) {
      if (!order.expires_at)
        return 72;
      const now = Date.now() / 1e3;
      const remaining = (order.expires_at - now) / 3600;
      return Math.max(0, remaining);
    },
    // 格式化剩余时间显示
    formatTimeRemaining(order) {
      const hours = this.getTimeRemaining(order);
      if (hours <= 0)
        return "已过期";
      if (hours < 1)
        return "不足1小时";
      if (hours < 24)
        return `${Math.floor(hours)}小时`;
      const days = Math.floor(hours / 24);
      const remainingHours = Math.floor(hours % 24);
      return `${days}天${remainingHours}小时`;
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: common_vendor.t($options.formatNumber($data.money)),
    b: common_vendor.t($options.currentShop.name),
    c: $data.currentShopType === "market"
  }, $data.currentShopType === "market" ? common_vendor.e({
    d: common_vendor.o((...args) => $options.listItem && $options.listItem(...args)),
    e: common_vendor.o((...args) => $options.showMyOrders && $options.showMyOrders(...args)),
    f: $data.marketList.length === 0
  }, $data.marketList.length === 0 ? {} : {}, {
    g: common_vendor.f($data.marketList, (order, k0, i0) => {
      return {
        a: order.item && order.item.img ? order.item.img : "/static/logo.png",
        b: common_vendor.t(order.item && order.item.name ? order.item.name : "未知物品"),
        c: common_vendor.t(order.price),
        d: common_vendor.t(order.quantity),
        e: common_vendor.t(order.seller),
        f: common_vendor.o(($event) => $options.buyMarketItem(order), order.id),
        g: order.id
      };
    })
  }) : common_vendor.e({
    h: common_vendor.f($options.currentShop.goods, (item, index, i0) => {
      return {
        a: common_vendor.t(item.name),
        b: common_vendor.t($options.getQualityName(item.quality)),
        c: $options.getQualityColor(item.quality),
        d: common_vendor.t(item.type),
        e: common_vendor.t(item.price),
        f: common_vendor.o(($event) => $options.buyItem(item), index),
        g: index,
        h: common_vendor.o(($event) => $options.showItemDetail(item), index)
      };
    }),
    i: $options.currentShop.goods.length === 0
  }, $options.currentShop.goods.length === 0 ? {} : {}), {
    j: $data.showDetail
  }, $data.showDetail ? common_vendor.e({
    k: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args)),
    l: $data.selectedItem
  }, $data.selectedItem ? common_vendor.e({
    m: common_vendor.t($data.selectedItem.name),
    n: common_vendor.t($options.getQualityName($data.selectedItem.quality)),
    o: $options.getQualityColor($data.selectedItem.quality),
    p: common_vendor.t($data.selectedItem.type),
    q: $data.selectedItem.description
  }, $data.selectedItem.description ? {
    r: common_vendor.t($data.selectedItem.description)
  } : {}, {
    s: $data.selectedItem.attack || $data.selectedItem.defense || $data.selectedItem.hp || $data.selectedItem.mp
  }, $data.selectedItem.attack || $data.selectedItem.defense || $data.selectedItem.hp || $data.selectedItem.mp ? common_vendor.e({
    t: $data.selectedItem.attack
  }, $data.selectedItem.attack ? {
    v: common_vendor.t($data.selectedItem.attack)
  } : {}, {
    w: $data.selectedItem.defense
  }, $data.selectedItem.defense ? {
    x: common_vendor.t($data.selectedItem.defense)
  } : {}, {
    y: $data.selectedItem.hp
  }, $data.selectedItem.hp ? {
    z: common_vendor.t($data.selectedItem.hp)
  } : {}, {
    A: $data.selectedItem.mp
  }, $data.selectedItem.mp ? {
    B: common_vendor.t($data.selectedItem.mp)
  } : {}) : {}, {
    C: common_vendor.t($data.selectedItem.price)
  }) : {}, {
    D: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args)),
    E: common_vendor.o(($event) => $options.buyItem($data.selectedItem)),
    F: common_vendor.o(() => {
    }),
    G: common_vendor.o((...args) => $options.closeDetail && $options.closeDetail(...args))
  }) : {}, {
    H: $data.showListModal
  }, $data.showListModal ? common_vendor.e({
    I: common_vendor.o(($event) => $data.showListModal = false),
    J: !$data.listItemData
  }, !$data.listItemData ? common_vendor.e({
    K: $options.filteredMyItems.length === 0
  }, $options.filteredMyItems.length === 0 ? {} : {}, {
    L: common_vendor.f($options.filteredMyItems, (item, k0, i0) => {
      return common_vendor.e({
        a: common_vendor.t(item.name),
        b: common_vendor.t($options.getQualityName(item.quality)),
        c: $options.getQualityColor(item.quality),
        d: item.quantity > 1
      }, item.quantity > 1 ? {
        e: common_vendor.t(item.quantity)
      } : {}, {
        f: item.id,
        g: common_vendor.o(($event) => $options.selectListItem(item), item.id)
      });
    })
  }) : common_vendor.e({
    M: common_vendor.t($data.listItemData.name),
    N: common_vendor.t($options.getQualityName($data.listItemData.quality)),
    O: $options.getQualityColor($data.listItemData.quality),
    P: common_vendor.t($data.listItemData.quantity || 1),
    Q: common_vendor.o((...args) => $options.decreaseQuantity && $options.decreaseQuantity(...args)),
    R: $data.listItemData.quantity || 1,
    S: $data.listQuantity,
    T: common_vendor.o(($event) => $data.listQuantity = $event.detail.value),
    U: common_vendor.o((...args) => $options.increaseQuantity && $options.increaseQuantity(...args)),
    V: $data.listPrice,
    W: common_vendor.o(($event) => $data.listPrice = $event.detail.value),
    X: $data.listPrice && $data.listQuantity
  }, $data.listPrice && $data.listQuantity ? {
    Y: common_vendor.t($data.listPrice * $data.listQuantity || 0)
  } : {}), {
    Z: common_vendor.o((...args) => $options.cancelListItem && $options.cancelListItem(...args)),
    aa: $data.listItemData
  }, $data.listItemData ? {
    ab: common_vendor.o((...args) => $options.confirmListItem && $options.confirmListItem(...args))
  } : {}, {
    ac: common_vendor.o(() => {
    }),
    ad: common_vendor.o(($event) => $data.showListModal = false)
  }) : {}, {
    ae: $data.showMyOrdersModal
  }, $data.showMyOrdersModal ? common_vendor.e({
    af: common_vendor.o(($event) => $data.showMyOrdersModal = false),
    ag: $data.myOrderList.length === 0
  }, $data.myOrderList.length === 0 ? {} : {}, {
    ah: common_vendor.f($data.myOrderList, (order, k0, i0) => {
      return {
        a: order.item && order.item.img ? order.item.img : "/static/logo.png",
        b: common_vendor.t(order.item && order.item.name ? order.item.name : "未知物品"),
        c: common_vendor.t(order.price),
        d: common_vendor.t(order.quantity),
        e: common_vendor.t(order.seller),
        f: common_vendor.t($options.formatTimeRemaining(order)),
        g: $options.getTimeRemaining(order) < 24 ? 1 : "",
        h: common_vendor.o(($event) => $options.unlinkOrder(order), order.id),
        i: order.id
      };
    }),
    ai: common_vendor.o(($event) => $data.showMyOrdersModal = false),
    aj: common_vendor.o(() => {
    }),
    ak: common_vendor.o(($event) => $data.showMyOrdersModal = false)
  }) : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-2a6aaf81"]]);
wx.createPage(MiniProgramPage);
