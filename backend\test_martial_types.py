#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试武功类型过滤功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from enhanced_martial_system import load_wugong_config

def test_martial_types():
    """测试武功类型过滤"""
    print("=== 测试武功类型过滤 ===")
    
    # 加载武功配置
    martials = load_wugong_config()
    
    # 统计各类型武功数量
    type_counts = {}
    for name, info in martials.items():
        martial_type = info.get('type', '未知')
        if martial_type not in type_counts:
            type_counts[martial_type] = []
        type_counts[martial_type].append(name)
    
    print(f"总共加载了 {len(martials)} 个武功")
    print("\n各类型武功分布:")
    for martial_type, names in type_counts.items():
        print(f"{martial_type}: {len(names)} 个")
        if len(names) <= 5:  # 只显示前5个武功名称
            print(f"  {', '.join(names)}")
        else:
            print(f"  {', '.join(names[:5])}...")
        print()
    
    # 测试特定类型的武功
    test_types = ['剑法', '刀法', '拳法', '空手', '招架', '轻功', '内功', '暗器']
    print("=== 测试特定类型武功 ===")
    for test_type in test_types:
        type_martials = [name for name, info in martials.items() if info.get('type') == test_type]
        print(f"{test_type}: {len(type_martials)} 个")
        if type_martials:
            print(f"  示例: {', '.join(type_martials[:3])}")
        print()

if __name__ == "__main__":
    test_martial_types() 