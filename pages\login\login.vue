<template>
	<view class="login-container">
		<!-- 游戏标题 -->
		<view class="game-title">
			<text class="title-text">仗剑江湖行</text>
			<text class="subtitle-text">武侠点击式游戏</text>
		</view>

		<!-- 登录表单 -->
		<view class="login-form" v-if="!isRegistering">
			<view class="form-title">登录江湖</view>
			
			<view class="input-group">
				<text class="input-label">账号</text>
				<view class="input-wrapper">
					<input 
						type="text" 
						class="input-field" 
						v-model="loginForm.username" 
						placeholder="请输入账号" 
						placeholder-style="color:#999;"
						confirm-type="next" 
						maxlength="20"
						cursor-spacing="10"
					/>
				</view>
			</view>
			
			<view class="input-group">
				<text class="input-label">密码</text>
				<view class="input-wrapper">
					<input 
						type="password" 
						class="input-field" 
						v-model="loginForm.password" 
						placeholder="请输入密码" 
						placeholder-style="color:#999;"
						confirm-type="done" 
						maxlength="20"
						cursor-spacing="10"
						@confirm="handleLogin" 
					/>
				</view>
			</view>
			
			<button class="login-btn primary-btn" @click="handleLogin">
				<text class="btn-text">登录</text>
			</button>
			
			<view class="form-footer">
				<text class="footer-text">还没有账号？</text>
				<text class="footer-link" @click="switchToRegister">立即注册</text>
			</view>
		</view>

		<!-- 注册表单 -->
		<view class="register-form" v-if="isRegistering">
			<view class="form-title">创建角色</view>
			
			<view class="input-group">
				<text class="input-label">账号</text>
				<view class="input-wrapper">
					<input 
						type="text" 
						class="input-field" 
						v-model="registerForm.username" 
						placeholder="请输入账号（3-20位字符）" 
						placeholder-style="color:#999;"
						confirm-type="next" 
						maxlength="20"
						cursor-spacing="10"
					/>
				</view>
			</view>
			
			<view class="input-group">
				<text class="input-label">密码</text>
				<view class="input-wrapper">
					<input 
						type="password" 
						class="input-field" 
						v-model="registerForm.password" 
						placeholder="请输入密码（6-20位字符）" 
						placeholder-style="color:#999;"
						confirm-type="next" 
						maxlength="20"
						cursor-spacing="10"
					/>
				</view>
			</view>
			
			<view class="input-group">
				<text class="input-label">确认密码</text>
				<view class="input-wrapper">
					<input 
						type="password" 
						class="input-field" 
						v-model="registerForm.confirmPassword" 
						placeholder="请再次输入密码" 
						placeholder-style="color:#999;"
						confirm-type="next" 
						maxlength="20"
						cursor-spacing="10"
					/>
				</view>
			</view>
			
			<view class="input-group">
				<text class="input-label">角色名称</text>
				<view class="input-wrapper">
					<input 
						type="text" 
						class="input-field" 
						v-model="registerForm.characterName" 
						placeholder="请输入角色名称（2-10位字符）" 
						placeholder-style="color:#999;"
						confirm-type="done" 
						maxlength="10"
						cursor-spacing="10"
						@confirm="handleRegister" 
					/>
				</view>
			</view>
			
			<view class="input-group">
				<text class="input-label">性别</text>
				<view class="gender-options">
					<view class="gender-option" :class="{ active: registerForm.gender === '男' }" @click="selectGender('男')">
						<text class="gender-text">男</text>
					</view>
					<view class="gender-option" :class="{ active: registerForm.gender === '女' }" @click="selectGender('女')">
						<text class="gender-text">女</text>
					</view>
				</view>
			</view>
			
			<view class="character-preview" v-if="registerForm.characterName">
				<text class="preview-label">角色预览</text>
				<view class="character-card">
					<text class="character-name">{{ registerForm.characterName }}</text>
					<text class="character-level">Lv.1 新手侠客</text>
				</view>
			</view>
			
			<button class="register-btn primary-btn" @click="handleRegister" :disabled="!isFormValid">
				<text class="btn-text">创建角色</text>
			</button>
			
			<view class="form-footer">
				<text class="footer-text">已有账号？</text>
				<text class="footer-link" @click="switchToLogin">立即登录</text>
			</view>

			<!-- 天赋说明 -->
			<view class="talent-desc" style="margin-top: 30rpx; color: #888; font-size: 24rpx; line-height: 1.7;">
				角色天赋说明：<br/>
				- 力量、悟性、身法、根骨四项天赋，初始总和100，每项最低15，注册时系统自动随机分配。<br/>
				- 富源属性：初始为1，影响角色的机遇和资源获取。<br/>
				- 天赋数值由服务器随机生成，确保公平性。
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="isLoading">
			<view class="loading-content">
				<text class="loading-text">{{ loadingText }}</text>
			</view>
		</view>
	</view>
</template>

<script>
import wsManager from '../../utils/websocket.js'
import { gameUtils } from '../../utils/gameData.js'

export default {
	data() {
		return {
			isRegistering: false,
			isLoading: false,
			loadingText: '正在处理...',
			loginForm: {
				username: '',
				password: ''
			},
			registerForm: {
				username: '',
				password: '',
				confirmPassword: '',
				characterName: '',
				gender: '男'
			}
		}
	},
	
	computed: {
		isFormValid() {
			const form = this.registerForm;
			return form.username.length >= 3 && 
				   form.username.length <= 20 &&
				   form.password.length >= 6 && 
				   form.password.length <= 20 &&
				   form.password === form.confirmPassword &&
				   form.characterName.length >= 2 && 
				   form.characterName.length <= 10;
		}
	},
	
	onLoad() {
		// 通知WebSocket管理器当前在登录页面
		wsManager.setLoginPageStatus(true);
		
		// 检查是否已登录，但不立即执行自动登录
		// 延迟执行，避免阻塞UI渲染
		setTimeout(() => {
			this.checkLoginStatus();
		}, 1000);
	},
	
	onUnload() {
		// 离开登录页面时通知WebSocket管理器
		wsManager.setLoginPageStatus(false);
	},
	
	methods: {
		checkLoginStatus() {
			const token = uni.getStorageSync('token');
			const userInfo = uni.getStorageSync('userInfo');
			
			if (token && userInfo) {
				// 已登录，但不立即跳转，先显示界面
				uni.showToast({
					title: '发现已保存的登录信息',
					icon: 'none',
					duration: 1500
				});
				
				// 延迟执行自动登录，确保UI已渲染完成
				setTimeout(() => {
					this.autoLogin();
				}, 1500);
			}
		},
		
		async autoLogin() {
			try {
				// 获取本地存储的token和用户信息
				const token = uni.getStorageSync('token');
				const userInfo = uni.getStorageSync('userInfo');
				
				if (!token) {
					return;
				}
				
				this.isLoading = true;
				this.loadingText = '正在自动登录...';
				
				// 连接WebSocket
				await wsManager.connect();
				
				// 发送认证消息并处理响应
				const response = await gameUtils.sendMessage({
					type: 'auth',
					data: {
						token: token,
						userInfo: userInfo
					}
				});
				
				if (response && response.type === 'auth_success') {
					this.handleAutoLoginSuccess(response.data);
				} else if (response && response.type === 'auth_failed') {
					this.handleAutoLoginFailed(response.data);
				} else {
					this.isLoading = false;
					uni.showToast({
						title: '自动登录失败，请手动登录',
						icon: 'none'
					});
				}
				
			} catch (error) {
				this.isLoading = false;
				uni.showToast({
					title: '自动登录失败，请手动登录',
					icon: 'none'
				});
			}
		},
		
		handleAutoLoginSuccess(data) {
			this.isLoading = false;
			
			// 更新本地存储的用户信息
			if (data.userInfo) {
				uni.setStorageSync('userInfo', data.userInfo);
			}
			
			uni.showToast({
				title: '自动登录成功',
				icon: 'success'
			});
			
			// 跳转到游戏页面
			setTimeout(() => {
				uni.switchTab({
					url: '/pages/index/index'
				});
			}, 1000);
		},
		
		handleAutoLoginFailed(data) {
			this.isLoading = false;
			// 自动登录失败时清除本地token和userInfo
			uni.removeStorageSync('token');
			uni.removeStorageSync('userInfo');
			uni.showToast({
				title: data.message || '自动登录失败，请重新登录',
				icon: 'none',
				duration: 3000
			});
		},
		
		switchToRegister() {
			this.isRegistering = true;
		},
		
		switchToLogin() {
			this.isRegistering = false;
		},
		
		selectGender(gender) {
			this.registerForm.gender = gender;
		},
		
		async handleLogin() {
			if (!this.loginForm.username || !this.loginForm.password) {
				uni.showToast({
					title: '请输入账号和密码',
					icon: 'none'
				});
				return;
			}
			this.isLoading = true;
			this.loadingText = '正在登录...';
			// 注册事件驱动回调
			if (!this._loginSuccessHandler) {
				this._loginSuccessHandler = (data) => {
					this.isLoading = false;
					if (data && data.token) {
						uni.setStorageSync('token', data.token);
					}
					if (data && data.userInfo) {
						uni.setStorageSync('userInfo', data.userInfo);
					}
					uni.showToast({ title: '登录成功', icon: 'success' });
					setTimeout(() => {
						uni.switchTab({ url: '/pages/index/index' });
					}, 1000);
					wsManager.off('login_success', this._loginSuccessHandler);
					wsManager.off('login_failed', this._loginFailedHandler);
				};
				wsManager.on('login_success', this._loginSuccessHandler);
			}
			if (!this._loginFailedHandler) {
				this._loginFailedHandler = (data) => {
					this.isLoading = false;
					uni.showToast({ title: data && data.message ? data.message : '登录失败，请重试', icon: 'none' });
					wsManager.off('login_success', this._loginSuccessHandler);
					wsManager.off('login_failed', this._loginFailedHandler);
				};
				wsManager.on('login_failed', this._loginFailedHandler);
			}
			// 连接WebSocket并发送登录消息（不 await sendMessage 的返回值）
			await wsManager.connect();
			wsManager.sendMessage('login', {
				username: this.loginForm.username,
				password: this.loginForm.password
			});
		},
		
		async handleRegister() {
			if (!this.isFormValid) {
				uni.showToast({
					title: '请检查输入信息',
					icon: 'none'
				});
				return;
			}
			
			try {
				this.isLoading = true;
				this.loadingText = '正在创建角色...';
				
				// 连接WebSocket
				await wsManager.connect();
				
				// 发送注册消息并处理响应
				const response = await gameUtils.sendMessage({
					type: 'register',
					data: {
						username: this.registerForm.username,
						password: this.registerForm.password,
						characterName: this.registerForm.characterName,
						gender: this.registerForm.gender
					}
				});
				
				if (response && response.type === 'register_success') {
					this.handleRegisterSuccess(response.data);
				} else if (response && response.type === 'register_failed') {
					this.handleRegisterFailed(response.data);
				} else {
					this.isLoading = false;
					uni.showToast({
						title: '注册失败，请重试',
						icon: 'none'
					});
				}
				
			} catch (error) {
				this.isLoading = false;
				uni.showToast({
					title: '注册失败: ' + error.message,
					icon: 'none'
				});
			}
		},
		
		async handleLoginSuccess(data) {
			this.isLoading = false;
			
			try {
				// 保存登录信息
				uni.setStorageSync('token', data.token);
				uni.setStorageSync('userInfo', data.userInfo);
				
				// 通知WebSocket管理器已不在登录页面
				wsManager.setLoginPageStatus(false);
				
				// 显示登录成功提示
				uni.showToast({ 
					title: '登录成功', 
					icon: 'success',
					duration: 1500
				});
				
				// 确保WebSocket连接已建立
				if (!wsManager.isConnected) {
					await wsManager.connect();
				}
				
				// 更新认证状态
				wsManager.isAuthed = true;
				
				// 直接跳转到首页，无需再次认证
				// 延迟跳转，确保提示显示完成
				setTimeout(() => {
					uni.switchTab({ 
						url: '/pages/index/index',
						success: function() {
						},
						fail: function(err) {
							// 尝试使用redirectTo作为备选方案
							uni.redirectTo({
								url: '/pages/index/index'
							});
						}
					});
				}, 1500);
			} catch (error) {
				uni.showToast({
					title: '登录过程中出现错误',
					icon: 'none'
				});
			}
		},
		
		handleLoginFailed(data) {
			this.isLoading = false;
			// 登录失败时清除本地token和userInfo
			uni.removeStorageSync('token');
			uni.removeStorageSync('userInfo');
			uni.showToast({
				title: data.message || '登录失败',
				icon: 'none',
				duration: 3000
			});
		},
		
		handleRegisterSuccess(data) {
			this.isLoading = false;
			
			try {
				// 保存登录信息
				uni.setStorageSync('token', data.token);
				uni.setStorageSync('userInfo', data.userInfo);
				
				// 通知WebSocket管理器已不在登录页面
				wsManager.setLoginPageStatus(false);
				
				// 准备天赋和富源信息字符串
				let talentStr = '';
				if (data.userInfo && data.userInfo.talent) {
					talentStr = `\n力量：${data.userInfo.talent['力量']}  悟性：${data.userInfo.talent['悟性']}  身法：${data.userInfo.talent['身法']}  根骨：${data.userInfo.talent['根骨']}`;
				}
				let fortuneStr = data.userInfo && data.userInfo.fortune ? `\n富源：${data.userInfo.fortune}` : '';
				
				// 显示注册成功对话框
				uni.showModal({
					title: '角色创建成功',
					content: `恭喜你，角色创建成功！${talentStr}${fortuneStr}`,
					showCancel: false,
					success: async () => {
						try {
							// 确保WebSocket连接已建立
							if (!wsManager.isConnected) {
								await wsManager.connect();
							}
							
							// 更新认证状态
							wsManager.isAuthed = true;
							
							// 显示跳转提示
							uni.showToast({ 
								title: '正在进入游戏', 
								icon: 'success',
								duration: 1500
							});
							
							// 直接跳转到首页
							setTimeout(() => {
								uni.switchTab({ 
									url: '/pages/index/index',
									success: function() {
									},
									fail: function(err) {
										// 尝试使用redirectTo作为备选方案
										uni.redirectTo({
											url: '/pages/index/index'
										});
									}
								});
							}, 1000);
						} catch (error) {
							uni.showToast({
								title: '进入游戏时出现错误',
								icon: 'none'
							});
						}
					}
				});
			} catch (error) {
				uni.showToast({
					title: '注册过程中出现错误',
					icon: 'none'
				});
			}
		},
		
		handleRegisterFailed(data) {
			this.isLoading = false;
			// 注册失败时清除本地token和userInfo
			uni.removeStorageSync('token');
			uni.removeStorageSync('userInfo');
			uni.showToast({
				title: data.message || '注册失败',
				icon: 'none'
			});
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 40rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.game-title {
	text-align: center;
	margin-bottom: 80rpx;
}

.title-text {
	font-size: 60rpx;
	font-weight: bold;
	color: white;
	text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
	display: block;
	margin-bottom: 20rpx;
}

.subtitle-text {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
	display: block;
}

.login-form,
.register-form {
	background: rgba(255, 255, 255, 0.95);
	border-radius: 30rpx;
	padding: 60rpx 40rpx;
	width: 100%;
	max-width: 600rpx;
	box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
	position: relative;
	z-index: 10;
}

.form-title {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 60rpx;
}

.input-group {
	margin-bottom: 40rpx;
	position: relative;
	z-index: 20;
}

.input-label {
	font-size: 28rpx;
	color: #666;
	margin-bottom: 20rpx;
	display: block;
}

/* 确保输入框可点击 */
.input-wrapper {
	position: relative;
	z-index: 999;
	margin-bottom: 10rpx;
}

.input-field {
	width: 100%;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 20rpx;
	padding: 0 30rpx;
	font-size: 28rpx;
	background: white;
	box-sizing: border-box;
	color: #333;
	position: relative;
	z-index: 1000;
}

.input-field:focus {
	border-color: #667eea;
	z-index: 1001;
}

.gender-options {
	display: flex;
	gap: 20rpx;
	position: relative;
	z-index: 20;
}

.gender-option {
	flex: 1;
	height: 80rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background: white;
	transition: all 0.3s ease;
	position: relative;
	z-index: 20;
}

.gender-option.active {
	border-color: #667eea;
	background: #667eea;
}

.gender-text {
	font-size: 28rpx;
	color: #333;
}

.gender-option.active .gender-text {
	color: white;
}

.character-preview {
	margin: 40rpx 0;
	padding: 30rpx;
	background: #f8f9fa;
	border-radius: 20rpx;
}

.preview-label {
	font-size: 24rpx;
	color: #666;
	margin-bottom: 20rpx;
	display: block;
}

.character-card {
	text-align: center;
}

.character-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.character-level {
	font-size: 24rpx;
	color: #666;
	display: block;
}

.primary-btn {
	width: 100%;
	height: 90rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border: none;
	border-radius: 45rpx;
	color: white;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 40rpx;
	box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	position: relative;
	z-index: 20;
}

.primary-btn:active {
	transform: translateY(2rpx);
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.4);
}

.primary-btn:disabled {
	background: #ccc;
	box-shadow: none;
}

.form-footer {
	text-align: center;
}

.footer-text {
	font-size: 28rpx;
	color: #666;
}

.footer-link {
	font-size: 28rpx;
	color: #667eea;
	margin-left: 10rpx;
}

.loading-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 9999;
}

.loading-content {
	background: white;
	border-radius: 20rpx;
	padding: 60rpx 40rpx;
	text-align: center;
}

.loading-text {
	font-size: 32rpx;
	color: #333;
}

.talent-desc {
	text-align: center;
	margin-top: 30rpx;
	color: #888;
	font-size: 24rpx;
	line-height: 1.7;
}
</style> 