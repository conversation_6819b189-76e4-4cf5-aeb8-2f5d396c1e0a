/**
 * WebSocket通信管理器
 * 负责与Python后端进行实时通信
 */

class WebSocketManager {
	constructor() {
		this.ws = null
		this.isConnected = false
		this.isAuthed = false // 认证状态
		this.reconnectAttempts = 0
		this.maxReconnectAttempts = -1  // -1表示无限重连
		this.reconnectInterval = 3000 // 3秒
		this.maxReconnectInterval = 30000 // 最大重连间隔30秒
		this.messageQueue = [] // 消息队列
		this.eventHandlers = {} // 事件处理器
		this.connecting = false // 防止重复连接
		this.heartbeatInterval = null // 心跳定时器
		this.heartbeatTimeout = null // 心跳超时定时器
		this.manualDisconnect = false // 标记是否为手动断开
		// 根据环境设置不同的服务器地址
		// 开发环境使用本地地址，生产环境使用实际服务器地址
		// 微信小程序需要使用 wss:// 或 ws:// 格式
		this.serverUrl = 'ws://localhost:8080' // 后端WebSocket地址（使用本地地址）
		this.disableAutoAuth = false // 默认启用自动认证功能
		this.isLoginPage = false // 标记是否在登录页面
		this.debug = false; // 关闭所有 WebSocket 调试日志
		this.connectPromise = null;

		// 认证/登录成功回调
		this.on('login_success', (data) => {
			this.isAuthed = true;
			this.log('WebSocket: 登录成功，认证状态已更新');
		});
		this.on('auth_success', (data) => {
			this.isAuthed = true;
			this.log('WebSocket: 认证成功，认证状态已更新');
			// 认证成功后自动刷新全局数据
			try {
				const gameState = require('./gameState').default || require('./gameState');
				if (gameState && typeof gameState.requestAllData === 'function') {
					this.log('认证成功，自动刷新全局数据');
					gameState.requestAllData();
				}
			} catch (e) {
				this.error('自动刷新全局数据失败:', e);
			}
		});
		this.on('login_failed', (data) => { 
			this.isAuthed = false; 
			this.log('WebSocket: 登录失败，认证状态已重置');
		});
		this.on('auth_failed', (data) => { 
			this.isAuthed = false; 
			this.log('WebSocket: 认证失败，认证状态已重置');
		});
	}

	// 日志方法
	log(message, ...args) {
		// 生产环境下禁用调试日志
	}

    // 新增：重连后自动跳转Index页面并广播关闭弹窗事件
    triggerReconnectActions() {
        // 跳转到首页
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        if (!currentPage || !currentPage.route || !currentPage.route.includes('pages/index/index')) {
            uni.reLaunch({ url: '/pages/index/index' });
        }
        // 全局事件通知所有页面关闭弹窗
        uni.$emit && uni.$emit('ws_reconnected');
    }
	
	// 错误日志方法
	error(message, ...args) {
		console.error(`[WebSocket错误] ${message}`, ...args);
	}

	/**
	 * 连接到WebSocket服务器（防止重复连接，彻底断开旧连接）
	 */
	connect() {
		if (this.isConnected) {
			this.log('WebSocket已连接，无需重新连接');
			return Promise.resolve();
		}
		if (this.connecting) {
			this.log('WebSocket正在连接中，请等待...');
			return this.connectPromise || Promise.reject(new Error('正在连接中'));
		}
		this.connecting = true;
		this.connectPromise = new Promise((resolve, reject) => {
			try {
				this.log('开始连接WebSocket服务器:', this.serverUrl);
				this.ws = uni.connectSocket({
					url: this.serverUrl,
					success: () => {
						this.log('WebSocket连接请求已发送');
					},
					fail: (error) => {
						this.error('WebSocket连接请求失败:', error);
						this.connecting = false;
						this.connectPromise = null;
						reject(error);
					}
				});
				uni.onSocketOpen((res) => {
					this.isConnected = true;
					this.connecting = false;
					this.connectPromise = null;
					this.reconnectAttempts = 0;
					this.log('WebSocket连接已建立');
					this.processMessageQueue();
					this.startHeartbeat();
					if (this.eventHandlers['connected']) {
						this.eventHandlers['connected'].forEach(fn => fn());
					}
                    // 新增：重连成功后自动跳转Index页面并关闭弹窗
                    this.triggerReconnectActions();
					if (!this.isLoginPage && !this.disableAutoAuth) {
						this.log('准备执行自动认证...');
						setTimeout(() => {
							this.autoAuthenticate();
						}, 500);
					} else {
						this.log('当前在登录页面或自动认证已禁用，跳过自动认证');
					}
					resolve(res);
				});
				uni.onSocketClose((res) => {
					this.isConnected = false;
					this.connecting = false;
					this.connectPromise = null;
					this.stopHeartbeat();
					if (this.eventHandlers['disconnected']) {
						this.eventHandlers['disconnected'].forEach(fn => fn());
					}
					this.log('WebSocket连接断开，code:', res.code, 'reason:', res.reason);
					if (!this.manualDisconnect && res.code !== 1000) {
						this.log('检测到异常断开，开始重连...');
						this.handleDisconnect();
					} else if (this.manualDisconnect) {
						this.log('手动断开连接，不进行重连');
						this.manualDisconnect = false;
					}
				});
				uni.onSocketError((res) => {
					this.isConnected = false;
					this.connecting = false;
					this.connectPromise = null;
					this.error('WebSocket连接错误:', res);
					if (this.eventHandlers['error']) {
						this.eventHandlers['error'].forEach(fn => fn(res));
					}
					this.handleDisconnect();
					reject(res);
				});
				uni.onSocketMessage((res) => {
					try {
						const message = JSON.parse(res.data);
						this.log('收到消息:', message);
						// 处理心跳响应
						if (message.type === 'pong') {
							this.handlePong();
							return;
						}
						// 处理不同类型的消息
						this.triggerEvent(message.type, message.data);
					} catch (error) {
						// 消息解析失败
						this.error('消息解析失败:', error, res.data);
					}
				});
				setTimeout(() => {
					if (!this.isConnected && this.connecting) {
						this.connecting = false;
						this.connectPromise = null;
						this.error('WebSocket连接超时');
						reject(new Error('连接超时'));
					}
				}, 15000);
			} catch (error) {
				this.connecting = false;
				this.connectPromise = null;
				this.error('WebSocket连接异常:', error);
				reject(error);
			}
		});
		return this.connectPromise;
	}
	
	/**
	 * 设置当前是否在登录页面
	 */
	setLoginPageStatus(isLoginPage) {
		this.isLoginPage = isLoginPage;
		this.log('设置登录页面状态:', isLoginPage ? '当前在登录页面' : '当前不在登录页面');
	}

	/**
	 * 断开WebSocket连接
	 */
	disconnect() {
		if (this.ws && this.isConnected) {
			this.log('手动断开WebSocket连接');
			uni.closeSocket();
			this.isConnected = false;
			this.connecting = false;
			this.manualDisconnect = true; // 标记为手动断开
		}
	}

	/**
	 * 处理连接断开
	 */
	handleDisconnect() {
		if (this.reconnectAttempts >= this.maxReconnectAttempts && this.maxReconnectAttempts !== -1) {
			this.log('达到最大重连次数，停止重连');
			return;
		}

		const delay = Math.min(
			this.reconnectInterval * Math.pow(1.5, this.reconnectAttempts),
			this.maxReconnectInterval
		);

		this.reconnectAttempts++;
		this.log(`第${this.reconnectAttempts}次重连，延迟${delay}ms`);

		setTimeout(() => {
			if (!this.isConnected && !this.connecting) {
				this.log('执行重连...');
				this.connect().catch(error => {
					this.error('重连失败:', error);
				});
			}
		}, delay);
	}

	/**
	 * 发送消息
	 */
	sendMessage(type, data = {}) {
		const message = JSON.stringify({ type, data });
		
		// 如果WebSocket未连接，加入队列
		if (!this.isConnected) {
			this.log('WebSocket未连接，消息加入队列:', { type, data });
			this.messageQueue.push(message);
			return Promise.reject(new Error('WebSocket未连接'));
		}
		
		return new Promise((resolve, reject) => {
			this.log('发送消息:', { type, data });
			uni.sendSocketMessage({
				data: message,
				success: () => {
					this.log('消息发送成功:', { type, data });
					resolve({ type, data });
				},
				fail: (error) => {
					this.error('消息发送失败:', error, { type, data });
					this.messageQueue.push(message);
					reject(error);
				}
			});
		});
	}

	/**
	 * 处理消息队列
	 */
	processMessageQueue() {
		if (this.messageQueue.length === 0) {
			return;
		}
		
		this.log(`处理消息队列，共${this.messageQueue.length}条消息`);
		
		// 复制队列并清空原队列
		const queue = [...this.messageQueue];
		this.messageQueue = [];
		
		// 逐条发送消息
		queue.forEach(message => {
			try {
				uni.sendSocketMessage({
					data: message,
					success: () => {
						this.log('队列消息发送成功:', message);
					},
					fail: (error) => {
						this.error('队列消息发送失败:', error, message);
						this.messageQueue.unshift(message);
					}
				});
			} catch (error) {
				this.error('队列消息发送失败:', error, message);
				this.messageQueue.unshift(message);
			}
		});
	}

	/**
	 * 触发事件
	 */
	triggerEvent(eventType, data) {
		// 特殊处理game_event事件，确保它总是被处理
		if (eventType === 'game_event') {
			// 导入gameState模块（避免循环引用问题）
			try {
				const gameState = require('./gameState').default;
				if (gameState && typeof gameState.handleGameEvent === 'function') {
					this.log(`特殊处理game_event事件`, data);
					
					// 确保数据格式正确
					const eventData = {
						type: data.type || 5, // 默认为奇遇事件
						content: data.content || data.description || '你遇到了一个江湖事件',
						rewards: data.rewards || {},
						realm_breakthrough: data.realm_breakthrough || null
					};
					
					gameState.handleGameEvent(eventData);
				}
			} catch (error) {
				this.error('处理game_event事件失败:', error);
			}
		}
		
		if (this.eventHandlers[eventType]) {
			this.log(`触发事件: ${eventType}`, data);
			this.eventHandlers[eventType].forEach(handler => handler(data));
		} else {
			this.log(`没有处理程序的事件: ${eventType}`, data);
		}
	}

	/**
	 * 注册事件处理程序
	 */
	on(eventType, handler) {
		if (!this.eventHandlers[eventType]) {
			this.eventHandlers[eventType] = [];
		}
		
		this.eventHandlers[eventType].push(handler);
		this.log(`注册事件处理程序: ${eventType}`);
	}

	/**
	 * 移除事件处理程序
	 */
	off(eventType, handler) {
		if (!this.eventHandlers[eventType]) {
			return;
		}
		
		const idx = this.eventHandlers[eventType].indexOf(handler);
		
		if (idx > -1) {
			this.eventHandlers[eventType].splice(idx, 1);
		}
	}

	/**
	 * 自动认证
	 */
	autoAuthenticate() {
		this.log('开始自动认证...');
		const token = uni.getStorageSync('token');
		
		this.log('认证信息检查:');
		this.log('- token存在:', !!token);
		
		if (token) {
			this.log('✅ 找到登录信息，发送认证消息');
			
			this.sendMessage('auth', {
				token: token
			});
			
			this.log('✅ 认证消息已发送');
		} else {
			this.log('❌ 未找到登录信息，无法自动认证');
		}
	}

	/**
	 * 发送认证消息
	 */
	authenticate() {
		// 获取用户信息或token
		const token = uni.getStorageSync('token') || '';
		
		this.log('手动发送认证消息');
		this.sendMessage('auth', {
			token: token
		});
	}

	/**
	 * 发送游戏动作
	 */
	sendGameAction(action, data = {}) {
		this.sendMessage('game_action', {
			action: action,
			...data
		})
	}

	/**
	 * 请求玩家数据
	 */
	requestPlayerData() {
		this.log('WebSocket: 请求玩家数据');
		this.sendMessage('get_player_data')
	}

	/**
	 * 请求背包数据
	 */
	requestInventoryData() {
		this.sendMessage('get_inventory_data')
	}

	/**
	 * 请求武功数据
	 */
	requestSkillsData() {
		this.sendMessage('get_skills_data')
	}

	/**
	 * 发送闯江湖请求
	 */
	sendAdventureRequest() {
		this.log('=== WebSocket: 发送闯江湖请求 ===');
		this.log('当前时间:', new Date().toLocaleString());
		this.log('WebSocket连接状态:', this.isConnected);
		this.log('服务器地址:', this.serverUrl);
		
		if (!this.isConnected) {
			this.log('❌ WebSocket未连接，无法发送请求');
			return;
		}
		
		this.log('✅ 发送adventure消息');
		this.sendMessage('adventure')
		this.log('✅ WebSocket: 闯江湖请求已发送');
	}

	/**
	 * 发送打坐请求
	 */
	sendMeditationRequest() {
		this.sendMessage('meditation')
	}

	/**
	 * 发送疗伤请求
	 */
	sendHealingRequest() {
		this.sendMessage('healing')
	}

	/**
	 * 发送装备操作
	 */
	sendEquipmentAction(action, itemId) {
		this.sendMessage('equipment_action', {
			action: action,
			itemId: itemId
		})
	}

	/**
	 * 发送武功操作
	 */
	sendSkillAction(action, skillId) {
		this.sendMessage('skill_action', {
			action: action,
			skillId: skillId
		})
	}

	/**
	 * 发送商店操作
	 */
	sendShopAction(action, data) {
		this.sendMessage('shop_action', {
			action: action,
			...data
		})
	}

	/**
	 * 发送市场操作
	 */
	sendMarketAction(action, data) {
		this.sendMessage('market_action', {
			action: action,
			...data
		})
	}

	/**
	 * 发送门派操作
	 */
	sendGuildAction(action, data) {
		this.sendMessage('guild_action', {
			action: action,
			...data
		})
	}

	/**
	 * 发送打造操作
	 */
	sendCraftingAction(action, data) {
		this.sendMessage('crafting_action', {
			action: action,
			...data
		})
	}

	/**
	 * 启动心跳机制
	 */
	startHeartbeat() {
		// 清除之前的心跳
		this.stopHeartbeat();
		// 每5分钟发送一次心跳
		this.heartbeatInterval = setInterval(() => {
			if (this.isConnected) {
				this.sendMessage('ping');
				// 设置心跳超时
				this.heartbeatTimeout = setTimeout(() => {
					this.log('心跳超时，断开连接');
					this.disconnect();
					// 尝试重连
					setTimeout(() => {
						this.connect();
					}, 1000);
				}, 10000); // 10秒超时
			}
		}, 300000); // 5分钟间隔
	}

	/**
	 * 停止心跳机制
	 */
	stopHeartbeat() {
		if (this.heartbeatInterval) {
			clearInterval(this.heartbeatInterval);
			this.heartbeatInterval = null;
		}
		
		if (this.heartbeatTimeout) {
			clearTimeout(this.heartbeatTimeout);
			this.heartbeatTimeout = null;
		}
	}

	/**
	 * 处理心跳响应
	 */
	handlePong() {
		// 收到心跳响应，清除超时
		if (this.heartbeatTimeout) {
			clearTimeout(this.heartbeatTimeout);
			this.heartbeatTimeout = null;
		}
	}
}

// 创建单例
const wsManager = new WebSocketManager();

export default wsManager; 