<view class="container data-v-1c7c41d0"><view wx:if="{{a}}" class="train-loading-mask data-v-1c7c41d0"><view class="train-loading-content data-v-1c7c41d0"><view class="train-spinner data-v-1c7c41d0"></view><text class="train-loading-text data-v-1c7c41d0">正在修炼...</text></view></view><view wx:if="{{b}}" class="train-result-msg data-v-1c7c41d0" style="background:#fffbe6;color:#ad6800;padding:8px 12px;margin-bottom:12px;border-radius:6px;font-size:14px">{{c}}</view><view class="category-tabs data-v-1c7c41d0"><view class="{{['tab-item', 'data-v-1c7c41d0', d && 'active']}}" bindtap="{{e}}"><text class="tab-text data-v-1c7c41d0">使用武功</text></view><view class="{{['tab-item', 'data-v-1c7c41d0', f && 'active']}}" bindtap="{{g}}"><text class="tab-text data-v-1c7c41d0">武功技能</text></view><view class="{{['tab-item', 'data-v-1c7c41d0', h && 'active']}}" bindtap="{{i}}"><text class="tab-text data-v-1c7c41d0">生活技能</text></view></view><view wx:if="{{j}}" class="data-v-1c7c41d0"><view wx:for="{{k}}" wx:for-item="typeObj" wx:key="h" class="skill-group data-v-1c7c41d0"><view class="skill-item data-v-1c7c41d0"><view class="skill-info data-v-1c7c41d0"><view class="title-selector-row data-v-1c7c41d0"><text class="group-title data-v-1c7c41d0">{{typeObj.a}}</text><view class="equip-selector data-v-1c7c41d0"><button class="martial-select-tag data-v-1c7c41d0" bindtap="{{typeObj.d}}" disabled="{{typeObj.e}}"><text class="martial-select-tag-text data-v-1c7c41d0">{{typeObj.b}} {{typeObj.c}}</text></button><button wx:if="{{typeObj.f}}" class="martial-clear-btn data-v-1c7c41d0" bindtap="{{typeObj.g}}"> × </button></view></view></view></view></view></view><view wx:elif="{{l}}" class="data-v-1c7c41d0" style="text-align:center;color:#aaa;padding:32px 0">暂无武功数据</view><view wx:if="{{m}}" class="data-v-1c7c41d0"><view wx:if="{{n}}" class="data-v-1c7c41d0"><view class="skill-group data-v-1c7c41d0"><view class="group-header data-v-1c7c41d0"><text class="group-title data-v-1c7c41d0">已学会武功技能（{{o}}）</text></view><view wx:for="{{p}}" wx:for-item="skill" wx:key="f" class="skill-item data-v-1c7c41d0"><view class="skill-info data-v-1c7c41d0"><text class="skill-name data-v-1c7c41d0">{{skill.a}}</text><text class="skill-level data-v-1c7c41d0">Lv.{{skill.b}}</text><text wx:if="{{skill.c}}" class="skill-school data-v-1c7c41d0">{{skill.d}}</text></view><button class="detail-btn data-v-1c7c41d0" bindtap="{{skill.e}}">详情</button></view><view wx:if="{{q}}" class="empty-skills data-v-1c7c41d0">暂无已学会武功</view></view></view><view wx:else class="data-v-1c7c41d0" style="text-align:center;color:#aaa;padding:32px 0">暂无武功技能</view></view><view wx:if="{{r}}" class="data-v-1c7c41d0"><view wx:if="{{s}}" class="data-v-1c7c41d0"><view class="skill-group data-v-1c7c41d0"><view class="group-header data-v-1c7c41d0"><text class="group-title data-v-1c7c41d0">生活技能</text></view><view wx:for="{{t}}" wx:for-item="skill" wx:key="d" class="skill-item data-v-1c7c41d0"><view class="skill-info data-v-1c7c41d0"><text class="skill-name data-v-1c7c41d0">{{skill.a}}</text><text class="skill-level data-v-1c7c41d0">Lv.{{skill.b}}</text></view><button class="detail-btn data-v-1c7c41d0" bindtap="{{skill.c}}">详情</button></view><view wx:if="{{v}}" class="empty-skills data-v-1c7c41d0">暂无生活技能</view></view></view><view wx:else class="data-v-1c7c41d0" style="text-align:center;color:#aaa;padding:32px 0">暂无生活技能</view></view><view wx:if="{{w}}" class="modal-overlay data-v-1c7c41d0" bindtap="{{Y}}"><view class="modal-content data-v-1c7c41d0" catchtap="{{X}}"><view class="modal-header data-v-1c7c41d0"><text class="modal-title data-v-1c7c41d0">武学详情</text><text class="modal-close data-v-1c7c41d0" bindtap="{{x}}">×</text></view><view wx:if="{{y}}" class="modal-body data-v-1c7c41d0"><view class="detail-header data-v-1c7c41d0"><text class="detail-name data-v-1c7c41d0">{{z}}</text><view class="detail-status data-v-1c7c41d0"><text class="detail-level data-v-1c7c41d0">{{A}}级</text><text wx:if="{{B}}" class="detail-school data-v-1c7c41d0">{{C}}</text><text wx:if="{{D}}" class="detail-equipped data-v-1c7c41d0">已装备</text></view></view><text class="detail-desc data-v-1c7c41d0">{{E}}</text><view wx:if="{{F}}" class="detail-progress data-v-1c7c41d0"><text class="progress-title data-v-1c7c41d0">进度:</text><view class="progress-bar data-v-1c7c41d0"><view class="progress-bg data-v-1c7c41d0"><view class="progress-fill data-v-1c7c41d0" style="{{'width:' + G}}"></view></view><text class="progress-text data-v-1c7c41d0">{{H}}/{{I}}</text></view></view><view wx:if="{{J}}" class="detail-effects data-v-1c7c41d0"><text class="effects-title data-v-1c7c41d0">当前效果:</text><text class="effects-text data-v-1c7c41d0">{{K}}</text></view><view wx:if="{{L}}" class="detail-special-effects data-v-1c7c41d0"><text class="special-effects-title data-v-1c7c41d0">武功特效:</text><text class="special-effects-text data-v-1c7c41d0">{{M}}</text></view><view class="detail-category data-v-1c7c41d0"><text class="category-title data-v-1c7c41d0">升级经验:</text><text class="category-text data-v-1c7c41d0">{{N}} / {{O}}</text></view><view wx:if="{{P}}" class="detail-moves data-v-1c7c41d0"><text class="moves-title data-v-1c7c41d0">招式列表:</text><view class="moves-list data-v-1c7c41d0"><view wx:for="{{Q}}" wx:for-item="move" wx:key="j" class="{{['move-item', 'data-v-1c7c41d0', move.k && 'move-unlocked', move.l && 'move-locked']}}"><view class="move-header data-v-1c7c41d0"><text class="move-name data-v-1c7c41d0">{{move.a}}</text><view class="{{['move-status-badge', 'data-v-1c7c41d0', move.c && 'status-unlocked', move.d && 'status-locked']}}">{{move.b}}</view></view><view class="move-details data-v-1c7c41d0"><text class="move-unlock-condition data-v-1c7c41d0">解锁条件: {{move.e}}级</text><text wx:if="{{move.f}}" class="move-attack data-v-1c7c41d0">攻击力: +{{move.g}}</text><text wx:if="{{move.h}}" class="move-defense data-v-1c7c41d0">防御力: +{{move.i}}</text></view></view></view></view></view><view class="modal-footer data-v-1c7c41d0"><button class="modal-btn cancel-btn data-v-1c7c41d0" bindtap="{{R}}">关闭</button><button wx:if="{{S}}" class="modal-btn confirm-btn data-v-1c7c41d0" bindtap="{{T}}" disabled="{{U}}"> 修炼 </button></view><view wx:if="{{V}}" class="data-v-1c7c41d0" style="background:#fffbe6;color:#ad6800;padding:6px 10px;margin-top:8px;border-radius:6px;font-size:13px">{{W}}</view></view></view><view wx:if="{{Z}}" class="modal-overlay data-v-1c7c41d0" bindtap="{{ag}}"><view class="modal-content data-v-1c7c41d0" catchtap="{{af}}"><view class="modal-header data-v-1c7c41d0"><text class="modal-title data-v-1c7c41d0">{{aa}} - 招式</text><text class="modal-close data-v-1c7c41d0" bindtap="{{ab}}">×</text></view><view wx:if="{{ac}}" class="modal-body data-v-1c7c41d0"><view class="moves-container data-v-1c7c41d0"><view wx:for="{{ad}}" wx:for-item="move" wx:key="c" class="move-item data-v-1c7c41d0"><text class="move-name data-v-1c7c41d0">{{move.a}}</text><text class="move-desc data-v-1c7c41d0">{{move.b}}</text></view></view></view><view class="modal-footer data-v-1c7c41d0"><button class="modal-btn cancel-btn data-v-1c7c41d0" bindtap="{{ae}}">关闭</button></view></view></view><view wx:if="{{ah}}" class="martial-select-modal-mask data-v-1c7c41d0" bindtap="{{an}}"><view class="martial-select-modal data-v-1c7c41d0" catchtap="{{am}}"><view class="martial-select-title data-v-1c7c41d0">选择{{ai}}武功</view><scroll-view class="martial-select-list data-v-1c7c41d0" scroll-y="true"><view wx:for="{{aj}}" wx:for-item="skill" wx:key="f" class="{{['martial-select-item', 'data-v-1c7c41d0', skill.g && 'selected']}}" bindtap="{{skill.h}}"><view class="martial-select-header data-v-1c7c41d0"><text class="martial-select-name data-v-1c7c41d0">{{skill.a}}</text><view class="martial-select-status data-v-1c7c41d0"><text class="martial-select-level data-v-1c7c41d0">Lv.{{skill.b}}</text><text class="martial-select-quality data-v-1c7c41d0">{{skill.c}}品质</text><text wx:if="{{skill.d}}" class="martial-select-equipped data-v-1c7c41d0">已装备</text></view></view><text class="martial-select-desc data-v-1c7c41d0">{{skill.e}}</text></view><view wx:if="{{ak}}" class="martial-select-empty data-v-1c7c41d0">暂无可选武功</view></scroll-view><button class="martial-select-cancel data-v-1c7c41d0" bindtap="{{al}}">取消</button></view></view><view wx:if="{{ao}}" class="modal-overlay data-v-1c7c41d0" style="z-index:9999"><view class="modal-content data-v-1c7c41d0" style="max-width:420px;min-width:320px"><view class="modal-header data-v-1c7c41d0"><text class="modal-title data-v-1c7c41d0">修炼日志</text><text class="modal-close data-v-1c7c41d0" bindtap="{{ap}}">×</text></view><scroll-view scroll-y="true" scroll-top="{{ar}}" class="modal-body train-log-scroll data-v-1c7c41d0" style="max-height:260px"><view wx:for="{{aq}}" wx:for-item="msg" wx:key="b" class="data-v-1c7c41d0" style="color:#ad6800;font-size:14px;margin-bottom:6px;line-height:1.6;text-align:left;word-break:break-all;white-space:normal">{{msg.a}}</view></scroll-view><view class="modal-footer data-v-1c7c41d0"><button class="modal-btn confirm-btn data-v-1c7c41d0" bindtap="{{as}}">关闭</button></view></view></view></view>